# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-12-19 09:17:36
# Description: 山东中长期合约电量分配
"""

import numpy as np
import pandas as pd
import calendar
import logging
logger = logging.getLogger()


class ContractElectricityDistribution:
    def __init__(self, year, month, package, package_selected_buyer, elec_demand_buyer, generator_params,
                 generator_overhaul):
        logger.info("---------------------山东中长期合约电量分配模型-------------------------")
        # 通用参数
        self.year = int(year)  # 目标年份
        self.month = int(month)  # 目标月份
        self.days_month = calendar.monthrange(self.year, self.month)[1]  # 目标月份天数
        self.time_list = pd.date_range("00:00", "23:00", freq="H").map(lambda s: str(s).split(" ")[1]).values  # 目标时刻列表

        # 套餐数据预处理：日分时电量比例字典, 日分时电价字典
        logger.info(f"1. 套餐数据预处理...")
        self.elec_ratio_dict, self.elec_price_dict = self.get_data_package(package)

        # 售电公司数据处理
        logger.info(f"2. 售电公司数据处理...")
        self.buyer_name_array, self.elec_buy_array, self.buyer_package_dict, self.df_elec_buy = self.get_data_buyer(
            package_selected_buyer, elec_demand_buyer)

        # 发电机组数据处理
        logger.info(f"3. 发电机组数据处理...")
        self.seller_name_array, self.generator_params_dict, self.elec_sell_dict = self.get_data_seller(generator_params,
                                                                                                       generator_overhaul)

    def get_data_package(self, package):
        """ 套餐数据处理 """
        package = pd.DataFrame(package)
        package["time"] = package["time"].map(lambda s: str(s)[:8])  # "00:00:00" ~ "23:00:00"
        package = package.sort_values(["month", "package", "time"])

        # 筛选当前月的套餐数据
        p_month = package[package["month"] == self.month]
        # 套餐日分时电量比例字典
        elec_ratio_dict = {p: p_month[p_month["package"] == p]["elec_ratio"].values for p in
                           p_month["package"].unique()}
        # 套餐日分时电价字典
        elec_price_dict = {p: p_month[p_month['package'] == p]['elec_price'].values for p in
                           p_month['package'].unique()}

        return elec_ratio_dict, elec_price_dict

    def get_data_buyer(self, package_selected_buyer, elec_demand_buyer):
        """ 售电侧电量需求数据 """

        # 1, 售电公司套餐选择
        package_buy = pd.DataFrame(package_selected_buyer)
        package_buy["month"] = package_buy["month"].astype(int)
        # 售电公司套餐字典
        buyer_package_dict = package_buy.drop("month", axis=1).set_index("buyer").to_dict()['package']

        # 2, 售电公司月分日电量需求
        elec_buy = pd.DataFrame(elec_demand_buyer)
        elec_buy["date"] = elec_buy["date"].map(lambda s: str(s).split(" ")[0])
        elec_buy["month"] = pd.to_datetime(elec_buy["date"]).map(lambda s: s.month)

        # 3, 数据组合，根据选择的套餐计算分时电量
        df_elec_buy = pd.merge(elec_buy, package_buy, on=["buyer", "month"], how="left")
        df_elec_buy = df_elec_buy[df_elec_buy["month"] == self.month]  # 筛选当月数据
        df_elec_buy["elec_ratio"] = df_elec_buy["package"].map(self.elec_ratio_dict)  # 电量比例
        # 日分时电量分解
        df_elec_buy[self.time_list] = (df_elec_buy["elec_demand"] * df_elec_buy["elec_ratio"]).apply(pd.Series)
        df_elec_buy = df_elec_buy.sort_values(["buyer", "date"]).reset_index(drop=True)
        df_elec_buy = df_elec_buy[["buyer", "package", "date"] + list(self.time_list)]

        # 4, 售电公司名称列表
        buyer_name_array = df_elec_buy["buyer"].unique()

        # 5, 结构化处理
        elec_array_list = []
        for buyer in buyer_name_array:
            df_tmp = df_elec_buy[df_elec_buy["buyer"] == buyer]
            df_tmp = df_tmp.sort_values("date")  # 按日期排序，保证日电量需求有序
            list_tmp = df_tmp[self.time_list].values.tolist()  # 二维列表，当前售电公司日分时电量需求
            elec_array_list.append(list_tmp)
            # print(f"{buyer}.shape = {np.array(list_tmp).shape}")

        elec_buy_array = np.array(elec_array_list)
        # print(elec_buy_array.shape)
        # print((len(buyer_name_array), self.days_month, 24))
        assert elec_buy_array.shape == (len(buyer_name_array), self.days_month, 24)

        return buyer_name_array, elec_buy_array, buyer_package_dict, df_elec_buy

    def get_data_seller(self, generator_params, generator_overhaul):
        """ 发电测: 参与目标月份发电工作的机组数据 """

        # 1. 机组参数
        generator_params = pd.DataFrame(generator_params)
        generator_params["month"] = generator_params["month"].astype(int)
        generator_params = generator_params[generator_params["month"] == self.month]

        # 2. 机组检修
        if len(generator_overhaul) != 0:
            generator_overhaul = pd.DataFrame(generator_overhaul)
            # 日期类别整理
            generator_overhaul['date_start_overhaul'] = generator_overhaul['date_start_overhaul'].map(lambda s: str(s).split(" ")[0])
            generator_overhaul['date_end_overhaul'] = generator_overhaul['date_end_overhaul'].map(lambda s: str(s).split(" ")[0])

            # 计算停检天数
            generator_overhaul["days_overhaul"] = (pd.to_datetime(generator_overhaul['date_end_overhaul']) - pd.to_datetime(generator_overhaul['date_start_overhaul'])).map(lambda s: s.days + 1)

            # 机组数据组合
            df_generator = pd.merge(generator_params, generator_overhaul, on=["month", "name_generator"], how="left")
            df_generator["days_overhaul"] = df_generator["days_overhaul"].fillna(0)  # 无检修的机组检修天数置为0
            df_generator = df_generator[df_generator["month"] == self.month]  # 筛选当月机组信息
        else:
            df_generator = generator_params.copy()
            df_generator["days_overhaul"] = 0

        # 3. 机组相关参数
        # 计算运行容量：用于计算双边合约负荷率
        df_generator["run_capacity"] = (df_generator["full_capacity"] / self.days_month) * (
                    self.days_month - df_generator["days_overhaul"])
        # 计算当月最大上网电量: 运行容量 * 24h * days_month天 * (1-厂用电率) * 双边合约负荷率上限
        df_generator["online_capacity_max"] = df_generator["run_capacity"] * 24 * self.days_month * (
                    1 - df_generator["power_consumption_rate"]) * df_generator["upper_limit_load_rate"]

        # 4. 机组名称列表
        generator_name_array = df_generator["name_generator"].unique()

        # 5. 机组参数字典
        generator_params_dict = df_generator.set_index("name_generator").T.to_dict()

        # 6. 机组最大上网电量字典
        elec_sell_dict = df_generator[['name_generator', 'online_capacity_max']].set_index('name_generator').to_dict()[
            'online_capacity_max']

        return generator_name_array, generator_params_dict, elec_sell_dict

    def run(self):
        """ 核心：中长期合约电量分配逻辑 """
        logger.info(f"5. while 循环匹配...")
        # 发售两侧数据
        elec_buy_array = self.elec_buy_array.copy()  # 售电侧需求数据, array
        elec_sell_dict = self.elec_sell_dict.copy()  # 发电侧供给数据, 每个机组当月可提供的最大上网电量

        # 结果保存字典, key: value = "发电机组_售电公司": "匹配电量数组"
        res_dict = {}

        i = 1
        while (elec_buy_array.sum() > 1) & (i < 100):  # 终止条件: 所有售电公司的电量需求都得到满足

            # logger.info(f"    第{i}次匹配")

            # 1. 售电侧: 最大需求量售电公司相关参数
            elec_buy_sum = elec_buy_array.sum(axis=1).sum(axis=1)   # 当月售电公司需求总量, array, shape=(售电公司数量,)
            idx_max_buyer = elec_buy_sum.argmax()                   # 当前售电公司索引, int
            name_max_buyer = self.buyer_name_array[idx_max_buyer]   # 当前售电公司名称, str
            elec_max_buyer = elec_buy_array[idx_max_buyer]          # 当前售电公司的电力需求曲线, array, shape=(当月天数, 24)
            package_max_buyer = self.buyer_package_dict[name_max_buyer]     # 当前售电公司所选套餐
            elec_ratio_hourly_buyer = self.elec_ratio_dict[package_max_buyer]  # 当前售电公司所选套餐的分时电量
            elec_ratio_daily_buyer = elec_max_buyer.sum(axis=1) / elec_max_buyer.sum()  # 当前售电公司月分日电量占比, array, shape=(days_month,)

            # 售电公司需求非0的日期索引
            nonzero_date_idx_buyer = np.nonzero(elec_max_buyer.sum(axis=1))[0]
            # print(f"{i}.1. 售电侧最大需求量： {elec_max_buyer.sum()}")

            # 2. 发电侧最大产能机组相关参数
            name_max_seller = None              # 最大发电能力对应机组名称
            elec_max_seller = None              # 当前机组本月最大上网电量
            param_max_seller = None             # 当前机组参数
            days_overhaul_max_seller = None     # 当前机组停检天数
            # print(f"{i}.2. 发电侧最大供应上网电量： {elec_max_seller}")

            elec_sell_dict_sorted = sorted(elec_sell_dict.items(), key=lambda s: s[1], reverse=True)    # 按照可发电总量排序
            for name_max_seller, elec_max_seller in elec_sell_dict_sorted:

                param_max_seller = self.generator_params_dict[name_max_seller]  # 当前机组参数
                days_overhaul_max_seller = int(param_max_seller["days_overhaul"])  # 当前机组停检天数

                # 当前机组无检修
                if param_max_seller["days_overhaul"] == 0:
                    break
                # 若有检修，并且 售电公司需求量非0日期 与 当前发电机组开机日期 有重合, 则选择当前机组; 否则选择下一个机组
                else:
                    idx_start = int(param_max_seller['date_start_overhaul'].split("-")[-1]) - 1
                    idx_end = int(param_max_seller['date_end_overhaul'].split("-")[-1]) - 1
                    # 发电测开机日期索引
                    nonzero_date_idx_seller = np.r_[np.arange(self.days_month)[:idx_start], np.arange(self.days_month)[idx_end + 1:]]
                    # 需求和供给交叉的索引数组
                    idx_intersect = np.intersect1d(nonzero_date_idx_buyer, nonzero_date_idx_seller)
                    if len(idx_intersect) != 0:
                        break

            logger.info(f"    第{i}次匹配: {name_max_buyer} - {name_max_seller}")

            # 3. 计算本次匹配电量
            scale_seller_buyer = elec_max_seller / elec_max_buyer.sum()     # 发电机组总电量 / 售电侧总需求电量
            if scale_seller_buyer < 1:  # 机组产能只能满足部分售电公司电量
                if days_overhaul_max_seller == 0:  # 当前机组无检修
                    elec_array = elec_max_buyer * scale_seller_buyer
                else:   # 当前机组有检修，将所有产能分配至工作日
                    idx_start = int(param_max_seller['date_start_overhaul'].split("-")[-1]) - 1
                    idx_end = int(param_max_seller['date_end_overhaul'].split("-")[-1]) - 1
                    elec_ratio_daily_buyer[idx_start: idx_end + 1] = np.zeros(days_overhaul_max_seller)  # 检修日电量占比设为0
                    elec_ratio_daily_seller = elec_ratio_daily_buyer / elec_ratio_daily_buyer.sum()  # 比例缩放至1, 即当前机组月分日电量比例
                    elec_daily_seller = elec_ratio_daily_seller * elec_max_seller   # 当前机组月分日电量
                    elec_array = np.array([elec_daily * elec_ratio_hourly_buyer for elec_daily in elec_daily_seller])   # 按照售电公司套餐比例分解
            else:   # 机组产能超过售电公司需求量
                elec_array = elec_max_buyer.copy()
                if days_overhaul_max_seller != 0:
                    idx_start = int(param_max_seller['date_start_overhaul'].split("-")[-1]) - 1
                    idx_end = int(param_max_seller['date_end_overhaul'].split("-")[-1]) - 1
                    elec_array[idx_start: idx_end + 1] = np.zeros(shape=(days_overhaul_max_seller, 24))

            # print(f"{i}.3. 本次匹配结果: {name_max_buyer}_{name_max_seller}\n{elec_array}")

            # 4. 本轮分配结果存储
            res_dict[f"{name_max_seller}_{name_max_buyer}"] = elec_array.tolist()

            # 5. 更新售电侧和发电侧电量数据
            elec_buy_array[idx_max_buyer] = elec_max_buyer - elec_array
            elec_sell_dict[name_max_seller] = elec_max_seller - elec_array.sum()

            i += 1

        # print("**" * 60)
        # print(f"售电侧名称列表: {self.buyer_name_array}")
        # print(f"售电侧剩余未满足: {elec_buy_array.sum(axis=1).sum(axis=1)}")
        # print("**" * 60)
        # print(f"发电侧剩余未分配: {elec_sell_dict}")

        return res_dict

    def get_data_result(self):
        """ 输出数据整理 """
        logger.info(f"4. 发售两侧电量匹配...")
        # 1. 售电侧数据："result_data_buyer"
        df_buy_out = pd.melt(frame=self.df_elec_buy, id_vars=["buyer", "package", "date"], value_vars=self.time_list, var_name="time", value_name="elec_demand")
        df_buy_out['date_time'] = df_buy_out['date'] + " " + df_buy_out["time"]
        df_buy_out = df_buy_out.sort_values(['buyer', 'date_time']).reset_index(drop=True)
        df_buy_out['elec_price'] = df_buy_out.apply(lambda s: self.elec_price_dict[s['package']][(self.time_list == s['time']).argmax()], axis=1)
        df_buy_out = df_buy_out[["buyer", "date_time", "elec_demand", "elec_price"]]
        # print(f"df_buy_out: \n{df_buy_out}")
        result_data_buyer = df_buy_out.to_dict("records")

        # 2. 发电侧数据："result_data_seller"
        data_dict = self.run()  # 电量匹配结果字典
        res_list = []
        for pair in data_dict.keys():
            name_seller = pair.split("_")[0]    # 发电机组名称
            name_buyer = pair.split("_")[-1]    # 售电公司名称

            df = pd.DataFrame(data={
                "date_time": pd.date_range(f"{self.year}-{self.month}-01", f"{self.year}-{self.month}-{self.days_month} 23:00:00", freq="H"),
                "elec_demand": np.array(data_dict[pair]).flatten()
            })

            df['date_time'] = df['date_time'].astype(str)
            df["time"] = df['date_time'].map(lambda s: s.split(" ")[1])
            df['seller'] = name_seller
            df['buyer'] = name_buyer

            # 根据售电公司所选套餐，添加分时价格
            df["package"] = df['buyer'].map(self.buyer_package_dict)
            df['elec_price'] = df.apply(lambda s: self.elec_price_dict[s['package']][(self.time_list == s['time']).argmax()], axis=1)

            res_list.append(df)

        df_sell = pd.concat(res_list).reset_index(drop=True)
        df_sell_out = df_sell[["seller", "buyer", "date_time", "elec_demand", "elec_price"]]
        # print(f"df_sell_out: \n{df_sell_out}")
        result_data_seller = df_sell_out.to_dict("records")

        # 3. 匹配电量数据："result_data_distribution"
        df_sell['month'] = self.month
        df_distribution = df_sell.groupby(['month', 'seller', 'buyer'])['elec_demand'].sum().reset_index().sort_values("seller").reset_index(drop=True)
        # print(f"df_distribution: \n{df_distribution}")
        result_data_distribution = df_distribution.to_dict("records")

        # 4. 数据组合输出
        result_data = {
            "result_data_distribution": result_data_distribution,  # 发售两侧月度匹配数据
            "result_data_buyer": result_data_buyer,  # 售电侧数据：分时电量 + 分时电价
            "result_data_seller": result_data_seller,  # 发电测数据：分时电量 + 分时电价
        }
        # print(f"res_dict: \n{result_data}")

        # logger.info(f'result: \n{result_data}')
        logger.info("--------------------- 山东中长期合约电量分配模型 计算完成-------------------------")

        return result_data


if __name__ == '__main__':
    path = r"F:\ToGeek\data_research\20221206_山东电量分配\data\data_fixed\data_input_m10.xlsx"
    year_ = 2022
    month_ = 10
    package_ = pd.read_excel(path, sheet_name="套餐数据")
    package_selected_buyer_ = pd.read_excel(path, sheet_name="售电公司套餐选择")
    elec_demand_buyer_ = pd.read_excel(path, sheet_name="售电公司电量需求")
    generator_params_ = pd.read_excel(path, sheet_name="机组参数")
    generator_overhaul_ = pd.read_excel(path, sheet_name="机组检修")

    model = ContractElectricityDistribution(year=year_,
                                            month=month_,
                                            package=package_,
                                            package_selected_buyer=package_selected_buyer_,
                                            elec_demand_buyer=elec_demand_buyer_,
                                            generator_params=generator_params_,
                                            generator_overhaul=generator_overhaul_)
    result = model.get_data_result()
    print(result)
    # import json
    # with open('0_result.json', 'w') as file:  # 导出文件名：data.json
    #     file.write(json.dumps(result,  # 需要导出的字典
    #                           indent=4,  # 缩进4空格
    #                           ensure_ascii=False))  # 中文字符不转义成Unicode
