# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.shandong.optimize_subsection_declaration_sd import \
    SubsectionDeclarationIncomeSD, SubsectionDeclarationProfitSD, MatrixSubsectionDeclarationSD


class GAProfitHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1300)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 7)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        model = SubsectionDeclarationProfitSD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                            lower_price=lower_price, upper_price=upper_price,
                                            min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                            default_subsection=default_subsection, price_decimal=price_decimal)
        result = model.predict()
        self.write(result)


class GAIncomeHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1300)
        default_subsection = params.pop('default_subsection', 7)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        model = SubsectionDeclarationIncomeSD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                              lower_price=lower_price, upper_price=upper_price, POP_SIZE=POP_SIZE,
                                              N_GENERATIONS=N_GENERATIONS, default_subsection=default_subsection,
                                              price_decimal=price_decimal)
        result = model.predict()
        self.write(result)


class MatrixGAProfitHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1300)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 7)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        model = MatrixSubsectionDeclarationSD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                            lower_price=lower_price, upper_price=upper_price,
                                            min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                            default_subsection=default_subsection, price_decimal=price_decimal)
        result = model.run()
        self.write(result)
