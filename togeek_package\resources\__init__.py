# -*- coding: utf-8 -*-

from .version_handler import VersionHandler

from .common.autocorr_prophet_handler import AutoCorrProphetHandler  # 自相关时间序列模型
from .common.distance_calculation_handler import DistanceCalculationHandler     # 通用距离计算
from .common.coal_consum_function_handler import CoalConsumFunctionHandler      # 煤耗曲线拟合
from .common.loadrate_fitting_handler import LoadRateFittingHandler    # 负荷率拟合
from .common.price_etr_handler import PricePredETRHandler      # 通用随机森林模型
from .common.price_bsf_handler import PricePredBSFHandler      # 通用三段拟合模型
from .common.price_xgb_handler import PricePredXGBHandler      # 通用提升树模型

from .price.node_pricing_handler import NodePricingHandler
from .price.declaration_pre_handler import DeclarationValueEvalHandler
from .price.declaration_correct_pre_handler import DeclarationCorrectValueHandler  # 竞价空间拟合负荷率修正法
from .price.sim_el_price_prediction_handler import ElPricePredictionEvalHandler
from .price.sim_el_price_prediction_handler import ElPricePredictionValueEvalHandler
from .price.mengxi.price_mx_prediction_handler import PricePredictionValueEvalHandlerMX
from .price.mengxi.price_mx_declaration_handler import DeclarationValueEvalHandlerMX
from .price.mengxi.price_mx_longtime_bsf_handler import PricePredBsfHandlerMx
from .price.zcqfsdjg_handler import ZcqfsdYueHandler, ZcqfsdXunHandler, ZcqfsdRiHandler  # 中长期分时段价格
from .price.boot_capacity_handler import BootValueEvalHandler
from .price.price_db_prediction_handler import ElPricePredictionValueEvalHandlerDB
from .price.price_db_declaration_handler import DeclarationValueEvalHandlerDB
from .price.db_boot_capacity_handler import BootValueEvalHandlerDB
from .price.mengxi.price_percentile_handler import PricePercentileHandlerMx
from .price.mengxi.price_mx_prediction_jingjia_handler import PricePredictionJingjiaHandlerMX
from .price.mengxi.price_mx_prediction_longtime48_handler import PricePredictionLongtimeHandlerMX48
from .price.mengxi.price_mx_prediction_node48_handler import PricePredictionLongtimenNodeHandlerMX48
from .price.mengxi.price_mx_prediction_provinces_handler import PriceProvincesHandlerMx
from .price.mengxi.hn_price_mx_prediction_d1_handler import PricePredictionValueEvalHandlerHnMX
from .price.mengxi.hn_price_mx_prediction_d2_handler import PricePredictionValueEvalHandlerHnMX2

from .load.society_daily_handler import SocietyDailyHandler
from .load.society_monthly_handler import SocietyMonthlyHandler
from .load.customer_load_handler import CustomerLoadHander
from .load.customer_load_handler import CustomerLoadPreHandler
from .load.customer_load_handler import CustomerLoadFasterPreHandler  # 输出模型参数的时间序列模型
from .load.newpower_handler import NewPowerLoadHander
from .load.installed_capacity_handler import InstalledCapacityHandler  # 全网装机容量
from .load.load_hb_pred_handler import LoadPredHanderHB  # 河北建投负荷预测
from .load.sun_power_pred_handler import SunPowerPredHandler # 单机组光伏预测
from .load.mengxi_newenergy_load_predict_handler import XGBMengxiNewenergyLoadPredictHandler #蒙西新能源负荷预测

from .optimize.freq_svr_intraday_handler import FreqSvrIntradayHandler
from .optimize.biding_space_handler import BiddingSpaceHandler
from .optimize.optimal_bid_handler import OptimalBidingHandler
from .optimize.operation_capacity_handler import OperationCapacityHandler  # 火电机组运行容量
from .optimize.subsection_declaration_handler import SubsectionDeclarationHandler   # 分段报价_遗传算法
from .optimize.subsection_declaration_profit_handler import SubsectionDeclarationProfitHandler  # 分段报价_收益最大_遗传算法
from .optimize.bided_power_handler import BidedPowerHandler, BiddedPowerHandlerSD     # 分时刻中标出力（已知机组信息+节点价格+分段报价）
from .optimize.load_declaration_handler import LoadDeclarationHandler   # 售电公司日前市场负荷申报推荐方案，一次调用只支持一个售电公司
from .optimize.optimize_sd_contract_electricity_distribution_handler import ContractElectricityDistributionHandler  # 山东中长期合约电量分解
from .optimize.optimize_sd_contract_electricity_distribution_v2_handler import ContractElectricityDistribution_v2_Handler  #


# from .optimize.trading_evaluation.segmentation_handler import AssignSegHandler, AdaptSegHandler, DistAdaptSegHandler, \
#     DistAdaptSegSplitHandler
# from .optimize.trading_evaluation.planning_evaluation_handler import StateEvaluationHandler, PlanningEvaluationHandler
# from .optimize.trading_evaluation.interp_margin_handler import InterpMarginHandler
# from .optimize.trading_evaluation.run_cost_handler import RunCostHandler
from .optimize.power_generator_handler import LoadGeneratorHandler
from .optimize.xny_lambda_avg_handler import XnyLambdaAvgHandler   # 新能源申报系数
from .optimize.subsection_declaration.genetic_algorithm_handler import GAIncomeHandler, GAProfitHandler  # 分段报价通用模型-遗传算法
from .optimize.subsection_declaration.marginal_cost_handler import AssignMCSCommonHandler, AdaptMCSCommonHandler, \
        DistAdaptMCSCommonHandler                                        # 分段报价通用模型-边际成本

from .optimize.genetic_algorithm_elec.days_total_handler import DaysTotalElecHandler    # 分段报价遗传算法-多天总发电量约束
from .optimize.genetic_algorithm_elec.days_daily_handler import DaysDailyElecHandler    # 分段报价遗传算法-多天多发电量约束
from .optimize.genetic_algorithm_elec.daily_total_handler import DailyTotalElecHandler  # 分段报价遗传算法-单天总发电量约束
from .optimize.genetic_algorithm_elec.matrix_daily_total_handler import MatrixDailyTotalElecHandler  # 分段报价遗传算法-单天总发电量约束-矩阵
from .optimize.optimize_deep_adjust_handler import OptimizeDeepAdjustHandler, OptimizeDeepAdjustCommonHandler  # 供热机组参与火电深度调峰市场优化
from .optimize.optimize_deep_adjust_eboiler_handler import OptimizeDeepAdjustEboilerHandler  # 供热机组参与火电深度调峰市场优化,固定发电机负荷
from .optimize.price_trans_provincial_handler import OptimizeProvincialEvalHandler           # 省间配电优化
from .optimize.shandong_subsection_declaration import GAIncomeHandlerSD, GAProfitHandlerSD, MatrixGAProfitHandlerSD, \
    AssignMCSHandlerSD, AdaptMCSHandlerSD, DistAdaptMCSHandlerSD                              # 山东省分段报价方案
from .optimize.optimize_sd_estorage_pv_income_handler import OptimizeEvStoragePvHandler     # 山东青岛中石油光储充一体化
from .optimize.shanxi_subsection_declaration import GAProfitHandlerSXGN, GAProfitHandlerSXJN, GAIdealProfitHandler  # 山西国能/京能收益最大/国能理想机组报价方案

from .optimize.mx_subsection_declaration.genetic_algorithm_handler import MxGAProfitHandler  # 蒙西分段报价通用模型-遗传算法
from .optimize.mx_subsection_declaration.marginal_cost_handler import AssignMCSCommonHandler, MxAdaptMCSCommonHandler, \
        MxDistAdaptMCSCommonHandler                                        # 蒙西分段报价通用模型-边际成本
from .optimize.mx_bided_power_handler import MxBidedPowerHandler    # 蒙西分时刻中标出力计算
from .optimize.mx_bided_long_power_handler import MxBidedLongPowerHandler
from .optimize.mengxi.mx_engergy_handler import MxEnergyHandler   # 蒙西储能策略模型
# from .optimize.optimize_energy_storage_handler import OptimizeEnergyStorageHandler, OptimizeEnergyStorageHandlerV2      # 广东储能容转需+峰谷套利优化
# from .optimize.optimize_energy_storage_handler import OptimizeEnergyStorageHandlerV3      # 广东储能容转需+峰谷套利优化
# from .optimize.optimize_energy_storage_handler import OptimizeEnergyStorageHandlerV4      # 广东储能容转需+峰谷套利优化+峰谷套利下限值约束
from .optimize.mengxi.mxxny_replacement_handler import MxReplacementXnyHandler
from .optimize.mengxi.mxhd_replacement_handler import MxReplacementHDHandler
# from .optimize.mx_energy_storage_income_handler import OptimizePvEnergyStorageIncomeHandler  # 蒙西光储模型储能策略
from .optimize.optimize_bid_strategy_handler import OptimizeCostHandler   # 河北售电竞价策略
from .optimize.liaoning.ln_genetic_algorithm_handler import LNGAProfitHandler   # 辽宁分段报价策略-度电收益最大
from .optimize.liaoning.ln_marginal_cost_handler import LNAdaptMCSCommonHandler, LNDistAdaptMCSCommonHandler  # 辽宁边际成本分段报价、节点电价分段报价
from .optimize import Spot_Declaration_lambda_SD_Handler        # 山东发电侧，现货申报系数
from .optimize.guangdong.gd_marginal_cost_handler import GDGAProfitHandler, GDGAProfitHandlerTS   # 广东分段报价策略-度电收益最大
from .optimize.guangdong.gd_ga_handler import GDGACustomProfitHandlerTS  # 自定义出力段的分段报价策略
from .optimize.guangdong.gd_dlcn.gd_dlcn_opt_handler import DlcnOptimizationHandlerTS  # 广东独立储能申报策略
from .optimize.mengxi.mxxny_replacement_handler import MxReplacementLyHandler   # 龙源蒙西合约置换交易策略
from .optimize.hebei.xiongantower_pv_storage_optimization.xiongan_tower_pv_storage_optimization_main_handler import XionganTowerPVStorageOptimizationHandlerTS
from .optimize.mengxi.optimize_mx_monthfile_handler import MXMonthlyFileHandler

from .optimize.nanwang.genetic_algorithm_handler import GAProfitHandlerNFDW  # 南方电网分段报价策略-收益最大
from .optimize.guangdong.gd_marginal_cost_handler import GAProfitHandlerGD  # 广东分段报价策略-收益最大

from .price.service_price_sx_declaration_handler import DeclarationValueEvalHandlerServiceSX
from .price.service_price_sx_sim_el_prediction_handler import PricePredictionValueEvalHandlerServiceSX
from .price.service_price_sx_declaration_common_handler import DeclarationValueEvalHandlerServiceCommonSX
from .price.service_price_sx_sim_el_prediction_common_handler import PricePredictionValueEvalHandlerServiceCommonSX
from .price.service_price_sx_similar_date_common_handler import SimPredPriceHandlerServiceCommonSX
from .price.service_price_sx_sim_el_prediction_common3_handler import PricePredictionValueEvalHandlerServiceCommon3SX
from .price.service_price_sx_declaration_common3_handler import DeclarationValueEvalHandlerServiceCommon3SX
from .price.service_price_sx_common_total_handler import PricePredTotalHandlerServiceCommonSX  # 山西45天统一接口
from .price.service_price_sd_handler import DataPredHandlerServiceCommonSD
from .price.service_price_sd_D1_handler import DataPredD1SDHandler      # 山东 D+1 出清电价预测

from .price.price_sd_declaration_fitting_handler import StandardDeclarationFittingHandlerSD  # 山东标准报价申报拟合价格预测
from .price.price_sx_etr_prediction_5d_handler import EtrPred5dHandlerSX                       # 山西京能5天价格预测_随机森林
from .price.price_sx_declaration_fitting_5d_handler import DeclarFitting5dHandlerSX            # 山西京能5天价格预测_竞价空间拟合
from .price.price_hb_declaration_fitting_handler import DeclarFittingHandlerHB     # 河北价格预测_竞价空间拟合
from .price.price_sd_typical_price_next_month_handler import TypicalPriceNextMonthHandlerSD     # 山东次月月典型电价预测
from .price.price_sd_ahead_price_D2D3_handler import AheadPriceD2D3HandlerSD, AheadPriceD2D9HandlerSD    # 山东D2D3日前价格预测
from .price.price_sd_price_diff_handler import PriceDiffPredHandlerSD  # 山东价差方向预测及申报策略模型
from .price.price_sd_sim_bidding_space_fitting_handler import SimSegFittingPriceHandlerSD  # 相似日竞价空间分段拟合价格预测
from .price.price_sx_price_diff_handler import PriceDiffPredHandlerSX  # 山西价差方向预测及申报策略模型
from .price.price_gd_price_diff_handler import PriceDiffPredHandlerGD  # 广东价差方向预测及申报策略模型
from .price.declaration_strategy_sd_buyer import DeclarationStrategyBuyerHandlerSD      # 山东售电侧申报方案策略
from .price.price_sax_etr_prediction_7d_handler import EtrPred7dHandlerSAX                       # 陕煤陕西7天价格预测_随机森林
from .price.mengxi.price_mx_prediction_handler import PricePredictionValueDtwHandlerMX
from .price.price_hn_price_ahead_etr_handler import PriceAheadETRHandlerHN  # 湖南节点价格预测
from .price.mengxi.price_mx_price_node_etr_handler import PriceNodeETRHandlerMX  # 蒙西节点价格预测
from .price.mengxi.price_mx_price_postproc_lr_handler import PricePostProcLRHandlerMX  # 蒙西节点价格LR后处理
from .price.price_ningxia_node_predict_handler import ETRNingxiaNodePricePredictHandler
from .price.price_gd_price_ahead_tab_handler import PriceAheadTabHandlerGD  # 广东日前价格预测
from .price.price_gd_price_real_tab_handler import PriceRealTabHandlerGD  # 广东实时价格预测
from .price.price_xj_longtime_node_handler import PredPriceWeatherHandlerXj
from .price.price_fujian_usp_predict_etr_handler import PriceD234ETRHandler #福建统一结算点电价预测

from .data_api import DataNwpHandlerHB  # 湖北相似日电价预测依赖气象数据接口


def register_resources():
    """
    :return: 注册资源
    """
    root = '/model_api'
    return [(f'{root}/version', VersionHandler),

            (f'{root}/common/autocorrprophet', AutoCorrProphetHandler),
            (f'{root}/common/distance_calculation', DistanceCalculationHandler),
            (f'{root}/common/coal_consum_function', CoalConsumFunctionHandler),
            (f'{root}/common/loadrate_fitting', LoadRateFittingHandler),
            (f'{root}/common/price/etr', PricePredETRHandler),  # 通用随机森林价格预测模型
            (f'{root}/common/price/bsf', PricePredBSFHandler),  # 通用竞价空间拟合价格预测模型
            (f'{root}/common/price/xgb_v1.0', PricePredXGBHandler),  # 通用提升树价格预测模型

            (f'{root}/price/node_pricing', NodePricingHandler),
            (f'{root}/price/declar_value', DeclarationValueEvalHandler),
            (f'{root}/price/declar_correct_value', DeclarationCorrectValueHandler),
            (f'{root}/price/eval', ElPricePredictionEvalHandler),
            (f'{root}/price/eval_value', ElPricePredictionValueEvalHandler),
            (f'{root}/price/zcqfsdjg/yue', ZcqfsdYueHandler),
            (f'{root}/price/zcqfsdjg/xun', ZcqfsdXunHandler),
            (f'{root}/price/zcqfsdjg/ri', ZcqfsdRiHandler),
            (f'{root}/price/boot_capacity', BootValueEvalHandler),
            (f'{root}/price/dongbei/eval_valuev1.0', ElPricePredictionValueEvalHandlerDB),  # 东北随机森林价格预测模型
            (f'{root}/price/dongbei/declar_valuev1.0', DeclarationValueEvalHandlerDB),  # 东北三段拟合价格预测模型
            (f'{root}/price/mengxi/eval_valuev1.0', PricePredictionValueEvalHandlerMX),  # 蒙西随机森林价格预测模型
            (f'{root}/price/mengxi/declar_valuev1.0', DeclarationValueEvalHandlerMX),   # 蒙西三段拟合价格预测模型
            (f'{root}/price/mengxi/longtime/bsgv1.0', PricePredBsfHandlerMx),   # 蒙西中长期三段拟合价格预测模型
            (f'{root}/price/mengxi/percentile', PricePercentileHandlerMx),   # 蒙西中长期三段拟合价格预测模型
            (f'{root}/price/mengxi/prediction_jingjiav1.0', PricePredictionJingjiaHandlerMX),   # 蒙西实际竞价空间-价格预测模型
            (f'{root}/price/mengxi/longtime/unify_metaphasev1.0', PricePredictionLongtimeHandlerMX48),   # 蒙西4~8天统一结算点价格预测模型
            (f'{root}/price/mengxi/longtime/node_metaphasev1.0', PricePredictionLongtimenNodeHandlerMX48),   # 蒙西4~8天节点价格预测模型
            (f'{root}/price/mengxi/provincesv1.0', PriceProvincesHandlerMx),   # 蒙西省间价格预测模型
            (f'{root}/price/mengxi/hn_eval_valuev1.0', PricePredictionValueEvalHandlerHnMX),   # 蒙西华能D+1价格预测模型
            (f'{root}/price/mengxi/hn_eval_valuev2.0', PricePredictionValueEvalHandlerHnMX2),   # 蒙西华能D+1价格预测模型
            (f'{root}/price/mengxi/node_pricev1.0', PriceNodeETRHandlerMX),   # 蒙西节点价格预测模型
            (f'{root}/price/mengxi/node_price_adjust1.0', PricePostProcLRHandlerMX),   # 蒙西节点价格LR后处理模型

            (f'{root}/price/dongbei/boot_capacityv1.0', BootValueEvalHandlerDB),    # 东北运行容量预测模型
            (f'{root}/price/shandong/declaration_fitting_pricev1.0', StandardDeclarationFittingHandlerSD),  # 山东申报分布拟合价格预测模型
            (f'{root}/price/shanxi/declaration_fitting_price_5d_v1.0', DeclarFitting5dHandlerSX),  # 竞价空间拟合模型
            (f'{root}/price/shanxi/etr_pred_price_5d_v1.0', EtrPred5dHandlerSX),                   # 随机森林预测模型
            (f'{root}/price/hebei/declaration_fitting_price_v1.0', DeclarFittingHandlerHB),        # 随机森林预测模型
            (f'{root}/price/shandong/typical_price_next_month_v1.0', TypicalPriceNextMonthHandlerSD),  # 山东次月月典型电价预测
            (f'{root}/price/shandong/ahead_price_d2d3_v1.0', AheadPriceD2D3HandlerSD),  # 山东D2D3日前价格预测
            (f'{root}/price/shandong/price_diff_v1.0', PriceDiffPredHandlerSD),  # 山东价差方向预测
            (f'{root}/price/shandong/sim_bidding_space_fitting_price_v1.0', SimSegFittingPriceHandlerSD),  # 山东价格预测
            (f'{root}/price/shanxi/price_diff_v1.0', PriceDiffPredHandlerSX),    # 山西价差方向预测
            (f'{root}/price/guangdong/price_diff_v1.0', PriceDiffPredHandlerGD),  # 广东价差方向预测
            (f'{root}/price/shandong/declaration_strategy_buyer_v1.0', DeclarationStrategyBuyerHandlerSD),  # 山东售电侧申报策略
            (f'{root}/price/shandong/ahead_price_d2d3_v2.0', AheadPriceD2D9HandlerSD),  # 山东D2D3日前价格预测, 使用新能源数据，从datasets取数
            (f'{root}/price/shaanxi/etr_pred_price_7d_v1.0', EtrPred7dHandlerSAX),                   # 随机森林预测模型
            (f'{root}/price/mengxi/kmeans_etr_d1d3_v1.0', PricePredictionValueDtwHandlerMX),                   # 蒙西DTW-KMEANS预测模型
            (f'{root}/price/hunan/price_ahead_etr_v1.0', PriceAheadETRHandlerHN),                   # 湖南节点价格预测模型
            (f'{root}/price/ningxia/node_price_predict', ETRNingxiaNodePricePredictHandler),
            (f'{root}/price/guangdong/price_ahead_tab_v1.0', PriceAheadTabHandlerGD),  # 广东日前价格预测
            (f'{root}/price/guangdong/price_real_tab_v1.0', PriceRealTabHandlerGD),  # 广东实时价格预测
            (f'{root}/price/xinjiang/longtime_predict_v1.0', PredPriceWeatherHandlerXj),  # 广东实时价格预测
            (f'{root}/price/fujian/usp_etr_predict', PriceD234ETRHandler), # 福建节点价格预测

            (f'{root}/load/society_daily', SocietyDailyHandler),
            (f'{root}/load/society_monthly', SocietyMonthlyHandler),
            (f'{root}/load/customer_load', CustomerLoadHander),
            (f'{root}/load/customer_load_pre', CustomerLoadPreHandler),
            (f'{root}/load/nationwide/customer_load_pre_faster_v1.0', CustomerLoadFasterPreHandler),  # 输出模型参数的时间序列模型
            (f'{root}/load/newpower_mx', NewPowerLoadHander),
            (f'{root}/load/shanxi/installed_capacity_v1.0', InstalledCapacityHandler),
            (f'{root}/load/hebei/load_pred_v1.0', LoadPredHanderHB),  # 河北建投
            (f'{root}/sun_power_pred', SunPowerPredHandler),  # 单机组光伏预测
            (f'{root}/load/mengxi/newenergy_load_predict', XGBMengxiNewenergyLoadPredictHandler),#蒙西新能源负荷预测
            

            (f'{root}/optimize/bidingspace', BiddingSpaceHandler),
            (f'{root}/optimize/freq_svr_intraday', FreqSvrIntradayHandler),
            (f'{root}/optimize/biding', OptimalBidingHandler),

            # (f'{root}/optimize/trading-evaluation/segmentation/assign', AssignSegHandler),
            # (f'{root}/optimize/trading-evaluation/segmentation/adapt', AdaptSegHandler),
            # (f'{root}/optimize/trading-evaluation/segmentation/dist_adapt', DistAdaptSegHandler),
            # (f'{root}/optimize/trading-evaluation/segmentation/dist_adapt/split', DistAdaptSegSplitHandler),

            # (f'{root}/optimize/trading-evaluation/state_eval', StateEvaluationHandler),
            # (f'{root}/optimize/trading-evaluation/plan_eval', PlanningEvaluationHandler),
            # (f'{root}/optimize/trading-evaluation/interp_margin_cost', InterpMarginHandler),
            # (f'{root}/optimize/trading-evaluation/run_cost', RunCostHandler),
            (f'{root}/optimize/operation_capacity', OperationCapacityHandler),
            (f'{root}/optimize/subsection_declaration', SubsectionDeclarationHandler),
            (f'{root}/optimize/subsection_declaration_profit', SubsectionDeclarationProfitHandler),
            (f'{root}/optimize/bided_power', BidedPowerHandler),
            (f'{root}/optimize/load_declaration', LoadDeclarationHandler),
            (f'{root}/optimize/generator_handler', LoadGeneratorHandler),
            (f'{root}/optimize/xny_lambda_avg', XnyLambdaAvgHandler),
            (f'{root}/optimize/shandong/spot_declaration_lambda_sd', Spot_Declaration_lambda_SD_Handler),    # 山东发电侧, 现货申报lambda

            (f'{root}/optimize/subsection_declaration/genetic_algorithm/income', GAIncomeHandler),
            (f'{root}/optimize/subsection_declaration/genetic_algorithm/profit', GAProfitHandler),
            (f'{root}/optimize/subsection_declaration/marginal_cost/adapt', AdaptMCSCommonHandler),
            (f'{root}/optimize/subsection_declaration/marginal_cost/dist', DistAdaptMCSCommonHandler),

            (f'{root}/optimize/shandong/subsection_declaration/genetic_algorithm/income_v1.0', GAIncomeHandlerSD),  # 山东省分段报价方案
            (f'{root}/optimize/shandong/subsection_declaration/genetic_algorithm/profit_v1.0', GAProfitHandlerSD),
            (f'{root}/optimize/shandong/subsection_declaration/genetic_algorithm/matrix_profit_v1.0', MatrixGAProfitHandlerSD),
            (f'{root}/optimize/shandong/subsection_declaration/marginal_cost/adapt_v1.0', AdaptMCSHandlerSD),
            (f'{root}/optimize/shandong/subsection_declaration/marginal_cost/assign_v1.0', AssignMCSHandlerSD),
            (f'{root}/optimize/shandong/subsection_declaration/marginal_cost/dist_v1.0', DistAdaptMCSHandlerSD),
            (f'{root}/optimize/shandong/bidded_power_v1.0', BiddedPowerHandlerSD),
            (f'{root}/optimize/shandong/estorage_pv_income', OptimizeEvStoragePvHandler),
            (f'{root}/optimize/mx/energy_incomes_v1.0', MxEnergyHandler),
            (f'{root}/optimize/mx/replacement_2.0', MxReplacementLyHandler),

            (f'{root}/optimize/shanxi/subsection_declaration/genetic_algorithm/guoneng_profit_v1.0', GAProfitHandlerSXGN),  # 国能山西最大收益分段报价
            (f'{root}/optimize/shanxi/subsection_declaration/genetic_algorithm/jingneng_profit_v1.0', GAProfitHandlerSXJN),  # 京能山西最大收益分段报价
            (f'{root}/optimize/shanxi/subsection_declaration/genetic_algorithm/guoneng_ideal_v1.0', GAIdealProfitHandler),  # 国能山西理想机组分段报价

            (f'{root}/optimize/mx_subsection_declaration/genetic_algorithm/profit', MxGAProfitHandler),     # 蒙西
            (f'{root}/optimize/mx_subsection_declaration/marginal_cost/adapt', MxAdaptMCSCommonHandler),
            (f'{root}/optimize/mx_subsection_declaration/marginal_cost/dist', MxDistAdaptMCSCommonHandler),
            (f'{root}/optimize/mx/bided_power_v1.0', MxBidedPowerHandler),
            (f'{root}/optimize/mx/bided_longtime_power_v1.0', MxBidedLongPowerHandler),
            (f'{root}/optimize/mx/replacement_xny_v1.0', MxReplacementXnyHandler),
            (f'{root}/optimize/mx/replacement_hd_v1.0', MxReplacementHDHandler),
            (f'{root}/optimize/mx/monthlyfile_v1.0', MXMonthlyFileHandler),
            # (f'{root}/optimize/mx/energy_storage_income', OptimizePvEnergyStorageIncomeHandler),

            (f'{root}/optimize/genetic_algorithm_elec/days_total_elec', DaysTotalElecHandler),
            (f'{root}/optimize/genetic_algorithm_elec/days_daily_elec', DaysDailyElecHandler),
            (f'{root}/optimize/genetic_algorithm_elec/daily_total_elec', DailyTotalElecHandler),
            (f'{root}/optimize/genetic_algorithm_elec/matrix_daily_total_elec', MatrixDailyTotalElecHandler),

            (f'{root}/optimize/optimize_deep_adjust', OptimizeDeepAdjustHandler),
            (f'{root}/optimize/optimize_deep_adjust_v1.1', OptimizeDeepAdjustEboilerHandler),
            (f'{root}/optimize/nationwide/optimize_deep_adjust_common_v1.0', OptimizeDeepAdjustCommonHandler),
            (f'{root}/optimize/optimize_trans_provincial', OptimizeProvincialEvalHandler),
            (f'{root}/optimize/shandong/contract_electricity_distribution_v1.0', ContractElectricityDistributionHandler),       # 山东省中长期合约电量分配
            (f'{root}/optimize/shandong/contract_electricity_distribution_v2.0', ContractElectricityDistribution_v2_Handler),   # 山东省中长期合约电量分配
            # (f'{root}/optimize/guangdong/optimize_energy_storage_v1.0', OptimizeEnergyStorageHandler),   # 广东容转需+峰谷套利优化
            # (f'{root}/optimize/guangdong/optimize_energy_storage_v2.0', OptimizeEnergyStorageHandlerV2),   # 分PCS, 广东容转需+峰谷套利优化
            # (f'{root}/optimize/guangdong/optimize_energy_storage_v3.0', OptimizeEnergyStorageHandlerV3),   # 分PCS, 广东容转需+峰谷套利优化
            # (f'{root}/optimize/guangdong/optimize_energy_storage_v4.0', OptimizeEnergyStorageHandlerV4),   # 分PCS, 广东容转需+峰谷套利优化+峰谷套利收益下限约束
            (f'{root}/optimize/hebei/optimize_cost', OptimizeCostHandler),   # 河北售电竞价策略
            (f'{root}/optimize/laoning/subsection_declaration/genetic_algorithm/profit_v1.0', LNGAProfitHandler),
            # 辽宁  度电收益最大分段报价模型
            (f'{root}/optimize/laoning/subsection_declaration/marginal_cost/adapt_v1.0', LNAdaptMCSCommonHandler),
            # 辽宁   边际成本分段报价
            (f'{root}/optimize/laoning/subsection_declaration/marginal_cost/dist_v1.0', LNDistAdaptMCSCommonHandler),
            # 辽宁  节点电价分段报价
            (f'{root}/optimize/guangdong/subsection_declaration/genetic_algorithm/profit_v1.0', GDGAProfitHandler),
            # 广东  分段报价策略-度电收益最大
            (f'{root}/optimize/guangdong/subsection_declaration/genetic_algorithm/profit_v2.0', GDGAProfitHandlerTS),
            # 广东  分段报价策略-度电收益最大:考虑中长期
            (f'{root}/optimize/guangdong/subsection_declaration/genetic_algorithm/profit_custom_v1.0',
             GDGACustomProfitHandlerTS),  # 广东  分段报价策略-度电收益最大:自定义出力段
            (f'{root}/optimize/guangdong/dlcn/declaration/profit_v1.0', DlcnOptimizationHandlerTS),  # 广东  独立储能申报策略
            (f'{root}/optimize/guangdong/subsection_declaration/genetic_algorithm/profit_v3.0', GAProfitHandlerGD),  # 广东 遗传算法分段报价 考虑最小开机台数

            (f'{root}/optimize/nanwang/subsection_declaration/genetic_algorithm_profit_v1.0', GAProfitHandlerNFDW),
            # 南方电网  分段报价策略-收益最大
            (f'{root}/optimize/hebei/xiongantower_pv_storage_optimization_v1.0', XionganTowerPVStorageOptimizationHandlerTS),  # 雄安铁塔光储优化策略

            (f'{root}/service/price/shanxi/ahead_price_bsf_15_v1.0', DeclarationValueEvalHandlerServiceSX),  # 山西省15天日前统一结算点价格预测_竞价空间
            (f'{root}/service/price/shanxi/ahead_price_etr_15_v1.0', PricePredictionValueEvalHandlerServiceSX),  # 山西省15天日前统一结算点价格预测_随机森林
            (f'{root}/service/price/shanxi/common_price_bsf_15_v1.0', DeclarationValueEvalHandlerServiceCommonSX),  # 山西省15天通用价格预测_竞价空间
            (f'{root}/service/price/shanxi/common_price_etr_15_v1.0', PricePredictionValueEvalHandlerServiceCommonSX),  # 山西省15天通用价格预测_随机森林
            (f'{root}/service/price/shanxi/common_price_sim_45_v1.0', SimPredPriceHandlerServiceCommonSX),  # 山西省45天通用价格预测_相似日
            (f'{root}/service/price/shanxi/common_price_bsf_3_v1.0', DeclarationValueEvalHandlerServiceCommon3SX),  # 山西省3天通用价格预测_竞价空间
            (f'{root}/service/price/shanxi/common_price_etr_3_v1.0', PricePredictionValueEvalHandlerServiceCommon3SX),  # 山西省3天通用价格预测_随机森林
            (f'{root}/service/price/shanxi/common_price_total_45_v1.0', PricePredTotalHandlerServiceCommonSX),  # 山西省45天统一价格预测
            (f'{root}/service/price/shandong/common_price_etr_8_v1.0', DataPredHandlerServiceCommonSD),  # 山东 D+1~D+8 价格预测
            (f'{root}/service/price/shandong/common_price_etr_8_v2.0', DataPredD1SDHandler),  # 山东 D+1 出清电价预测

            # 数据接口
            (f'{root}/data_service/nwp_HB_v1.0', DataNwpHandlerHB),  # 湖北相似日电价预测依赖气象数据接口
            ]
