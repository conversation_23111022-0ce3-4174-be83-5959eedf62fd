# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2023-01-17 14:54:45
# Description: 通用三段拟合模型，可用于日前价格预测，实时价格预测
"""


import numpy as np
import pandas as pd
import jenkspy
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


class PricePredBSF:

    def __init__(self, all_data, date_list_pred, min_value=0, max_value=1500):

        logger.info("---------------------- 通用价格预测方法：三段拟合法BSF --------------------------")

        self.all_data = all_data    # 传入json数据
        self.date_list_pred = date_list_pred    # 预测日期列表
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

    def data_process(self):
        """
        传入数据整理
        """

        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame(self.all_data)
        data = data.sort_index()    # 日期索引升序排列

        # 字段检测
        for col in ["label", "bidding_space"]:
            if col not in data.columns:
                logger.info(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")
                raise ValueError(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")

        # 添加日期字段
        data['date'] = data.index.map(lambda s: s.split(" ")[0])

        return data

    @staticmethod
    def get_history_date(history_data):
        """
        从传入历史数据 history_data 中选取价格不全为0的最大历史日期 history_date
        """

        history_date_list = history_data['date'].unique().tolist()  # 历史数据日期列表
        history_date_list = sorted(history_date_list, reverse=True)  # 历史日期倒序排列

        history_date = None     # 初始化
        for date in history_date_list:
            if history_data[history_data['date'] == date]['label'].sum() == 0:  # 若当前历史日期价格全为0，则不选取当天为训练数据(NaN值求和为0)
                continue
            elif history_data[history_data['date'] == date]['label'].nunique() == 1:    # 若当前历史日期价格只有1个取值，则不选取当天为训练数据(NaN值求和为0)
                continue
            else:
                history_date = date
                break

        if history_date is None:
            raise ValueError(f"传入历史数据中，没有满足预测需求的历史数据，请检查传入数据！")

        return history_date

    def predict(self):
        """
        预测、输出
        """

        # 1. 数据处理
        source_data = self.data_process()

        # 2. 遍历预测
        y1 = 0
        result = pd.DataFrame(columns=['date_time', 'label_pred', 'type'])

        for date_pred in self.date_list_pred:

            history_data = source_data[source_data['date'] < date_pred].dropna()    # 历史数据
            history_date = self.get_history_date(history_data)  # 筛选用于预测的历史日期

            logger.info(f"date_pred: {date_pred}, date_history: {history_date}")

            # 筛选历史日期数据curdata和待预测日期数据curdatab
            curdata = source_data[source_data['date'] == history_date]
            curdatab = source_data[source_data['date'] == date_pred]

            # 价格乘竞价空间
            curdata['jenkspy'] = curdata['label'] * curdata['bidding_space']
            curdata.reset_index(inplace=True, drop=True)
            breaks = jenkspy.jenks_breaks(curdata['jenkspy'], 3)
            b1 = curdata[curdata['jenkspy'] <= breaks[1]]
            b2 = curdata[(curdata['jenkspy'] > breaks[1]) & (curdata['jenkspy'] <= breaks[2])]
            b3 = curdata[curdata['jenkspy'] > breaks[2]]

            b2jingjiamean = b2['bidding_space'].mean()
            b2rqrpmean = b2['label'].mean()

            k32_2m3all = ((b3['label'] - b2rqrpmean) / (b3['bidding_space'] - b2jingjiamean)).mean()

            # 第三段直线
            p13 = np.poly1d([k32_2m3all, np.mean(b3['label']) - k32_2m3all * np.mean(b3['bidding_space'])])

            # 多项式拟合
            parameter1 = np.polyfit(curdata['bidding_space'], curdata['label'], 1)
            p1 = np.poly1d(parameter1)
            parameter3 = np.polyfit(curdata['bidding_space'], curdata['label'], 3)
            p3 = np.poly1d(parameter3)

            todayuse = 1
            if parameter3[0] > 0:
                delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
                if delta < 0:
                    todayuse = 3
                else:
                    x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                    x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                    y1 = p3(x1)
                    y2 = p3(x2)
                    if x1 > 0 and y1 < y2 * 1.2:
                        todayuse = 33
            else:
                if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                    todayuse = 13
                else:
                    todayuse = 1

            pre_data = []
            if todayuse == 1:
                pre_data = p1(curdatab['bidding_space'])
            elif todayuse == 13:
                pre_data = np.maximum(np.maximum(p1(curdatab['bidding_space']), self.min_value), p13(curdatab['bidding_space']))
            elif todayuse == 3:
                pre_data = np.maximum(p3(curdatab['bidding_space']), self.min_value)
            elif todayuse == 33:
                pre_data = np.maximum(p3(curdatab['bidding_space']), y1)

            # 修正价格上下限
            pre_data = np.maximum(pre_data, self.min_value)
            pre_data = np.minimum(pre_data, self.max_value)

            result_tmp = pd.DataFrame(columns=['date_time', 'label_pred', 'type'])
            result_tmp['date_time'] = curdatab.index.copy()
            result_tmp['label_pred'] = list(pre_data)
            result_tmp['type'] = [todayuse] * curdatab.shape[0]
            result = pd.concat([result, result_tmp], axis=0)

        # 3. 整理输出结构
        result = result.set_index('date_time')
        result_json = result.to_dict()

        # 4. 写入log
        logger.info("------------------ 通用价格预测方法：三段拟合法BSF 预测完成！ ---------------------")

        return result_json
