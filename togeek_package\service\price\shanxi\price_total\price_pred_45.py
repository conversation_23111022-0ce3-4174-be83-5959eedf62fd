#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2024/1/29 10:29 
@Info    ：45天价格预测竞价空间拟合、随机森林
    3天的数据：交易中心公布
    4~15天的数据：4~5天的新能源使用交易中心公布，6~15天的新能源使用国能日新新能源+其它预测
    16~45天的数据：国能日新新能源+其它预测
'''

import pandas as pd
import numpy as np
import pwlf
import requests
from prophet import Prophet
import pymysql
from sklearn.ensemble import ExtraTreesRegressor
from datetime import date, datetime, timedelta
from multiprocessing import Pool
import logging
import warnings
import os


warnings.filterwarnings('ignore')
logger = logging.getLogger()
cpu_num = os.cpu_count()


class PricePredTotal:
    def __init__(self, data, pred_date=None, min_price=0, max_price=1500, days=45):
        logger.info(f"-----------------山西省_{days}天_价格预测开始---------------------")
        self.msg = ""
        self.min_price = min_price
        self.max_price = max_price
        if not pred_date:
            self.pred_date = str(date.today() - timedelta(1))[:10]
        else:
            self.pred_date = str(datetime.strptime(pred_date, '%Y-%m-%d') - timedelta(1))[:10]
        self.start_date = datetime.strptime(self.pred_date, '%Y-%m-%d') - timedelta(29)  # 历史30天的数据
        self.end_date = datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(3)     # 未来3天的预测数据

        data1, msg1, flag1 = self._prepare_data(data)  # 传入给模型的数据
        self.msg += msg1
        # data1.to_excel('data1_送参.xlsx', index=False)
        data2, msg2, flag2 = self._capature_data(self.start_date, self.end_date)  # 从datasets(交易中心)获取的数据
        self.msg += msg2
        # data2.to_excel('data2_交易中心.xlsx', index=False)
        data_tmp = pd.merge(data1, data2, on='date_time', how='right')
        data_tmp['date_time'] = pd.to_datetime(data_tmp['date_time'])
        # data_tmp.to_excel('data1_data2_合并.xlsx', index=False)

        data3, msg3, self.flag3 = self._capature_xny()  # 从数据库获取45天的新能源预测数据
        self.flag = flag1 * flag2 * self.flag3
        self.msg += msg3
        logger.info(self.msg)
        if self.flag3:
            data3['新能源负荷'] = data3['wind'] + data3['light']
            data3 = data3[['date_time', '新能源负荷']]
            # data3.to_excel('data3_国能日新.xlsx', index=False)
            data3['date_time'] = pd.to_datetime(data3['date_time'])

            self.source_data = pd.DataFrame(
                {"date_time": pd.date_range(str(self.start_date)[:10], periods=75 * 96, freq='15T')})
            self.source_data = pd.merge(self.source_data, data_tmp, on='date_time', how='left')
            # self.source_data.to_excel('sourcedata_datatmp_merge.xlsx', index=False)
            data3.set_index('date_time', inplace=True, drop=True)
            self.source_data.set_index('date_time', inplace=True, drop=True)
            self.source_data.update(data3, overwrite=False)
            # self.source_data.to_excel('sourcedata_data3_update.xlsx')
            # 预测未来45天的统调负荷、外送计划、检修容量、必开、必停容量等
            self.cols1 = list(data1.columns)  # 传入的新字段
            self.cols1.remove('date_time')
            self.cols1.remove('price')
            data4 = self._pred_load_mult(data_tmp, ['统调负荷', '联络线计划', '检修总容量'] + self.cols1, self.pred_date)
            # data4.to_excel('data4_预测.xlsx', index=False)
            data4['date_time'] = pd.to_datetime(data4['date_time'])
            data4.set_index('date_time', inplace=True, drop=True)
            self.source_data.update(data4, overwrite=False)
            self.source_data['date'] = self.source_data.index.astype(str).map(lambda x: x.split(' ')[0])
            self.source_data['time'] = self.source_data.index.astype(str).map(lambda x: x.split(' ')[1])
            self.source_data['小时'] = self.source_data['time'].map(lambda x: x.split(':')[0])
            self.source_data['小时'] = self.source_data['小时'].astype(float)
            del self.source_data['time']
            # self.source_data.to_excel('sourcedata_data4_update.xlsx')

    @staticmethod
    def _data_verification(start_date_need, end_date_need, start_date, end_date, num_queried, num_null, type_, flag):
        days_need = (datetime.strptime(end_date_need.split(" ")[0], '%Y-%m-%d') -
                     datetime.strptime(start_date_need.split(" ")[0], '%Y-%m-%d')).days + 1

        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d') -
                     datetime.strptime(start_date.split(" ")[0], '%Y-%m-%d')).days + 1
        num_should = days_need * 96

        msg = ""
        # 1. 数据长度检查
        s1 = f"{type_}应从{start_date_need}至{end_date_need}共{days_need}天，数据{num_should}条, " \
             f"实际为{start_date}至{end_date}共{days}天，数据{num_queried}条, 两者一致, "
        if num_queried != num_should:
            s1 = f"{type_}应从{start_date_need}至{end_date_need}共{days_need}天，数据{num_should}条, " \
                 f"实际为{start_date}至{end_date}共{days}天，数据{num_queried}条，两者不一致，请检查数据的完整性！"
            flag *= False
        msg += s1

        # 2. 空值检查
        s2 = f"无空缺值。\n"
        if num_null != 0:
            s2 = f"有{num_null}个空缺值, 请检查数据的完整性！\n"
            flag *= False
        msg += s2
        return msg, flag

    def _prepare_data(self, data):
        flag = True
        cols = list(data.keys())
        msg = ""
        for col in cols:
            tmp = pd.DataFrame(list(data[col].items()), columns=['date_time', col])
            tmp = tmp[(tmp['date_time'] >= str(self.start_date)) & (tmp['date_time'] <= (str(self.end_date)[:10] + " 23:45:00"))]
            tmp.dropna(inplace=True)
            start_date = tmp['date_time'].values[0]
            end_date = tmp['date_time'].values[-1]
            num_queried = tmp.shape[0]
            num_null = tmp.isnull().sum().sum()
            start_date_need = str(self.start_date.date())

            if 'price' not in col:
                end_date_need = str(self.end_date.date())
            else:
                end_date_need = self.pred_date

            msg_, flag = self._data_verification(start_date_need, end_date_need, start_date[:10], end_date[:10],
                                                 num_queried, num_null, f"传入模型的数据[{col}]", True)
            msg += msg_
            logger.info(msg)
        df = pd.DataFrame.from_dict(data)
        df['date_time'] = df.index
        df.reset_index(inplace=True, drop=True)
        return df, msg, flag

    def _capature_data(self, start_date, end_date):
        flag = True
        grid = 'SHANXI'
        url = 'http://datasets.togeek.cn/datasets/api/indexes/'
        ids = ['264', '263', '121', '265']
        items = ['/日前/统调负荷', '/日前/联络线计划/总加', '/日前/检修总容量', '/日前/新能源负荷']
        columns = ['统调负荷', '联络线计划', '检修总容量', '新能源负荷']
        start_date_ = str(start_date)
        end_date_ = str(end_date)[:10] + " 23:45:00"
        end_date_xny = str(self.end_date + timedelta(2))[:10] + " 23:45:00"
        data = pd.DataFrame({'time': pd.date_range(start_date_, end_date_xny, freq='15min')})
        new_column = ['date_time']
        data['time'] = data['time'].astype(str)
        msg = ""
        start_date_need = str(start_date.date())

        for i, id_ in enumerate(ids):
            if '新能源' in items[i]:
                params = {
                    'grid': grid,
                    'startTime': start_date_,
                    'endTime': end_date_xny,
                    'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
                    'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
                }
                end_date_need = str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(5))[:10]
            else:
                params = {
                    'grid': grid,
                    'startTime': start_date_,
                    'endTime': end_date_,
                    'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
                    'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
                }
                end_date_need = str(end_date)[:10]
            r = requests.get(url + id_, params=params)
            if r:
                try:
                    tmp = pd.DataFrame(r.json()['data'][items[i]]['points'])
                    num_queried = tmp.shape[0]
                    num_null = tmp.isnull().sum().sum()
                    msg_tmp, flag = self._data_verification(start_date_need, end_date_need, start_date_[:10], end_date_[:10],
                                                            num_queried, num_null, "96点" + columns[i] +
                                                            f"数据[{items[i]}]", flag)
                    data = pd.merge(data, tmp, on='time', how='left')
                    new_column.append(columns[i])
                    msg += msg_tmp
                    # break
                except Exception as e:
                    flag *= False
                    msg += f"查询数据时出错：{e}, 查询不到{start_date}至{end_date}的96点'{columns[i]}[{items[i]}]'数据！\n"
            else:
                flag *= False
                msg += f"数据请求出错，无法访问数据接口:{url + id_}！\n"
        data.columns = new_column
        logger.info(msg)
        # print(msg)
        return data, msg, flag

    def _capature_xny(self):
        flag = True
        msg = ""
        conn = pymysql.connect(host="*************",
                               user="tuji",
                               passwd="tuji800",
                               db="extana")
        cursor = conn.cursor()
        sql = "select pred_date, `time`, wind, light from power_xny_pred_d1d45_gnrx where pred_date='%s'" % self.pred_date
        cursor.execute(sql)
        input = cursor.fetchall()
        cursor.close()
        df = pd.DataFrame(input, columns=['pred_date', 'time', 'wind', 'light'])
        if not df.empty:
            # df['time'] = df['time'].astype('datetime64[ns]') - timedelta(minutes=15)
            df['pred_date'] = df['pred_date'].astype('datetime64[ns]')
            df.rename(columns={'time': 'date_time'}, inplace=True)
            msg += f"获取{self.pred_date}预测的新能源数据成功！\n"
        else:
            msg += f"获取不到{self.pred_date}预测的新能源数据，请检查！\n"
            flag = False
        return df, msg, flag

    def _pred_load(self, hist_data, cols, pred_date, days=45):
        # 如果历史数据的最后一天不是pred_date，需要将统调负荷数据的预测日期补齐
        hist_data.dropna(inplace=True)
        last_date = hist_data['date_time'].tolist()[-1]
        n = (datetime.strptime(pred_date, '%Y-%m-%d') - last_date).days + 1
        # n = (datetime.strptime(pred_date, '%Y-%m-%d') - datetime.strptime(last_date.split(" ")[0], '%Y-%m-%d')).days
        res = pd.DataFrame()
        for col in cols:
            data = hist_data[['date_time', col]]
            data.columns = ['ds', 'y']
            data['ds'] = pd.to_datetime(data['ds'])
            params = {"seasonality_mode": 'multiplicative'}
            ts_model = Prophet(**params)
            ts_model.fit(data)
            future = ts_model.make_future_dataframe(periods=(days + n) * 96, include_history=False, freq='15min')
            pred = ts_model.predict(future)[['ds', 'yhat']]
            pred.columns = ['date_time', col]
            if res.empty:
                res = pred
            else:
                res = pd.merge(res, pred, on='date_time')
        return res

    def _pred_load_mult(self, hist_data, cols, pred_date, days=45):
        # 如果历史数据的最后一天不是pred_date，需要将统调负荷数据的预测日期补齐
        self.tmp_data = hist_data.dropna()
        last_date = self.tmp_data['date_time'].tolist()[-1]
        n = (datetime.strptime(pred_date, '%Y-%m-%d') - last_date).days + 1
        # 多进程调用时间序列
        pool = Pool(processes=cpu_num-2)
        items = []
        for col in cols:
            items.append((col, days, n))
        result = pool.map(self._prophet_pred, items)
        pool.close()
        res_df = pd.DataFrame()
        for tmp in result:
            if res_df.empty:
                res_df = tmp
            else:
                res_df = pd.merge(res_df, tmp, on='date_time')
        return res_df

    def _prophet_pred(self, item):
        col, days, n = item
        data = self.tmp_data[['date_time', col]]
        data.columns = ['ds', 'y']
        data['ds'] = pd.to_datetime(data['ds'])
        params = {"seasonality_mode": 'multiplicative'}
        ts_model = Prophet(**params)
        ts_model.fit(data)
        future = ts_model.make_future_dataframe(periods=(days + n) * 96, include_history=False, freq='15min')
        pred = ts_model.predict(future)[['ds', 'yhat']]
        pred.columns = ['date_time', col]
        return pred

    def bsf_fit(self, hist_data, pred_data):
        tmp_data = pd.DataFrame(columns=["date_time", "pred_price"])

        # 多项式拟合
        parameter1 = np.polyfit(hist_data['竞价空间'], hist_data['price'], 1)
        p1 = np.poly1d(parameter1)
        parameter3 = np.polyfit(hist_data['竞价空间'], hist_data['price'], 3)
        p3 = np.poly1d(parameter3)

        p = p1
        if parameter3[0] > 0:
            delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
            if delta < 0:
                p = p3
            else:
                x1 = -1 / 3 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                x2 = -1 / 3 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                y1 = p3(x1)
                y2 = p3(x2)
                if x1 > 0 and y1 < y2 * 1.2:
                    p = p3

        pre_data = np.maximum(p(pred_data['竞价空间']), self.min_price)

        # 修正价格上下限为（0， 1500）
        pre_data = np.maximum(pre_data, self.min_price)
        pre_data = np.minimum(pre_data, self.max_price)

        tmp_data['date_time'] = pred_data.index
        tmp_data['pred_price'] = list(pre_data)
        return tmp_data

    def pwlf_fit(self, hist_data, pred_data):
        """线性分段"""
        tmp_data = pd.DataFrame(columns=["date_time", "pred_price"])

        x = hist_data['竞价空间'].values
        y = hist_data['price'].values

        # 判断分3段还是2段
        lb = (y < 200).sum()
        if lb > 0:
            n = 3
        else:
            n = 2

        x_pred = pred_data['竞价空间'].values
        my_pwlf = pwlf.PiecewiseLinFit(x, y)
        my_pwlf.fit(n)
        y_pred = my_pwlf.predict(x_pred)

        # 修正价格上下限为（0， 1500）
        y_pred = np.maximum(y_pred, self.min_price)
        y_pred = np.minimum(y_pred, self.max_price)

        tmp_data['date_time'] = pred_data.index
        tmp_data['pred_price'] = list(y_pred)
        return tmp_data

    def price_pred_bsf(self, data, mode='cubic'):
        if mode == 'cubic':
            fit_func = self.bsf_fit
        else:
            fit_func = self.pwlf_fit
        price = data[['date', 'price']].dropna()
        pred_date_ = price['date'].sort_values(ascending=False)[0]

        # 前5天，使用预测日进行拟合
        hist1 = data[data['date'] == pred_date_].dropna()
        pred1 = data[(data['date'] >= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(1))[:10]) &
                     (data['date'] <= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(5))[:10])]
        res1 = fit_func(hist1, pred1)

        # 5~15天，使用历史5天的数据进行拟合
        hist2 = data[(data['date'] >= str(datetime.strptime(pred_date_, '%Y-%m-%d') - timedelta(4))[:10]) &
                     (data['date'] <= pred_date_)].dropna()
        pred2 = data[(data['date'] >= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(6))[:10]) &
                     (data['date'] <= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(15))[:10])]
        res2 = fit_func(hist2, pred2)

        # 16~45天，使用历史10天的数据进行拟合
        hist3 = data[(data['date'] >= str(datetime.strptime(pred_date_, '%Y-%m-%d') - timedelta(9))[:10]) &
                     (data['date'] <= pred_date_)].dropna()
        pred3 = data[(data['date'] >= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(16))[:10]) &
                     (data['date'] <= str(datetime.strptime(self.pred_date, '%Y-%m-%d') + timedelta(45))[:10])]
        res3 = fit_func(hist3, pred3)

        res = pd.concat([res1, res2], axis=0)
        res = pd.concat([res, res3], axis=0)
        res['date_time'] = res['date_time'].astype(str)
        return res

    def price_pred_etr(self, data, n_est=100, max_depth=8):
        # 准备训练数据,划分测试集
        features = ['统调负荷', '新能源负荷', '联络线计划', '检修总容量', '小时', '竞价空间'] + self.cols1
        train = data[data['date'] <= str(self.pred_date)[:10]].dropna()
        test = data.loc[data['date'] > str(self.pred_date)[:10], features]
        X_train = train[features]
        y_train = train[['price']]

        if X_train.shape[0] == 0:
            raise ValueError('删除null值后，训练数据为空，请检查传入数据')

        # 模型训练
        etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)
        etr.fit(X=X_train, y=y_train)

        # 判断预测日训练数据是否含NaN值
        if not test.isnull().sum().sum() == 0:
            raise ValueError('预测日数据含NaN值，请检查!')
        if test.shape[0] == 0:
            raise ValueError('预测日无数据，请检查!')
        res = etr.predict(X=test)

        # 预测结果处理
        result = pd.DataFrame(index=test.index)
        result['pred_price'] = res

        # 对预测值进行最大最小值修正
        result['pred_price'] = result['pred_price'].map(lambda s: self.min_price if s < self.min_price else self.max_price if s > self.max_price else s)

        train_zero = train[train['price'] == 0]
        threshold_value = train_zero['竞价空间'].quantile(0.75)

        result[['小时', '竞价空间']] = test[['小时', '竞价空间']]

        if threshold_value != np.NaN:
            result.loc[result['竞价空间'] <= threshold_value, 'pred_price'] = 0

        result.reset_index(inplace=True)
        result['date_time'] = result['date_time'].astype(str)
        return result[['date_time', 'pred_price']]

    def run(self, to_json=False):  # , method='bsf'
        if self.flag3:
            data = self.source_data
            data['竞价空间'] = data['统调负荷'] - data['联络线计划'] - data['新能源负荷']
            pred_bsf = self.price_pred_bsf(data, mode='cubic')  # linear
            pred_etr = self.price_pred_etr(data)
        else:
            pred_bsf = pd.DataFrame(columns=['date_time', 'pred_price'])
            pred_etr = pd.DataFrame(columns=['date_time', 'pred_price'])

        if to_json:
            res = {"etr_pred": pred_etr.to_dict(), "bsf_pred": pred_bsf.to_dict(), "message": self.msg}
        else:
            res = {"etr_pred": pred_etr, "bsf_pred": pred_bsf, "message": self.msg}
        return res


if __name__ == '__main__':
    data = pd.read_excel(r"C:\Users\<USER>\Desktop\test.xlsx")
    data.set_index('date_time', inplace=True, drop=True)
    data.index = data.index.astype(str)
    ppt = PricePredTotal(data, pred_date='2024-01-31')
    res = ppt.run(to_json=True)
    # res['etr_pred'].to_excel('etr.xlsx', index=False)
    # res['bsf_pred'].to_excel('bsf.xlsx', index=False)
    print(res['message'])
    print(res)
