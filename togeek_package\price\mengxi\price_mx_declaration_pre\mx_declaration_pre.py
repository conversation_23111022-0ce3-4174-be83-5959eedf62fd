#!/usr/bin/env python
# coding: utf-8

import datetime
import numpy as np
import pandas as pd
import jenkspy
import logging
from tglibs.easy_date_time import construct_time_index
from togeek_package.price.mengxi.price_mx_declaration_pre.model_config import ModelConfig
logger = logging.getLogger()


class Declaration:
    def __init__(self, source_data, date_list, province, jiedian_type, sj_sign, sj_date):
        self.province = province
        self.jiedian_type = jiedian_type
        self.source_data = self.pre_source(pd.DataFrame(source_data))
        self.date_list = date_list
        self.sj_sign = sj_sign
        self.sj_date = sj_date
        # self.result = self.pre_price()

    def pre_source(self, source_data):
        if 'date_time' not in source_data.columns:
            source_data = source_data.reset_index()
            source_data = source_data.rename(columns={'index': 'date_time'})
        source_data['date_time'] = source_data['date_time'].astype(str)
        source_data = self._validate_data(source_data)
        source_data['日期'] = source_data['date_time'].map(lambda s: str(s)[0:10])
        source_data['time'] = source_data['date_time'].map(lambda s: str(s).split(' ')[-1])
        source_data['week'] = pd.to_datetime(source_data['日期']).dt.weekday.map(lambda s: int(s) + 1)
        source_data['时间'] = source_data['date_time'].map(lambda s: int(int(str(s)[11:13])*4+(int(str(s)[14:16])/15)+1))
        return source_data

    def _validate_data(self, data):
        config = ModelConfig()
        if self.jiedian_type == '统一结算点':
            beijianshu_keys = config.get_tongyijiesuan_beijianshu(self.province)
            jianshu_keys = config.get_tongyijiesuan_jianshu(self.province)
        elif self.jiedian_type == '节点':
            beijianshu_keys = config.get_jiedian_beijianshu(self.province)
            jianshu_keys = config.get_jiedian_jianshu(self.province)
        else:
            print(self.jiedian_type, ' 该结算点类型暂不支持')
            raise Exception(self.jiedian_type + '该节点类型暂不支持')
        data['竞价空间'] = data[beijianshu_keys].sum(axis=1) - data[jianshu_keys].sum(axis=1)
        return data

    def predict(self, train_data, pre_data, min_value, max_value):
        train_data['jenkspy'] = train_data['price'] * train_data['竞价空间']
        train_data.reset_index(inplace=True, drop=True)
        breaks = jenkspy.jenks_breaks(train_data['jenkspy'], 3)
        b1 = train_data[train_data['jenkspy'] <= breaks[1]]
        b2 = train_data[(train_data['jenkspy'] > breaks[1]) & (train_data['jenkspy'] <= breaks[2])]
        b3 = train_data[train_data['jenkspy'] > breaks[2]]
        b2jingjiamean = b2['竞价空间'].mean()
        b2rqrpmean = b2['price'].mean()
        # k32_2m3all = gmean((b3['rqrp']-b2rqrpmean)/(b3['jingjia']-b2jingjiamean))
        k32_2m3all = ((b3['price'] - b2rqrpmean) / (b3['竞价空间'] - b2jingjiamean)).mean()
        # 第三段的直线
        p13 = np.poly1d([k32_2m3all, np.mean(b3['price']) - k32_2m3all * np.mean(b3['竞价空间'])])

        # 多项式拟合
        parameter1 = np.polyfit(train_data['竞价空间'], train_data['price'], 1)
        p1 = np.poly1d(parameter1)
        parameter3 = np.polyfit(train_data['竞价空间'], train_data['price'], 3)
        p3 = np.poly1d(parameter3)

        todayuse = 1
        if parameter3[0] > 0:
            delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
            if delta < 0:
                todayuse = 3
            else:
                x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                y1 = p3(x1)
                y2 = p3(x2)
                if x1 > 0 and y1 < y2 * 1.2:
                    todayuse = 33
        else:
            if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                todayuse = 13
            else:
                todayuse = 1

        if todayuse == 1:
            pre_data = p1(pre_data['竞价空间'])
        elif todayuse == 13:
            pre_data = np.maximum(np.maximum(p1(pre_data['竞价空间']), 0), p13(pre_data['竞价空间']))
        elif todayuse == 3:
            pre_data = np.maximum(p3(pre_data['竞价空间']), 0)
        elif todayuse == 33:
            pre_data = np.maximum(p3(pre_data['竞价空间']), y1)
        # 修正价格上下限为（0， 1500）
        pre_data = np.maximum(pre_data, min_value)
        pre_data = np.minimum(pre_data, max_value)

        return list(pre_data)

    def pre_price(self, min_value, max_value, to_json=False):
        '''
        '''
        logger.info("-------------三段拟合法预测出清电价开始-------------------")
        y1 = 0
        columns_list = ["date_time"]
        columns_list.append(f"价格预测值")
        result = pd.DataFrame(columns=columns_list)
        for rdata in self.date_list:
            result_tmp = pd.DataFrame(columns=columns_list)
            result_tmp['date_time'] = construct_time_index([rdata, rdata], freq='15T')
            # 统一为一个价格
            input_data = self.source_data[['date_time', '日期', 'week', '竞价空间', '价格']]
            input_data = input_data.rename(columns={'价格': 'price'})
            if self.sj_sign == 1:
                # 如果星期在date列表里，表示省间现货，筛选历史表，取最大的一条
                week = int(datetime.datetime.strptime(rdata, "%Y-%m-%d").weekday()) + 1
                if week in self.sj_date:
                    history_data = input_data[(input_data['week'].isin(self.sj_date)) & ([input_data['日期'] < rdata])]
                else:
                    # 如果不在列表里，表示运行日非省间现货，删选历史表
                    history_data = input_data[(~(input_data['week'].isin(self.sj_date))) & ([input_data['日期'] < rdata])]
                history_date = history_data.dropna()['日期'].max()
            else:
                history_date = input_data[input_data['日期'] < rdata].dropna()['日期'].max()
            logger.info('history_date:', history_date)
            # 取历史
            train_data = input_data[input_data['日期'] == history_date]
            # 预测日
            pre_data = input_data[input_data['日期'] == rdata]
            pre_list = self.predict(train_data, pre_data, min_value, max_value)
            result_tmp[f"价格预测值"] = pre_list
            result = pd.concat([result, result_tmp], axis=0)
        result['date_time'] = result['date_time'].astype(str)
        result = result.set_index('date_time')
        if to_json:
            result = result.to_dict()
        logger.info(f'result = {result}')
        logger.info("-------------三段拟合法预测出清电价结束-------------------")
        return result


if __name__ == '__main__':
    jiedian_type = '统一结算点'
    province = '蒙西'
    source_data = pd.read_excel(r"D:\02file\2022\22lstm价格预测负准确率优化\mx_input.xlsx")
    date_list = ['2022-07-23', '2022-07-24']
    p = Declaration(source_data, date_list, province, jiedian_type, sj_sign=0, sj_date=[])
    pre = p.pre_price()
    print(pre)



