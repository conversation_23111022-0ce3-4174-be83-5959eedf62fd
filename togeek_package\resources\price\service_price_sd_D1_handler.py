# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-12-05 17:30:41
Description :   山东 D+1 日前/实时 统一出清电价预测 (96点)
    内容：
        D+1统一出清电价_日前：相似日竞价空间拟合
        D+1统一出清电价_实时：通用随机森林模型
"""

import pandas as pd
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shandong import DataPredD1SD


class DataPredD1SDHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        date_bidding_start = params.pop('date_bidding_start')   # 竞价日起始日期
        date_bidding_end = params.pop('date_bidding_end')       # 竞价日结束日期

        bid_date_list = pd.date_range(date_bidding_start, date_bidding_end).map(lambda s: str(s).split(" ")[0]).values
        data_pred_list = []
        for bid_date in bid_date_list:
            m = DataPredD1SD(bid_date=bid_date)
            data_pred = m.predict()
            data_pred_list.append(data_pred)

        data_pred_D1 = pd.concat(data_pred_list)

        data_pred_json = {
            "data_pred": data_pred_D1.to_dict("record"),
        }
        self.write(data_pred_json)
