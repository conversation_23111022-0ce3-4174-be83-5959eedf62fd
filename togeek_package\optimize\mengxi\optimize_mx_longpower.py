#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/5/17 14:40
# <AUTHOR> Darlene
'''
   计算中长期中标出力
   1、拟合边际成本曲线
   2、校验数据（）
   3、算中标
'''
import pandas as pd
import numpy as np
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


class MxLongBidedPower:
    def __init__(self, generators, prices, costs):
        """
            param generators: 机组参数
            param prices: 机组节点电价
            param costs：边际成本数据
            param subsetction_coe：预测中标系数
        """
        self.generators = pd.DataFrame(generators)
        self.prices = pd.DataFrame(prices)
        self.costs = pd.DataFrame(costs)

        # 默认值
        self.freqs = [24, 48, 96]  # 每日的时间点的数量只能是这些数字
        # self.gap = 15  # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        # self.X_BOUND = [0, 1]  # 百分比

        # 写入log
        logger.info("------------------已知机组信息、价格和分段报价，计算各机组分时刻中标出力------------------------")
        msg = self._prepare_load()  # 数据预处理+数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        '''
        1.判断预测日是否要停机
        2.判断最小出力是否等于最小技术出力
        3.边际成本曲线拟合
        4.计算要预测的点数
        5.取价格的时间，算中标出力
        6.
        '''
        # 判断预测日是否要停机
        if 'stop_dates' not in self.generators.columns:  # 如果没有该列，则设置为默认值0，即不停机
            self.generators['stop_dates'] = ''
        # self.generators['to_stop'].fillna(0, inplace=True)  # 空值填充为0

        # 判断最小出力是否等于最小技术出力
        if 'is_min_capacity' not in self.generators.columns:  # 如果没有该列，则设置为默认值1，即设置最小出力等于最小技术出力
            self.generators['is_min_capacity'] = 1
        self.generators['is_min_capacity'].fillna(1, inplace=True)  # 空值填充为1
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        # 预测出力用的最大发电能力和最小发电能力，理论出力用的是最大、最小出力
        for col in ['rated_capacity', 'min_capacity', 'max_capacity', 'upward', 'downward']:
            self.generators[col] = self.generators[col].astype('float')
        # 修正 prices 数据类型并排序
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['date_time'] = self.prices['date_time'].astype('str')
        self.prices['date'] = self.prices['date_time'].map(lambda x: str(x).split(' ')[0])
        # self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
        self.prices['real_price'] = self.prices['real_price'].astype('float')
        self.prices = self.prices.sort_values(['generator', 'date_time']).reset_index(drop=True)  # 按时间排序
        # 修正边际成本数据
        self.costs['generator'] = self.costs['generator'].astype('str')
        self.costs['load'] = self.costs['load'].astype('float')
        self.costs['cost'] = self.costs['cost'].astype('float')



        msg = ""
        # 1、检测机组信息表中前5个字段(generator、rated_capacity、min_capacity、max_capacity、upward)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['rated_capacity'] + hasnull['min_capacity'] + hasnull[
            'max_capacity'] + hasnull['upward']
        if num_null > 0:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中有" + str(
                num_null) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中没有空值；"
        # 下坡速率要是空值则用上坡速率代替
        self.generators['downward'].fillna(self.generators['upward'], inplace=True)
        # 起始出力如果是空值则用最小出力代替；
        self.generators['beginning_power'].fillna(self.generators['min_capacity'], inplace=True)



        # 2、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freq中的一种
                price = self.prices[self.prices['generator'] == g].copy().dropna()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中; "
                else:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中; "
                    raise Exception()
            except:
                msg = msg + "价格数据有异常，请检查传入的价格数据。"
            # finally:
            #     msg = msg + "数据验证结束！"
        # 3、循环generator，检查成本
        generators = self.costs['generator'].unique()
        if len(generators) != len(self.generators['generator']):
            msg += "3, costs中应有" + str(len(self.generators['generator'])) + "台机组，costs表中有" + \
                   str(len(generators)) + "台机组，机组数量不一致，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            for g in generators:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null = hasnull['generator'] + hasnull['load'] + hasnull['cost']
                    if num_null > 0:
                        msg = msg + "3, 机组" + str(g) + "的generator、load、cost中有" + \
                              str(num_null) + "个空值，请检查传入数据。"
                        logger.info(msg)
                        raise Exception()
                    else:
                        msg = msg + "3, 机组" + str(g) + "的generator、load、cost中没有空值,"
                except:
                    msg += "3, 机组" + str(g) + "的输入数据有异常。"
                    logger.info(msg)
                    raise Exception()
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。"
        # 4、修正成本
        try:
            self._correct_cost()
            msg += '成本修正完成。\n'
        except:
            msg += '成本修正异常。'
            logger.info(msg)
            raise Exception()
        return msg

    def _correct_cost(self):
        """
        处理传入的边际成本曲线不符合单调递增的情况，末段价格增加 10*段数 元/MWh
        先将出力按照从小到大的顺序排序，再将成本下降的曲线修正为最大成本+非常小的值
        :return: None
        """
        new_cost = pd.DataFrame(columns=['generator', 'load', 'cost'])
        self.costs = self.costs[['generator', 'load', 'cost']]
        self.costs.sort_values(['generator', 'load'], inplace=True)
        for g in self.costs['generator'].unique():
            costs = self.costs[self.costs['generator'] == g]
            costs.reset_index(drop=True, inplace=True)
            # subsection = self.generators.loc[self.generators['generator'] == g, 'subsection'].values[0]
            costs['diff'] = costs['cost'].diff().fillna(0)
            # start_cost = costs['cost'].min()
            end_cost = costs['cost'].max()
            idx = list(costs[costs['diff'] <= 0].index)
            if (len(idx) == 1) | (sum(costs['diff']) == 0):  # 如果价格递增或者是一条直线则不进行修正
                new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
                continue
            elif int(idx[1]) == 1:
                start_cost = costs['cost'][0]
                num = len(costs)
                if start_cost == end_cost:
                    start_cost = costs['cost'].min()
                costs['cost'] = list(np.linspace(start_cost, end_cost, num, dtype=float))
            else:
                start_cost = costs['cost'][int(idx[1]) - 1]
                num = len(costs) - int(idx[1]) + 1
                if start_cost == end_cost:
                    start_cost = costs['cost'].min()
                costs.loc[int(idx[1]) - 1:, 'cost'] = list(np.linspace(start_cost, end_cost, num, dtype=float))
            new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
        self.costs = new_cost

    def x_from_raw(self, raw_x, raw_y, y):
        """
        根据给定的y，从原始曲线中求出x（对于对应多个x时，返回均值）
        :param y: 给定y
        :return: 原始曲线中的x
        """
        y = np.asarray(y, dtype=float)
        xs = []
        for x_i, y_i, x_next, y_next in np.c_[raw_x[:-1], raw_y[:-1], raw_x[1:], raw_y[1:]]:
            x = np.empty_like(y, dtype=float)
            x[:] = np.nan
            x_i, x_next, y_i, y_next = (x_i, x_next, y_i, y_next) if y_next >= y_i else (x_next, x_i, y_next, y_i)
            valid = (y >= y_i) & (y <= y_next)
            x[valid] = (x_i + x_next) / 2 if y_i == y_next else np.interp(y[valid], [y_i, y_next], [x_i, x_next])
            xs.append(x)
        x = np.empty_like(y, dtype=float)
        x[:] = np.nan
        xs = np.vstack(xs).T
        valid = ~np.all(np.isnan(xs), axis=1)
        x[valid] = np.nanmean(xs[valid], axis=1)
        return x

    def predict(self):
        bidded_power = []  # 分时刻中标出力
        # 1、循环generator；
        for g in self.generators['generator']:
            # 当前机组信息
            # 1.首先边际成本曲线拟合
            # 2.获取机组的基础数据，最大出力、最小出力，爬坡速率、下坡速率
            # 3.看前一天的最后一个时刻出力是否传入了
            # 4.计算机组中标出力
            stop_dates = self.generators[self.generators['generator'] == g]['stop_dates'].map(lambda x: str(x).split(','))  # 找停机日期
            min_capacity = float(self.generators[self.generators['generator'] == g]['min_capacity'])
            max_capacity = float(self.generators[self.generators['generator'] == g]['max_capacity'])
            subsetction_coe = float(self.generators[self.generators['generator'] == g]['subsetction_coe']) # 预测中标系数

            upward = float(self.generators[self.generators['generator'] == g]['upward'])  # 爬坡速率
            downward = float(self.generators[self.generators['generator'] == g]['downward'])  # 下坡速率

            try:
                beginning_power = float(
                    self.generators[self.generators['generator'] == g]['beginning_power'])  # 前一天最后一个时刻的出力
            except:
                beginning_power = 0
            is_min_capacity = int(
                self.generators[self.generators['generator'] == g]['is_min_capacity'])  # 最小出力是否为最小技术出力
            # 当前机组对应的节点价格
            price = self.prices[self.prices['generator'] == g]  # 当前机组分时刻节点价格
            # 预测日期列表
            dates = price['date'].drop_duplicates().to_list()
            days = len(dates)  # 预测天数
            freq = price.shape[0] / days  # 当前机组时点数目
            gap = 1440 / freq  # 当前机组时间间隔



            tmp_bidded_power = price[['generator', 'date_time', 'date']]
            # 1. 取所有的价格数据，设为y，取costs的raw_x和raw_y
            out_price = list(price['real_price'])
            self.cost = self.costs[self.costs['generator'] == g].copy()
            raw_x = list(self.cost['load'])
            raw_y = list(self.cost['cost'])
            minprice = min(self.cost['cost'])
            maxprice = max(self.cost['cost'])
            # 2. 用y算出中标出力x1
            out_power = self.x_from_raw(raw_x, raw_y, out_price)
            price['x1'] = out_power
            # 有很多空值，是价格超过成本曲线的价格，需要补全nan
            price.loc[price['real_price'] > maxprice, 'x1'] = max_capacity
            price.loc[price['real_price'] < minprice, 'x1'] = min_capacity
            xrs = []
            start = beginning_power

            # 循环时间
            for arp in price.itertuples():
                xr = arp.x1
                if arp.x1 > start:    # 比较这一时刻的x1与上一时刻
                    xr = min(start + upward * gap, arp.x1, max_capacity) * subsetction_coe
                elif arp.x1 < start:
                    xr = max(max(start - downward * gap, arp.x1) * subsetction_coe, min_capacity)
                xrs.append(xr)
                start = xr
            tmp_bidded_power['real_power'] = xrs
            # 6. 停机date全部设为0
            tmp_bidded_power.loc[tmp_bidded_power['date'].isin(stop_dates), 'real_power'] = 0
            # 7. 删除price
            del(tmp_bidded_power['date'])

            bidded_power.append(tmp_bidded_power)

        bidded_power = pd.concat(bidded_power)

        result = bidded_power.to_dict('list')
        logger.info(f'result = {result}')
        logger.info("---------------------------分时刻中标出力计算模型结束--------------------------")
        return result

if __name__ == '__main__':
    generators = pd.read_excel(r"D:\07data\bided_power_input.xlsx", sheet_name='generators', index_col=None)
    prices = pd.read_excel(r"D:\07data\bided_power_input.xlsx", sheet_name='prices', index_col=None)
    costs = pd.read_excel(r"D:\07data\bided_power_input.xlsx", sheet_name='costs', index_col=None)
    prices['date_time'] = prices['date'].astype('str') + ' ' + prices['time'].astype('str')
    del(prices['date'])
    del(prices['time'])
    print(costs.to_dict('list'))
    # model = MxLongBidedPower(generators=generators, prices=prices, costs=costs)
    # result = model.predict()
    # print(pd.DataFrame(result))
