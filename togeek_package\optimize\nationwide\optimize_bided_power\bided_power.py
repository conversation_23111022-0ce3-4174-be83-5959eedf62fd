# -*- coding: utf-8 -*
"""
Author: san
Date: 2021-12-27 15:01:54
LastEditTime: 2022-01-01 09:40:06
Description:
输入：
    1、机组信息 generators：机组ID、额定容量、机组最小出力、最大出力、机组爬坡速率、机组下坡速率
    2、节点价格 prices: 机组ID、时间、日前节点电价与实时节点电价；
    3、机组分段报价信息 subsection_declaration：机组ID、分段、出力、报价
输出：
    1、分时点中标出力 bided_power：机组ID，时刻，日前中标出力, 实时中标出力
"""

import numpy as np
import pandas as pd
pd.set_option('display.max_rows', None)
import operator
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


class BidedPower:
    def __init__(self, generators, prices, subsection_declaration, min_price=0, max_price=1500):
        """
        param generators: 机组参数
        param prices: 机组节点电价
        param subsection_declaration：机组分段报价
        """
        self.generators = pd.DataFrame(generators)
        self.prices = pd.DataFrame(prices)
        self.subsection_declaration = pd.DataFrame(subsection_declaration)

        # 默认值
        self.freqs = [24, 48, 96]  # 每日的时间点的数量只能是这些数字
        # self.gap = 15  # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        # self.X_BOUND = [0, 1]  # 百分比
        self.P_BOUND = [min_price, max_price]  # 报价范围

        # 写入log
        logger.info("------------------已知机组信息、价格和分段报价，计算各机组分时刻中标出力------------------------")
        msg = self._prepare_load()  # 数据预处理+数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 判断预测日是否要停机
        if 'to_stop' not in self.generators.columns:  # 如果没有该列，则设置为默认值0，即不停机
            self.generators['to_stop'] = 0
        self.generators['to_stop'].fillna(0, inplace=True)  # 空值填充为0

        # 判断最小出力是否等于最小技术出力
        if 'is_min_capacity' not in self.generators.columns:  # 如果没有该列，则设置为默认值1，即设置最小出力等于最小技术出力
            self.generators['is_min_capacity'] = 1
        self.generators['is_min_capacity'].fillna(1, inplace=True)  # 空值填充为1
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        for col in ['rated_capacity', 'min_capacity', 'max_capacity', 'upward', 'downward']:
            self.generators[col] = self.generators[col].astype('float')
        # 修正 prices 数据类型并排序
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
        self.prices['real_price'] = self.prices['real_price'].astype('float')
        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 按时间排序
        # 修正 subsection_declaration 数据类型并排序
        self.subsection_declaration['generator'] = self.subsection_declaration['generator'].astype('str')
        self.subsection_declaration['subsection'] = self.subsection_declaration['subsection'].astype('int')
        self.subsection_declaration['power'] = self.subsection_declaration['power'].astype('float')
        self.subsection_declaration['price'] = self.subsection_declaration['price'].astype('float')
        self.subsection_declaration = self.subsection_declaration.sort_values(['generator', 'subsection']).reset_index(drop=True)  # 按分段序号排序

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、rated_capacity、min_capacity、max_capacity、upward)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['rated_capacity'] + hasnull['min_capacity'] + hasnull[
            'max_capacity'] + hasnull['upward']
        if num_null > 0:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中有" + str(
                num_null) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中没有空值；"
        # 下坡速率要是空值则用上坡速率代替
        self.generators['downward'].fillna(self.generators['upward'], inplace=True)

        # 2、循环generator，检查价格；      
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freq中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中; "
                else:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中; "
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                #   gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(
                        g) + " 数据验证通过。"
                elif operator.eq(list(price['时间']), curtimes1) & freq == 96:
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(
                        g) + " 数据验证通过。"
                elif operator.eq(list(price['时间']), curtimes1) & freq == 48:
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(
                        g) + " 数据验证通过。"
                elif operator.eq(list(price['时间']), curtimes1) & freq == 24:
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(
                        g) + " 数据验证通过。"
                else:
                    msg = msg + "2.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    raise Exception()
            except:
                msg = msg + "价格数据有异常，请检查传入的价格数据。"
            # finally:
            #     msg = msg + "数据验证结束！"
        return msg

    # 评价函数： 依据分段出力x和对应的分段报价p，计算日前和实时市场各时点的中标出力
    def _income(self, x, p, price, XR_BOUND, upward, downward, is_min_capacity, gap, num_subsection, beginning_power):
        # 本应该是前一天的最后一个点的出力，但是由于每天0点的用电量并不大，所以就暂时按照机组最小出力替代
        if is_min_capacity == 0:
            XR_BOUND[0] = 0
        point_xa_power = min(max(XR_BOUND[0], beginning_power), XR_BOUND[1])
        point_xr_power = min(max(XR_BOUND[0], beginning_power), XR_BOUND[1])
        xas = []
        xrs = []
        for arp in price.itertuples():
            xa = XR_BOUND[0]  # 初始化：日前市场，当前时刻出力
            xr = XR_BOUND[0]  # 初始化：实时市场，当前时刻出力
            i = num_subsection - 1  # - 1
            while i >= 0:
                if arp.ahead_price >= p[i]:
                    xa = x[i]
                    break
                else:
                    i = i - 1
            i = num_subsection - 1  # - 1
            while i >= 0:
                if arp.real_price >= p[i]:
                    xr = x[i]
                    break
                else:
                    i = i - 1

            #   日前市场爬坡及下坡速率修正
            if xa > point_xa_power + upward * gap:
                xa = point_xa_power + upward * gap  # 爬坡速率修正
                if xa > XR_BOUND[1]:
                    xa = XR_BOUND[1]
                point_xa_power = xa
            elif xa < point_xa_power - downward * gap:
                xa = point_xa_power - downward * gap    # 下坡速率修正
                if xa < XR_BOUND[0]:
                    xa = XR_BOUND[0]
                point_xa_power = xa
            else:
                point_xa_power = xa

            #   实时市场爬坡及下坡速率修正
            if xr > point_xr_power + upward * gap:  # 爬坡速率修正
                xr = point_xr_power + upward * gap
                if xr > XR_BOUND[1]:
                    xr = XR_BOUND[1]
                point_xr_power = xr
            elif xr < point_xr_power - downward * gap:  # 下坡速率修正
                xr = point_xr_power - downward * gap
                if xr < XR_BOUND[0]:
                    xr = XR_BOUND[0]
                point_xr_power = xr
            else:
                point_xr_power = xr

            xas.append(xa)
            xrs.append(xr)
        return xas, xrs

        # 停机方案

    def shut_down_scheme(self, XR_BOUND, subsection, freq, g):
        logger.info("机组" + str(g) + "需要停机，给出停机方案")
        x = []
        p = []
        for i in range(len(subsection) - 1):
            j = len(subsection) - i - 1
            x.append(XR_BOUND[0] + i)
            p.append(self.P_BOUND[1] - 0.01 * j)
        x.append(XR_BOUND[1])
        p.append(self.P_BOUND[1])

        # 返回分时刻中标出力
        xas = [0] * freq
        xrs = [0] * freq

        return xas, xrs

    def predict(self):
        bidded_power = []  # 分时刻中标出力
        # 1、循环generator；      
        for g in self.generators['generator']:
            # 当前机组信息
            to_stop = int(self.generators[self.generators['generator'] == g]['to_stop'])
            min_capacity = float(self.generators[self.generators['generator'] == g]['min_capacity'])
            max_capacity = float(self.generators[self.generators['generator'] == g]['max_capacity'])
            XR_BOUND = [min_capacity, max_capacity]  # 出力边界
            upward = float(self.generators[self.generators['generator'] == g]['upward'])    # 爬坡速率
            downward = float(self.generators[self.generators['generator'] == g]['downward'])    # 下坡速率
            try:
                beginning_power = float(self.generators[self.generators['generator'] == g]['beginning_power'])  # 前一天最后一个时刻的出力
            except:
                beginning_power = 0
            is_min_capacity = int(self.generators[self.generators['generator'] == g]['is_min_capacity'])  # 最小出力是否为最小技术出力
            # 当前机组对应的节点价格
            price = self.prices[self.prices['generator'] == g]  # 当前机组分时刻节点价格
            freq = price.shape[0]   # 当前机组时点数目
            gap = 1440 / freq   # 当前机组时间间隔

            # 当前机组分段报价信息
            subsection = self.subsection_declaration[self.subsection_declaration['generator'] == g]  # 当前机组的分段报价信息
            x = subsection['power'].tolist()
            p = subsection['price'].tolist()
            num_subsection = len(subsection)

            if to_stop == 1:
                xas, xrs = self.shut_down_scheme(XR_BOUND, subsection, freq, g)
            else:
                xas, xrs = self._income(x=x, p=p, price=price, XR_BOUND=XR_BOUND, upward=upward, downward=downward,
                                        is_min_capacity=is_min_capacity, gap=gap, num_subsection=num_subsection,
                                        beginning_power=beginning_power)
            tmp_bidded_power = price[['generator', 'time']]
            tmp_bidded_power['ahead_power'] = xas
            tmp_bidded_power['real_power'] = xrs

            bidded_power.append(tmp_bidded_power)

        bidded_power = pd.concat(bidded_power)

        result = bidded_power.to_dict('list')
        logger.info(f'result = {result}')
        logger.info("---------------------------分时刻中标出力计算模型结束--------------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta

    time0 = time.time()
    generators_test = pd.read_excel(r"D:\ToGeek\work\model_data\optimize\bided_power\input.xlsx", sheet_name='generators',
                                    index_col=None)
    prices_test = pd.read_excel(r"D:\ToGeek\work\model_data\optimize\bided_power\input.xlsx", sheet_name='prices', index_col=None)
    subsection_declaration_test = pd.read_excel(r"D:\ToGeek\work\model_data\optimize\bided_power\input.xlsx", sheet_name='subsection_declaration', index_col=None)
    model = BidedPower(generators=generators_test, prices=prices_test, subsection_declaration=subsection_declaration_test)
    result_test = model.predict()
    print(pd.DataFrame(result_test)[:96])
    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1 - time0))}")
