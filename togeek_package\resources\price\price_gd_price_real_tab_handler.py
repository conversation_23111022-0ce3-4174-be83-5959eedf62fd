#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
<AUTHOR>
@Date    ：2025/7/25
@Info    ：广东实时价格预测
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.guangdong.price_real_tabpfn import PriceRealTabGD


class PriceRealTabHandlerGD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        D = params.get('D')
        print("D_Param:", D)
        df=params.get('df')
        df_holiday=params.get('df_holiday')
        df_gfs=params.get('df_gfs')
       
        pred = PriceRealTabGD(
            D=D,
            df=df,
            df_holiday=df_holiday,
            df_gfs=df_gfs,
        )
        result = pred._pred()
        self.write(result)
