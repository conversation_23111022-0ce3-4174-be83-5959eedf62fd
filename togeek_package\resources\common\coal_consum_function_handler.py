"""
Author: <PERSON><PERSON>
Datetime: 2022/12/2/002 14:53
Info:
"""


from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_coal_consum_function import CoalConsumFunction


class CoalConsumFunctionHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        hist_data = params.pop('hist_data')
        new_data = params.pop('new_data')
        f = params.pop('f', [2.62035442e-06, -1.86329900e-06, -1.70390088e-07, -3.63837059e-09, -1.00638635e-03,  4.13925120e-04,
         1.89755507e-05, 4.29128466e-01, -8.27666586e-03, 1.28001993e-02])
        r = params.pop("r", 0.5)
        delete_r = params.pop('delete_r', {'P': 5, 'D': 30})
        model = CoalConsumFunction(hist_data=hist_data, new_data=new_data, f=f, r=r, delete_r=delete_r)
        self.write(model.run())