# -*- coding: utf-8 -*-


import os
import pandas as pd
import numpy as np
import datetime
from numpy import *
import logging
from sklearn.linear_model import LinearRegression
import warnings
warnings.filterwarnings("ignore")


logger = logging.getLogger()

D1 = ["04:00", "06:00", "08:00", "10:00", "12:00", "14:00", "16:00", "18:00", "20:00", "22:00"]
D3 = ["04:00", "06:00", "08:00", "11:00", "14:00", "17:00", "20:00"]
D4 = ["05:00", "08:00", "11:00", "14:00", "20:00", "22:00"]

class NewPower:
    '''
    输入数据：历史观测风速、历史风电负荷、历史光负荷、预测日以及未来十天的风速数据、预测日期列表、蒙西全地区风速计算公式
    输出数据：预测日以及未来十天的风电负荷、每时刻的光负荷均值、新能源负荷预测值
    '''
    def __init__(self, speed_data, power_feng, power_guang, yuce_list, weight_dic):
        self.yuce_list = yuce_list
        self.all_fengsu, self.re = self.pre_data(speed_data, weight_dic)
        self.power_feng = pd.merge(self.all_fengsu, pd.DataFrame(power_feng), how='left')
        self.power_guang = pd.DataFrame(power_guang)
        self.power_guang['date'] = self.power_guang['date_time'].map(lambda x:str(x).split(' ')[0])
        self.power_guang['time'] = self.power_guang['date_time'].map(lambda x:str(x).split(' ')[-1])
        self.power_feng['date'] = self.power_feng['date_time'].map(lambda x:str(x).split(' ')[0])
        self.power_feng['time'] = self.power_feng['date_time'].map(lambda x:str(x).split(' ')[-1])
        self.result = self.pre_predict()


    def pre_data(self, data, weight_dic):
        '''
        1、算总地区风速
        2、数据校验：至少有5天的历史数据有14个点
        3、数据校验：未来十天的风速点，第1-3天是10个点，第4天7个点，第5-10天6个点,所有数据点数正确
        '''
        fengji_data = pd.DataFrame(data)
        all_fengsu = {}
        re = False
        try:
            fengji_data['date_time'] = fengji_data['business_date'].astype(str) + ' ' + \
                                       fengji_data['business_time'].astype(str)
            all_date = fengji_data['date_time'].drop_duplicates().tolist()
            for date in all_date:
                one_time = fengji_data[fengji_data['date_time'] == date]
                fengsu = 0
                for key, values in weight_dic.items():
                    if one_time[one_time['spot'] == key].empty:
                        logger.info("{}{}无数据!!!".format(date, key))
                        continue
                    else:
                        fengsu = fengsu + one_time[one_time['spot'] == key]['wind_speed'].values[0] * values
                count = sum(list(weight_dic.values()))
                all_fengsu[date] = fengsu / count
            all_fengsu = pd.DataFrame(list(all_fengsu.items()))
            all_fengsu.columns = ["date_time", "wind_speed"]
            all_fengsu = all_fengsu.sort_values(by='date_time').reset_index(drop=True)   # 蒙西地区所有风速
            all_fengsu['date'] = all_fengsu['date_time'].map(lambda x: str(x).split(' ')[0])
            all_fengsu['time'] = all_fengsu['date_time'].map(lambda x: str(x).split(' ')[-1])
            re = self.check_data(all_fengsu)
        except Exception as err:
            logger.error("计算风速时出现错误:{}".format(err))
        return all_fengsu, re

    def check_data(self, all_fengsu):
        '''
        2、数据校验：至少有5天的历史数据有14个点
        '''
        re = False
        try:
            # 获取预测日最小的一天
            min_date = sort(self.yuce_list)[0]
            history_data = all_fengsu[all_fengsu['date'] < min_date]
            tmp_data = history_data.groupby('date').count().reset_index()
            count = tmp_data[tmp_data['time'] >= 10].shape[0]   # 行数
            if count >= 5:
                re = True
        except Exception as err:
            logger.error("数据校验时出错:{}".format(err))
        return re

    def pre_predict(self):
        '''
        1、相似日计算
        2、线性回归
        3、修正
        4、风光总加
        '''
        result = {}
        for date in self.yuce_list:
            yc_date = self.get_date(date, 9)
            # 取d~d+2日的日期和时间列表
            yc_list = []
            for i in range(len(yc_date)):
                if i <= 2:
                    for time in D1:
                        yc_list.append(yc_date[i] + ' ' + time)
                elif i == 3:
                    for time in D3:
                        yc_list.append(yc_date[i] + ' ' + time)
                else:
                    for time in D4:
                        yc_list.append(yc_date[i] + ' ' + time)
            for yc_time in yc_list:
                print(yc_time, self.all_fengsu)
                fs = self.all_fengsu[self.all_fengsu['date_time'] == yc_time]['wind_speed'].values[0]
                history_data = self.all_fengsu[
                    (self.all_fengsu['date_time'] < yc_time) & (self.all_fengsu['time'] == yc_time.split(' ')[-1])]
                history_data.loc[:, 'speed_dif'] = abs(history_data.loc[:, 'wind_speed'] - fs)
                date = history_data.sort_values('speed_dif')['date_time'].to_list()[:3]
                data = self.power_feng[self.power_feng['date_time'].isin(date)]
                sim_fh = mean(data['风电负荷'].to_list())
                liner = LinearRegression()
                pre_x = [fs]
                fuhe_data = self.power_feng[
                    (self.power_feng['date_time'] < yc_time) & (self.power_feng['time'] == yc_time.split(' ')[-1])]
                train_x, train_y = np.reshape(history_data['wind_speed'].tolist(), (-1, 1)), np.reshape(
                    fuhe_data['风电负荷'].tolist(), (-1, 1))
                liner.fit(train_x, train_y)
                y_pred = liner.predict(np.reshape(pre_x, (-1, 1)))
                result[yc_time] = (y_pred[0][0] + sim_fh) / 2
            result = pd.DataFrame(list(result.items()))
            result.columns = ['date_time', '风电预测值']
            guangfu_data = self.get_guangfu(yc_list)
            result = pd.merge(result, guangfu_data)
            result['新能源预测值'] = result['风电预测值'] + result['光伏预测值']
        return result.to_dict('list')

    def get_guangfu(self, yc_list):
        '''
        光伏的预测值
        '''
        guangfu_data = {}
        all_time = list(sort(D1 + D3 + D4))
        power_g = self.power_guang[self.power_guang['time'].isin(all_time)].groupby('time').mean().reset_index()
        for yc_time in yc_list:
            time = yc_time.split(' ')[-1]
            guangfu_data[yc_time] = power_g[power_g['time'] == time]['光负荷'].values[0]
        guangfu_data = pd.DataFrame(list(guangfu_data.items()))
        guangfu_data.columns = ['date_time', '光伏预测值']
        return guangfu_data

    def get_date(self, date, n):
        now = pd.to_datetime(date)
        i = 0
        yc_date = []
        while i <= n:
            delta = datetime.timedelta(days=i)
            n_days = (now + delta)
            yc_date.append(n_days.strftime('%Y-%m-%d'))
            i = i + 1
        yc_date = list(sort(yc_date))
        return yc_date

if __name__ == '__main__':
    speed_data = pd.read_excel("fengji.xlsx")
    print(speed_data.to_dict('list'))
    power_feng = pd.read_excel("powerfeng.xlsx")

    power_guang = pd.read_excel("powerguang.xlsx")
    power_feng['date_time'] = power_feng['date'].astype(str) + ' ' + power_feng['time'].astype(str)

    power_guang['date_time'] = power_guang['date'].astype(str) + ' ' + power_guang['time'].astype(str)
    print(power_guang.to_dict('list'))
    yuce_list = ['2022-05-15']
    weight_dic = {"China - 101巴-西乌素风电#03风机": 0.09,
                    "China - 102巴-嘉合风电#08风机": 0.09,
                    "China - 103巴-乌日德风电#129风机": 0.09,
                     "China - 213包-巴音风电#112风机": 0.09,
                     "China - 216包-红泥井风电#16风机": 0.09,
                     "China - 214包-双水泉风电#131风机": 0.09,
                     "China - 327呼-旭日汗光伏#28逆变器": 0.1,
                     "China - 324呼-夏日风电#14风机": 0.06,
                     "China - 430辉-宏盘风电#15风机": 0.06,
                     "China - 434辉-辉腾锡勒风电#58风机": 0.06,
                     "China - 753白-长春风电#9风机": 0.06,
                     "China - 436辉-库伦风电#62风机": 0.06,
                     "China - 545玫-玫瑰营风电#98风机": 0.06,
                     "China - 756白-嘉耀风电#2风机": 0.05,
                     "China - 757白-乌宁巴图风场#2风机": 0.05}
    # pre_power = NewPower(speed_data, power_feng, power_guang, yuce_list, weight_dic)
    # print(pd.DataFrame(pre_power.result))