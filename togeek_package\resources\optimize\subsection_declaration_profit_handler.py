# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_subsection_declaration_profit import SubsectionDeclarationProfit


class SubsectionDeclarationProfitHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        print(generators)
        prices = params.pop('prices')
        costs = params.pop('costs')
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 6)
        POP_SIZE = params.pop('POP_SIZE', 1000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 100)
        model = SubsectionDeclarationProfit(generators=generators, prices=prices, costs=costs,
                                            min_profit=min_profit, default_subsection=default_subsection,
                                            POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS)
        result = model.predict()
        self.write(result)
