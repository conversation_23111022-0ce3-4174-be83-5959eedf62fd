import pandas as pd
import os
import numpy as np
import json
from pyscipopt import Model, quicksum

class DlcnOptimization:
    def __init__(self, input_price_data, time_of_use=None, profit=0, init_soc=5,
                 min_soc=5, max_soc=95, capacity=200, power=100):
        """
        模型用到的参数
        ahead_price: 预测的日前节点电价，96点数组，单位:元/MWh
        time_of_use: 时段划分，24点列表，值为[0,1,2,3]分别代表谷、平、峰、尖时段
        profit: 储能度电盈利要求，单位:元/MWh,默认为 0
        init_soc: 初始的S0C，单位:%，默认为 5%
        min_soc: 允许的最小的SOC，单位：%，默认为 5%
        max_soc: 允许的最大的SOC，单位：%，默认为 95%
        capacity: 储能容量，单位:MWh，默认为 200
        power: 储能的最大功率，单位:MW，默认为 100
        eta: 储能的充放电效率，默认为 1
        """
        self.ahead_price, self.input_price = self.get_price_data(input_price_data)
        self.profit = profit
        self.init_soc = init_soc / 100
        self.min_soc = min_soc / 100
        self.max_soc = max_soc / 100
        self.capacity = capacity
        self.power = power
        self.eta = 1

        self.time_of_use = time_of_use
        if self.time_of_use is not None:
            self.time_of_use_96 = np.repeat(time_of_use, 4)
        else:
            self.time_of_use_96 = None

    def get_price_data(self, input_price_data):
        df = pd.DataFrame(input_price_data)
        try:
            if len(df) < 96:
                raise ValueError("数据行数不足96行")
            else:
                prices = df['price'].values[:96]
                return prices, df
        except Exception as e:
            print(f"读取数据失败: {e}")
            return None, df

    def model_dlcn(self):
        """
        构建优化模型，用 pyscipopt-5.5.0 求解
        返回: (时间, 功率策略, SOC百分比)
        """
        interval_hours = 1 / 4  # 15分钟间隔
        interval_nums = 96

        model = Model("EnergyStorageOptimization")

        T = interval_nums

        # ======== 决策变量 ========
        P_ch = {}
        for t in range(T):
            P_ch[t] = model.addVar(vtype="CONTINUOUS", lb=0, ub=self.power, name=f"P_ch_{t}")

        P_dis = {}
        for t in range(T):
            P_dis[t] = model.addVar(vtype="CONTINUOUS", lb=0, ub=self.power, name=f"P_dis_{t}")

        S = {}
        for t in range(T + 1):
            S[t] = model.addVar(vtype="CONTINUOUS",
                                lb=self.min_soc * self.capacity,
                                ub=self.max_soc * self.capacity,
                                name=f"S_{t}")

        u = {}
        for t in range(T):
            u[t] = model.addVar(vtype="BINARY", name=f"u_{t}")

        v = {}
        for t in range(T):
            v[t] = model.addVar(vtype="BINARY", name=f"v_{t}")

        start_charge = {}
        for t in range(T):
            start_charge[t] = model.addVar(vtype="BINARY", name=f"start_charge_{t}")

        start_discharge = {}
        for t in range(T):
            start_discharge[t] = model.addVar(vtype="BINARY", name=f"start_discharge_{t}")

        # ======== 目标函数 ========
        total_rev = quicksum(
            (P_dis[t] - P_ch[t]) * self.ahead_price[t] for t in range(T)) * interval_hours
        model.setObjective(total_rev, sense="maximize")

        # ======== 约束条件 ========
        model.addCons(S[0] == self.init_soc * self.capacity, "initial_soc")

        for t in range(T):
            model.addCons(
                S[t + 1] == S[t] + self.eta * P_ch[t] * interval_hours - (P_dis[t] / self.eta) * interval_hours,
                f"soc_update_{t}")

        for t in range(T):
            model.addCons(P_ch[t] <= u[t] * self.power, f"exclusive1_{t}")
            model.addCons(P_dis[t] <= v[t] * self.power, f"exclusive2_{t}")
            model.addCons(u[t] + v[t] <= 1, f"mutual_exclusive_{t}")

        min_power = self.power * 0.1
        for t in range(T):
            model.addCons(P_ch[t] >= min_power * u[t], f"min_charge_{t}")
            model.addCons(P_dis[t] >= min_power * v[t], f"min_discharge_{t}")

        for t in range(T):
            if t == 0:
                model.addCons(start_charge[t] == u[t], f"start_charge_{t}")
                model.addCons(start_discharge[t] == v[t], f"start_discharge_{t}")
            else:
                model.addCons(start_charge[t] >= u[t] - u[t - 1], f"start_charge_change_{t}")
                model.addCons(start_discharge[t] >= v[t] - v[t - 1], f"start_discharge_change_{t}")

        for t in range(T):
            if t >= 1:
                model.addCons(start_charge[t] <= u[t], f"new_start_charge1_{t}")
                model.addCons(start_charge[t] <= 1 - u[t - 1], f"new_start_charge2_{t}")
                model.addCons(start_discharge[t] <= v[t], f"new_start_discharge1_{t}")
                model.addCons(start_discharge[t] <= 1 - v[t - 1], f"new_start_discharge2_{t}")

        model.addCons(quicksum(start_charge[t] for t in range(T)) <= 2, "total_continuous_charge")
        model.addCons(quicksum(start_discharge[t] for t in range(T)) <= 2, "total_continuous_discharge")

        if self.time_of_use_96 is not None:
            for t in range(T):
                if self.time_of_use_96[t] in [2, 3]:
                    model.addCons(P_ch[t] == 0, f"peak_charge_restriction_{t}")
            print("已应用峰尖时段禁止充电约束")
        else:
            print("未提供时段划分参数，不应用峰尖时段充电限制")

        # ======== 设置求解器参数 ========
        model.setRealParam("limits/time", 300)  # 求解时间限制(秒)
        model.setRealParam("limits/gap", 0.001)  # 相对间隙容忍度

        # ======== 模型求解 ========
        try:
            model.optimize()

            if model.getStatus() == "optimal" or model.getStatus() == "timelimit":
                P_sto_strategy = []
                soc_percent = []

                for t in range(T):
                    net_power = round(model.getVal(P_dis[t]) - model.getVal(P_ch[t]), 2)
                    P_sto_strategy.append(net_power)

                    soc_value = model.getVal(S[t + 1])
                    soc_percent.append(round(soc_value / self.capacity * 100, 2))

                return P_sto_strategy, soc_percent
            else:
                print(f"求解未成功完成，状态: {model.getStatus()}")
                # 尝试获取当前解
                P_sto_strategy = []
                soc_percent = []

                for t in range(T):
                    P_ch_val = model.getVal(P_ch[t]) if model.getVal(P_ch[t]) is not None else 0
                    P_dis_val = model.getVal(P_dis[t]) if model.getVal(P_dis[t]) is not None else 0
                    P_sto_strategy.append(P_dis_val - P_ch_val)

                    soc_value = model.getVal(S[t + 1]) if model.getVal(
                        S[t + 1]) is not None else self.init_soc * self.capacity
                    soc_percent.append(soc_value / self.capacity * 100)

                print("返回当前可行解")
                return P_sto_strategy, soc_percent

        except Exception as e:
            print(f"求解过程中发生错误: {e}")

            P_sto_strategy = []
            soc_percent = []

            for t in range(T):
                P_ch_val = model.getVal(P_ch[t]) if hasattr(model, 'getVal') and model.getVal(
                    P_ch[t]) is not None else 0
                P_dis_val = model.getVal(P_dis[t]) if hasattr(model, 'getVal') and model.getVal(
                    P_dis[t]) is not None else 0
                P_sto_strategy.append(P_dis_val - P_ch_val)

                soc_value = model.getVal(S[t + 1]) if hasattr(model, 'getVal') and model.getVal(
                    S[t + 1]) is not None else self.init_soc * self.capacity
                soc_percent.append(soc_value / self.capacity * 100)

            print("返回当前可行解")
            return P_sto_strategy, soc_percent

    def get_dlcn_results(self, json=True):
        """
        返回最终的储能策略，根据json参数返回不同格式
        json=True: 返回JSON格式(字典列表)
        json=False: 返回DataFrame格式(时间列为字符串)
        """
        power_storage, soc_percent = self.model_dlcn()

        power_storage = [round(p, 2) for p in power_storage]

        res_df = pd.DataFrame()
        res_df['date_time'] = self.input_price.index.astype(str)
        res_df = res_df.set_index('date_time')
        res_df['储能功率(MW)'] = power_storage
        res_df['SOC(%)'] = soc_percent

        if json == True:
            return res_df.to_dict()
        else:
            return res_df

if __name__ == '__main__':
    # ========= 读取数据 ==========
    data_dir = r"E:\PythonProject\tg_gd_dlcn\Data"
    input_file = os.path.join(data_dir, "入参.txt")

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        print(f"成功从{input_file}读取入参数据")
    except FileNotFoundError:
        print(f"错误：未找到文件 {input_file}，请确保Data文件夹和入参.txt存在")
    except json.JSONDecodeError as e:
        print(f"解析JSON数据失败: {e}")
        input_data = {}
    except Exception as e:
        print(f"读取入参数据失败: {e}")
        input_data = {}

    params = {
        'input_price_data': input_data.get('ahead_price'),
        'profit': input_data.get('profit', 0),
        'init_soc': input_data.get('init_soc', 5),
        'min_soc': input_data.get('min_soc', 5),
        'max_soc': input_data.get('max_soc', 95),
        'capacity': input_data.get('capacity', 200),
        'power': input_data.get('power', 100),
        'time_of_use': input_data.get('time_of_use', None)
    }

    # ======== 初始化优化器并运行 ========
    if input_data:
        optimizer = DlcnOptimization(**params)
        res = optimizer.get_dlcn_results()
        print("优化结果:")
        if isinstance(res, dict):
            print({k: {k1: v1[:5] if isinstance(v1, list) else v1 for k1, v1 in v.items()} for k, v in res.items()})
        else:
            print(res.head())
    else:
        print("未获取到有效输入数据，程序终止")