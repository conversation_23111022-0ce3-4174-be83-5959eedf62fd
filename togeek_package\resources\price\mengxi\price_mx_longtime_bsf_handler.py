#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/5/12 17:23
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_longtime_pre import MxPricePredBsf

class PricePredBsfHandlerMx(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        all_data = params.pop('all_data')
        price = params.pop('price')
        t_date = params.pop('t_date')
        days = params.pop('days', 15)
        pro_params = params.pop('pro_params', {})
        points = params.get('points', 96)
        min_value = params.get("min_value", 0)
        max_value = params.get("max_value", 1500)
        m = MxPricePredBsf(all_data=all_data, price=price, t_date=t_date, days=days, pro_params=pro_params,
                           points=points, min_value=min_value, max_value=max_value)
        pred = m.predict()
        self.write(pred)