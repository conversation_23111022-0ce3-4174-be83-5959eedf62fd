#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/8/23 14:45 
@Info    ：

'''

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shanxi.sx_price_diff_pred import PriceDiffPredSX


class PriceDiffPredHandlerSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get('data')
        pred_lst = params.get('pred_lst')
        model = params.get('model', 'cbc')
        periods = params.get('periods', 30)
        scale = params.get('scale', 0.2)
        threshold_proba = params.get('threshold_proba', 0.6)
        pred = PriceDiffPredSX(data=data, pred_lst=pred_lst, periods=periods, scale=scale,
                             threshold_proba=threshold_proba, model=model)
        result = pred.run(to_json=True)
        self.write(result)
