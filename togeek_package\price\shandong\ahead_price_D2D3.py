# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-06-12 13:27:50
Description :   D+2~D+9日前价格预测模型，用于国华山东项目
"""


import numpy as np
import pandas as pd
import requests as rs
from datetime import timedelta
from chinese_calendar import is_workday
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import ExtraTreesRegressor
import logging
import warnings


logger = logging.getLogger()
warnings.filterwarnings('ignore')


class AheadPriceD2D3:

    def __init__(self, data, date_bidding: str, date_run: str, time_point: int, min_price, max_price, num_lag, days_train):
        logger.info("---------------------- Start: 山东D+1 ~ D+9 日前电价预测 --------------------------")
        self.data = data                        # 传入数据，json格式
        self.date_bidding = date_bidding        # 竞价日, 如"2023-06-12"
        self.date_run = date_run                # 运行日, 如"2023-06-14"
        self.time_point = time_point            # 每天的分段数量
        self.min_price = min_price              # 价格下限
        self.max_price = max_price              # 价格上限

        self.num_lag = num_lag                  # 构造lag特征的数量
        self.days_train = days_train            # 训练使用的历史数据天数
        self.message_error = ""                 # 错误信息

        self.days_diff = (pd.to_datetime(date_run) - pd.to_datetime(date_bidding)).days     # 竞价日和运行日之间的天数
        logger.info(f"竞价日: {date_bidding}, 运行日: {date_run}, 预测类型: D+{self.days_diff}")

    def data_verification(self):
        """
        数据校验：筛选过去30天(含)竞价日的日前价格实际值
        如果数据校验失败，只返回message
        否则，返回处理后的历史日前价格实际值
        :return:
        """

        # 读取数据
        df = pd.DataFrame(self.data)

        # 日期规范化，排序
        df.index = pd.to_datetime(df.index).astype(str)
        df = df.sort_index()

        # 添加字段： date, time
        df["date"] = df.index.map(lambda s: s.split(" ")[0])
        df["time"] = df.index.map(lambda s: s.split(" ")[1])

        # 筛选前30天数据
        date_train_start = str(pd.to_datetime(self.date_bidding) - timedelta(30)).split(" ")[0]
        df1 = df[(df['date'] > date_train_start) & (df['date'] <= self.date_bidding)]

        # 数据校验
        num_sample_should = self.time_point * 30
        num_sample_true = df1.shape[0]  # 传入的历史日前实际价格长度
        num_null = df1['ahead_price'].isnull().sum()  # 传入的历史日前实际价格缺失值数量

        # 数据错误信息保存至：self.message_error，若无错误，self.message=""
        if num_sample_true != num_sample_should:
            self.message_error += f"传入数据中，历史日前实际价格应为 {num_sample_should} 条，实为{num_sample_true}条, 请检查传入数据! "
        elif num_null != 0:
            self.message_error += f"传入数据中， 历史日前实际价格存在缺失值, 缺失值数量：{num_null}, 请检查传入数据! "
        else:
            logger.info(f"数据校验：历史日前实际价格应为 {num_sample_should} 条, 实为 {num_sample_true} 条, 数据校验通过.")

        return df1

    def feature_engineering(self, df_true):
        """
        特征工程
        :param df_true: 历史实际日前价格。
        :return:
        """
        # 构造预测日的日期，时间
        df_test = pd.DataFrame(index=pd.date_range(start=pd.to_datetime(self.date_bidding) + timedelta(1), periods=self.days_diff * self.time_point, freq=f"{1440 / self.time_point}T").astype(str))
        df_test['ahead_price'] = -1
        df_test['date'] = df_test.index.map(lambda s: s.split(" ")[0])
        df_test["time"] = df_test.index.map(lambda s: s.split(" ")[1])

        # 数据组合 --> dfc
        dfc = pd.concat([df_true, df_test])
        dfc = dfc.sort_index()

        # 峰平谷特征_山东: cols_fpg_list
        time_list = pd.date_range(start="00:00:00", periods=self.time_point, freq=f"{1440/self.time_point}T").map(lambda s: str(s).split(" ")[1]).to_list()

        if self.time_point == 24:
            feng_gu = ["gu"]*7 + ["ping"] + ["feng"]*3 + ["jian"] + ["ping"]*4 + ["feng"]*3 + ["jian"]*2 + ["ping"]*2 + ["gu"]
        elif self.time_point == 96:
            feng_gu = ["gu"]*28 + ["ping"]*6 + ["feng"]*8 + ["jian"]*4 + ["ping"]*18 + ["feng"]*12 + ["jian"]*8 + ["ping"]*8 + ["gu"]*4
        else:
            self.message_error += f"参数 time_point 不在 [24, 96]中，请核对数据!"

        feng_gu_dict = {k: v for k, v in zip(time_list, feng_gu)}
        dfc["period"] = dfc['time'].map(feng_gu_dict)
        cols_fpg_list = list(pd.get_dummies(dfc["period"]).columns)
        dfc[cols_fpg_list] = pd.get_dummies(dfc["period"])

        # 小时特征: cols1
        cols1 = [f"hour_{i:02}" for i in range(24)]
        dfc[cols1] = pd.get_dummies(dfc['time'].map(lambda s: s[:2]))

        # 周几特征: cols2
        cols2 = [f"dayofweek_{i}" for i in range(7)]
        dfc[cols2] = pd.get_dummies(pd.to_datetime(dfc.index).map(lambda s: s.dayofweek)).values

        # 是否工作日: cols3
        cols3 = ["is_workday"]
        dfc[cols3[0]] = pd.to_datetime(dfc.index).map(lambda s: is_workday(s)) * 1

        # lag特征: cols_lag_list
        idx_lag_list = np.arange(self.days_diff, self.num_lag + self.days_diff)
        cols_lag_list = []
        for idx_lag in idx_lag_list:
            col_lag = f"D_{idx_lag - self.days_diff}"
            dfc[col_lag] = dfc["ahead_price"].shift(idx_lag * self.time_point)
            cols_lag_list.append(col_lag)

        # 特征汇总
        features = cols_lag_list + cols1 + cols2 + cols3 + cols_fpg_list
        data_all = dfc[["date", "ahead_price"] + features].dropna()

        return data_all, features

    def predict(self):
        # 构造输出数据结构
        result = {
            "ahead_price_pred": {},
            "message_error": "",
        }

        # 数据校验
        df_true = self.data_verification()
        if len(self.message_error) != 0:
            result["message_error"] = self.message_error
        else:
            # 特征工程
            data_all, feature_list = self.feature_engineering(df_true)
            # 划分数据集
            data_train = data_all[data_all["date"] <= self.date_bidding]
            data_train = data_train.iloc[-self.days_train * self.time_point:, :]
            data_test = data_all[data_all["date"] == self.date_run]
            X_train = data_train[feature_list]
            X_test = data_test[feature_list]
            y_train = data_train["ahead_price"].values
            df_pred = data_test[["ahead_price"]]
            logging.info(f"训练数据范围：{data_train['date'].unique()[0]} ~ {data_train['date'].unique()[-1]}")
            logging.info(f"训练数据特征：{feature_list}")

            # 标准化
            scaler = StandardScaler()
            scaler.fit(X_train)
            X_train_std = scaler.transform(X_train)
            X_test_std = scaler.transform(X_test)

            # 模型预测
            model = ExtraTreesRegressor()
            model.fit(X_train_std, y_train)
            y_pred = model.predict(X_test_std)
            df_pred["ahead_price_pred"] = np.clip(y_pred, self.min_price, self.max_price)   # 极值修正
            result["ahead_price_pred"] = df_pred.to_dict()["ahead_price_pred"]

        logger.info("---------------------- End: 山东D+2、D+3 日前电价预测 --------------------------")

        return result


class AheadPriceD2D10:

    def __init__(self, date_bidding: str):
        logger.info("---------------------- Start: 山东D+2 ~ D+10 日前电价预测查询 v2.0 from datasets --------------------------")
        self.date_bidding = date_bidding        # 竞价日, 如"2023-09-21"
        self.message_error = ""                 # 错误信息
        logger.info(f"竞价日: {date_bidding}")

    @staticmethod
    def get_data_datasets(grid: str, key_dict: dict, date_start: str, date_end: str):
        """
        从 datasets 数据库查询数据
        Parameters
        ----------
        grid: 省份, 可选["SHANXI", "SHANDONG", "MENGXI"]
        key_dict: 字段名称: 索引，示例： {"/日前/统一结算点电价": 399, "/实时/统一结算点电价": 405}。从表 datasets/ds_index_meta 中查询
        date_start: 查询起始日期
        date_end: 查询终止日期

        Returns data
        -------

        """
        # 1. 构造请求参数
        url = f'http://139.9.77.109:8025/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
        params = {
            'grid': grid,
            'startTime': f"{date_start} 00:00:00",
            'endTime': f"{date_end} 23:45:00",
            'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
            'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
        }

        # 2. 获取数据
        res_dict = rs.get(url, params=params)

        # 3. 数据整理
        df_list = []

        for key in list(key_dict.keys()):
            df_tmp = pd.DataFrame(res_dict.json()['data'][key]['points'])
            df_tmp = df_tmp.rename(columns={'value': key})
            df_list.append(df_tmp)

        date_time_list = pd.date_range(f"{date_start} 00:00:00", f"{date_end} 23:45:00", freq="15T").astype(str).values
        df = pd.DataFrame({"time": date_time_list})
        for i in range(len(list(key_dict.keys()))):
            df = pd.merge(df, df_list[i], on='time', how="left")
        df = df.rename(columns={"time": "date_time"})
        df['date'] = df['date_time'].map(lambda s: s.split(" ")[0])
        df = df.set_index(["date_time", "date"])

        return df

    def predict(self):
        # 构造输出数据结构
        result = {
            "ahead_price_pred": {},
            "message_error": "",
        }

        # 从datasets平台查询山东D+2~D+10预测数据
        df_pred_list = []
        for d in range(2, 11):
            run_date = str(pd.to_datetime(self.date_bidding) + timedelta(d)).split(" ")[0]
            logger.info(f"开始查询：D+{d}: {run_date}")

            # 查询字典
            if d == 10:
                key_dict = {f"山东价格预测_d+10": 1898}
            else:
                key_dict = {f"山东价格预测_d+{d}": 1134 + d}

            try:
                df_pred = self.get_data_datasets(
                    grid="SHANDONG",
                    key_dict=key_dict,
                    date_start=run_date,
                    date_end=run_date,
                )
            except:
                self.message_error += f"竞价日：{self.date_bidding}, 运行日：{run_date}, 预测数据获取失败，请检查！"
                result["message_error"] = self.message_error
                return result
            df_pred.columns = ["ahead_price_pred"]
            df_pred_list.append(df_pred)

        # 预测数据整理 & 输出
        df_pred = pd.concat(df_pred_list)
        df_pred = df_pred.reset_index(-1).drop("date", axis=1)
        result["ahead_price_pred"] = df_pred.to_dict()["ahead_price_pred"]

        logger.info("---------------------- End: 山东D+2 ~ D+10 日前电价预测查询 v2.0 from datasets --------------------------")

        return result


if __name__ == '__main__':
    # import json
    # path_demo_data = r"F:\ToGeek\data_research\data_shandong\data_input_SD_D23.json"
    # with open(path_demo_data, "r", encoding="utf-8") as f:
    #     data_input = json.loads(f.read())
    #
    # m = AheadPriceD2D3(
    #     data=data_input["data"],
    #     # date_bidding=data_input["date_bidding"],
    #     date_bidding="2023-06-13",
    #     # date_run=data_input["date_run"],
    #     date_run="2023-06-22",
    #     time_point=data_input["time_point"],
    #     min_price=data_input["min_price"],
    #     max_price=data_input["max_price"],
    #     num_lag=21,
    #     days_train=1,
    # )
    # print(m.predict())

    m = AheadPriceD2D10(date_bidding="2024-05-22")
    print(pd.DataFrame(m.predict()))
