#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/7/12 10:12 
@Info    ：山东省：依据分段报价方案模拟中标出力

'''


import numpy as np
import pandas as pd
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


class SimulateBiddedPower:
    def __init__(self, generator, price, sub_decl, type=1, decimal=3):
        self.type = type  # 是否为线性报价，默认为1-线性报价
        self.decimal = int(decimal)
        self.freqs = [24, 48, 96]  # 时点数
        logger.info("-----------已知机组信息、报价方案和出清电价，计算各机组的中标出力-----------")
        self.gener, self.price, self.sub_decl, self.msg, self.flag = self._prepare_data(generator, price, sub_decl)
        logger.info(self.msg)
        # print(self.msg)

    def _prepare_data(self, generator, price, sub_decl):
        flag = 1  # 信息检查通过的标志
        # 1 读取数据
        gener = pd.DataFrame(generator)
        price = pd.DataFrame(price)
        sub_decl = pd.DataFrame(sub_decl)

        # 2 修正gener/price/sub_decl的数据类型
        gener['generator'] = gener['generator'].astype('str')
        if 'beginning_power' not in gener.columns:
            gener['beginning_power'] = gener['min_power']
        gener['beginning_power'].fillna(gener['min_power'], inplace=True)  # 起始出力为空则用最小技术出力填充
        for col in ['rated_capacity', 'min_power', 'beginning_power', 'upward']:
            gener[col] = gener[col].astype('float')

        price['generator'] = price['generator'].astype('str')
        price['time'] = price['time'].astype('str')
        price['ahead_price'] = price['ahead_price'].astype('float')
        price['real_price'] = price['real_price'].astype('float')
        price.sort_values(['generator', 'time']).reset_index(drop=True)  # 按时间排序

        sub_decl['generator'] = sub_decl['generator'].astype('str')
        for col in ['subsection', 'power']:
            sub_decl[col] = sub_decl[col].astype('int')
        sub_decl['price'] = sub_decl['price'].astype('float')
        sub_decl.sort_values(['generator', 'subsection']).reset_index(drop=True)  # 按分段序号排序

        # 3 信息检查
        msg = ""
        # 检查gener表中是否有空值
        hasnull = gener.isnull().sum()
        num_null = hasnull['generator'] + hasnull['rated_capacity'] + hasnull['min_power'] + hasnull['upward']
        if num_null > 0:
            msg += "1. gener表中generator、rated_capacity、min_power、upward中有" + str(num_null) + "个空值，请检查generator数据!"
            flag *= 0
        else:
            msg += "1. gener表中generator、rated_capacity、min_power、upward中没有空值；"

        # 检查price表
        for g in gener['generator']:
            msg += f"2. 开始检查price表中机组 {g} 的价格数据，"
            tmp_price = price[price['generator'] == g]
            # 检查是否有空值
            num_null = tmp_price.isnull().sum().values.sum()
            if num_null > 0:
                msg += f"price表中有{num_null}个空值，请检查price数据!"
                flag *= 0
            # 价格数据必须是【24， 48， 96】中的一种
            freq = tmp_price.shape[0]
            if freq in self.freqs:
                msg += f"2.1 机组{g}的电价行数为{freq},在{self.freqs}之中；"
            else:
                msg += f"2.1 机组{g}的电价行数为{freq},不在{self.freqs}之中，请检查price数据!"
                flag *= 0

            # 检查时间是否为00:00~23:xx，否则不合格, gap为间隔分钟数，96点为15min 48点为30min 24点为60min
            gap = int(24 * 60 / freq)
            # tmp_price['时间'] = tmp_price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
            tmp_price['时间'] = tmp_price['time'].map(
                lambda s: int(s.split(":")[0]) * (60 / gap) + int(s.split(":")[1]) / gap)
            curtimes0 = list(range(freq))
            curtimes1 = list(range(1, freq + 1))
            if tmp_price['时间'].tolist() == curtimes0:
                msg += f"2.2 要求 {freq} 个点的时间从 00:00 开始，每隔 {gap} 分钟一个点，确认正确，机组 {g} 数据验证通过。"
            elif (tmp_price['时间'].tolist() == curtimes1) & freq == 96:
                msg = msg + f"2.2, 要求 {freq} 个点的时间从 00:15 开始，每隔 {gap} 分钟一个点，确认正确，机组 {g} 数据验证通过。"
            elif (tmp_price['时间'].tolist() == curtimes1) & freq == 48:
                msg = msg + f"2.2, 要求 {freq} 个点的时间从 00:30 开始，每隔 {gap} 分钟一个点，确认正确，机组 {g} 数据验证通过。"
            elif (tmp_price['时间'].tolist() == curtimes1) & freq == 24:
                msg = msg + f"2.2, 要求 {freq} 个点的时间从 01:00 开始，每隔 {gap} 分钟一个点，确认正确，机组 {g} 数据验证通过。"
            else:
                msg += f"2.2 价格数据不完整，机组 {g} 数据验证不通过，请检查price数据的时刻！"
                flag *= 0
        return gener, price, sub_decl, msg, flag

    def _simulate_bidded_power(self, x, p, price, col):  # col = ['ahead_price', 'real_price']
        """
        依据分段报价方案、出清电价，模拟中标出力
        :param x:
        :param p:
        :param col:
        :return:
        """
        xs = []  # 中标出力
        # 前一天的最后一个点的出力
        last_point_power = max(self.min_power, self.beginning_power)

        # 爬坡速率修正
        for p_t in price[col]:
            x_t = 0  # 初始化：当前时刻出力
            i = len(p) - 1  #

            # 1 出清价格大于报价，才能中标，最小到p[0]=0, 一定中标，中了最小出力
            if self.type == 1:
                while i >= 0:
                    if p_t >= p[i]:
                        if i == len(p) - 1:
                            x_t = x[i]  # 出力最高中到最大出力段，此时为直线报价方案
                        else:
                            x_t = x[i + 1] - (p[i + 1] - p_t) / (p[i + 1] - p[i]) * (
                                        x[i + 1] - x[i])  # 最小出力~最大出力，按照线性报价方案
                        break
                    else:
                        i = i - 1
            else:
                while i >= 0:
                    if p_t >= p[i]:
                        x_t = x[i]
                        break
                    else:
                        i = i - 1

            # 判断机组中标出力是否为0，如果是0，则不再进行爬坡速率及出力上下限的约束，暂时不考虑停机时长约束及允许开机
            if x_t != 0:
                # 2 爬坡速率修正，且需要修正为整数
                if x_t > last_point_power + self.upward * self.gap:
                    last_point_power = int(last_point_power + self.upward * self.gap)
                    x_t = last_point_power
                elif x_t < last_point_power - self.upward * self.gap:
                    last_point_power = np.ceil(last_point_power - self.upward * self.gap)
                    x_t = last_point_power
                else:
                    last_point_power = x_t

                # 3 每个时点出力的上下限修正 -- 暂时不考虑分时段上下限约束
                # if x_t > upper_power:
                #     last_point_power = upper_power
                #     x_t = last_point_power
                # elif x_t < lower_power:
                #     last_point_power = lower_power
                #     x_t = last_point_power
            xs.append(round(x_t, self.decimal))
        return np.array(xs)

    def run(self, to_json=True):
        result = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        if self.flag:
            # 循环generator进行模拟中标出力计算；
            for g in self.gener['generator']:
                logger.info(f"---开始计算机组{g}的中标出力---")

                # 1.1、获取当前机组边界信息及节点电价
                gener = self.gener[self.gener['generator'] == g]
                price = self.price[self.price['generator'] == g]
                sub_decl = self.sub_decl[self.sub_decl['generator'] == g]
                freq = price.shape[0]
                self.gap = int(1440 / freq)

                # 1.2、获取分段报价方案
                x = sub_decl['power'].tolist()
                p = sub_decl['price'].tolist()
                self.rated_capa = int(gener['rated_capacity'])
                self.min_power = float(gener['min_power'])
                self.beginning_power = float(gener['beginning_power'])
                self.upward = int(gener['upward'])

                tmp_res = pd.DataFrame()
                tmp_res['generator'] = [g] * freq
                tmp_res['time'] = price['time'].tolist()
                if "ahead_price" in price.columns:
                    xas = self._simulate_bidded_power(x, p, price, 'ahead_price')  # 日前市场中标出力
                    tmp_res['ahead_power'] = xas
                if "real_price" in price.columns:
                    xrs = self._simulate_bidded_power(x, p, price, 'real_price')  # 实时市场中标出力
                    tmp_res['real_power'] = xrs
                result = pd.concat([result, tmp_res], axis=0)
            result.reset_index(drop=True, inplace=True)
        if to_json:
            result = result.to_dict("dict")
        logger.info("------------模拟中标出力模型运行结束--------------")
        return {"result": result, "message": self.msg}


if __name__ == '__main__':
    generator = {
        "generator": {0: 1, 1: 2},
        "rated_capacity": {0: 650, 1: 650},
        "min_power": {0: 230, 1: 230},
        "beginning_power": {0: 251, 1: 265},
        "upward": {0: 3, 1: 3},
    }
    price = {
        "generator": {
            0: 1,
            1: 1,
            2: 1,
            3: 1,
            4: 1,
            5: 1,
            6: 1,
            7: 1,
            8: 1,
            9: 1,
            10: 1,
            11: 1,
            12: 1,
            13: 1,
            14: 1,
            15: 1,
            16: 1,
            17: 1,
            18: 1,
            19: 1,
            20: 1,
            21: 1,
            22: 1,
            23: 1,
            24: 1,
            25: 1,
            26: 1,
            27: 1,
            28: 1,
            29: 1,
            30: 1,
            31: 1,
            32: 1,
            33: 1,
            34: 1,
            35: 1,
            36: 1,
            37: 1,
            38: 1,
            39: 1,
            40: 1,
            41: 1,
            42: 1,
            43: 1,
            44: 1,
            45: 1,
            46: 1,
            47: 1,
            48: 1,
            49: 1,
            50: 1,
            51: 1,
            52: 1,
            53: 1,
            54: 1,
            55: 1,
            56: 1,
            57: 1,
            58: 1,
            59: 1,
            60: 1,
            61: 1,
            62: 1,
            63: 1,
            64: 1,
            65: 1,
            66: 1,
            67: 1,
            68: 1,
            69: 1,
            70: 1,
            71: 1,
            72: 1,
            73: 1,
            74: 1,
            75: 1,
            76: 1,
            77: 1,
            78: 1,
            79: 1,
            80: 1,
            81: 1,
            82: 1,
            83: 1,
            84: 1,
            85: 1,
            86: 1,
            87: 1,
            88: 1,
            89: 1,
            90: 1,
            91: 1,
            92: 1,
            93: 1,
            94: 1,
            95: 1,
            96: 2,
            97: 2,
            98: 2,
            99: 2,
            100: 2,
            101: 2,
            102: 2,
            103: 2,
            104: 2,
            105: 2,
            106: 2,
            107: 2,
            108: 2,
            109: 2,
            110: 2,
            111: 2,
            112: 2,
            113: 2,
            114: 2,
            115: 2,
            116: 2,
            117: 2,
            118: 2,
            119: 2,
            120: 2,
            121: 2,
            122: 2,
            123: 2,
            124: 2,
            125: 2,
            126: 2,
            127: 2,
            128: 2,
            129: 2,
            130: 2,
            131: 2,
            132: 2,
            133: 2,
            134: 2,
            135: 2,
            136: 2,
            137: 2,
            138: 2,
            139: 2,
            140: 2,
            141: 2,
            142: 2,
            143: 2,
            144: 2,
            145: 2,
            146: 2,
            147: 2,
            148: 2,
            149: 2,
            150: 2,
            151: 2,
            152: 2,
            153: 2,
            154: 2,
            155: 2,
            156: 2,
            157: 2,
            158: 2,
            159: 2,
            160: 2,
            161: 2,
            162: 2,
            163: 2,
            164: 2,
            165: 2,
            166: 2,
            167: 2,
            168: 2,
            169: 2,
            170: 2,
            171: 2,
            172: 2,
            173: 2,
            174: 2,
            175: 2,
            176: 2,
            177: 2,
            178: 2,
            179: 2,
            180: 2,
            181: 2,
            182: 2,
            183: 2,
            184: 2,
            185: 2,
            186: 2,
            187: 2,
            188: 2,
            189: 2,
            190: 2,
            191: 2,
        },
        "time": {
            0: "00:00:00",
            1: "00:15:00",
            2: "00:30:00",
            3: "00:45:00",
            4: "01:00:00",
            5: "01:15:00",
            6: "01:30:00",
            7: "01:45:00",
            8: "02:00:00",
            9: "02:15:00",
            10: "02:30:00",
            11: "02:45:00",
            12: "03:00:00",
            13: "03:15:00",
            14: "03:30:00",
            15: "03:45:00",
            16: "04:00:00",
            17: "04:15:00",
            18: "04:30:00",
            19: "04:45:00",
            20: "05:00:00",
            21: "05:15:00",
            22: "05:30:00",
            23: "05:45:00",
            24: "06:00:00",
            25: "06:15:00",
            26: "06:30:00",
            27: "06:45:00",
            28: "07:00:00",
            29: "07:15:00",
            30: "07:30:00",
            31: "07:45:00",
            32: "08:00:00",
            33: "08:15:00",
            34: "08:30:00",
            35: "08:45:00",
            36: "09:00:00",
            37: "09:15:00",
            38: "09:30:00",
            39: "09:45:00",
            40: "10:00:00",
            41: "10:15:00",
            42: "10:30:00",
            43: "10:45:00",
            44: "11:00:00",
            45: "11:15:00",
            46: "11:30:00",
            47: "11:45:00",
            48: "12:00:00",
            49: "12:15:00",
            50: "12:30:00",
            51: "12:45:00",
            52: "13:00:00",
            53: "13:15:00",
            54: "13:30:00",
            55: "13:45:00",
            56: "14:00:00",
            57: "14:15:00",
            58: "14:30:00",
            59: "14:45:00",
            60: "15:00:00",
            61: "15:15:00",
            62: "15:30:00",
            63: "15:45:00",
            64: "16:00:00",
            65: "16:15:00",
            66: "16:30:00",
            67: "16:45:00",
            68: "17:00:00",
            69: "17:15:00",
            70: "17:30:00",
            71: "17:45:00",
            72: "18:00:00",
            73: "18:15:00",
            74: "18:30:00",
            75: "18:45:00",
            76: "19:00:00",
            77: "19:15:00",
            78: "19:30:00",
            79: "19:45:00",
            80: "20:00:00",
            81: "20:15:00",
            82: "20:30:00",
            83: "20:45:00",
            84: "21:00:00",
            85: "21:15:00",
            86: "21:30:00",
            87: "21:45:00",
            88: "22:00:00",
            89: "22:15:00",
            90: "22:30:00",
            91: "22:45:00",
            92: "23:00:00",
            93: "23:15:00",
            94: "23:30:00",
            95: "23:45:00",
            96: "00:00:00",
            97: "00:15:00",
            98: "00:30:00",
            99: "00:45:00",
            100: "01:00:00",
            101: "01:15:00",
            102: "01:30:00",
            103: "01:45:00",
            104: "02:00:00",
            105: "02:15:00",
            106: "02:30:00",
            107: "02:45:00",
            108: "03:00:00",
            109: "03:15:00",
            110: "03:30:00",
            111: "03:45:00",
            112: "04:00:00",
            113: "04:15:00",
            114: "04:30:00",
            115: "04:45:00",
            116: "05:00:00",
            117: "05:15:00",
            118: "05:30:00",
            119: "05:45:00",
            120: "06:00:00",
            121: "06:15:00",
            122: "06:30:00",
            123: "06:45:00",
            124: "07:00:00",
            125: "07:15:00",
            126: "07:30:00",
            127: "07:45:00",
            128: "08:00:00",
            129: "08:15:00",
            130: "08:30:00",
            131: "08:45:00",
            132: "09:00:00",
            133: "09:15:00",
            134: "09:30:00",
            135: "09:45:00",
            136: "10:00:00",
            137: "10:15:00",
            138: "10:30:00",
            139: "10:45:00",
            140: "11:00:00",
            141: "11:15:00",
            142: "11:30:00",
            143: "11:45:00",
            144: "12:00:00",
            145: "12:15:00",
            146: "12:30:00",
            147: "12:45:00",
            148: "13:00:00",
            149: "13:15:00",
            150: "13:30:00",
            151: "13:45:00",
            152: "14:00:00",
            153: "14:15:00",
            154: "14:30:00",
            155: "14:45:00",
            156: "15:00:00",
            157: "15:15:00",
            158: "15:30:00",
            159: "15:45:00",
            160: "16:00:00",
            161: "16:15:00",
            162: "16:30:00",
            163: "16:45:00",
            164: "17:00:00",
            165: "17:15:00",
            166: "17:30:00",
            167: "17:45:00",
            168: "18:00:00",
            169: "18:15:00",
            170: "18:30:00",
            171: "18:45:00",
            172: "19:00:00",
            173: "19:15:00",
            174: "19:30:00",
            175: "19:45:00",
            176: "20:00:00",
            177: "20:15:00",
            178: "20:30:00",
            179: "20:45:00",
            180: "21:00:00",
            181: "21:15:00",
            182: "21:30:00",
            183: "21:45:00",
            184: "22:00:00",
            185: "22:15:00",
            186: "22:30:00",
            187: "22:45:00",
            188: "23:00:00",
            189: "23:15:00",
            190: "23:30:00",
            191: "23:45:00",},
        "ahead_price": {
            0: 445.0,
            1: 415.0,
            2: 405.0,
            3: 401.0,
            4: 400.0,
            5: 400.0,
            6: 400.0,
            7: 399.0,
            8: 398.4,
            9: 398.4,
            10: 398.4,
            11: 398.4,
            12: 398.4,
            13: 398.4,
            14: 398.4,
            15: 398.4,
            16: 398.4,
            17: 398.4,
            18: 398.0,
            19: 398.0,
            20: 398.0,
            21: 398.0,
            22: 398.4,
            23: 398.4,
            24: 398.4,
            25: 398.4,
            26: 398.4,
            27: 398.4,
            28: 398.4,
            29: 397.5,
            30: 393.0,
            31: 385.0,
            32: 380.0,
            33: 365.0,
            34: 350.0,
            35: 330.0,
            36: 294.0,
            37: 260.0,
            38: 266.0,
            39: 230.0,
            40: 250.0,
            41: 215.0,
            42: 162.0,
            43: 299.0,
            44: 280.0,
            45: 280.0,
            46: 270.0,
            47: 220.0,
            48: 200.0,
            49: 0.0,
            50: 0.0,
            51: 0.0,
            52: 0.0,
            53: 0.0,
            54: 0.0,
            55: 0.0,
            56: 0.0,
            57: 0.0,
            58: 0.0,
            59: 214.0,
            60: 200.0,
            61: 230.0,
            62: 215.0,
            63: 220.0,
            64: 0.0,
            65: 0.0,
            66: 214.0,
            67: 231.0,
            68: 253.0,
            69: 282.0,
            70: 295.0,
            71: 338.0,
            72: 360.0,
            73: 380.0,
            74: 385.0,
            75: 395.0,
            76: 398.0,
            77: 398.4,
            78: 398.4,
            79: 398.4,
            80: 397.0,
            81: 395.0,
            82: 386.86,
            83: 385.0,
            84: 385.0,
            85: 382.0,
            86: 380.0,
            87: 380.0,
            88: 370.0,
            89: 361.15,
            90: 355.0,
            91: 350.0,
            92: 350.0,
            93: 340.0,
            94: 340.0,
            95: 332.0,
            96: 261.36,
            97: 270.0,
            98: 270.0,
            99: 270.0,
            100: 270.0,
            101: 270.0,
            102: 270.0,
            103: 261.36,
            104: 261.36,
            105: 261.36,
            106: 261.36,
            107: 261.36,
            108: 261.36,
            109: 261.36,
            110: 261.36,
            111: 260.0,
            112: 262.72,
            113: 265.0,
            114: 270.0,
            115: 270.0,
            116: 280.0,
            117: 325.0,
            118: 350.0,
            119: 362.0,
            120: 378.0,
            121: 385.0,
            122: 390.0,
            123: 390.0,
            124: 380.0,
            125: 378.0,
            126: 362.0,
            127: 355.0,
            128: 320.0,
            129: 280.0,
            130: 274.0,
            131: 270.0,
            132: 250.0,
            133: 246.0,
            134: 230.0,
            135: 214.0,
            136: 220.0,
            137: 214.0,
            138: 195.0,
            139: 280.0,
            140: 270.0,
            141: 242.92,
            142: 250.0,
            143: 230.0,
            144: 235.0,
            145: 235.0,
            146: 230.0,
            147: 220.0,
            148: 160.0,
            149: 160.0,
            150: 180.0,
            151: 220.0,
            152: 220.0,
            153: 230.0,
            154: 255.0,
            155: 265.0,
            156: 250.0,
            157: 255.0,
            158: 265.0,
            159: 281.08,
            160: 261.36,
            161: 332.0,
            162: 370.0,
            163: 380.0,
            164: 397.98,
            165: 399.0,
            166: 405.5,
            167: 425.0,
            168: 480.36,
            169: 520.0,
            170: 700.0,
            171: 800.0,
            172: 999.0,
            173: 1000.0,
            174: 900.0,
            175: 800.0,
            176: 700.0,
            177: 569.0,
            178: 500.36,
            179: 500.0,
            180: 500.0,
            181: 485.0,
            182: 460.0,
            183: 449.0,
            184: 435.0,
            185: 420.0,
            186: 410.0,
            187: 401.19,
            188: 400.0,
            189: 400.0,
            190: 398.4,
            191: 398.0,},
        "real_price": {
            0: 400.0,
            1: 419.0,
            2: 400.0,
            3: 398.4,
            4: 398.4,
            5: 398.4,
            6: 398.4,
            7: 397.0,
            8: 390.0,
            9: 390.0,
            10: 398.4,
            11: 397.0,
            12: 385.0,
            13: 385.0,
            14: 386.86,
            15: 381.72,
            16: 370.0,
            17: 397.0,
            18: 386.86,
            19: 353.0,
            20: 385.0,
            21: 389.0,
            22: 380.0,
            23: 395.0,
            24: 386.86,
            25: 389.0,
            26: 395.0,
            27: 385.0,
            28: 350.0,
            29: 353.0,
            30: 300.0,
            31: 299.0,
            32: 299.0,
            33: 261.36,
            34: 251.0,
            35: 260.0,
            36: 299.0,
            37: 261.0,
            38: 0.0,
            39: 230.0,
            40: 250.0,
            41: 0.0,
            42: 0.0,
            43: 0.0,
            44: 9.0,
            45: 135.0,
            46: 270.0,
            47: 0.0,
            48: 0.0,
            49: 0.0,
            50: 0.0,
            51: 0.0,
            52: 0.0,
            53: 0.0,
            54: 0.0,
            55: 0.0,
            56: 0.0,
            57: 0.0,
            58: 0.0,
            59: 0.0,
            60: 0.0,
            61: 0.0,
            62: 100.0,
            63: 100.0,
            64: 0.0,
            65: 0.0,
            66: 0.0,
            67: 208.0,
            68: 335.0,
            69: 300.0,
            70: 299.61,
            71: 315.64,
            72: 345.49,
            73: 340.0,
            74: 332.0,
            75: 370.0,
            76: 378.73,
            77: 395.0,
            78: 397.0,
            79: 382.0,
            80: 370.0,
            81: 360.0,
            82: 360.0,
            83: 350.0,
            84: 350.0,
            85: 340.0,
            86: 332.0,
            87: 325.0,
            88: 350.0,
            89: 340.0,
            90: 340.0,
            91: 330.0,
            92: 335.0,
            93: 310.0,
            94: 330.0,
            95: 320.0,
            96: 0.0,
            97: 0.0,
            98: 0.0,
            99: 0.0,
            100: 0.0,
            101: 0.0,
            102: 0.0,
            103: 0.0,
            104: 0.0,
            105: 0.0,
            106: 0.0,
            107: 0.0,
            108: 0.0,
            109: 0.0,
            110: 0.0,
            111: 0.0,
            112: 0.0,
            113: 0.0,
            114: 0.0,
            115: 0.0,
            116: 0.0,
            117: 0.0,
            118: 0.0,
            119: 0.0,
            120: 0.0,
            121: 0.0,
            122: 0.0,
            123: 0.0,
            124: 0.0,
            125: 0.0,
            126: 0.0,
            127: 0.0,
            128: 0.0,
            129: 0.0,
            130: 0.0,
            131: 0.0,
            132: 0.0,
            133: 0.0,
            134: 0.0,
            135: 0.0,
            136: 0.0,
            137: 0.0,
            138: 0.0,
            139: 0.0,
            140: 0.0,
            141: 0.0,
            142: 0.0,
            143: 0.0,
            144: 0.0,
            145: 0.0,
            146: 0.0,
            147: 0.0,
            148: 0.0,
            149: 0.0,
            150: 0.0,
            151: 0.0,
            152: 0.0,
            153: 0.0,
            154: 0.0,
            155: 0.0,
            156: 0.0,
            157: 0.0,
            158: 0.0,
            159: 0.0,
            160: 0.0,
            161: 0.0,
            162: 0.0,
            163: 0.0,
            164: 0.0,
            165: 0.0,
            166: 0.0,
            167: 0.0,
            168: 0.0,
            169: 0.0,
            170: 0.0,
            171: 0.0,
            172: 0.0,
            173: 0.0,
            174: 0.0,
            175: 0.0,
            176: 0.0,
            177: 0.0,
            178: 0.0,
            179: 0.0,
            180: 0.0,
            181: 0.0,
            182: 0.0,
            183: 0.0,
            184: 0.0,
            185: 0.0,
            186: 0.0,
            187: 0.0,
            188: 0.0,
            189: 0.0,
            190: 0.0,
            191: 0.0,},
    }
    sub_decl = {
        "generator": {
            0: 1,
            1: 1,
            2: 1,
            3: 1,
            4: 1,
            5: 1,
            6: 2,
            7: 2,
            8: 2,
            9: 2,
            10: 2,
        },
        "subsection": {
            0: 1,
            1: 2,
            2: 3,
            3: 4,
            4: 5,
            5: 6,
            6: 1,
            7: 2,
            8: 3,
            9: 4,
            10: 5,
        },
        "power": {0: 230,
            1: 350,
            2: 490,
            3: 530,
            4: 610,
            5: 650,
            6: 390,
            7: 510,
            8: 550,
            9: 630,
            10: 650,},
        "price": {0: 0,
            1: 11.91,
            2: 139.63,
            3: 267.45,
            4: 279.89,
            5: 1013.74,
            6: 23.03,
            7: 55.44,
            8: 125.43,
            9: 246.62,
            10: 496.57,}
    }

    m = SimulateBiddedPower(generator, price, sub_decl, type=1)
    res = m.run(to_json=False)
    print(res)
