# -*- coding: utf-8 -*-
"""
Author: Laney
Datetime: 2023/02/01/028 13:15
Info:
相似日法长周期（45天）价格预测
"""

import pandas as pd
from scipy.spatial import distance
import requests
from prophet import Prophet
from datetime import date, datetime, timedelta
import logging
import warnings


warnings.filterwarnings('ignore')
logger = logging.getLogger()


class SimPredPrice:
    def __init__(self, price, run_date=None):
        logger.info("----------------------相似日法_45天_通用价格预测开始--------------------------")
        self.price, self.msg = self._prepare_price(price)
        if not run_date:
            self.run_date = str(date.today())
        else:
            self.run_date = run_date
        self.start_date = datetime.strptime(self.run_date, '%Y-%m-%d') - timedelta(27)  # 最近28天的数据
        self.end_date = datetime.strptime(self.run_date, '%Y-%m-%d') + timedelta(45)    # 未来45天的预测数据

        hist_flag = True
        hist_start_date = str(self.start_date)
        hist_end_date = self.run_date + " 23:45:00"

        future_start_date = str(datetime.strptime(self.run_date, '%Y-%m-%d') + timedelta(1))
        future_end_date = str(self.end_date).split(" ")[0] + " 23:45:00"

        hist_ids = ['264', '265', '263']
        hist_items = ['/日前/统调负荷', '/日前/新能源负荷', '/日前/联络线计划/总加']
        hist_columns = ['统调负荷预测', '新能源负荷预测', '联络线计划']

        url = 'http://datasets.togeek.cn/datasets/api/indexes/'  # 数据接口
        self.hist_data, self.hist_flag, self.hist_msg = self._prepare_data(url, hist_start_date, hist_end_date,
                                                                           hist_ids, hist_items, hist_columns,
                                                                           hist_flag)
        self.hist_data.sort_values('date_time', inplace=True)
        self.future_data, self.future_flag, self.future_msg = self._prepare_future_xny(future_start_date,
                                                                                       future_end_date)

    def _data_verification(self, start_date, end_date, num_should, num_queried, num_null, type_, days, flag):
        """
        数据长度检查+空值检查
        :param num_should: 指定周期内，理论数据长度
        :param num_queried: 指定周期内，查到的实际数据长度
        :param num_null: 指定周期内，空值数量
        :param type_: 待校验的数据类型
        :return: 查询结果简明信息
        """
        msg = ""
        # 1. 数据长度检查
        s1 = f"{start_date}至{end_date}, 合计{days}天, {type_}应为{num_should}条, 实为{num_queried}条, 两者一致, "
        if num_queried != num_should:
            s1 = f"{start_date}至{end_date}, 合计{days}天, {type_}应为{num_should}条, 实为{num_queried}条, 两者不一致，请检查数据的完整性！"
            flag *= False
        msg += s1

        # 2. 空值检查
        s2 = f"无空缺值。\n"
        if num_null != 0:
            s2 = f"有{num_null}个空缺值, 请检查数据的完整性！\n"
            flag *= False
        msg += s2
        return msg, flag

    def _prepare_price(self, price):
        price = pd.DataFrame(price)
        price['date_time'] = price['date_time'].astype(str)
        price.sort_values('date_time', inplace=True)
        start_date = price['date_time'].values[0]
        end_date = price['date_time'].values[-1]
        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d') - datetime.strptime(start_date.split(" ")[0],
                                                                                          '%Y-%m-%d')).days + 1
        num_should = days * 96  # 负荷数据正确条数
        num_queried = price.shape[0]
        num_null = price.isnull().sum().sum()

        msg, flag = self._data_verification(start_date, end_date, num_should, num_queried, num_null, "传入模型的价格数据[price]",
                                            days, True)
        logger.info(msg)
        return price, msg

    def _prepare_data(self, url, start_date, end_date, ids, items, columns, flag):
        params = {
            'grid': 'SHANXI',
            'startTime': start_date,
            'endTime': end_date,
            'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
            'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
        }
        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d')
                - datetime.strptime(start_date.split(" ")[0], '%Y-%m-%d')).days + 1
        data = pd.DataFrame({'time': pd.date_range(start_date, end_date, freq='15min')})
        new_column = ['date_time']
        data['time'] = data['time'].astype(str)
        num_should = days * 96  # 负荷数据正确条数
        msg = ""
        for i, id_ in enumerate(ids):
            r = requests.get(url + id_, params=params)
            if r:
                try:
                    for name, dct in r.json()['data'].items():
                        tmp = pd.DataFrame(dct['points'])
                        num_queried = tmp.shape[0]
                        num_null = tmp.isnull().sum().sum()
                        msg_tmp, flag = self._data_verification(start_date, end_date, num_should, num_queried, num_null,
                                                                "96点" + columns[i] + f"数据[{name}]", days, flag)
                        data = pd.merge(data, tmp, on='time', how='left')
                        new_column.append(columns[i])
                        msg += msg_tmp
                        # break
                except Exception as e:
                    flag *= False
                    msg += f"查询数据时出错：{e}, 查询不到{start_date}至{end_date}的96点'{columns[i]}[{items[i]}]'数据！\n"
            else:
                msg += f"数据请求出错，无法访问数据接口:{url + id_}！\n"
        data.columns = new_column
        data['date'] = data['date_time'].map(lambda x: x.split(' ')[0])
        data['time'] = data['date_time'].map(lambda x: x.split(' ')[1])
        logger.info(msg)
        # print(msg)
        return data, flag, msg

    def _prepare_future_xny(self, start_date, end_date):
        msg = ""
        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d') - datetime.strptime(start_date.split(" ")[0],
                                                                                          '%Y-%m-%d')).days + 1
        num_should = days * 4  # 负荷数据正确条数
        flag = True
        res = pd.DataFrame(columns=['date_time', 'date', 'time', '小时', '新能源负荷预测'])
        dt_ = self.run_date.split(" ")[0].replace("-", "")

        try:
            r = requests.get('https://www.91weather.com/tuji/power_predict/shanxi/45d/%s.json' % dt_)
            res = pd.DataFrame(r.json())
            res.columns = ['date_time', 'wind', 'solar', '新能源负荷预测']
            res['date'] = res['date_time'].map(lambda x: x.split(" ")[0])
            res['time'] = res['date_time'].map(lambda x: x.split(' ')[1])
            num_queried = res.shape[0]
            num_null = res.isnull().sum().sum()
            msg_tmp, flag = self._data_verification(start_date, end_date, num_should, num_queried, num_null,
                                                    "45d的[大贤/新能源负荷预测]数据", days, flag)
            msg += msg_tmp
        except Exception as e:
            msg += f"查询数据时出错：{e}, 查询不到{start_date}至{end_date}45d的[大贤/新能源负荷预测]数据！\n"
            flag *= False
        logger.info(msg)
        # print(msg)
        return res[['date_time', 'date', 'time', '新能源负荷预测']], flag, msg

    def _pred_load(self, days=45):
        # 如果历史数据的最后一天不是run_date，需要将统调负荷数据的预测日期补齐
        last_date = self.hist_data['date_time'].tolist()[-1]
        n = (datetime.strptime(self.run_date, '%Y-%m-%d') - datetime.strptime(last_date.split(" ")[0], '%Y-%m-%d')).days
        data = self.hist_data[['date_time', '统调负荷预测']]
        data.columns = ['ds', 'y']
        data['ds'] = pd.to_datetime(data['ds'])
        params = {"seasonality_mode": 'multiplicative'}
        ts_model = Prophet(**params)
        ts_model.fit(data)
        future = ts_model.make_future_dataframe(periods=(days + n) * 96, include_history=False, freq='15min')
        pred = ts_model.predict(future)[['ds', 'yhat']]
        pred.columns = ['date_time', '统调负荷预测']
        return pred

    @staticmethod
    def _get_four_points_data(data):
        # 将历史竞价空间数据处理成1天有4个点，每个点的值为过去6个小时的均值
        data['date_time2'] = data['date_time'].shift(8)
        data.dropna(inplace=True)

        data['date_time2'] = pd.to_datetime(data['date_time2'])
        data.set_index('date_time2', inplace=True, drop=True)

        new = data.resample('6H', label='right').mean().reset_index()
        new['date_time'] = pd.to_datetime(new['date_time2'])
        new['date_time'] = new['date_time'].map(lambda x: x + timedelta(hours=2))
        del new['date_time2']
        # new = new[['date_time', '竞价空间']]

        new['date'] = new['date_time'].astype(str).map(lambda s: s.split(' ')[0]).values
        new['time'] = new['date_time'].astype(str).map(lambda s: s.split(' ')[1]).values
        return new

    @staticmethod
    def select_similar_date(hist_load, curdata):
        hist_ = hist_load.pivot(index='time', columns='date', values='竞价空间')

        # 删除空值
        hist_.dropna(axis=1, inplace=True)
        hist_date_lst = hist_.columns.to_list()
        tmp = []
        v = curdata['竞价空间'].values
        for i, date_ in enumerate(hist_date_lst[:-1]):
            dis_lst = {'date': date_}
            u = hist_.loc[:, date_].values
            if len(v) == 3:
                u = u[1:]
            # 曼哈顿距离
            dis_lst['cityblock'] = distance.cityblock(u, v)
            tmp.append(dis_lst)
        res_ = pd.DataFrame(tmp)
        res_ = res_.sort_values('cityblock')
        similar_date = res_['date'].values[0]
        return similar_date

    def pred_price(self, to_json=False):
        # 校验数据条件
        # self.hist_flag = 1
        # self.future_flag = 1
        if self.hist_flag * self.future_flag == 0:
            result = pd.DataFrame(columns=['date_time', 'pred_price'])

        else:
            # 准备历史数据
            # 计算历史日期的竞价空间数据
            self.hist_data['竞价空间'] = self.hist_data['统调负荷预测'] - self.hist_data['新能源负荷预测'] - self.hist_data['联络线计划']

            # 合并历史负荷数据及价格数据
            self.hist_data = pd.merge(self.hist_data, self.price, on='date_time').reset_index()
            hist_data = self.hist_data[self.hist_data['date'] <= self.run_date]
            hist_data1 = self._get_four_points_data(hist_data)

            # 准备预测日的竞价空间数据
            future_load = self._pred_load(days=45)
            future_load1 = self._get_four_points_data(future_load)
            self.future_data['date_time'] = pd.to_datetime(self.future_data['date_time'])
            future_data = pd.merge(self.future_data, future_load1[['date_time', '统调负荷预测']], on='date_time')
            future_deliver = hist_data1[['time', '联络线计划']].groupby('time').mean().reset_index()
            future_data = pd.merge(future_data, future_deliver, on='time')
            future_data['竞价空间'] = future_data['统调负荷预测'] - future_data['新能源负荷预测'] - future_data['联络线计划']

            # 开始预测
            result = pd.DataFrame(columns=['date_time', 'pred_price'])
            pred_dates = future_data['date'].unique()
            for pdate in pred_dates:
                tmp = pd.DataFrame()
                tmp['date_time'] = pd.date_range(pdate, periods=96, freq='15min')
                curdata = future_data[future_data['date'] == pdate]
                curdata = curdata.dropna()
                similar_date = self.select_similar_date(hist_data1, curdata)
                tmp['pred_price'] = self.hist_data[self.hist_data['date'] == similar_date]['price'].values
                result = pd.concat([result, tmp], axis=0)
            result['date_time'] = result['date_time'].astype(str)

        if to_json:
            result = result.to_dict("list")
            result['message'] = self.msg + self.hist_msg + self.future_msg
        logger.info("----------------------相似日法_45天_通用价格预测完成--------------------------")
        return result


if __name__ == '__main__':
    price = pd.read_excel(r"C:\Users\<USER>\Desktop\rq_price.xlsx")
    bsf = SimPredPrice(price, run_date='2023-10-12')
    data = bsf.pred_price(to_json=True)
    print(data)

