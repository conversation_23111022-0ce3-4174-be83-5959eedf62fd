# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-05-19 17:42:03
Description :   山东D+2、D+3日前电价预测，用于国华山东项目
"""


from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shandong.ahead_price_D2D3 import AheadPriceD2D3, AheadPriceD2D10


class AheadPriceD2D3HandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get("data")                       # 历史实际日前价格, dict
        date_bidding = params.get("date_bidding")       # 竞价日, str, 如："2023-06-13"
        date_run = params.get("date_run")               # 运行日, str, 如："2023-06-16"
        time_point = params.get("time_point", 24)       # 每天的时刻点数, 默认：24
        min_price = params.get("min_price", -80)        # 价格下限, 默认：-80
        max_price = params.get("max_price", 1300)       # 价格上限, 默认：1300
        num_lag = params.get("num_lag", 21)             # lag特征数量，默认：21, 可实现D+1~D+9的预测
        days_train = params.get("days_train", 1)        # 训练用历史数据天数

        model = AheadPriceD2D3(
            data=data,
            date_bidding=date_bidding,
            date_run=date_run,
            time_point=time_point,
            min_price=min_price,
            max_price=max_price,
            num_lag=num_lag,
            days_train=days_train,
        )
        result = model.predict()
        self.write(result)


class AheadPriceD2D9HandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        date_bidding = params.get("date_bidding")       # 竞价日, str, 如："2023-09-21"

        model = AheadPriceD2D10(date_bidding=date_bidding)
        result = model.predict()
        self.write(result)
