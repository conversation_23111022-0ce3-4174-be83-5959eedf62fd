# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_price_xgb import PricePredXGB


class PricePredXGBHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        all_data = params.pop('all_data')
        date_list_pred = params.pop('date_list_pred')
        days_train = params.pop('days_train', 30)
        min_value = params.pop('min_value', 0)
        max_value = params.pop('max_value', 1500)

        model = PricePredXGB(all_data=all_data,
                             date_list_pred=date_list_pred,
                             days_train=days_train,
                             min_value=min_value,
                             max_value=max_value)
        result_json = model.predict()
        self.write(result_json)
