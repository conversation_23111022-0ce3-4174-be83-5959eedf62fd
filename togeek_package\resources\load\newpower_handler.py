# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.nationwide.load_new_power_mx import NewPower


class NewPowerLoadHander(RequestHandlerBase):
    def put(self):
        weight_dic = {"China - 101巴-西乌素风电#03风机": 0.09,
                    "China - 102巴-嘉合风电#08风机": 0.09,
                    "China - 103巴-乌日德风电#129风机": 0.09,
                     "China - 213包-巴音风电#112风机": 0.09,
                     "China - 216包-红泥井风电#16风机": 0.09,
                     "China - 214包-双水泉风电#131风机": 0.09,
                     "China - 327呼-旭日汗光伏#28逆变器": 0.1,
                     "China - 324呼-夏日风电#14风机": 0.06,
                     "China - 430辉-宏盘风电#15风机": 0.06,
                     "China - 434辉-辉腾锡勒风电#58风机": 0.06,
                     "China - 753白-长春风电#9风机": 0.06,
                     "China - 436辉-库伦风电#62风机": 0.06,
                     "China - 545玫-玫瑰营风电#98风机": 0.06,
                     "China - 756白-嘉耀风电#2风机": 0.05,
                     "China - 757白-乌宁巴图风场#2风机": 0.05}
        params = j2o(self.request.body.decode())
        speed_data = params.get('speed_data')
        power_feng = params.get('power_feng')
        power_guang = params.get('power_guang')
        yuce_list = params.get("yuce_list")
        weight_dic = params.get('weight_dic', weight_dic)
        pred = NewPower(speed_data=speed_data, power_feng=power_feng, power_guang=power_guang, yuce_list=yuce_list, weight_dic=weight_dic)
        self.write(pred.result)