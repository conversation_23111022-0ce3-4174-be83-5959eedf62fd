# -*- coding: utf-8 -*-
"""
Author: Laney
Datetime: 2023/02/01/028 13:15
Info:
随机森林法中长期（3天）价格预测
"""

import pandas as pd
import numpy as np
import requests
from prophet import Prophet
from sklearn.ensemble import ExtraTreesRegressor
from datetime import date, datetime, timedelta
import logging
import warnings


warnings.filterwarnings('ignore')
logger = logging.getLogger()


class CommonEtrPredictPrice3:
    def __init__(self, price, run_date=None):
        logger.info("----------------------随机森林_3天_通用价格预测开始--------------------------")
        self.price, self.msg = self._prepare_price(price)
        if not run_date:
            self.run_date = str(date.today())
        else:
            self.run_date = run_date
        self.start_date = datetime.strptime(self.run_date, '%Y-%m-%d') - timedelta(29)  # 最近30天的数据
        self.end_date = datetime.strptime(self.run_date, '%Y-%m-%d') + timedelta(3)     # 未来3天的预测数据

        hist_flag = True
        hist_start_date = str(self.start_date)
        hist_end_date = self.run_date + " 23:45:00"

        future_start_date = str(datetime.strptime(self.run_date, '%Y-%m-%d') + timedelta(1))
        future_end_date = str(self.end_date).split(" ")[0] + " 23:45:00"

        self.params = {
            'grid': 'SHANXI',
            'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
            'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
        }
        self.ids = ['264', '265', '263', '121']
        self.items = ['/日前/统调负荷', '/日前/新能源负荷', '/日前/联络线计划/总加', '/日前/检修总容量']
        self.columns = ['统调负荷预测', '新能源负荷预测', '联络线计划', '检修总容量']
        self.url = 'http://datasets.togeek.cn/datasets/api/indexes/'  # 数据接口

        self.hist_data, self.hist_flag, self.hist_msg = self._prepare_data(self.url, hist_start_date, hist_end_date,
                                                                           self.ids, self.items, self.columns,
                                                                           hist_flag)
        self.future_data, self.future_flag, self.future_msg = self._prepare_future_data(future_start_date,
                                                                                        future_end_date, days=3)

    @staticmethod
    def _data_verification(start_date, end_date, num_should, num_queried, num_null, type_, days, flag):
        """
        数据长度检查+空值检查
        :param num_should: 指定周期内，理论数据长度
        :param num_queried: 指定周期内，查到的实际数据长度
        :param num_null: 指定周期内，空值数量
        :param type_: 待校验的数据类型
        :return: 查询结果简明信息
        """

        msg = ""
        # 1. 数据长度检查
        s1 = f"{start_date}至{end_date}, 合计{days}天, {type_}应为{num_should}条, 实为{num_queried}条, 两者一致, "
        if num_queried != num_should:
            s1 = f"{start_date}至{end_date}, 合计{days}天, {type_}应为{num_should}条, 实为{num_queried}条, 两者不一致，请检查数据的完整性！"
            flag *= False
        msg += s1

        # 2. 空值检查
        s2 = f"无空缺值。\n"
        if num_null != 0:
            s2 = f"有{num_null}个空缺值, 请检查数据的完整性！\n"
            flag *= False
        msg += s2
        return msg, flag

    def _prepare_data(self, url, start_date, end_date, ids, items, columns, flag):
        params = {
            'grid': 'SHANXI',
            'startTime': start_date,
            'endTime': end_date,
            'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
            'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
        }
        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d')
                - datetime.strptime(start_date.split(" ")[0], '%Y-%m-%d')).days + 1
        data = pd.DataFrame({'time': pd.date_range(start_date, end_date, freq='15min')})
        new_column = ['date_time']
        data['time'] = data['time'].astype(str)
        num_should = days * 96  # 负荷数据正确条数
        msg = ""
        for i, id_ in enumerate(ids):
            r = requests.get(url + id_, params=params)
            if r:
                try:
                    for name, dct in r.json()['data'].items():
                        tmp = pd.DataFrame(dct['points'])
                        num_queried = tmp.shape[0]
                        num_null = tmp.isnull().sum().sum()
                        msg_tmp, flag = self._data_verification(start_date, end_date, num_should, num_queried, num_null,
                                                                "96点" + columns[i] + f"数据[{name}]", days, flag)
                        data = pd.merge(data, tmp, on='time', how='left')
                        new_column.append(columns[i])
                        msg += msg_tmp
                        # break
                except Exception as e:
                    flag *= False
                    msg += f"查询数据时出错：{e}, 查询不到{start_date}至{end_date}的96点'{columns[i]}[{items[i]}]'数据！\n"
            else:
                msg += f"数据请求出错，无法访问数据接口:{url + id_}！\n"
        data.columns = new_column
        data['date'] = data['date_time'].map(lambda x: x.split(' ')[0])
        data['time'] = data['date_time'].map(lambda x: x.split(' ')[1])
        data['小时'] = data['time'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        # logger.info(msg)
        print(msg)
        return data, flag, msg

    def _prepare_price(self, price):
        price = pd.DataFrame(price)
        price['date_time'] = price['date_time'].astype(str)
        price.sort_values('date_time', inplace=True)
        start_date = price['date_time'].values[0]
        end_date = price['date_time'].values[-1]
        days = (datetime.strptime(end_date.split(" ")[0], '%Y-%m-%d') - datetime.strptime(start_date.split(" ")[0],
                                                                                          '%Y-%m-%d')).days + 1
        num_should = days * 96  # 负荷数据正确条数
        num_queried = price.shape[0]
        num_null = price.isnull().sum().sum()

        msg, flag = self._data_verification(start_date, end_date, num_should, num_queried, num_null, "传入模型的价格数据[price]",
                                            days, True)
        # logger.info(msg)
        print(msg)
        return price, msg

    def _pred_load(self, days=3):
        data = self.hist_data[['date_time', '统调负荷预测']]
        data.columns = ['ds', 'y']
        data['ds'] = pd.to_datetime(data['ds'])
        params = {"seasonality_mode": 'multiplicative'}
        ts_model = Prophet(**params)
        ts_model.fit(data)
        future = ts_model.make_future_dataframe(periods=days * 96, include_history=False, freq='15min')
        pred = ts_model.predict(future)[['ds', 'yhat']]
        pred.columns = ['date_time', '统调负荷预测']
        return pred

    def _prepare_future_xny(self):
        flag = True
        res = pd.DataFrame(columns=['date_time', 'date', 'time', '小时', '新能源负荷预测'])
        dt_ = self.run_date.replace("-", "")
        url = 'https://www.91weather.com/tuji/power_predict/shanxi/15d/%s.json' % dt_
        msg = ""
        # 获取未来3天的新能源数据
        try:
            r = requests.get(url)
            res = pd.DataFrame(r.json())
            res.columns = ['date_time', 'guangfu', 'fengli', '新能源负荷预测']
            res.sort_values('date_time', inplace=True)
            res['date_time'] = pd.date_range(start=res['date_time'].values[0][:10], freq='15T', periods=res.shape[0])
            res['date_time'] = res['date_time'].astype(str)
            res['date'] = res['date_time'].map(lambda x: x.split(" ")[0])
            res['time'] = res['date_time'].map(lambda x: x.split(' ')[1])
            res['小时'] = res['time'].map(lambda x: x.split(':')[0])
            res['小时'] = res['小时'].astype(float)
            num_null = res.isnull().sum().sum()
            msg += f"从datasets接口共获取到{res.shape[0]}条数据，"
            if num_null != 0:
                msg += f"有{num_null}个空缺值, 请检查数据的完整性！\n"
            else:
                msg += f"无空缺值。\n"
        except Exception as e:
            msg += f"查询数据时出错：{e}, 查询不到{self.run_date}日预测的15d_96点[大贤/新能源负荷预测]数据, url为：{url}！\n"
            flag *= False
        # logger.info(msg)
        print(msg)
        return res[['date_time', 'date', 'time', '小时', '新能源负荷预测']], flag, msg

    def _prepare_future_data(self, start_date, end_date, days=3):
        idx = pd.date_range(start_date, periods=96 * days, freq="15min")
        result = pd.DataFrame({'date_time': idx})
        result['date'] = result['date_time'].astype(str).map(lambda x: x.split(' ')[0])
        result['time'] = result['date_time'].astype(str).map(lambda x: x.split(' ')[1])
        result['小时'] = result['time'].map(lambda x: x.split(':')[0])
        result['小时'] = result['小时'].astype(float)
        msg = ""
        flag = True
        for i in range(len(self.ids)):
            ids = self.ids[i]
            items = self.items[i]
            columns = self.columns[i]
            tmp_data, tmp_flag, tmp_msg = self._prepare_data(self.url, start_date, end_date, [ids], [items], [columns],
                                                             True)
            msg += tmp_msg
            # tmp_flag = False  # 测试专用
            if (not tmp_flag) & (ids == '264'):
                # 时间序列计算未来3天的统调负荷数据
                tmp_data = self._pred_load(days)
                msg += "【统调负荷预测】数据暂未披露，采用时间序列模型预测未来3天的统调负荷数据，数据获取成功；\n"
            if (not tmp_flag) & (ids == '265'):
                # 从datasets接口获取大贤\新能源数据
                tmp_data, tmp_flag, tmp_msg = self._prepare_future_xny()
                if tmp_flag:
                    msg += "【日前\新能源负荷】数据暂未披露，从接口获取未来3天的新能源负荷预测数据，数据获取成功；\n"
                else:
                    flag = False
                    msg += "【日前\新能源负荷】数据暂未披露，从接口获取未来3天的新能源负荷预测数据，数据获取失败！\n"
            if (not tmp_flag) & (ids in ['263', '121']):  # 获取联络线计划、检修机组容量数据
                data_ = self.hist_data[['time', columns]][-96 * days:].groupby('time').mean()[columns].tolist() * days
                tmp_data = pd.DataFrame({'date_time': idx, columns: data_})
                msg += f"【{columns}】数据暂未披露，采用历史{days}天的均值进行预测；\n"

            tmp_data['date_time'] = pd.to_datetime(tmp_data['date_time'])
            result = pd.merge(result, tmp_data[['date_time', columns]], on='date_time')
            # logger.info(msg)
            print(msg)
        # 删除缺失值
        result = result.dropna(axis=0, subset=['统调负荷预测', '新能源负荷预测', '联络线计划', '检修总容量'])
        # 计算竞价空间
        result['竞价空间'] = result['统调负荷预测'] - result['新能源负荷预测'] - result['联络线计划']

        return result, flag, msg

    def pred_price(self, n_est=100, max_depth=8, to_json=False):
        # 校验数据条件
        # self.hist_flag = 1
        # self.future_flag = 1
        if self.hist_flag * self.future_flag == 0:
            result = pd.DataFrame(columns=['date_time', 'pred_price'])
            logger.info(self.msg + self.hist_msg + self.future_msg)
        else:
            # 合并历史负荷数据及价格数据
            self.hist_data = pd.merge(self.hist_data, self.price, on='date_time').reset_index()
            self.hist_data['竞价空间'] = self.hist_data['统调负荷预测'] - self.hist_data['新能源负荷预测'] - self.hist_data['联络线计划']

            # 准备预测日的竞价空间数据
            self.future_data.sort_values('date_time', inplace=True)
            self.future_data.set_index('date_time', inplace=True)
            self.hist_data.sort_values('date_time', inplace=True)
            self.hist_data.set_index('date_time', inplace=True)

            # 开始预测
            result = pd.DataFrame()
            pred_dates = self.future_data['date'].unique()

            # 准备训练数据,划分测试集
            features = ['统调负荷预测', '新能源负荷预测', '联络线计划', '检修总容量', '小时', '竞价空间']
            train = self.hist_data.dropna()
            X_train = train[features]
            y_train = train[['price']]

            if X_train.shape[0] == 0:
                raise ValueError('删除null值后，训练数据为空，请检查传入数据')

            # 模型训练
            etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)
            etr.fit(X=X_train, y=y_train)

            for pdate in pred_dates:
                X_pred = self.future_data.loc[self.future_data['date'] == pdate, features]

                # 判断预测日训练数据是否含NaN值
                if not X_pred.isnull().sum().sum() == 0:
                    raise ValueError('预测日数据含NaN值，请检查!')
                if X_pred.shape[0] == 0:
                    raise ValueError('预测日无数据，请检查!')
                res = etr.predict(X=X_pred)

                # 预测结果处理
                tmp = pd.DataFrame(index=X_pred.index)
                tmp['pred_price'] = res

                result = pd.concat([result, tmp], axis=0)

                # 对预测值进行最大最小值修正
                result['pred_price'] = result['pred_price'].map(lambda s: 0 if s < 0 else 1500 if s > 1500 else s)

                train_zero = train[train['price'] == 0]
                train_zero_01 = train_zero[train_zero['小时'] <= 7]
                train_zero_02 = train_zero[train_zero['小时'] > 7]

                threshold_value_01 = train_zero_01['竞价空间'].quantile(0.75)
                threshold_value_02 = train_zero_02['竞价空间'].quantile(0.75)

                result[['小时', '竞价空间']] = X_pred[['小时', '竞价空间']]

                if threshold_value_01 != np.NaN:
                    result.loc[((result['小时'] <= 7) & (result['竞价空间'] <= threshold_value_01)), 'pred_price'] = 0
                if threshold_value_02 != np.NaN:
                    result.loc[((result['小时'] > 7) & (result['竞价空间'] <= threshold_value_02)), 'pred_price'] = 0

            result.reset_index(inplace=True)
            result['date_time'] = result['date_time'].astype(str)
            result = result[['date_time', 'pred_price']]
            logger.info(self.msg + self.hist_msg + self.future_msg)
        if to_json:
            result = result.to_dict('list')
            result["message"] = self.msg + self.hist_msg + self.future_msg
        logger.info("----------------------随机森林_3天_通用价格预测结束--------------------------")
        return result


if __name__ == '__main__':
    price0 = pd.read_excel(r"C:\Users\<USER>\Desktop\rq_price.xlsx")
    price1 = {'date_time': price0.date_time,
              'price': price0.price}

    bsf = CommonEtrPredictPrice3(price1, run_date='2023-10-12')
    data = bsf.pred_price(to_json=False)
    print(data)

