#!/usr/bin/env python
# coding: utf-8

import pandas as pd
from json import loads
from datetime import timedelta
import logging
logger = logging.getLogger()


class Ri:
    def __init__(self, rgd, xianjia):
        self.rgd = rgd
        self.xianjia = self.prepare_xianjia(xianjia=xianjia)

    def prepare_xianjia(self, xianjia):
        """
        当前方法，仅从限价数据中获取：待预测日期
        """
        if isinstance(xianjia, str):
            xianjia = loads(xianjia)
        if isinstance(xianjia, dict):
            xianjia = pd.DataFrame(xianjia).drop_duplicates()
        type_dict = {'year': int, 'month': int, 'time_point': int, 'refer': float, 'lower': float, 'upper': float}
        xianjia = xianjia.astype(type_dict)
        # 限价数据排序
        xianjia = xianjia.sort_values(['year', 'month', 'ri', 'time_point']).reset_index(drop=True)
        return xianjia

    def prepare_train(self, data):
        if isinstance(data, str):
            data = loads(data)
        if isinstance(data, dict):
            data = pd.DataFrame(data).sort_values('date')
        data = data.dropna()  # 删除空值日期
        data['date'] = pd.to_datetime(data['date'])
        data.columns = ['date_time', 'rgd_jiage']
        data['date'] = data['date_time'].map(lambda s: str(s).split(' ')[0])
        data['time'] = data['date_time'].map(lambda s: str(s).split(' ')[1])
        df = data.pivot(index='date', columns='time', values='rgd_jiage')
        return df

    def predict_ri(self, to_json=True):
        xianjia = self.xianjia
        logger.info("-------------中长期日滚动价格预测开始-------------------")
        logger.info(self.rgd)
        logger.info(self.xianjia)
        df = self.prepare_train(self.rgd)
        yuce_date_end = str(xianjia['year'].values[-1]) + '-' + str(xianjia['month'].values[-1]) + '-' + str(xianjia['ri'].values[-1])
        yuce_date_start = str(pd.to_datetime(yuce_date_end) - timedelta(days=5)).split(' ')[0]
        yuce_list = []
        for i in range(1, 6):
            start = str(pd.to_datetime(yuce_date_start) + timedelta(days=i)).split(' ')[0]      # 预测日期，如'2021-08-25'
            pred = pd.DataFrame(pd.date_range(start=start, periods=24, freq='H'), columns=['date'])
            yuce = df[-i:].mean().tolist()  # 预测列表
            pred['rgd_jiage'] = yuce
            yuce_list.append(pred)
        pred_rgd = pd.concat(yuce_list).reset_index(drop=True)

        if to_json:
            result = {'date': pred_rgd['date'].astype('str').tolist(),
                      'rgd_jiage': pred_rgd['rgd_jiage'].tolist()}
        else:
            result = pred_rgd[['date', 'rgd_jiage']]
        logger.info("-------------中长期日滚动价格预测结束-------------------")
        return result


if __name__ == '__main__':
    import warnings
    warnings.filterwarnings('ignore')
    from sklearn.metrics import r2_score
    rgd = pd.read_csv(r"C:\Users\<USER>\Desktop\zcqfsdjg\ri\rgd_train.csv")
    xianjia = pd.read_csv(r"C:\Users\<USER>\Desktop\zcqfsdjg\ri\xianjia_ri.csv")
    rgd_test = pd.read_csv(r"C:\Users\<USER>\Desktop\zcqfsdjg\ri\rgd_test.csv")
    p = Ri(rgd=rgd, xianjia=xianjia)
    pred = p.predict_ri(to_json=False)
    acc_rgd = 1 - abs((rgd_test['rgd_jiage'] - pred['rgd_jiage'])/rgd_test['rgd_jiage']).mean()
    r2_rgd = r2_score(rgd_test['rgd_jiage'], pred['rgd_jiage'])
    print(f'acc_rgd: {acc_rgd}\nr2_rgd: {r2_rgd}')
    print(pd.DataFrame(pred))



