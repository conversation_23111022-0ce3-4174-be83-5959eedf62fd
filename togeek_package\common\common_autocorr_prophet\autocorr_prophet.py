# -*- coding: utf-8 -*
# valid license

import pandas as pd
from prophet import Prophet
from json import loads
import logging
import datetime

logger = logging.getLogger()


class Prediction:
    def __init__(self, data, holiday, special_sign=0, special_days=[], pre_days=1, point_day=96, cap=None, floor=None, include_history=False):
        """
        :param data: dict, 历史数据
        :param holiday: dict, 假期数据
        :param pre_days: int, 预测天数，默认为: 1
        :param point_day: int, 每天的样本数，可选[24, 48, 96]，默认为: 96
        :param cap: float or list, 预测值的上限， 默认为: None
        :param floor: float or list, 预测值的下限, 默认为: None
        :param include_history: bool, 是否预测历史日期，默认为: False
        """
        # 写入log
        logger.info("---------------------------------------------------------")
        logger.info("------------------自相关时间序列预测-------------------------")
        logger.info(
            f"data:{data}, holiday:{holiday}, pre_days:{pre_days}, point_day={point_day}, cap={cap}, floor={floor}, "
            f"include_history={include_history}, special_sign:{special_sign}, special_days:{special_days}")

        # 季节模式设为乘法模型
        self.params = {"seasonality_mode": 'multiplicative'}
        self.data = self._prepare_load(data, cap, floor, self.params)
        self._prepare_holiday(holiday, self.params)
        self.pre_days = pre_days
        self.point_day = point_day
        self.pre_length = pre_days * point_day
        self.inc_his = include_history
        self.point_day = point_day
        self.special_sign = special_sign
        self.special_days = special_days
        self.freq = {24: 'H', 48: '30min', 96: '15min'}[point_day]
        self.cap = cap
        self.floor = floor

    @staticmethod
    def _prepare_load(data, cap, floor, params):
        if isinstance(data, str):
            data = loads(data)
        if isinstance(data, dict):
            data['ds'] = pd.to_datetime(data['ds'])
            data = pd.DataFrame(data)
        data['ds'] = pd.to_datetime(data['ds'])
        data['week'] = data['ds'].dt.weekday.map(lambda s: int(s) + 1)
        # if cap is not None:
        #     data['cap'] = cap
        # if floor is not None:
        #     data['floor'] = floor
        # if 'cap' in data:
        #     params['growth'] = 'logistic'
        # if 'floor' in data:
        #     params['growth'] = 'logistic'
        # data.to_excel('data.xlsx')
        return data

    @staticmethod
    def _prepare_holiday(holiday, params):
        if holiday is None:
            return
        if isinstance(holiday, str):
            holiday = loads(holiday)
        if isinstance(holiday, dict):
            holiday["ds"] = pd.to_datetime(holiday["ds"])
            holiday = pd.DataFrame(holiday)
        params["holidays"] = holiday

    def get_datelist(self, start_date, end_date):
        date_list = []
        # start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
        # end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
        date_list.append(start_date)
        while start_date < end_date:
            start_date += datetime.timedelta(days=1)
            date_list.append(start_date)
        return date_list

    def get_special(self, date_list):
        '''
        找到在特殊列表里的，和不在特殊列表里的
        Parameters
        ----------
        date_list

        Returns
        -------

        '''
        date_pre = pd.DataFrame(columns=['date', 'week'])
        date_pre['date'] = date_list
        date_pre['date'] = pd.to_datetime(date_pre['date'])
        date_pre['week'] = date_pre['date'].map(lambda x: x.weekday() + 1)
        speical_day = date_pre[date_pre['week'].isin(self.special_days)]['date'].tolist()
        unspeical_day = date_pre[~(date_pre['week'].isin(self.special_days))]['date'].tolist()
        return speical_day, unspeical_day

    def train_model(self, train_data, pre_length, user):
        '''
        不同历史数据下训练模型，预测结果
        Parameters
        ----------
        train_data ： 历史数据
        pre_length ： 预测长度
        user ： 用户名
        res_list ： 预测结果

        Returns
        -------

        '''
        # params中包含['seasonality_mode', 'growth', 'holidays']
        model = Prophet(**self.params)
        model.fit(train_data)
        future = model.make_future_dataframe(periods=pre_length, include_history=self.inc_his, freq=self.freq)
        pred = model.predict(future)[['ds', 'yhat']]
        pred['users'] = user
        # 上下限限制
        if (self.cap is not None) and (self.floor is not None):
            pred["yhat"] = pred["yhat"].map(lambda s: self.floor if s < self.floor else s)
            pred["yhat"] = pred["yhat"].map(lambda s: self.cap if s > self.cap else s)
        # # 若预测值小于0，则设置为0
        # pred.loc[pred[pred["yhat"] < self.floor].index, "yhat"] = self.floor

        return pred

    def predict(self, to_json=True):
        """
        :param to_json: 为了测试而设置的参数，默认为True，输出为dict
        """
        users = self.data['users'].unique().tolist()
        res_list = []
        for user in users:
            # print(f'-----start {user}-----')
            # 获取用户数据
            tmp = self.data[self.data['users'] == user].reset_index(drop=True)
            # 按照时间，升序排列
            tmp = tmp.sort_values('ds')
            # 删除辅助列['users']
            del tmp['users']
            # 是否特殊模式
            if self.special_sign == 1:
                if self.special_days:
                    max_date = pd.to_datetime(tmp.dropna()['ds'].max())
                    start_date = max_date + datetime.timedelta(days=1)
                    end_date = max_date + datetime.timedelta(days=self.pre_days)
                    pre_list = self.get_datelist(start_date, end_date)
                    speical_day, unspeical_day = self.get_special(pre_list)
                    # 筛选历史数据，分别做训练
                    speical_history = tmp[tmp['week'].isin(self.special_days)]
                    unspeical_history = tmp[~(tmp['week'].isin(self.special_days))]
                    del speical_history['week']
                    del unspeical_history['week']
                    if speical_day:
                        speical_result = self.train_model(speical_history, len(speical_day) * self.point_day, user)
                        res_list.append(speical_result)
                    elif unspeical_day:
                        unspeical_result = self.train_model(unspeical_history, len(unspeical_day) * self.point_day, user)
                        res_list.append(unspeical_result)
            else:
                del tmp['week']
                pred = self.train_model(tmp, self.pre_length, user)
                res_list.append(pred)

        result = pd.concat(res_list)

        if to_json:
            result = {'users': result.users.tolist(),
                      'ds': result.ds.astype(str).tolist(),
                      'pred': result.yhat.tolist()}
        # 将结果写入log
        logger.info("---------------------------预测完成--------------------------")
        logger.info(f'result: {result}')
        return result


if __name__ == "__main__":
    data = pd.read_csv(r"D:\02file\0000文档\03模型类资料\模型API测试\data\common\data_train.csv")
    holiday = pd.read_csv(r"D:\02file\0000文档\03模型类资料\模型API测试\data\common\holiday.csv")
    p = Prediction(data=data, holiday=holiday, pre_days=5, point_day=96, include_history=False)
    # p = Prediction(data=data, holiday=holiday, pre_days=2, point_day=96, include_history=False)
    pre = p.predict(False)
    print(pre)
    pd.DataFrame(pre).to_excel(r"D:\3_images\prophet1.xlsx")
