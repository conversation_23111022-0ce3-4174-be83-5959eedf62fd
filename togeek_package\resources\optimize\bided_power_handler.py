# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_bided_power import BidedPower
from togeek_package.optimize.shandong import SimulateBiddedPower


class BidedPowerHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        subsection_declaration = params.pop('subsection_declaration')
        model = BidedPower(generators=generators, prices=prices, subsection_declaration=subsection_declaration)
        result = model.predict()
        self.write(result)


class BiddedPowerHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generator = params.pop('generators')
        price = params.pop('prices')
        sub_decl = params.pop('subsection_declaration')
        type = params.pop('type', 1)
        decimal = params.pop('decimal', 3)
        model = SimulateBiddedPower(generator=generator, price=price, sub_decl=sub_decl, type=type, decimal=decimal)
        result = model.run()
        self.write(result)
