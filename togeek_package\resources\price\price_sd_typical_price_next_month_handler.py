# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-05-19 17:42:03
Description :   山东次月月典型电价预测，用于国华山东项目
"""


from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
# from togeek_package.price.shandong.standard_declaration_fitting import StandardDeclarationFitting
from togeek_package.price.shandong.typical_price_next_month_sd import TypicalPriceNextMonth


class TypicalPriceNextMonthHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get("data")                       # 历史实际价格, dict
        year_pred = params.get("year_pred")             # 待预测年份, str, "2023"
        month_pred = params.get("month_pred")           # 待预测月份, str, "05"
        time_points = params.get("time_points", 96)     # 每天的时刻点数, 默认96
        weight_m_1 = params.get("weight_m_1", 0.8)      # 预测月前1月的权重, 经调参，0.8最佳
        min_price = params.get("min_price", -80)        # 价格下限, 默认-80
        max_price = params.get("max_price", 1300)       # 价格上限, 默认1300

        model = TypicalPriceNextMonth(data=data,
                                      year_pred=year_pred,
                                      month_pred=month_pred,
                                      time_points=time_points,
                                      weight_m_1=weight_m_1,
                                      min_price=min_price,
                                      max_price=max_price
                                      )
        result = model.pred_monthly_price()
        self.write(result)
