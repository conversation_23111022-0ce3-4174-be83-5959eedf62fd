# -*- coding: utf-8 -*-

from Cython.Build import cythonize
from setuptools import setup, Extension
from embed_vl_code import embed

code = 'togeek_prediction'

ext = cythonize([
    Extension('sim_el_price_prediction', [embed('sim_el_price_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('sim_el_price_prediction_value', [embed('sx_etr_price_pred_5d.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('customer_load_pre', [embed('customer_load_pre.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('bidding_space_ele', [embed('bidding_space_ele.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('model_config', [embed('model_config.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('customer_load_pre_cluster', [embed('customer_load_pre_cluster.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('customer_load_pre_faster', [embed('customer_load_pre_faster.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('ci_prediction', [embed('ci_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('declaration_pre', [embed('sx_declaration_fitting_5d.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('fsi_prediction', [embed('fsi_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('funcs', [embed('funcs.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('node_price_prediction', [embed('node_price_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('optimal_bid', [embed('optimal_bid.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('plan_evaluation', [embed('plan_evaluation.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('sd_prediction', [embed('sd_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('sm_prediction', [embed('sm_prediction.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('segmentation', [embed('segmentation.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('zcqfsdjg_yue', [embed('zcqfsdjg_yue.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('zcqfsdjg_xun', [embed('zcqfsdjg_xun.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('zcqfsdjg_ri', [embed('zcqfsdjg_ri.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('autocorr_prophet', [embed('autocorr_prophet.py', vl_type='VLB', code=code, path='../..')]),
    Extension('operation_capacity', [embed('operation_capacity.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration', [embed('subsection_declaration.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_profit', [embed('subsection_declaration_profit.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('bided_power', [embed('bided_power.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('load_declaration', [embed('load_declaration.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('boot_capacity', [embed('boot_capacity.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('generator_coal', [embed('generator_coal.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('optimize_xny_lambda_avg', [embed('optimize_xny_lambda_avg.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('crossover', [embed('crossover.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('mutation', [embed('mutation.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('ranking', [embed('ranking.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('selection', [embed('selection.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('base', [embed('base.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('GA', [embed('GA.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('SubsectionGA', [embed('SubsectionGA.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('SubsectionGA_period', [embed('SubsectionGA_period.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('SubsectionGA_period_total', [embed('SubsectionGA_period_total.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_profit_common', [embed('subsection_declaration_profit_sd.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('subsection_declaration_income_common', [embed('subsection_declaration_income_sd.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('segmentation_common', [embed('segmentation_sd.py', vl_type='VLB', code=code, path='../../../..')]),
    Extension('newpower_load_pre', [embed('newpower_load_pre.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('distance_calculation', [embed('distance_calculation.py', vl_type='VLB', code=code, path='../..')]),
    Extension('optimize_deep_adjust', [embed('optimize_deep_adjust.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('optimize_deep_adjust_common', [embed('optimize_deep_adjust_common.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('optimize_deep_adjust_eboiler', [embed('optimize_deep_adjust_eboiler.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('price_trans_provincial', [embed('price_trans_provincial.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('coal_consum_function', [embed('coal_consum_function.py', vl_type='VLB', code=code, path='../..')]),
    Extension('declaration_correct_pre', [embed('declaration_correct_pre.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('matrix_SubsectionGA', [embed('matrix_SubsectionGA.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('db_declaration_pre', [embed('db_declaration_pre.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('db_price_prediction_value', [embed('db_price_prediction_value.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('db_model_config', [embed('db_model_config.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('mx_declaration_pre', [embed('mx_declaration_pre.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('mx_price_prediction_value', [embed('mx_price_prediction_value.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('loadrate_fitting', [embed('loadrate_fitting.py', vl_type='VLB', code=code, path='../..')]),
    Extension('contract_electricity_distribution', [embed('contract_electricity_distribution.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('ahead_price_bsf_15', [embed('ahead_price_bsf_15.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('ahead_price_etr_15', [embed('ahead_price_etr_15.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('db_boot_capacity', [embed('db_boot_capacity.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('standard_declaration_fitting', [embed('standard_declaration_fitting.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('price_etr', [embed('price_xgb.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('price_bsf', [embed('price_bsf.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('common_price_bsf_15', [embed('common_price_bsf_15.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('common_price_etr_15', [embed('common_price_etr_15.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('common_price_sim_45', [embed('common_price_sim_45.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('common_price_bsf_3', [embed('common_price_bsf_3.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('common_price_etr_3', [embed('common_price_etr_3.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('matrix_subsection_declaration_profit_sd', [embed('matrix_subsection_declaration_profit_sd.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_income_sd', [embed('subsection_declaration_income_sd.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_profit_sd', [embed('subsection_declaration_profit_sd.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('segmentation_sd', [embed('segmentation_sd.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_profit_sxgn', [embed('subsection_declaration_profit_sxgn.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('subsection_declaration_profit_sxjn', [embed('subsection_declaration_profit_sxjn.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('price_xgb', [embed('price_xgb.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('installed_capacity', [embed('installed_capacity.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('sx_declaration_fitting_5d', [embed('sx_declaration_fitting_5d.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('sx_etr_price_pred_5d', [embed('sx_etr_price_pred_5d.py', vl_type='VLB', code=code, path='../../..')]),
    Extension('hb_price_bsf', [embed('hb_price_bsf.py', vl_type='VLB', code=code, path='../../..')]),
])

setup(ext_modules=ext)
