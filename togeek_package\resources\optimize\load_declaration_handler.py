# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_load_declaration import LoadDeclaration


class LoadDeclarationHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        history = params.pop('history')
        predictions = params.pop('predictions')
        limits = params.pop('limits')
        p = LoadDeclaration(history=history, predictions=predictions, limits=limits)
        pred = p.predict(to_json=True)
        self.write(pred)

