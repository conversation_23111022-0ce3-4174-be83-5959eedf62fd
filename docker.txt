apt-get update
apt-get install -y wget bzip2 ca-certificates libglib2.0-0 libxext6 libsm6 libxrender1 git mercurial subversion
rm -rf /var/lib/apt/lists/*

wget --quiet https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-py38_23.9.0-0-Linux-x86_64.sh -O ~/miniconda.sh
/bin/bash ~/miniconda.sh -b -p /opt/conda
rm ~/miniconda.sh
/opt/conda/bin/conda clean --all -y
echo "export PATH=/opt/conda/bin:$PATH" > /etc/profile.d/conda.sh
/opt/conda/bin/conda init bash
PATH="/opt/conda/bin:$PATH"
conda activate

pip install prophet tornado statsmodels scikit-learn jenkspy xgboost GPy lightgbm catboost requests chinese_calendar pwlf PyMySQL SQLAlchemy networkx

# 设置工作目录
WORKDIR /app

# 将宿主机的代码目录挂载到容器的 /app 目录
# 注意：这一步在运行容器时通过 -v 参数完成

# 指定容器启动时运行的命令
# 假设你的 Python 程序名为 main.py
CMD ["python", "main.py"]