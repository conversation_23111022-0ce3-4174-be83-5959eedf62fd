#!/usr/bin/env python
# coding: utf-8

import datetime
import numpy as np
import pandas as pd
import jenkspy
import logging
import warnings
from togeek_package.lib.data_verification import data_verify

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class Declaration:
    def __init__(self, source_data, date_list, sj_sign, sj_date, min_value=0, max_value=1500):
        logger.info("-------------三段拟合法预测出清电价开始-------------------")
        self.source_data = self.pre_source(source_data)
        self.date_list = date_list  # 带预测日期列表
        self.sj_sign = sj_sign
        self.sj_date = sj_date
        self.min_value = min_value      # 价格下限，默认为0
        self.max_value = max_value      # 价格上限，默认为1500
        # self.result = self.pre_price()

    def pre_source(self, source_data):
        """
        数据字段校验 + 数据整理
        :param source_data: json格式, 市场边界条件
        :return:
        """
        # 1. 数据校验
        source_data, message = data_verify(data_json=source_data,
                                           feature_list=['日前必开机组', '日前必停机组', '检修总容量', '日前联络线计划'],
                                           threshold_null=0.35)
        logger.info(message)

        # 2. 数据整理
        source_data['日期'] = source_data['datetime'].map(lambda s: str(s)[0:10])
        source_data['time'] = source_data['datetime'].map(lambda s: str(s).split(' ')[-1])
        source_data['week'] = pd.to_datetime(source_data['日期']).dt.weekday.map(lambda s: int(s) + 1)
        source_data['时间'] = source_data['datetime'].map(lambda s: int(int(str(s)[11:13])*4+(int(str(s)[14:16])/15)+1))
        source_data['jingjia'] = source_data['负荷预测'] - source_data['日前新能源负荷预测'] - source_data['日前联络线计划']

        return source_data

    # def acc(self, pred, real):
    #     predlist = pred.tolist()
    #     reallist = real.tolist()
    #     x = np.abs(np.array(predlist) - np.array(reallist)).sum() / real.sum()
    #     return 1 - x

    @staticmethod
    def get_history_date(history_data):
        """
        从传入历史数据 history_data 中选取价格不全为0的最大历史日期 history_date
        """
        history_date_list = history_data['日期'].unique().tolist()  # 历史日期列表
        history_date_list = sorted(history_date_list, reverse=True)  # 历史日期倒序排列

        history_date = None     # 初始化
        for date in history_date_list:
            if history_data[history_data['日期'] == date]['日前价格'].sum() == 0:
                # print(f"{date}价格数据全为0，继续寻找前一个历史时期！")
                continue
            elif history_data[history_data['日期'] == date]['日前价格'].sum() == 1500 * 96:
                # print(f"{date}价格数据全为1500，继续寻找前一个历史时期！")
                continue
            elif history_data[history_data['日期'] == date].shape[0] <= 3:
                # print(f"{date}价格数据去除边界点后剩余条数小于3，继续寻找前一个历史时期！")
                continue
            else:
                history_date = date
                break
        return history_date

    def pre_price(self, to_json=False):
        """
        从sj_date计算
        Parameters
        ----------
        to_json

        Returns
        -------

        """
        y1 = 0
        result = pd.DataFrame(columns=["date_time", "预测日前价格", "type"])

        for rdata in self.date_list:
            result_tmp = pd.DataFrame(columns=["date_time", "预测日前价格", "type"])
            pre_data = []

            # 选择用于拟合的历史日期: history_date
            if self.sj_sign == 1:
                # 如果星期在date列表里，表示省间现货，筛选历史表，取最大的一条
                # 如果历史数据没有符合条件的选项，选前一天
                week = int(datetime.datetime.strptime(rdata, "%Y-%m-%d").weekday()) + 1
                try:
                    if week in self.sj_date:
                        history_data = self.source_data[(self.source_data['week'].isin(self.sj_date)) & ([self.source_data['日期'] < rdata])]
                    else:
                        # 如果不在列表里，表示运行日非省间现货，删选历史表
                        history_data = self.source_data[(~(self.source_data['week'].isin(self.sj_date))) & ([self.source_data['日期'] < rdata])]

                except:
                    history_data = self.source_data[self.source_data['日期'] < rdata].dropna()
            else:
                history_data = self.source_data[self.source_data['日期'] < rdata].dropna()  # 历史数据

            # 历史数据去除边界点
            history_data = history_data[(history_data['日前价格'] > 0) & (history_data['日前价格'] < 1500)]  # 筛选价格
            history_date = self.get_history_date(history_data)
            logger.info(f"date_pred: {rdata}, date_history: {history_date}")

            # 筛选历史日期数据curdata和待预测日期数据curdatab
            curdata = self.source_data[self.source_data['日期'] == history_date]
            curdata = curdata[(curdata['日前价格'] > 0) & (curdata['日前价格'] < 1500)]  # 筛选价格
            curdatab = self.source_data[self.source_data['日期'] == rdata]

            # 自然分段法，分三段绘制散点图
            # 价格乘竞价空间
            # print(curdata['日前价格'])
            # print(curdata['jingjia'])
            curdata['jenkspy'] = curdata['日前价格'] * curdata['jingjia']
            curdata.reset_index(inplace=True, drop=True)
            breaks = jenkspy.jenks_breaks(curdata['jenkspy'].to_list(), 3)
            # b1 = curdata[curdata['jenkspy'] <= breaks[1]]
            b2 = curdata[(curdata['jenkspy'] > breaks[1]) & (curdata['jenkspy'] <= breaks[2])]
            b3 = curdata[curdata['jenkspy'] > breaks[2]]
            b2jingjiamean = b2['jingjia'].mean()
            b2rqrpmean = b2['日前价格'].mean()
            k32_2m3all = ((b3['日前价格'] - b2rqrpmean) / (b3['jingjia'] - b2jingjiamean)).mean()
            # 第三段的直线
            p13 = np.poly1d([k32_2m3all, np.mean(b3['日前价格']) - k32_2m3all * np.mean(b3['jingjia'])])

            # 多项式拟合
            x_list = curdata['jingjia'].to_list()      # 横坐标列表：竞价空间
            y_list = curdata['日前价格'].to_list()      # 纵坐标列表：价格数据

            parameter1 = np.polyfit(x_list, y_list, 1)
            p1 = np.poly1d(parameter1)
            parameter3 = np.polyfit(x_list, y_list, 3)
            p3 = np.poly1d(parameter3)

            todayuse = 1
            if parameter3[0] > 0:
                delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
                if delta < 0:
                    todayuse = 3
                else:
                    x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                    x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                    y1 = p3(x1)
                    y2 = p3(x2)
                    if x1 > 0 and y1 < y2 * 1.2:
                        todayuse = 33
            else:
                if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                    todayuse = 13
                else:
                    todayuse = 1

            if todayuse == 1:
                pre_data = p1(curdatab['jingjia'])  # 1次直线直接预测
            elif todayuse == 13:
                pre_data = np.maximum(np.maximum(p1(curdatab['jingjia']), self.min_value), p13(curdatab['jingjia']))     # 两根直线中的大值
            elif todayuse == 3:
                pre_data = np.maximum(p3(curdatab['jingjia']), self.min_value)   # 3次曲线
            elif todayuse == 33:
                pre_data = np.maximum(p3(curdatab['jingjia']), y1)  # 3次曲线与直线中的大值
                pre_data = np.where((curdatab['jingjia'].values < x1), p3(curdatab['jingjia']), pre_data)     # 小于第一个极值点x1的部分使用3次曲线

            # 修正价格上下限
            pre_data = np.maximum(pre_data, self.min_value)     # 价格下限修正
            pre_data = np.minimum(pre_data, self.max_value)     # 价格上限修正

            result_tmp['date_time'] = curdatab['日期'] + ' ' + curdatab['time']
            result_tmp['预测日前价格'] = list(pre_data)
            result_tmp['type'] = [todayuse] * curdatab.shape[0]
            result = pd.concat([result, result_tmp], axis=0)
        if to_json:
            result = result.to_dict('list')
        logger.info(f'result = {result}')
        logger.info("-------------三段拟合法预测出清电价结束-------------------")
        return result


if __name__ == '__main__':
    import json
    file_path = r"F:\ToGeek\2023_model_fix\source_data_bsf_error_type1.json"
    with open(file_path, "r", encoding="utf-8") as f:
        source_data_test = json.loads(f.read())
    # source_data_test = data_dict['source_data']

    date_list_pred = ['2023-01-12']
    p = Declaration(source_data_test, date_list_pred, sj_sign=0, sj_date=[])
    pre = p.pre_price(False)
    print(pre['预测日前价格'].values)
