#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28 16:23
# <AUTHOR> Darlene

'''
    1、场站历史功率数据，场站id，风/光，时间，功率
    2、场站气象数据（需要知道场站的经纬度）， 场站id，时间，t， 风速，辐照。。。
    3、装机容量：市、装机、风/光
    4、市级气象数据：市、温度(°C),相对湿度(%),降水量(mm),降水概率(%),风力(级别),风速(m/s),风向(°),大气压强(百帕),体感温度(°C),
                   太阳能总辐射(W/m^2),天气现象
    5、节假日：日期，是否节假日
'''
import pandas as pd
import numpy as np
import datetime
from xgboost import XGBRegressor
from sklearn.preprocessing import StandardScaler
from concurrent.futures import ThreadPoolExecutor

class PreXnyLoadYn:
    def __init__(self, node_power, all_power, i_capacity, all_weather, holidays, pre_date, pre_days):
        print(f"=====================开始进行云南负荷预测=================")
        self.feng_node, self.guang_node = pd.DataFrame(node_power)
        self.weather_feng, self.weather_guang, self.power_feng, self.power_guang = self.processing_weather(all_weather, i_capacity, all_power)
        self.holidays = pd.DataFrame(holidays)
        self.pre_date = pre_date
        self.pre_days = pre_days

    def prepare_data(self, node_power):
        '''
        1、场站历史功率：node_id, type, date_time, power
        2、场站气象数据：node_id, date_time, t, wind, ...  -- 没有
        3、市级气象数据：city, date_time, t, t_surface, rh_surface,p_surface,pro_surface,wind_force,wind_speed,
        wind_direction,a_pressure,fl_surface, solar_irradiance, weather_condition
        4、节假日：date, is_holiday
        '''
        node_power = pd.DataFrame(node_power)
        node_power = node_power.rename_axis(
            'date_time').reset_index() if 'date_time' not in node_power.columns else node_power
        node_power = node_power.sort_values('date_time')
        # 筛选风电数据并重塑为宽表格式
        feng_node = (
            node_power.query("type == '风'")
            .pivot_table(index='date_time', columns='node_id', values='power', aggfunc='sum')
        )
        # 计算所有风电场总功率并命名新列
        feng_node.insert(0, 'all_node', feng_node.sum(axis=1))
        feng_node = feng_node.add_prefix('feng_')
        # 筛选光伏数据并重塑为宽表格式
        guang_node = (
            node_power.query("type == '光'")
            .pivot_table(index='date_time', columns='node_id', values='power', aggfunc='sum')
        )
        # 计算所有光伏场站总功率并命名新列
        guang_node.insert(0, 'all_node', guang_node.sum(axis=1))
        guang_node = guang_node.add_prefix('guang_')
        return feng_node, guang_node

    def processing_weather(self, weather_df, i_capacity, all_power):
        '''
        1、处理气象数据
        2、新增特征
        3、滞后特征
        4、算装机占比，给气象数据求加权平均
        t_surface, rh_surface,p_surface,pro_surface,wind_force,wind_speed,
        wind_direction,a_pressure,fl_surface, solar_irradiance, weather_condition
        '''
        weather_df = pd.DataFrame(weather_df)
        weather_df = weather_df.rename_axis(
            'date_time').reset_index() if 'date_time' not in weather_df.columns else weather_df
        # 主要处理天气现象字段
        weather_list = weather_df['weather_condition'].unique()
        cond_df = pd.DataFrame(weather_list, columns=['weather_condition'])
        cond_df['weather_cid'] = range(1, len(cond_df) + 1)
        weather_df = weather_df.merge(cond_df, how='left', on='weather_condition')
        del weather_df['weather_condition']
        # 分风、光特征
        weather_feng = weather_df[['city', 'date_time', 't_surface', 'wind_force', 'wind_speed', 'wind_direction', 'a_pressure', 'weather_condition']]
        weather_guang = weather_df[['city', 'date_time', 't_surface', 'rh_surface','p_surface','pro_surface', 'a_pressure',
                                    'fl_surface', 'solar_irradiance', 'weather_condition']]
        # 新增特征
        # 风功率密度 (W/m²)
        # 其中空气密度估算公式：ρ = P/(R_d*T) (R_d=287 J/kg·K, T为绝对温度)
        weather_feng['wind_power_density'] = weather_feng.map(lambda row: 0.5 * ((row['a_pressure'] * 0.029) /
                                                                             (8.314 + row['t_surface'])) * (row['wind_speed'] ** 3), axis=1)
        # 等效风速
        weather_feng['effective_wind_speed'] = (weather_feng['wind_speed'] ** 3).rolling(6).mean() ** (1 / 3)
        # 湍流强度 (TI)
        weather_feng['turbulence_intensity'] = weather_feng['wind_speed'].rolling(6).std() / (weather_feng['wind_speed'].rolling(6).mean() + 1e-5)

        # 风向稳定性
        weather_feng['wind_dir_stability'] = weather_feng['wind_direction'].rolling(12).std()
        # 转换为U/V分量
        weather_feng['u_wind'] = weather_feng['wind_speed'] * np.sin(np.radians(weather_feng['wind_direction']))
        weather_feng['v_wind'] = weather_feng['wind_speed'] * np.cos(np.radians(weather_feng['wind_direction']))

        # 主导风向区间 (8方位)
        weather_feng['wind_dir_octant'] = pd.cut(weather_feng['wind_direction'], bins=np.arange(0, 361, 45),
                                       labels=['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'])

        # 风向与地形朝向的匹配度 (假设地形主风向为225°)
        weather_feng['terrain_match'] = np.cos(np.radians(weather_feng['wind_direction'] - 225))
        # 滞后特征
        features = ['wind_speed', 't_surface']
        lags = [1, 3, 6, 12, 24]
        for feat in features:
            for lag in lags:
                weather_feng[f'{feat}_lag_{lag}'] = weather_feng[feat].shift(lag)

        # 光伏板温度估算
        weather_guang['pv_temp'] = weather_guang['t_surface'] + 0.035 * weather_guang['solar_irradiance']

        # 功率温度损失
        weather_guang['temp_loss_factor'] = 1 - 0.004 * (weather_guang['pv_temp'] - 25)  # γ≈0.004/°C

        # 温度辐照耦合效应
        weather_guang['temp_rad_coupling'] = weather_guang['solar_irradiance'] * weather_guang['pv_temp'] / 1000
        # 滞后特征
        features = ['solar_irradiance', 't_surface']
        lags = [1, 3, 6, 12, 24]
        for feat in features:
            for lag in lags:
                weather_guang[f'{feat}_lag_{lag}'] = weather_guang[feat].shift(lag)

        # 变化率特征
        weather_guang['pressure_change_rate'] = weather_guang['a_pressure'].diff(3) / 3  # 3小时变化率
        weather_guang['rad_derivative'] = weather_guang['solar_irradiance'].diff()
        # 计算每个类别占总和的占比
        i_capacity = pd.DataFrame(i_capacity)
        fc = i_capacity[i_capacity['type'] == '风']
        gc = i_capacity[i_capacity['type'] == '光']
        totalf = fc['capacity'].sum()
        fc_z = fc.groupby('city')['capacity'].sum() / totalf
        totalg = gc['capacity'].sum()
        gc_z = gc.groupby('city')['capacity'].sum() / totalg
        # 按装机占比算加权平均
        weather_feng = weather_feng.merge(fc_z, how='left', on='city').set_index(['date_time', 'city'])
        weather_feng.update(weather_feng.drop(columns=['capacity'], axis=0)).reset_index()
        weather_feng = weather_feng.goupby('date_time').sum().reset_index()
        weather_guang = weather_guang.merge(gc_z, how='left', on='city').set_index(['date_time' 'city'])
        weather_guang.update(weather_guang.drop(columns=['capacity'], axis=0)).reset_index()
        weather_guang = weather_guang.goupby('date_time').sum().reset_index()
        # 按装机占比分离风、光负荷
        # 一次性计算，一次性赋值，无冗余 copy
        scale = fc['capacity'].sum(), gc['capacity'].sum()
        total = sum(scale) or 1  # 防 0
        f_zb, g_zb = (s / total for s in scale)

        power_feng = all_power.assign(power=all_power['power'] * f_zb)
        power_guang = all_power.assign(power=all_power['power'] * g_zb)
        return weather_feng, weather_guang, power_feng, power_guang


    def pre_data(self, pre_list, all_df, cols):
        '''
        1、组合场站风/光功率和气象数据，新增特征
        2、划分train和test
        3、预测
        4、结果处理
        '''
        all_df['date'] = all_df['date_time'].map(lambda x: str(x).split(' ')[0])
        all_df['date_time'] = all_df.to_datetime(all_df['date_time'])
        all_df['hour'] = all_df['date_time'].map(lambda x: x.hour)
        all_df['day_of_week'] = all_df['date_time'].map(lambda x: x.dayofweek)
        all_df['day_of_month'] = all_df['date_time'].map(lambda x: x.day)
        all_df['month'] = all_df['date_time'].map(lambda x: x.month)
        all_df['is_weekend'] = all_df['date_time'].isin([5, 6]).astype(int)
        all_df['date_time'] = all_df['date_time'].astype(str)
        train_data = all_df[all_df['date'] < self.pre_date]
        test_data = all_df[all_df['date'].isin(pre_list)]
        train_X = train_data.drop(columns=['date_time', 'power'])
        train_y = train_data[['power']]
        # 4. 数据标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(train_X)
        model = XGBRegressor(
            objective='reg:squarederror',
            n_estimators=200,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        model.fit(X_train_scaled, train_y)
        # 取目标列
        X_test_fe = test_data.drop(columns=['date_time', 'power'])
        # 标准化
        X_test_scaled = scaler.transform(X_test_fe)
        y_pred = model.predict(X_test_scaled)
        y_test = test_data[['date_time']]
        y_test[f"pre_{cols}"] = y_pred
        return y_test






    def get_result(self, json=True):
        '''
        同时预测风和光
        '''
        results = pd.DataFrame()
        try:
            pre_date = datetime.datetime.strptime(self.pre_date, '%Y-%m-%d')
            pre_list = [(pre_date + datetime.timedelta(days=d)).strftime('%Y-%m-%d') for d in self.pre_days]
            all_feng = self.feng_node.merge(self.weather_feng, how='outer', on='date_time')
            all_guang = self.guang_node.merge(self.weather_guang, how='outer', on='date_time')
            with ThreadPoolExecutor(max_workers=2) as pool:
                # 把函数与参数打包成 (callable, *args) 形式
                futures = [
                    pool.submit(self.pre_data, pre_list, all_feng, 'feng'),
                    pool.submit(self.pre_data, pre_list, all_guang, 'guang'),
                ]
                for f in futures:
                    if results.empty:
                        results = f.result().copy(deep=True)
                    else:
                        results = pd.concat([results, f.result()])
        except Exception as err:
            print(f"预测云南新能源负荷预测出错，报错信息如下：{err}")
        print(f"==================云南新能源负荷预测完成================")
        if json == True:
            return results.to_dict('list')
        else:
            return results