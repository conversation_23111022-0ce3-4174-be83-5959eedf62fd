#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/9/8 14:20 
@Info    ：使用相似日的竞价空间，进行分段线性拟合

'''

import numpy as np
import pandas as pd
from scipy.spatial import distance
from scipy import optimize
import jenkspy
from datetime import date, timedelta
import logging
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger()


class SimSegFittingPriceSD:
    def __init__(self, data, periods=7, min_price=-80, max_price=1300):  # periods-从历史多长时间中找相似日, 默认从历史7天中查找相似日
        self.source_data = self.pre_source(data)
        self.period = periods  # 历史日期长度
        self.min_price = min_price
        self.max_price = max_price

    def pre_source(self, data):
        """
        数据预处理：如果数据中没有“竞价空间”字段，则计算竞价空间
        :param source_data: 传入数据
        :return:
        """
        data = pd.DataFrame.from_dict(data, orient='index').T
        cols = data.columns.to_list()
        cols.remove('date_time')
        for col in cols:
            data[col] = data[col].astype(float)
        data.drop_duplicates(inplace=True)
        data['date'] = data['date_time'].map(lambda s: str(s)[0:10])
        data['time'] = data['date_time'].map(lambda s: str(s).split(' ')[-1])
        if '竞价空间' not in data.columns:
            data['竞价空间'] = data['日前直调负荷'] - data[['日前联络线受电负荷', '日前风电总加', '日前光伏总加', '日前核电总加', '日前自备机组总加']].sum(axis=1)
        return data

    def select_similar_date(self, comp_data, pred_data, sim_col, sim_method='minmax'):
        """
        计算与预测日的竞价空间相似的日期
        :param data: 负荷预测数据
        :param pred_date: 预测日期
        :return: 相似日期
        """
        comp_data['date_time'] = pd.to_datetime(comp_data['date_time'])
        comp_data.set_index('date_time', drop=True, inplace=True)
        comp_data1 = comp_data[[sim_col]].resample('D').agg([min, max]).reset_index()
        comp_data1.columns = ['date_time', sim_col+'_min', sim_col+'_max']

        pred_bd_max = pred_data[sim_col].max()  # 预测日的竞价空间最大值
        pred_bd_min = pred_data[sim_col].min()  # 预测日的竞价空间最小值

        comp_data1["max_diff"] = abs(comp_data1[sim_col+'_max'] - pred_bd_max)
        comp_data1["min_diff"] = abs(comp_data1[sim_col+'_min'] - pred_bd_min)
        comp_data1['minmax'] = comp_data1["max_diff"] + comp_data1["min_diff"]
        comp_data1['date_time'] = comp_data1['date_time'].astype(str)

        result = comp_data1[['date_time', 'minmax']]
        result.columns = ['date', 'minmax']

        # 考虑全天96个时点的竞价空间/负荷率
        hist_ = comp_data.pivot(index='time', columns='date', values=sim_col)

        # 空值填充
        hist_ = hist_.fillna(method="ffill").fillna(method='bfill')
        hist_date_lst = hist_.columns.to_list()
        tmp = []
        v = pred_data[sim_col].values
        for i, date in enumerate(hist_date_lst):
            dis_lst = {'date': date}
            u = hist_.loc[:, date].values
            # 余弦距离
            dis_lst['cosine'] = distance.cosine(u, v)
            # 曼哈顿距离
            dis_lst['cityblock'] = distance.cityblock(u, v)
            # 欧氏距离
            dis_lst['euclidean'] = distance.euclidean(u, v)
            tmp.append(dis_lst)
        tmp_res = pd.DataFrame(tmp)
        result = pd.merge(result, tmp_res, on='date')

        similar_date = result.sort_values(sim_method)['date'].values[0]
        return similar_date

    def segments_fit(self, X, Y, count):
        xmin = X.min()
        xmax = X.max()

        seg = np.full(count - 1, (xmax - xmin) / count)

        px_init = np.r_[np.r_[xmin, seg].cumsum(), xmax]
        py_init = np.array([Y[np.abs(X - x) < (xmax - xmin) * 0.01].mean() for x in px_init])
        # 查看py_init中有无空值
        # idx = np.where(np.isnan(py_init))[0]
        #
        # if idx.size != 0:
        #     py_init[idx[0]] = (py_init[idx[0]-1] + py_init[idx[0]+1]) / 2

        def func(p):
            seg = p[:count - 1]
            py = p[count - 1:]
            px = np.r_[np.r_[xmin, seg].cumsum(), xmax]
            return px, py

        def err(p):
            px, py = func(p)
            Y2 = np.interp(X, px, py)
            return np.mean((Y - Y2) ** 2)

        r = optimize.minimize(err, x0=np.r_[seg, py_init], method='Nelder-Mead')
        return func(r.x)

    def fitting_price(self, hist_data, curr_data, mode='jenkspy'):
        """
        竞价空间拟合算法
        :param curr_data: 相似日
        :param hist_data: 预测日
        :return: 拟合结果
        """
        # 分段线性拟合
        x = hist_data['竞价空间'].values
        y = hist_data['电价'].values

        if mode == 'jenkspy':
            hist_data['jenkspy'] = hist_data['竞价空间'] * hist_data['电价']
            spots = jenkspy.jenks_breaks(hist_data['jenkspy'], n_classes=2)
            breaks = hist_data[hist_data['jenkspy'] < spots[1]]['竞价空间'].max()
        else:
            px, py = self.segments_fit(x, y, 2)
            px.sort()
            breaks = px[1]
        hist_tmp1 = hist_data[hist_data['竞价空间'] < breaks]
        hist_tmp2 = hist_data[hist_data['竞价空间'] >= breaks]

        param1 = np.polyfit(hist_tmp1['竞价空间'], hist_tmp1['电价'], 1)
        p1 = np.poly1d(param1)

        param2 = np.polyfit(hist_tmp2['竞价空间'], hist_tmp2['电价'], 1)
        p2 = np.poly1d(param2)

        # 结果预测及上下限修正
        curr_tmp1 = curr_data[curr_data['竞价空间'] < breaks]
        curr_tmp2 = curr_data[curr_data['竞价空间'] >= breaks]

        curr_pred1 = np.minimum(np.maximum(p1(curr_tmp1['竞价空间']), self.min_price), self.max_price)
        curr_pred2 = np.minimum(np.maximum(p2(curr_tmp2['竞价空间']), self.min_price), self.max_price)

        pred_data1 = pd.DataFrame({'date_time': curr_tmp1['date_time'], 'pred': curr_pred1})
        pred_data2 = pd.DataFrame({'date_time': curr_tmp2['date_time'], 'pred': curr_pred2})
        pred_data = pd.concat([pred_data1, pred_data2], axis=0)

        # 价格修正2，对于山东省，将价格小于0的值，全部修正为最小价格
        pred_data.loc[pred_data['pred'] < 0, 'pred'] = self.min_price
        pred_data.sort_values('date_time', inplace=True)

        return pred_data

    def pred_price(self, pred_date, is_sim=True, sim_method='minmax', mode='jenkspy', to_json=False):
        logger.info("-------------相似日竞价空间拟合法预测出清电价开始-------------------")
        logger.info(f"pred_date: {pred_date}")

        y, m, d = pred_date.split("-")
        comp_start_date = str(date(int(y), int(m), int(d)) - timedelta(self.period))
        comp_end_date = str(date(int(y), int(m), int(d)) - timedelta(1))

        # 确定相似日查找的历史数据
        # data_train = self.source_data[~self.source_data["电价"].isnull()]
        # comp_end_date = data_train["date"].max()
        # comp_start_date = str(pd.to_datetime(comp_end_date) - timedelta(self.period + 1)).split(" ")[0]
        comp_data = self.source_data.loc[(self.source_data['date'] >= comp_start_date) &
                                         (self.source_data['date'] <= comp_end_date),
                                         ["date_time", 'date', 'time', '竞价空间']]
        pred_data = self.source_data.loc[self.source_data['date'] == pred_date, ["date_time", 'date', 'time', '竞价空间']]
        if is_sim:
            similar_date = self.select_similar_date(comp_data, pred_data, sim_col='竞价空间', sim_method=sim_method)
        else:
            similar_date = comp_end_date
        hist_data = self.source_data.loc[self.source_data['date'] == similar_date, ["date_time", 'date', 'time', '竞价空间', '电价']]
        logger.info(f"similar_date: {similar_date}")
        # print('similar_date:', similar_date)
        result = self.fitting_price(hist_data, pred_data, mode=mode)

        if to_json:
            result = result.to_dict('list')
        # logger.info(f'result = {result}')
        logger.info("-------------相似日竞价空间拟合法预测出清电价结束-------------------")
        return result


def cal_acc(res, pred_cols, real_col, val=55):
    res['date_time'] = res['date_time'].astype(str)
    res['date'] = res['date_time'].map(lambda x: x.split(" ")[0])

    # 计算修正后的预测电价和实际电价
    res[real_col + '_corr'] = np.maximum(res[real_col], val)

    # 生成每天的平均价格
    aprice = res.groupby('date').mean()[real_col].reset_index()
    aprice.columns = ['date', 'areal']
    aprice1 = res.groupby('date').mean()[real_col + '_corr'].reset_index()
    aprice1.columns = ['date', 'areal_corr']

    # 合并数据
    res = pd.merge(res, aprice, on='date')
    res = pd.merge(res, aprice1, on='date')

    # 计算准确度
    for pred_col in pred_cols:
        res[pred_col + '_corr'] = np.maximum(res[pred_col], val)

        # 计算方法1 - 项目方法；客户方法
        res['acc_orig_' + pred_col] = res.apply(
            lambda x: 1 - abs(x[pred_col] - x[real_col]) / x['areal'] if x['areal'] != 0 else 1 if x[real_col] == x[
                pred_col] else 0, axis=1)
        res['acc_orig_' + pred_col] = res['acc_orig_' + pred_col].map(lambda x: x if x >= 0 else 0)

        res['acc_cust_' + pred_col] = res.apply(
            lambda x: 1 - 2 * abs(x[pred_col] - x[real_col]) / (abs(x[real_col]) + abs(x[pred_col])) if abs(
                x[real_col]) + abs(x[pred_col]) != 0 else 0, axis=1)
        res['acc_cust_' + pred_col] = res['acc_cust_' + pred_col].map(lambda x: x if x >= 0 else 0)

        # 计算方法 - 值修正后：项目方法和客户方法
        res['acc_orig_curr_' + pred_col] = res.apply(
            lambda x: 1 - abs(x[pred_col + '_corr'] - x[real_col + '_corr']) / x['areal_corr'] if x[
                real_col + '_corr'] != 0 else 1 if
            x[real_col + '_corr'] == x[pred_col + '_corr'] else 0, axis=1)
        res['acc_orig_curr_' + pred_col] = res['acc_orig_curr_' + pred_col].map(lambda x: x if x >= 0 else 0)

        res['acc_cust_corr_' + pred_col] = res.apply(
            lambda x: 1 - 2 * abs(x[pred_col + '_corr'] - x[real_col + '_corr']) / (
                        abs(x[real_col + '_corr']) + abs(x[pred_col + '_corr'])), axis=1)
        res['acc_cust_corr_' + pred_col] = res['acc_cust_corr_' + pred_col].map(lambda x: x if x >= 0 else 0)

    # 整理数据
    res['date_time'] = pd.to_datetime(res['date_time'])
    res.set_index('date_time', drop=True, inplace=True)

    return res


if __name__ == '__main__':
    # data = pd.read_excel("data_input/通用竞价空间模型.xlsx", sheet_name="原数据")
    # data1 = pd.read_excel(r"C:\Users\<USER>\Desktop\SHANDONG.xlsx", sheet_name="Sheet2")
    # data2 = pd.read_excel(r"C:\Users\<USER>\Desktop\SHANDONG.xlsx", sheet_name="Sheet22")
    # data.columns = ['date_time', '竞价空间', '日前电价', 'date']

    # 使用预测的新能源数据进行电价预测，24点数据
    # data = pd.read_excel("data_input/通用竞价空间模型.xlsx", sheet_name="新数据")
    # data.columns = ['date_time', '竞价空间', '竞价空间1', '实时电价', '日前电价', 'date']
    # print(data.head())

    periods = 7
    is_sim = True
    sim_method = 'euclidean'
    mode = 'seg'
    n = 1
    """
    res = pd.DataFrame()
    # 1 循环预测
    for i in range(82):
        pred_date = str(date(2023, 9, 3) + timedelta(i))
        end_date = str(date(2023, 9, 3) + timedelta(i-n))
        start_date = str(date(2023, 9, 3) - timedelta(i + periods + 1))
        tmp_data1 = data1[(data1['date'] >= start_date) & (data1['date'] < end_date)]
        tmp_data2 = data2[data2['date'] == pred_date]
        tmp_data = pd.concat([tmp_data1, tmp_data2], axis=0)
        del tmp_data['date']
        try:
            m = SimSegFittingPriceSD(tmp_data)
            tmp = m.pred_price(pred_date, is_sim, sim_method)
            res = pd.concat([res, tmp], axis=0)
        except Exception as e:
            print(pred_date, e)
        # break
    res.to_excel(f"data_output/pred_{sim_method}_{mode}_911m_t22.xlsx", index=False)
    """

    # 2 计算准确度
    data = pd.read_excel(f"data_output/pred_{sim_method}_{mode}_911m_t33.xlsx")
    accs = cal_acc(data, ['pred'], 'real')
    accs.to_excel(f"data_output/预测准确度_pred_{sim_method}_{mode}_911m_t33.xlsx")
    print(accs.head())

