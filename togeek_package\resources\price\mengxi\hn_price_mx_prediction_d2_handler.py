#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/6/6 18:05
# <AUTHOR> <PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_huaneng.huaneng_mx_price_prediction_d2 import HNMxElPriceValueDataset2
from tornado import gen, concurrent
from tglibs.easy_json import j2o


class PricePredictionValueEvalHandlerHnMX2(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        province = data.get('province', '蒙西')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']
        days = data.get('days', 30)
        n_est = data.get('n_est', 100)
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 1500)
        max_power = data.get('max_power', 34660)
        r = yield self.predict(province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value, max_power)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value, max_power):
        elpd_val = HNMxElPriceValueDataset2(all_data, province, date_list_yuce, jiedian_type, max_power)
        result = elpd_val.predict_day_price(days=days, n_est=n_est, min_value=min_value, max_value=max_value)
        return result.to_dict()
