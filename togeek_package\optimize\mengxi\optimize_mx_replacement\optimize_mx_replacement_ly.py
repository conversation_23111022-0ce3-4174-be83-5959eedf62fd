# -*- coding: utf-8 -*-
# @Time : 2025/5/23 14:44
# <AUTHOR> Darlene
'''
    合约置换模型，分补贴场站和平价场站，分别进行策略置换
'''
import datetime

import pyomo.environ as pyo
from pyomo.opt import SolverFactory
import pandas as pd
import requests
import time
import gc


'''
    1、现货数据：出清电量、结算电价、统一结算点电价、上网电量、计量电量
    2、合约数据：年度合约量、价，月度合约量、价，基数合约量、价
    3、考核系数：
    4、绿电、补贴：绿电电价、补贴电价
'''

class OptimizeMxContrace:
    def __init__(self, gen_data, node_data, price_data, contract_data, coe_data, lv_price, other_price,
                 opt_date, points, limit_num, return_url):
        # 包含机组对应场站  节点电价，出清电量，上网电量，计量电量   年度合约量、价，月度合约量、价，基数合约量、价 # 补贴价格 缺额回收价格 超额回收价格
        self.gen_data, self.node_data, self.contract_data, self.other_price, self.yuenei, self.type = self.prepare_data(gen_data, node_data,
                                                                                                contract_data,
                                                                                                other_price)
        self.price_data = price_data  # 统一结算点电价
        self.coe_data = coe_data  # 缺额回收、超额回收、风险防范等系数
        self.lv_price = lv_price  # 绿电电价
        self.opt_date = opt_date
        self.points= points
        self.return_url = return_url
        self.limit_num = limit_num   # 单日单场站限制次数

    def prepare_data(self, gen_data, node_data, contract_data, other_price):
        '''

        :param gen_data:
        :param node_data:
        :param contract_data:
        :return:
        '''
        type = pd.DataFrame({"type": ["年度", "月度", "基数", "东送"], "h": [1, 2, 3, 4]})
        try:
            # 使用向量化操作提高效率
            gen_data = pd.DataFrame(gen_data)
            gen_data['n'] = range(1, len(gen_data) + 1)
            # 使用inplace=True减少内存分配
            # 使用merge替代多次分组
            n = pd.DataFrame(node_data).sort_values('date_time')
            node_data = (
                pd.DataFrame(node_data)
                .merge(gen_data[['node', 'n']], on='node', how='left')
                .dropna()
                .sort_values('date_time')
            )
            # 向量化时间处理
            node_data['time'] = node_data['date_time'].str.split().str[1]
            node_data = node_data.drop(columns=['node'])
            node_data['date_time'] = pd.to_datetime(node_data['date_time'])
            node_data['t'] = node_data['date_time'].map(
                lambda s: int(s.hour * 4 + s.minute / 15 + 1)
            )
            node_data['date_time'] = node_data['date_time'].astype(str)

            other_price = pd.DataFrame(other_price).merge(gen_data[['node', 'n']], how='left', on='node').dropna()
            del (other_price['node'])

            # 按类别、场站、时间，算总电量和平均电价
            # 合约数据处理优化
            contract_data = (
                pd.DataFrame(contract_data)
                .assign(feiyong=lambda x: x['contract_power'] * x['contract_price'])
                .groupby(['node', 'date_time', 'type'])
                .agg({'contract_power': 'sum', 'feiyong': 'sum'})
                .reset_index()
            )
            contract_data['contract_price'] = contract_data['feiyong'] / contract_data['contract_power']
            del contract_data['feiyong']
            # 造一份合约数据，补齐各个类别
            contract_data = contract_data.merge(gen_data[['node', 'n']], how='left', on='node').dropna()
            del (contract_data['node'])

            con_tmf = node_data[['n', 'date_time']].sort_values('date_time')
            nian = contract_data[contract_data['type'] == '年度'][
                ['n', 'date_time', 'type', 'contract_power', 'contract_price']]
            yue = contract_data[contract_data['type'] == '月度'][
                ['n', 'date_time', 'type', 'contract_power', 'contract_price']]
            jishu = contract_data[contract_data['type'] == '基数'][
                ['n', 'date_time', 'type', 'contract_power', 'contract_price']]
            nian = con_tmf.merge(nian, how='left', on=['n', 'date_time']).drop_duplicates()
            nian['type'] = '年度'
            nian['contract_price'] = nian['contract_price'].fillna(0)
            nian['contract_power'] = nian['contract_power'].fillna(0)
            yue = con_tmf.merge(yue, how='left', on=['n', 'date_time']).drop_duplicates()
            yue['type'] = '月度'
            yue['contract_price'] = yue['contract_price'].fillna(0)
            yue['contract_power'] = yue['contract_power'].fillna(0)
            jishu = con_tmf.merge(jishu, how='left', on=['n', 'date_time']).drop_duplicates()
            jishu['type'] = '基数'
            jishu['contract_price'] = 282.9
            jishu['contract_power'] = jishu['contract_power'].fillna(0)

            if not contract_data[contract_data['type'] == '东送'].empty:
                dongs = contract_data[contract_data['type'] == '东送'][
                    ['n', 'date_time', 'type', 'contract_power', 'contract_price']]
                dongs = con_tmf.merge(dongs, how='left', on=['n', 'date_time']).drop_duplicates()
                dongs['type'] = '东送'
                dongs['contract_price'] = dongs['contract_price'].fillna(0)
                dongs['contract_power'] = dongs['contract_power'].fillna(0)
                contract_df = pd.concat([nian, yue, jishu, dongs])
            else:
                print("没有东送")
                contract_df = pd.concat([nian, yue, jishu])
            contract_df = contract_df.merge(type, how='left', on='type').dropna()
            del contract_df['type']
            contract_df['n'] = contract_df['n'].astype(int)
            contract_df['date_time'] = pd.to_datetime(contract_df['date_time'])
            contract_df['t'] = contract_df['date_time'].map(
                lambda s: int(s.hour * 4 + s.minute / 15 + 1)
            )
            contract_df['date_time'] = contract_df['date_time'].astype(str)
            gen_data['n'] = gen_data['n'].astype(int)
            node_data['n'] = node_data['n'].astype(int)
            other_price['n'] = other_price['n'].astype(int)
            other_price = other_price.set_index('n')

            # 月内合约
            yuenei = contract_data[contract_data['type'] == '月内'][['n', 'date_time', 'type', 'contract_power', 'contract_price']]
            yuenei = con_tmf.merge(yuenei, how='left', on=['n', 'date_time']).drop_duplicates()
            yuenei['date_time'] = pd.to_datetime(yuenei['date_time'])
            yuenei['t'] = yuenei['date_time'].map(
                lambda s: int(s.hour * 4 + s.minute / 15 + 1)
            )
            yuenei['date_time'] = yuenei['date_time'].astype(str)
            yuenei['type'] = '月度'
            yuenei['contract_price'] = yuenei['contract_price'].fillna(0)
            yuenei['contract_power'] = yuenei['contract_power'].fillna(0)
            return gen_data, node_data, contract_df, other_price, yuenei, type
        except Exception as e:
            print(f"处理数据报错，报错信息：{e}")
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), type


    def get_model(self, df_node, df_con, df_coe, price_time, df_yuenei):
        '''
        :return:
        '''
        theta_que = df_coe['theta_que'].iloc[0]  # 缺额回收电量比例
        theta_chao = df_coe['theta_chao'].iloc[0]  # 超额回收电量比例
        f_helidu = df_coe['f_helidu'].iloc[0]  # 曲线合理度基准


        # 初始化模型

        # 场站id列表
        n_list = self.gen_data['n'].to_list()
        # 给一个合约列表  分别表示，年度、月度、基数
        heyue_list = self.contract_data['h'].drop_duplicates().to_list()
        t_list = self.price_data['t'].drop_duplicates().to_list()
        model = pyo.ConcreteModel()
        # 场站名称列表   补贴场站和补贴场站交易，平价场站间交易，相互独立
        model.N = pyo.Set(initialize=n_list)
        model.H = pyo.Set(initialize=heyue_list)
        model.T = pyo.Set(initialize=t_list)

        # 出清电量
        model.chuqing_power = pyo.Param( model.T, model.N, within=pyo.NonNegativeReals,
                                         initialize={(t, n): df_node.loc[(t, n), 'chuqing_power'] for t in model.T for n in model.N})
        # 节点电价
        model.jiedian_price = pyo.Param(model.T, model.N, within=pyo.Reals,
                                        initialize={(t, n): df_node.loc[(t, n), 'node_price'] for t in model.T for n in model.N})
        # 合约价
        model.heyue_price = pyo.Param(model.T, model.N, model.H, within=pyo.NonNegativeReals, initialize={(t, n, h): df_con.loc[(t, n, h),
        'contract_price'] for t in model.T for n in model.N for h in model.H})

        # 合约量
        model.heyue_power = pyo.Param(model.T, model.N, model.H, within=pyo.NonNegativeReals, initialize={(t, n, h): df_con.loc[(t, n, h),
        'contract_power'] for t in model.T for n in model.N for h in model.H})

        # 统一结算点电价
        model.price = pyo.Param(model.T, within=pyo.NonNegativeReals, initialize={t: price_time.loc[t, 'price'] for t in model.T})

        # 该时刻计量电量
        model.jiliang_power = pyo.Param(model.T, model.N, within=pyo.NonNegativeReals,
                                        initialize={(t, n): df_node.loc[(t, n), 'jiliang_power'] for t in model.T for n in model.N})
        # 上网电量
        model.shangwang_power = pyo.Param(model.T, model.N, within=pyo.NonNegativeReals,
                                          initialize={(t, n): df_node.loc[(t, n), 'shangwang_power'] for t in model.T for n in model.N})

        # 月内合约量
        model.yuenei_power = pyo.Param(model.T, model.N, within=pyo.NonNegativeReals,
                                       initialize={(t, n): df_yuenei.loc[(t, n), 'contract_power'] for t in model.T for n in model.N})
        # 月内合约价
        model.yuenei_price = pyo.Param(model.T, model.N, within=pyo.NonNegativeReals,
                                       initialize={(t, n): df_yuenei.loc[(t, n), 'contract_price'] for t in model.T for n in model.N})


        # 中间变量设定
        # 交易前-合约电量 不包含月内
        model.heyue_power_b = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)
        # 交易后-合约电量  不包含月内
        model.heyue_power_a = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)
        # 交易后的合约持仓  包含月内
        model.heyue_power_alla = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)
        # 交易后-合约均价 不包含月内
        model.heyue_price_a = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)

        # 电能电费
        model.s_dianneng = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)
        # 月内曲线合理度
        model.f_heli = pyo.Var(model.T, model.N, within=pyo.NonNegativeReals)
        # 置换次数
        model.cishu = pyo.Var(model.N, within=pyo.NonNegativeReals)

        # 初始化未知
        model.transfer = pyo.Var(model.T, model.N, model.N, model.H, within=pyo.NonNegativeIntegers)


        # 2 置换约束
        # 2.1 转出的电量之和不超过合约总量
        def rule_out(model, t, n, h):
            return model.heyue_power[t, n, h] >= sum(model.transfer[t, n, i, h] for i in model.N)
        model.rule_out = pyo.Constraint(model.T, model.N, model.H, rule=rule_out)

        # 2.2 对角线以上为0
        def rule_upper_triangle_zero(model, t, i, j, h):
            if i <= j:
                return model.transfer[t, i, j, h] == 0
            else:
                return pyo.Constraint.Skip

        model.upper_triangle_zero = pyo.Constraint(model.T, model.N, model.N, model.H, rule=rule_upper_triangle_zero)
        # 2.3 转移后的合约电量不超过上网电量
        def rule_in(model, t, n):
            return model.heyue_power_alla[t, n] <= model.shangwang_power[t, n]
        model.rule_in = pyo.Constraint(model.T, model.N, rule=rule_in)
        # 2.4 转移后的全场站合约总量等于转移前的全场站合约总量
        def rule_all(model, t):
            return sum(model.heyue_power_a[t, n] for n in model.N) == sum(model.heyue_power_b[t, n] for n in model.N)
        model.rule_all = pyo.Constraint(model.T, rule=rule_all)

        # 3、变量约束
        # 3.1 转移前的合约电量  不包含月内
        def rule_powerhb(model, t, n):
            return model.heyue_power_b[t, n] == sum(model.heyue_power[t, n, h] for h in model.H)
        model.rule_powerhb = pyo.Constraint(model.T, model.N, rule=rule_powerhb)
        # 3.2 转移后的合约电量  不包含月内
        def rule_powerha(model, t, n):
            return model.heyue_power_a[t, n] == model.heyue_power_b[t, n] - sum(
                model.transfer[t, n, i, h] for i in model.N for h in model.H) + sum(
                model.transfer[t, i, n, h] for i in model.N for h in model.H)
        model.rule_powerha = pyo.Constraint(model.T, model.N, rule=rule_powerha)

        # 3.6 转移后的合约持仓  包含月内
        def rule_poweralla(model, t, n):
            return model.heyue_power_alla[t, n] == model.heyue_power_a[t, n] + model.yuenei_power[t, n]
        model.rule_poweralla = pyo.Constraint(model.T, model.N, rule=rule_poweralla)
        # 3.7 交易单元电能量电费
        def rule_s_dianneng(model, t, n):
            # 转出电费
            s_out = sum(sum(model.transfer[t, n, i, j] * model.heyue_price[t, n, j] for i in model.N) for j in model.H)
            # 转入电费
            s_in = sum(sum(model.transfer[t, i, n, j] * model.heyue_price[t, i, j] for i in model.N) for j in model.H)
            # 总电费  + 月内
            s_all = sum(model.heyue_power[t, n, j] * model.heyue_price[t, n, j] for j in model.H) - s_out + s_in + model.yuenei_power[t, n] * model.yuenei_price[t, n]
            # 总电量
            p_all = model.heyue_power_a[t, n] + model.yuenei_power[t, n]
            return model.s_dianneng[t, n] == model.chuqing_power[t, n] * model.jiedian_price[t, n] + s_all - p_all * model.price[n]
        model.rule_s_dianneng = pyo.Constraint(model.T, model.N, rule=rule_s_dianneng)

        # 3.8 sum（月内计量电量）*缺额回收电量比例-sum（月内合约电量）<0 取 0 ，不算sum，只算当前时点
        def rule_q_quee(model, t, n):
            return model.jiliang_power[t, n] * theta_que - (model.heyue_power_a[t, n] + model.yuenei_power[t, n]) <= 0
        model.rule_q_quee = pyo.Constraint(model.T, model.N, rule=rule_q_quee)

        # 3.9 sum（月内合约电量）-sum（月内计量电量）*超额回收电量比例<0 ，不算sum，只算当前时点
        def rule_q_chaoe(model, t, n):
            return (model.heyue_power_a[t, n] + model.yuenei_power[t, n]) - model.jiliang_power[t, n] * theta_chao  <= 0
        model.rule_q_chaoe = pyo.Constraint(model.T, model.N, rule=rule_q_chaoe)

        # 3.10 月内曲线合理度小于
        def rule_fheli(model, t, n):
            # 1、历史取小值 + 当前取小值
            p_min = pyo.Expr_if(model.heyue_power_alla[t, n] >= model.chuqing_power[t, n],
                                model.chuqing_power[t, n], model.heyue_power_alla[t, n])
            # 2、 历史取均值 + 当前取均值
            p_mean = (model.chuqing_power[t, n] + model.heyue_power_alla[t, n]) / 2
            return p_min / p_mean - f_helidu <= 0
        model.rule_fheli = pyo.Constraint(model.T, model.N, rule=rule_fheli)

        # 3.11 单场站单日转移次数不超过limit_num
        def rule_limit(model, n):
            epsilon = 0.0001
            # 统计从节点n转出的正转移量（大于epsilon）
            count_out = sum(
                pyo.Expr_if(model.transfer[t, n, i, h] > epsilon, 1, 0)
                for i in model.N
                for h in model.H
                for t in model.T
            )
            # 统计转入节点n的正转移量（大于epsilon）
            count_in = sum(
                pyo.Expr_if(model.transfer[t, i, n, h] > epsilon, 1, 0)
                for i in model.N
                for h in model.H
                for t in model.T
            )
            num_all = count_out + count_in
            return num_all <= self.limit_num

        model.rule_limit = pyo.Constraint(model.N, rule=rule_limit)


        def obj_rule(model):
            obj_fun = sum(sum(model.s_dianneng[t, n] for n in model.N) for t in model.T)
            return obj_fun

        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)
        # 求解问题
        solver = SolverFactory('ipopt')
        solver.options['nlp_scaling_method'] = 'gradient-based'
        solver.options['tol'] = 1e-3
        solver.options['acceptable_tol'] = 1e-3
        solver.options['print_level'] = 0   # 不输出任何信息
        solver.options['max_iter'] = 2000  # 最大迭代次数
        try:
            # solver = SolverFactory('glpk')
            sol = solver.solve(model, tee=True)
        except Exception as e:
            print(f"计算出错，{e}，再执行一遍")
            sol = solver.solve(model, tee=True)
        if sol['Solver'][0]['Status'] != 'error':
            print('求解成功！')
            longterm_result = pd.DataFrame()
            # 结果处理
            # 1、处理中长期持仓数据
            heyue_t = []
            heyue_n = []
            heyue_power_a = []
            heyue_power_b = []
            s_dianneng = []
            for t in model.T:
                for n in model.N:
                    heyue_t.append(t)
                    heyue_n.append(n)
                    heyue_power_a.append(float(round(pyo.value(model.heyue_power_a[t, n]), 4)))
                    heyue_power_b.append(float(round(pyo.value(model.heyue_power_b[t, n]), 4)))
                    s_dianneng.append(float(round(pyo.value(model.s_dianneng[t, n]), 4)))
            longterm_result['n'] = heyue_n
            longterm_result['t'] = heyue_t
            longterm_result['heyue_power_a'] = heyue_power_a
            longterm_result['heyue_power_b'] = heyue_power_b
            longterm_result['s_dianneng'] = s_dianneng
            transfer_result = pd.DataFrame()
            # 2、处理转让数据
            out_list = []
            out_id = []
            in_id = []
            t_list = []
            type_id = []
            for t in model.T:
                for i in model.N:
                    for j in model.N:
                        for h in model.H:
                            # if i != j:
                            t_list.append(t)
                            out_list.append(float(round(pyo.value(model.transfer[t, i, j, h]), 2)))
                            out_id.append(i)
                            in_id.append(j)
                            type_id.append(h)
            transfer_result['t'] = t_list
            transfer_result['转出站点'] = out_id
            transfer_result['转入站点'] = in_id
            transfer_result['h'] = type_id
            transfer_result['转出电量'] = out_list
        else:
            longterm_result = pd.DataFrame()
            # 结果处理
            # 1、处理中长期持仓数据
            heyue_t = []
            heyue_n = []
            heyue_power_a = []
            heyue_power_b = []
            s_dianneng = []
            for t in model.T:
                for n in model.N:
                    heyue_t.append(t)
                    heyue_n.append(n)
                    heyue_power_a.append(0)
                    heyue_power_b.append(0)
                    s_dianneng.append(0)
            longterm_result['n'] = heyue_n
            longterm_result['t'] = heyue_t
            longterm_result['heyue_power_a'] = heyue_power_a
            longterm_result['heyue_power_b'] = heyue_power_b
            longterm_result['s_dianneng'] = s_dianneng
            transfer_result = pd.DataFrame()
            # 2、处理转让数据
            out_list = []
            out_id = []
            in_id = []
            type_id = []
            t_list = []
            for t in model.T:
                for i in model.N:
                    for j in model.N:
                        for h in model.H:
                            t_list.append(t)
                            out_list.append(0)
                            out_id.append(i)
                            in_id.append(j)
                            type_id.append(h)
            transfer_result['t'] = t_list
            transfer_result['转出站点'] = out_id
            transfer_result['转入站点'] = in_id
            transfer_result['type'] = type_id
            transfer_result['转出电量'] = out_list
        del sol
        gc.collect()
        return longterm_result, transfer_result


    def make_request_with_retry(self, result, max_retries=3, timeout=20):
        """
        带重试机制的HTTP请求
        :param url: 请求URL
        :param max_retries: 最大重试次数 (默认3次)
        :param timeout: 请求超时时间(秒)
        :return: 响应对象或None
        """
        retry_count = 0
        headers = {
            "authorization" : self.return_url['authorization']
        }

        while retry_count <= max_retries:
            try:
                # 使用上下文管理器确保连接关闭
                with requests.post(self.return_url['url'], headers=headers, timeout=timeout, json=result) as response:
                    response.raise_for_status()  # 检查HTTP错误状态
                    print(f"请求成功! 状态码: {response.status_code}")
                    return response

            except (requests.exceptions.RequestException, requests.exceptions.HTTPError) as e:
                retry_count += 1
                if retry_count > max_retries:
                    print(f"请求失败超过{max_retries}次，停止尝试。错误信息: {str(e)}")
                    return 0

                wait_time = 2 ** retry_count  # 指数退避策略
                print(f"请求失败 ({retry_count}/{max_retries})，{wait_time}秒后重试... 错误: {str(e)}")
                time.sleep(wait_time)

        return 0

    def point_to_time(self, point):
        """
        根据96点的时刻点数反推时间点
        :param point: 96点的时刻点数（0-95）
        :return: 时间点（格式为HH:MM）
        """
        if point < 1 or point > 96:
            raise ValueError("时刻点数必须在0到95之间")
        point = point - 1
        # 每个点代表15分钟
        minutes = point * 15
        # 计算小时和分钟
        hours = minutes // 60
        minutes = minutes % 60

        # 格式化时间
        time_str = f"{hours:02d}:{minutes:02d}:{00:02d}"
        return time_str


    def get_contmp(self, before_con, trans_con):
        '''
        算置换后的合约持仓和合约均价
        按场站按合约，算合约均价时，带转入场站的价格0
        Parameters
        ----------
        before_con  置换前合约
        trans_con   置换矩阵

        Returns   置换后合约
        -------
        '''
        # 提取所有唯一的 (station, time_point, contract_type) 组合
        all_stations = set(before_con['n'].unique()) | set(trans_con['转出站点'].unique()) | set(
            trans_con['转入站点'].unique())
        all_time_points = set(before_con['t'].unique()) | set(trans_con['t'].unique())
        all_contract_types = set(before_con['h'].unique()) | set(trans_con['h'].unique())

        # 创建完整的索引
        from itertools import product
        full_index = pd.DataFrame(
            product(all_stations, all_time_points, all_contract_types),
            columns=['n', 't', 'h']
        )

        # 对齐 before_con 数据
        before_con_aligned = (
            full_index
            .merge(before_con, on=['n', 't', 'h'], how='left')
            .fillna({'contract_power': 0, 'contract_price': 0})
        )

        # 计算每个场站的转出总电量
        out_total = trans_con.groupby(['转出站点', 't', 'h'])['转出电量'].sum().reset_index()
        out_total.rename(columns={'转出站点': 'n', '转出电量': 'total_out'}, inplace=True)

        # 计算每个场站的转入总电量及转入电费总和
        in_total = (
            trans_con
            .merge(before_con_aligned,
                   left_on=['转入站点', 't', 'h'],
                   right_on=['n', 't', 'h'],
                   suffixes=('_trans', '_before'))
            .assign(in_power=lambda x: x['转出电量'],
                    in_fee=lambda x: x['转出电量'] * x['h'])
            .groupby(['转入站点', 't', 'h'])[['in_power', 'in_fee']].sum()
            .reset_index()
            .rename(columns={'转入站点': 'n'})
        )

        # 合并原始数据与转入转出数据
        merged = (
            before_con_aligned
            .merge(out_total, on=['n', 't', 'h'], how='left')
            .merge(in_total, on=['n', 't', 'h'], how='left')
            .fillna({'total_out': 0, 'in_power': 0, 'in_fee': 0})
        )

        # 计算置换后的合约电量和均价，处理除零情况
        con_result = merged.assign(
            heyue_power_typea=lambda x: x['contract_power'] - x['total_out'] + x['in_power'],
            original_fee=lambda x: (x['contract_power'] - x['total_out']) * x['contract_price'],
            heyue_price_typea=lambda x: (x['original_fee'] + x['in_fee']) / x['heyue_power_typea'].replace(0, float('nan'))
        )[['n', 't', 'h', 'heyue_power_typea', 'heyue_price_typea']].dropna()
        return con_result


    def get_result(self, json=False):
        '''
        1、检查数据
        2、构造模型
        '''
        start_time = datetime.datetime.now()  # 记录开始时间
        result = {"调用失败，请检查数据！"}
        res = True
        if not res:
            return result
        else:
            self.price_data = pd.DataFrame(self.price_data)
            self.price_data['date_time'] = pd.to_datetime(self.price_data['date_time'])
            self.price_data['t'] = self.price_data['date_time'].map(
                lambda s: int(s.hour * 4 + s.minute / 15 + 1)
            )
            self.price_data['date_time'] = self.price_data['date_time'].astype(str)
            longterm_result = pd.DataFrame()
            con_result = pd.DataFrame()
            transfer_result = pd.DataFrame()
            try:
                # 节点相关数据.
                df_node = self.node_data.dropna().set_index(['t', 'n'])
                # 合约相关
                df_con = self.contract_data.dropna().set_index(['t', 'n', 'h'])
                df_coe = pd.DataFrame(self.coe_data)
                # 月内合约
                df_yuenei = self.yuenei.set_index(['t', 'n'])
                price_time = self.price_data.set_index('t')
                assert not df_con.index.duplicated().any(), "合约数据存在重复索引"
                start_time1 = datetime.datetime.now()  # 记录开始时间
                longterm_tmp, transfer_tmp = self.get_model(df_node, df_con, df_coe, price_time, df_yuenei)
                # 自己算con_tmp, df_con, transfer_tmp
                con_tmp = self.get_contmp(df_con.reset_index(), transfer_tmp)
                end_time1 = datetime.datetime.now()  # 记录结束时间
                total_time1 = end_time1 - start_time1
                print(f"\n{'=' * 50}")
                print(f"求解器计算用时: {total_time1}")
                print(f"{'=' * 50}\n")
                longterm_tmp['time'] = longterm_tmp['t'].map(lambda x: self.point_to_time(x))
                longterm_tmp['date'] = self.opt_date
                longterm_tmp['date_time'] = longterm_tmp['date'] + ' ' + longterm_tmp['time']
                del longterm_tmp['date']
                del longterm_tmp['time']
                transfer_tmp['time'] = transfer_tmp['t'].map(lambda x: self.point_to_time(x))
                transfer_tmp['date'] = self.opt_date
                transfer_tmp['date_time'] = transfer_tmp['date'] + ' ' + transfer_tmp['time']
                del transfer_tmp['date']
                del transfer_tmp['time']
                con_tmp['time'] = con_tmp['t'].map(lambda x: self.point_to_time(x))
                con_tmp['date'] = self.opt_date
                con_tmp['date_time'] = con_tmp['date'] + ' ' + con_tmp['time']
                del con_tmp['date']
                del con_tmp['time']
            except Exception as e:
                print(f"{time}运行出错，跳过.", e)
                longterm_tmp = pd.DataFrame(columns=['date_time', 'n', 'heyue_power_a', 'heyue_power_b', 'heyue_price_a', 's_dianneng'])
                transfer_tmp = pd.DataFrame(columns=['date_time', '转出站点', '转入站点', 'h', '转出电量'])
                con_tmp = pd.DataFrame(columns=['date_time', 'n', 'h', 'heyue_power_typea', 'heyue_price_typea'])
            if not longterm_tmp.empty:
                longterm_result = pd.concat([longterm_result, longterm_tmp])
            else:
                longterm_result = longterm_tmp.copy(deep=True)
            if not con_tmp.empty:
                con_result = pd.concat([con_result, con_tmp])
            else:
                con_result = con_tmp.copy(deep=True)
            if not transfer_tmp.empty:
                transfer_result = pd.concat([transfer_result, transfer_tmp])
            else:
                transfer_result = transfer_tmp.copy(deep=True)
            transfer_result = transfer_result.merge(self.gen_data[['n', 'node']], how='left', left_on='转出站点',
                                                    right_on='n')
            del transfer_result['n']
            del transfer_result['转出站点']
            transfer_result = transfer_result.rename(columns={'node': '转出站点'})
            transfer_result = transfer_result.merge(self.gen_data[['n', 'node']], how='left', left_on='转入站点',
                                                    right_on='n')
            del transfer_result['n']
            del transfer_result['转入站点']
            transfer_result = transfer_result.rename(columns={'node': '转入站点'})
            transfer_result = transfer_result.merge(self.type, how='left', on='h')
            del transfer_result['h']
            con_result = con_result.merge(self.gen_data[['n', 'node']], how='left', on='n')
            del con_result['n']
            con_result = con_result.merge(self.type, how='left', on='h')
            del con_result['h']
            longterm_result = longterm_result.merge(self.gen_data[['n', 'node']], how='left', on='n')
            del longterm_result['n']
            end_time = datetime.datetime.now()  # 记录结束时间
            total_time = end_time - start_time
            print(f"\n{'=' * 50}")
            print(f"数据处理总用时: {total_time}")
            print(f"{'=' * 50}\n")
            if json == True:
                longterm_result = longterm_result.to_dict('list')
                transfer_result = transfer_result.to_dict('list')
                con_result = con_result.to_dict('list')
                result = {}
                result["longterm_result"] = longterm_result
                result["transfer_result"] = transfer_result
                result["con_result"] = con_result
                if self.return_url['url'] == '':
                    pass
                else:
                    re = self.make_request_with_retry(result)
                return result
            else:
                return longterm_result, transfer_result, con_result


if __name__ == '__main__':
    gen_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_场站.xlsx')
    node_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_节点数据_4.xlsx').iloc[:, 1:].dropna()
    del node_data['价格']
    node_data['date'] = node_data['date_time'].map(lambda x: x.split(' ')[0])
    # print(node_data[node_data['date'] == '2025-04-01'].to_dict('list'))
    con_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_合约数据_4.xlsx')
    con_data['contract_power'] = con_data['contract_power'] / 4
    con_data = con_data[con_data['node'] != '风_国电龙源巴音1期']
    con_data['date'] = con_data['date_time'].map(lambda x: x.split(' ')[0])
    # print(con_data[con_data['date'] == '2025-04-01'].to_dict('list'))
    e_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_考核系数_4.xlsx')
    # 5、读其他价格
    o_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_其他电价_4.xlsx')
    # 6、读统一结算点电价
    p_data = pd.read_excel(r'D:\02file\2025\01蒙西\中长期交易/1_统一结算点电价.xlsx').iloc[:, 1:][['date_time', '价格预测值']]
    p_data = p_data.rename(columns={'价格预测值': 'price'})
    p_data['date'] = p_data['date_time'].map(lambda x: x.split(' ')[0])
    # print(p_data[p_data['date'] == '2025-04-01'].to_dict('list'))
    # 7、取4月的所有日期
    node_data['date'] = node_data['date_time'].map(lambda x: x.split(' ')[0])
    date_list = node_data['date'].drop_duplicates().to_list()
    use_all = []
    for date in date_list:
        print(date)
        n_data = node_data[node_data['date'] == date]
        pdata = p_data[p_data['date'] == date]
        cdata = con_data[con_data['date'] == date]
        opt = OptimizeMxContrace(gen_data=gen_data, node_data=n_data, price_data=pdata, contract_data=cdata,
                                 coe_data=e_data, lv_price=27, other_price=o_data, opt_date=date,
                                 points=96, limit_num=200, return_url={'url':'', 'authorization':''})
        feiyong, trans, con = opt.get_result()
        print(feiyong)
        print(trans)
        print(con)
        feiyong.to_excel(rf"D:\02file\2025\01蒙西\中长期交易/输出临时结果/0_tmp{date}费用.xlsx")
        trans.to_excel(rf"D:\02file\2025\01蒙西\中长期交易/输出临时结果/0_tmp{date}置换.xlsx")
        con.to_excel(rf"D:\02file\2025\01蒙西\中长期交易/输出临时结果/0_tmp{date}持仓.xlsx")

















