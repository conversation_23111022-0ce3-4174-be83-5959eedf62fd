"""
Author      :   z<PERSON><PERSON>i
Date        :   2024-11-20 12:02:45
Description :   湖北相似日电价预测依赖气象数据接口
"""

import os
import requests as rs
import pandas as pd
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase


class DataNwpHandlerHB(RequestHandlerBase):
    def put(self):
        # 参数解析
        params = j2o(self.request.body.decode())
        p_date = params.pop('p_date')

        # 请求数据地址
        url = f"http://**************:22880/project/nwp_HB/p_date_{p_date}_nwp_HB.xlsx"

        # 请求数据读取
        r = rs.get(url)
        path_save = 'tmp.xlsx'
        if r.status_code != 200:
            res_dict = {"message": f"数据获取失败, 请检查数据是否存在: {p_date}_nwp_HB.xlsx"}
        else:
            # 将文件内容保存到本地
            with open(path_save, 'wb') as file:
                file.write(r.content)

            # 读取Excel文件并整理
            df = pd.read_excel(path_save)
            res_dict = df.to_dict(orient='list')

            # 删除临时文件
            os.remove(path_save)
        self.write(res_dict)
