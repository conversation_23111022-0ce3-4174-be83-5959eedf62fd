# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-02-21 15:35:32
# Description: 售电公司日前市场负荷申报推荐方案，一次调用只支持一个售电公司

输入数据：要求传入历史30天数据
    1，history：历史数据
        date_time: 日期时间
        pre_rq:    日前价格预测值
        real_rq:   日前价格实际值
        pre_ss:    实时价格预测值
        real_ss:   实时价格实际值
    2，predictions: 申报日预测数据
        date_time: 日期时间
        pre_rq:    日前价格预测值
        pre_ss:    实时价格预测值
        pre_load:  负荷预测值
    3, limits: 负荷上下限限制
        default = [0.8, 1.2]
输出数据：
    1，分时刻推荐申报负荷
        date_time:    日期时间
        load_radical: 激进型申报负荷
        load_balance: 平衡型申报负荷
"""
import pandas as pd
import logging
logger = logging.getLogger()


class LoadDeclaration:
    def __init__(self, history, predictions, limits=None):
        logger.info("--------------------Start: 售电公司日前市场负荷申报推荐方案------------------")
        if limits is None:
            limits = [0.8, 1.2]
        self.gap = None     # 从传入数据中获取
        self.freq = None    # 从传入数据中获取
        self. history = self.data_process(pd.DataFrame(history))
        self.predictions = self.data_process(pd.DataFrame(predictions))
        self.limits = limits
        self.confidences = self._train()

    def data_process(self, data):
        """
        数据校验 + 预处理
        """
        # ① 数据预处理
        data = data.dropna()  # 删除空值
        if 'date_time' in data.columns:
            data = data.set_index('date_time')
        data = data.sort_index()  # 按日期升序排列

        # ② 数据验证
        data['date'] = data.index.map(lambda s: str(s).split(' ')[0])
        data['time'] = data.index.map(lambda s: str(s).split(' ')[1])
        self.freq = data['time'].nunique()
        self.gap = 1440.0 / self.freq
        if not (data.groupby('date').count() != self.freq).sum().sum() == 0:
            raise ValueError(f"'{data}' 的数据粒度不等于{self.freq}，请检查！")

        # ③ 新增字段
        data['timepoint'] = data.index.map(lambda s: int(int(str(s)[11:13]) * (60 / self.gap) + (int(str(s)[14:16]) / self.gap) + 1))
        data['pre_diff'] = data['pre_ss'] - data['pre_rq']
        data['type_p'] = data['pre_diff'].map(lambda s: -1 if s < 0 else 1)
        return data

    def _train(self):
        """
        功能：计算每个时间点的准确率
        """
        history = self.history
        history['real_diff'] = history['real_ss'] - history['real_rq']
        history['type_r'] = history['real_diff'].map(lambda s: -1 if s < 0 else 1)

        # 计算置信度
        confidences = []
        for timepoint in range(1, 97):
            x = history.loc[history["timepoint"] == timepoint, ["pre_rq"]]
            x11 = history.loc[(history["type_p"] == 1) & (history["type_r"] == 1) & (history["timepoint"] == timepoint), ["pre_rq"]].count()
            x_1_1 = history.loc[(history["type_p"] == -1) & (history["type_r"] == -1) & (history["timepoint"] == timepoint), ["pre_rq"]].count()
            confidence = (x11 + x_1_1) / x.count()
            confidences.append(confidence[0])
        return confidences

    def load_radical(self, x):
        """
        激进型
        """
        if x.type_p == -1:
            return x.pre_load * (1 - self.confidences[x.timepoint - 1] * (1 - self.limits[0]))
        else:
            return x.pre_load * (1 + self.confidences[x.timepoint - 1] * (self.limits[1] - 1))

    def load_balance(self, x):
        """
        平衡型
        """
        if x.type_p == -1:
            return x.pre_load * (1 - self.confidences[x.timepoint - 1] * self.confidences[x.timepoint - 1] * (1 - self.limits[0]))
        else:
            return x.pre_load * (1 + self.confidences[x.timepoint - 1] * self.confidences[x.timepoint - 1] * (self.limits[1] - 1))

    def predict(self, to_json=True):
        predictions = self.predictions
        # 激进型
        predictions['load_radical'] = predictions.apply(lambda s: self.load_radical(s), axis=1)
        # 平衡型
        predictions['load_balance'] = predictions.apply(lambda s: self.load_balance(s), axis=1)
        result = predictions.reset_index()[['date_time', 'load_radical', 'load_balance']]

        if to_json:
            result = result.to_dict('list')
            # result = {'date_time': predictions['date_time'].to_list(),
            #           'load_radical': predictions['load_radical'].to_list(),
            #           'load_balance': predictions['load_balance'].to_list()}
        logger.info(f'result: \n {pd.DataFrame(result)}')
        logger.info(f"-------------------- End: 售电公司日前市场负荷申报推荐方案 ------------------")
        return result


if __name__ == '__main__':
    # 1、历史日前、实时统一结算点，预测电价、实际电价;pre_rq real_rq pre_ss real_ss
    # 2、上限要求，下限要求；
    # 3、申报日日前、实时统一结算点预测电价、预测用电量；
    history_test = pd.read_excel(r"F:\ToGeek\20220221_交易策略封装\input.xlsx", sheet_name='history')
    predictions_test = pd.read_excel(r"F:\ToGeek\20220221_交易策略封装\input.xlsx", sheet_name='predictions')
    limits_test = [0.8, 1.2]

    p = LoadDeclaration(history=history_test, predictions=predictions_test, limits=limits_test)
    pred = p.predict(to_json=False)
    print(pred)










































