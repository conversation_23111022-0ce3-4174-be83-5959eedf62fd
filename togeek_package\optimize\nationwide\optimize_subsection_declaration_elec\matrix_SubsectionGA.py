"""
Author: <PERSON>y
Datetime: 2022/7/12/012 9:28
Info:
"""

"""
Author: Laney
Datetime: 2022/4/18/018 15:57
Info:套用scikit-opt中的GA遗传算法，将optimize_subsection_declare_profit方法改进，合成为同一个ga包
优化算法：多机组总发电量约束-单天
使用遗传算法求解分段报价：
    满足发电量上下限的要求
    同时收益最大（收入-成本） / 或 成本最小(煤耗最小)
"""

import numpy as np
import operator
import pandas as pd
from .base import SkoBase
from abc import ABCMeta, abstractmethod
from .operators import crossover, mutation, ranking, selection
import logging


logger = logging.getLogger()


class GeneratorInfoDaily:
    def __init__(self, generators, costs, prices, elecs, func_mode='profit',
                 subsection=10, max_iter=50, precision=1):
        logger.info("---------------多机组_单天_总发电量_分段报价----------------------")
        logger.info(f"generators:{generators}, costs:{costs}, prices:{prices}, elecs:{elecs}, func_mode={func_mode}")
        self.generators = pd.DataFrame(generators)    # 机组信息
        self.costs = pd.DataFrame(costs)              # 成本曲线
        self.prices = pd.DataFrame(prices)            # 节点电价
        self.elecs = pd.DataFrame(elecs)              # 发电量约束
        self.func_mode = func_mode                    # 目标函数的模式：cost - 成本最小， profit - 收益最大
        self.max_iter = max_iter                      # 遗传算法最大迭代次数
        self.precision = precision                    # 遗传算法精度

        self.subsection = subsection                  # 分段数量
        self.freqs = [24, 48, 96]                     # 每日的时间点的数量只能是这些数字
        self.freq = 96                                # 每日的时段数默认为96
        self.gap = 15                                 # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正

        self.min_elec = 0                             # 最低发电量（中长期电量）
        self.max_elec = 0                             # 最高发电量（目标发电量）

        self.lb = None                                # 遗传算法分段出力和报价的下限
        self.ub = None                                # 遗传算法分段出力和报价的上限
        self.n_dim = None                             # 遗传算法传入参数的维度
        msg = self._prepare_load()
        # print(msg)
        logger.info(msg)
        self._get_lb_ub()

    def _prepare_load(self):
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        self.generators['state'] = self.generators['state'].astype('int')
        for col in ['installed_capacity', 'min_power', 'min_step', 'upward_speed', 'beginning_power', 'lower_power', 'upper_power', 'auxiliary_power_ratio']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 prices 数据类型并排序
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['datetime'] = pd.to_datetime(self.prices['datetime'])
        self.prices['date'] = self.prices['datetime'].apply(lambda x: x.date())
        self.prices['time'] = self.prices['datetime'].apply(lambda x: x.time())
        self.prices['price'] = self.prices['price'].astype('float')
        self.prices.sort_values('datetime').reset_index(drop=True, inplace=True)  # 对价格数据按照时间先后进行排序

        # 修正 costs 数据类型
        self.costs['generator'] = self.costs['generator'].astype('str')
        self.costs['load_rate'] = self.costs['load_rate'].astype('float')
        self.costs['cost'] = self.costs['cost'].astype('float')

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、installed_capacity、min_power、min_step、upward_speed、state)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + hasnull['min_step'] + \
                   hasnull['upward_speed'] + hasnull['state']
        if num_null > 0:
            msg += "1.1, generator、installed_capacity、min_power、min_step、upward_speed、state中有" + str(num_null) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.1, generator、installed_capacity、min_power、min_step、upward_speed、state中没有空值；"

        # 2、检查发电量约束条件
        hasnull2 = self.elecs.isnull().sum()
        num_null2 = hasnull2['min_elec'] + hasnull2['max_elec']
        if num_null2 > 0:
            msg += "1.2, min_elec、max_elec中有" + str(num_null2) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.2, min_elec、max_elec中没有空值。"
        # 更新发电量约束
        self.min_elec = self.elecs['min_elec'][0]
        self.max_elec = self.elecs['max_elec'][0]

        # 3、检查机组状态，更新最低及最高发电量
        stop_generators = self.generators[self.generators['state'] == 0]
        if stop_generators.shape[0] >=1:
            msg += "1.3, 有" + str(len(stop_generators)) + "台机组(" + str(stop_generators['generator'].tolist()) + ")为停机状态;\n"
            # 修正最低及最高发电量
            self.min_elec = min(self.min_elec, sum(self.generators['min_power'] * self.generators['state'] * 24))
            self.max_elec = min(self.max_elec, sum(self.generators['installed_capacity'] * self.generators['state'] * 24))
            # 修正机组信息：去除停机状态（state=0）的机组，进行给出分段报价优化方案
            self.generators = self.generators[self.generators['state'] == 1].copy()
            self.generators = self.generators.reset_index(drop=True)
        else:
            msg += "1.3, 所有机组均为开机状态；\n"

        # 起始出力如果是空值则用最小出力代替；出力下限如果是空值则用最小出力代替；出力上限如果是空值，则用装机容量代替；
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['lower_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['upper_power'].fillna(self.generators['installed_capacity'], inplace=True)

        # 2、循环date，检查价格；
        generator = self.prices['generator'].unique()
        if set(self.generators['generator']) > set(generator):
            msg += "3, generators中有" + str(list(self.generators['generator'])) + "机组，prices表中有" + str(
                list(generator)) + "机组，机组信息不一致，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            for g in self.generators['generator']:
                prices = self.prices[self.prices['generator'] == g]
                try:
                    for dt in prices['date'].unique():
                        # 1、价格必须有值，行数只能是freq中的一种
                        price = prices[prices['date'] == dt].copy()
                        freq = price.shape[0]
                        if freq in self.freqs:
                            msg += "2.1, " + str(dt) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中;  "
                        else:
                            msg = msg + "2.1, " + str(dt) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中;  "
                            logger.info(msg)
                            raise Exception()
                        # 2、检测时间是不是从0点00到23点xx，否则不合格；
                        # gap为间隔分钟数，96为15,48为30,24为60
                        self.gap = 1440 / freq
                        price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
                        curtimes0 = list(range(freq))  # [0, 95]
                        curtimes1 = list(range(1, freq + 1))  # [1, 96]
                        if operator.eq(list(price['时间']), curtimes0):
                            msg += "2.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(self.gap) + " 分钟一个点，确认正确，数据验证通过；\n"
                        elif operator.eq(list(price['时间']), curtimes1) & freq == 96:
                            msg += "2.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(self.gap) + " 分钟一个点，确认正确，数据验证通过；\n"
                        elif operator.eq(list(price['时间']), curtimes1) & freq == 48:
                            msg += "2.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(self.gap) + " 分钟一个点，确认正确，数据验证通过；\n"
                        elif operator.eq(list(price['时间']), curtimes1) & freq == 24:
                            msg += "2.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(self.gap) + " 分钟一个点，确认正确，数据验证通过；\n"
                        else:
                            msg += "2.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                            logger.info(msg)
                            raise Exception()
                except:
                    msg += "2, 价格数据有异常，请检查传入的价格数据。"
                    logger.info(msg)
                    raise Exception()

        # 3、循环generator，检查变动成本
        generator = self.costs['generator'].unique()
        if set(self.generators['generator']) > set(generator):
            msg += "3, generators中有" + str(list(self.generators['generator'])) + "机组，costs表中有" + str(
                list(generator)) + "机组，机组信息不一致，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            for g in generator:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull3 = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null3 = hasnull3['generator'] + hasnull3['load_rate'] + hasnull3['cost']
                    if num_null3 > 0:
                        msg = msg + "3, 机组" + str(g) + "的generator、load_rate、cost中有" + str(num_null3) + "个空值，请检查传入数据。"
                        logger.info(msg)
                        raise Exception()
                    else:
                        msg = msg + "3, 机组" + str(g) + "的generator、load_rate、cost中没有空值,"
                except:
                    msg += "3, 机组" + str(g) + "的输入数据有异常。"
                    logger.info(msg)
                    raise Exception()
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。"
        return msg

    def _get_lb_ub(self):
        """
        根据传入的机组信息（机组数量、出力上下限、报价上下限），得到遗传算法需要传入参数的维度及上下限
        :return: None
        """
        self.lb = []  # 出力及报价的下限
        self.ub = []  # 出力及报价的上限

        for mp, ip in zip(self.generators['min_power'], self.generators['installed_capacity']):
            for s in range(self.subsection):
                self.lb.append(mp)
                self.ub.append(ip)
            self.lb.extend([0] * self.subsection)
            self.ub.extend([1500] * self.subsection)
        self.n_dim = len(self.ub)  # GA传入参数的维度

    def func(self, x):
        """
        根据func_mode判断目标函数是成本最小，还是收益最大模型，最终给出目标函数
        因为GA的目标是计算最小值，如果计算最大值时，需要转换为求负值的最小值
        :param x: 目标函数的参数，成本或收入
        :return: 目标函数
        """
        if self.func_mode == 'coal':      # 煤耗最低模型
            return np.sum(x, axis=1)
        elif self.func_mode == 'ecoal':   # 供电煤耗最低（度电煤耗最低）
            return x
        elif self.func_mode == 'cost':    # 成本最小模型
            return np.sum(x, axis=1)
        elif self.func_mode == 'profit':  # 收益最大模型
            return -np.sum(x, axis=1)

    # 约束条件：最小发电量约束
    def constraint_min_power(self, elec):
        return self.min_elec - sum(elec)

    # 约束条件：最大发电量约束
    def constraint_max_power(self, elec):
        return sum(elec) - self.max_elec


class GeneticAlgorithmBase(SkoBase, metaclass=ABCMeta):
    def __init__(self, func, n_dim, generator,
                 size_pop=500, max_iter=100, prob_mut=0.001,
                 constraint_eq=tuple(), constraint_ueq=tuple(), early_stop=None):
        self.func = func
        assert size_pop % 2 == 0, 'size_pop must be even integer'  # 种群大小必须是偶数
        self.size_pop = size_pop      # 种群大小
        self.max_iter = max_iter      # 最大迭代次数
        self.prob_mut = prob_mut      # 变异概率
        self.n_dim = n_dim            # 传入参数的维度
        self.early_stop = early_stop  # 提前停止迭代的次数
        self.generator = generator    # 机组信息实例对象

        # constraint:
        self.has_constraint = len(constraint_eq) > 0 or len(constraint_ueq) > 0
        self.constraint_eq = list(constraint_eq)    # a list of equal functions with ceq[i] = 0
        self.constraint_ueq = list(constraint_ueq)  # a list of unequal constraint functions with c[i] <= 0

        self.Chrom = None
        self.X = None      # shape = (size_pop, n_dim)
        self.Y_raw = None  # shape = (size_pop,) , value is f(x)
        self.Y = None      # shape = (size_pop,) , value is f(x) + penalty for constraint
        self.FitV = None   # shape = (size_pop,)

        self.generation_best_X = []   # 每一代最优的x
        self.generation_best_Y = []   # 每一代最优的y

        self.all_history_Y = []       # 所有的y
        self.all_history_FitV = []    # 所有的适应度

        self.best_x, self.best_y = None, None

    @abstractmethod
    def chrom2x(self, Chrom):
        pass

    def cal_xp_matrix(self):
        costs = np.zeros((self.size_pop, self.generator.freq))    # 成本
        profits = np.zeros((self.size_pop, self.generator.freq))  # 收益
        elecs = np.zeros((self.size_pop, self.generator.freq))    # 发电量
        coals = np.zeros((self.size_pop, self.generator.freq))      # 煤耗
        for g in range(self.generator.generators.shape[0]):
            gener = self.generator.generators['generator'][g]
            installed_capacity = self.generator.generators['installed_capacity'][g]  # 最大出力
            min_power = self.generator.generators['min_power'][g]  # 最小出力
            min_step = self.generator.generators['min_step'][g]    # 最小步长
            upward_speed = self.generator.generators['upward_speed'][g]  # 爬坡速率
            beginning_power = self.generator.generators['beginning_power'][g]  # 起始出力
            auxiliary_power_ratio = self.generator.generators['auxiliary_power_ratio'][g]  # 上网电量比例

            # 前一天的最后一个点的出力
            point_xa_power = max(min_power, beginning_power)
            price = self.generator.prices[self.generator.prices['generator'] == gener]['price'].copy()

            # 成本曲线拟合
            p2 = np.polyfit(self.generator.costs[self.generator.costs['generator'] == gener]['load_rate'],
                            self.generator.costs[self.generator.costs['generator'] == gener]['cost'], 2)

            freq = price.shape[0]
            gap = 1440 / freq
            num = self.generator.subsection

            # 选取机组对应的分段出力报价
            x = self.X[:, num * (2 * g): num * (2 * g + 1)]
            p = self.X[:, num * (2 * g + 1): num * (2 * g + 2)]
            x = np.insert(x, 0, min_power, 1)  # 第一段为最小出力值
            p = np.insert(p, 0, 0, 1)          # 第一段报价为0值
            x = x[:, :-1]
            p = p[:, :-1]

            # 1 考虑最小步长，规整到最小步长的间隔，间隔太小就减少分段，所以分段数可能小于subsection
            # 修正分段报价的步长 = 最小出力 + round（（分段出力 - 最小出力）/ 间隔）* 间隔
            # 间隔 = max{最小步长，一般为（装机容量-最小技术出力）* 5%， 给定间隔，一般为1，蒙西为装机容量*5%}
            # 修正后的出力和价格为x_c和p_c, 此时分段数量为subsection，当段数不符合时设置为与上一段的值相同

            x_c = min_power + np.around(np.around((x - min_power) / min_step) * min_step)
            x_c[:, -1] = installed_capacity  # 将最后一段修正为最大出力，也即装机容量

            x_diff = np.diff(x_c)
            x_diff = np.minimum(x_diff, 1)  # 将价格差大于0的值修改为1，0：与上一段的差值为0即相等，1：与上一段的差值大于0，即不相等

            p_diff = np.diff(p)
            p_diff = p_diff * x_diff  # 价差跟出力差（0或1）相乘，将不符合的段数的出力与价格更改为与前一段相同，从而保证矩阵的大小
            p_c = np.cumsum(p_diff, axis=1)
            p_c = np.insert(p_c, 0, 0, axis=1)  # 插入第一列的值0
            p_c = np.around(p_c, 2)

            # 2 模拟中标出力
            # 分段价格数据扩展维度为 size_pop * freq * subsection
            p3 = np.tile(p_c, freq)
            p3 = p3.reshape(self.size_pop, freq, num)

            # 日前价格扩展维度为 size_pop * freq * subsection
            ap = np.array(price.tolist())
            ap1 = np.tile(ap, (num, 1)).T
            ap3 = np.array([ap1] * self.size_pop)

            # 计算日前价格与实时价格的差异
            diff = ap3 - p3                # 日前价格-实际价格
            np.putmask(diff, diff > 0, 1)  # 将大于0的值设置为1
            diff2 = np.maximum(diff, 0)    # 将小于0的值设置为0

            # 计算中标出力的索引
            diff3 = diff2.sum(axis=2, dtype=int) - 1

            # 根据索引值，计算出力值 -- 矩阵计算
            mask = np.arange(self.size_pop)
            res = x_c[:, diff3]
            res = res[mask, mask]

            # 爬坡速率修正--该过程需要循环检查直至爬坡速率符合要求
            res_c = res.copy()
            while True:
                res_diff = np.diff(res_c)
                idx = np.where(abs(res_diff) > int(upward_speed))[1]
                if idx.size > 0:
                    num = min(idx) + 1
                    res_i = res_diff[:, :num]
                    res_i = np.maximum(res_i, -int(upward_speed))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                    res_i = np.minimum(res_i, int(upward_speed))   # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                    res_ci = np.insert(res_i, 0, point_xa_power, axis=1)
                    res_ci = np.cumsum(res_ci, axis=1)
                    res_c[:, :num + 1] = res_ci
                else:
                    break

            # 3 计算发电量, 成本/收入
            elec = res_c / (60 / gap)                                    # 发电量
            load_rate = res_c / installed_capacity                       # 负荷率
            cost = p2[0] * (load_rate ** 2) + p2[1] * load_rate + p2[2]  # 成本
            coal = cost * elec                                           # 煤耗
            ap2 = np.array([ap] * self.size_pop)  # 将日前价格维度扩充至 size_pop * freq
            # profit = (ap2 - cost) * elec                              # 不考虑厂用电率的收益
            profit = ap2 * elec * auxiliary_power_ratio - cost * elec   # 考虑厂用电率的收益

            coals += coal
            elecs += elec
            costs += cost
            profits += profit

        ecoals = np.sum(coals, axis=1) / np.sum(elecs, axis=1)

        if self.generator.func_mode == 'coal':     # 煤耗最低类型
            return coals, elecs
        elif self.generator.func_mode == 'ecoal':  # 供电煤耗最低（度电煤耗）
            return ecoals, elecs
        elif self.generator.func_mode == 'cost':   # 成本最小类型
            return costs, elecs
        elif self.generator.func_mode == 'profit':  # 收益最大类型
            return profits, elecs

    def cal_best_x_matrix(self, xp):
        """
        根据最后得到的最好染色体，计算得到分段出力和报价，并模拟中标出力情况
        :param xp: 最好的染色体 - self.best_X
        :return: 机组ID、时间、中标出力的列表，分段报价的结果
        """
        result = []
        generator_id = []  # 机组ID
        p_time = []        # 时刻
        xas = []           # 出力
        elecs = 0
        costs = 0
        profits = 0
        coals = 0
        for g in range(self.generator.generators.shape[0]):
            tmp_result = {}
            gener = self.generator.generators['generator'][g]
            installed_capacity = self.generator.generators['installed_capacity'][g]  # 最大出力
            min_power = self.generator.generators['min_power'][g]  # 最小出力
            min_step = self.generator.generators['min_step'][g]  # 最小步长
            upward_speed = self.generator.generators['upward_speed'][g]  # 爬坡速率
            beginning_power = self.generator.generators['beginning_power'][g]  # 起始出力
            auxiliary_power_ratio = self.generator.generators['auxiliary_power_ratio'][g]  # 上网电量比例

            # 前一天的最后一个点的出力
            point_xa_power = max(min_power, beginning_power)
            price = self.generator.prices[self.generator.prices['generator'] == gener]['price'].copy()
            time = self.generator.prices[self.generator.prices['generator'] == gener]['datetime'].copy()

            # 成本曲线拟合
            p2 = np.polyfit(self.generator.costs[self.generator.costs['generator'] == gener]['load_rate'],
                            self.generator.costs[self.generator.costs['generator'] == gener]['cost'], 2)

            freq = price.shape[0]
            gap = 1440 / freq
            num = self.generator.subsection

            # 修正第一段出力和报价
            x = np.insert(xp[num * (2 * g): num * (2 * g + 1)], 0, min_power)  # 第一段为最小出力值
            p = np.insert(xp[num * (2 * g + 1): num * (2 * g + 2)], 0, 0)      # 第一段报价为0值
            x = x[:-1]
            p = p[:-1]
            # 1 考虑最小步长，规整到最小步长的间隔，间隔太小就减少分段，所以分段数可能小于subsection
            # 修正分段报价的步长 = 最小出力 + round（（分段出力 - 最小出力）/ 间隔）* 间隔
            # 间隔 = max{最小步长，一般为（装机容量-最小技术出力）* 5%， 给定间隔，一般为1，蒙西为装机容量*5%}
            # 修正后的出力和价格为x_c和p_c, 此时分段数量为subsection，当段数不符合时设置为与上一段的值相同

            x_c = min_power + np.around(np.around((x - min_power) / min_step) * min_step)
            x_c[-1] = installed_capacity  # 将最后一段修正为最大出力，也即装机容量

            x_diff = np.diff(x_c)
            x_diff = np.minimum(x_diff, 1)  # 将价格差大于0的值修改为1，0：与上一段的差值为0即相等，1：与上一段的差值大于0，即不相等

            p_diff = np.diff(p)
            p_diff = p_diff * x_diff  # 价差跟出力差（0或1）相乘，将不符合的段数的出力与价格更改为与前一段相同，从而保证矩阵的大小
            p_c = np.cumsum(p_diff)
            p_c = np.insert(p_c, 0, 0)  # 插入第一列的值0
            p_c = np.around(p_c, 2)

            # 2 模拟中标出力
            # 分段价格数据扩展维度为 size_pop * freq * subsection
            p3 = np.tile(p_c, freq)
            p3 = p3.reshape(freq, num)

            # 日前价格扩展维度为 size_pop * freq * subsection
            ap = np.array(price.tolist())
            ap1 = np.tile(ap.reshape(-1, 1), num)
            # ap3 = np.array([ap1] * 1)

            # 计算日前价格与实时价格的差异
            diff = ap1 - p3  # 日前价格-实际价格
            np.putmask(diff, diff > 0, 1)  # 将大于0的值设置为1
            diff2 = np.maximum(diff, 0)    # 将小于0的值设置为0

            # 根据索引值，计算出力值 -- 矩阵计算
            diff3 = diff2.sum(axis=1, dtype=int) - 1  # 计算中标出力的索引
            res = x_c[diff3]

            # 爬坡速率修正--该过程需要循环检查直至爬坡速率符合要求
            res_c = res.copy()
            while True:
                res_diff = np.diff(res_c)
                idx = np.where(abs(res_diff) > int(upward_speed))[0]
                if idx.size > 0:
                    num = min(idx) + 1
                    res_i = res_diff[:num]
                    res_i = np.maximum(res_i, -int(upward_speed))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                    res_i = np.minimum(res_i, int(upward_speed))  # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                    res_ci = np.insert(res_i, 0, point_xa_power)
                    res_ci = np.cumsum(res_ci)
                    res_c[:num + 1] = res_ci
                else:
                    break

            # 3 计算发电量, 成本/收入
            elec = res_c / (60 / gap)                                    # 发电量
            load_rate = res_c / installed_capacity                       # 负荷率
            cost = p2[0] * (load_rate ** 2) + p2[1] * load_rate + p2[2]  # 成本
            coal = cost * elec                                           # 煤耗
            # profit = (ap - cost) * elec                                # 不考虑厂用电率的收益
            profit = ap * elec * auxiliary_power_ratio - cost * elec     # 考虑厂用电率的收益

            generator_id.extend([gener] * freq)  # 机组ID
            p_time.extend(time.tolist())  # 时刻
            xas.extend(list(res_c))  # 中标出力

            costs += cost.sum()
            elecs += elec.sum()
            profits += profit.sum()
            coals += coal.sum()

            tmp_result['generator'] = str(gener)
            tmp_result['power'] = str(np.unique(x_c))
            tmp_result['price'] = str(np.unique(p_c))
            # tmp_result['power'] = str(list(np.unique(x_c)))
            # tmp_result['price'] = str(list(np.unique(p_c)))
            tmp_result['ecoal'] = sum(coal) / sum(elec)
            tmp_result['coal'] = sum(coal)
            tmp_result['cost'] = sum(cost)
            tmp_result['profit'] = sum(profit)
            tmp_result['elec'] = sum(elec)
            result.append(tmp_result)

        ecoal = coals / elecs

        if self.generator.func_mode == 'coal':
            self.best_y = coals
        elif self.generator.func_mode == 'ecoal':
            self.best_y = ecoal
        elif self.generator.func_mode == 'cost':
            self.best_y = costs
        elif self.generator.func_mode == 'profit':
            self.best_y = profits
        return generator_id, p_time, xas, result

    def x2y_matrix(self):
        """
        根据种群中染色体对应的值计算目标函数的值
        :return:目标函数值
        """
        results, elecs = self.cal_xp_matrix()
        self.Y_raw = self.func(results)

        if not self.has_constraint:
            self.Y = self.Y_raw
        else:
            # constraint
            penalty_eq = np.array([np.sum(np.abs([c_i(elec) for c_i in self.constraint_eq])) for elec in elecs])  # 等式约束
            penalty_ueq = np.array(
                [np.sum(np.abs([max(0, c_i(elec)) for c_i in self.constraint_ueq])) for elec in elecs])  # 不等式约束
            self.Y = self.Y_raw + 1e5 * penalty_eq + 1e5 * penalty_ueq
        return self.Y

    def deal_result(self):
        """
        对得到的分段报价结果进行整理
        :return: 分段报价及中标出力
        """
        # generator_id, p_time, xas, subsection = self.cal_best_x(self.best_x)
        generator_id, p_time, xas, subsection = self.cal_best_x_matrix(self.best_x)
        res_power = pd.DataFrame({'generator': generator_id, 'datetime': p_time, 'power': xas})
        res_power = res_power.astype(str)
        return subsection, res_power

    @abstractmethod
    def ranking(self):
        pass

    @abstractmethod
    def selection(self):
        pass

    @abstractmethod
    def crossover(self):
        pass

    @abstractmethod
    def mutation(self):
        pass

    def run(self, max_iter=None):
        result = {}
        self.max_iter = max_iter or self.max_iter
        best = []
        for i in range(self.max_iter):
            print(f"第{i}次迭代---", time.time())
            self.X = self.chrom2x(self.Chrom)
            self.Y = self.x2y_matrix()  # 运行时间长

            self.ranking()
            self.selection()
            self.crossover()
            self.mutation()

            # record the best ones
            generation_best_index = self.FitV.argmax()
            self.generation_best_X.append(self.X[generation_best_index, :])
            self.generation_best_Y.append(self.Y[generation_best_index])
            self.all_history_Y.append(self.Y)
            self.all_history_FitV.append(self.FitV)

            if self.early_stop:     # 如果设置提前结束的迭代次数，当所有最好结果不在改变时，停止迭代
                best.append(min(self.generation_best_Y))
                if len(best) >= self.early_stop:
                    if best.count(min(best)) == len(best):
                        break
                    else:
                        best.pop(0)

        global_best_index = np.array(self.generation_best_Y).argmin()
        self.best_x = self.generation_best_X[global_best_index]
        subsection, res_power = self.deal_result()
        result['subsection_declaration'] = subsection
        result['bidded_power'] = res_power.to_dict('list')

        logger.info(f'result = {result}')
        logger.info("---------------发电量约束分段报价模型运行结束----------------------")
        return result

    fit = run


class GADaily(GeneticAlgorithmBase):
    """genetic algorithm

    Parameters
    ----------------
    func : function
        The func you want to do optimal
    n_dim : int
        number of variables of func
    lb : array_like
        The lower bound of every variables of func
    ub : array_like
        The upper bound of every variables of func
    constraint_eq : tuple
        equal constraint
    constraint_ueq : tuple
        unequal constraint
    precision : array_like
        The precision of every variables of func
    size_pop : int
        Size of population
    max_iter : int
        Max of iter
    prob_mut : float between 0 and 1
        Probability of mutation
    Attributes
    ----------------------
    Lind : array_like
         The num of genes of every variable of func（segments）
    generation_best_X : array_like. Size is max_iter.
        Best X of every generation
    generation_best_ranking : array_like. Size if max_iter.
        Best ranking of every generation
    Examples
    -------------
    https://github.com/guofei9987/scikit-opt/blob/master/examples/demo_ga.py
    """

    def __init__(self, func, n_dim, generator,
                 size_pop=500, max_iter=100,
                 prob_mut=0.001,
                 lb=-1, ub=1,
                 constraint_eq=tuple(), constraint_ueq=tuple(),
                 precision=1e-7, early_stop=None):
        super().__init__(func, n_dim, generator, size_pop, max_iter, prob_mut, constraint_eq, constraint_ueq, early_stop)

        self.lb, self.ub = np.array(lb) * np.ones(self.n_dim), np.array(ub) * np.ones(self.n_dim)
        self.precision = np.array(precision) * np.ones(self.n_dim)  # works when precision is int, float, list or array

        # Lind is the num of genes of every variable of func（segments）
        Lind_raw = np.log2((self.ub - self.lb) / self.precision + 1)
        self.Lind = np.ceil(Lind_raw).astype(int)

        # if precision is integer:
        # if Lind_raw is integer, which means the number of all possible value is 2**n, no need to modify
        # if Lind_raw is decimal, we need ub_extend to make the number equal to 2**n,
        self.int_mode_ = (self.precision % 1 == 0) & (Lind_raw % 1 != 0)
        self.int_mode = np.any(self.int_mode_)
        if self.int_mode:
            self.ub_extend = np.where(self.int_mode_
                                      , self.lb + (np.exp2(self.Lind) - 1) * self.precision
                                      , self.ub)

        self.len_chrom = sum(self.Lind)
        self.crtbp()  # 创建初始种群

    def crtbp(self):
        # create the population
        self.Chrom = np.random.randint(low=0, high=2, size=(self.size_pop, self.len_chrom))
        return self.Chrom

    def gray2rv(self, gray_code):
        # Gray Code to real value: one piece of a whole chromosome
        # input is a 2-dimensional numpy array of 0 and 1.
        # output is a 1-dimensional numpy array which convert every row of input into a real number.
        _, len_gray_code = gray_code.shape
        b = gray_code.cumsum(axis=1) % 2
        mask = np.logspace(start=1, stop=len_gray_code, base=0.5, num=len_gray_code)
        return (b * mask).sum(axis=1) / mask.sum()

    def chrom2x(self, Chrom):
        cumsum_len_segment = self.Lind.cumsum()
        X = np.zeros(shape=(self.size_pop, self.n_dim))
        for i, j in enumerate(cumsum_len_segment):
            if i == 0:
                Chrom_temp = Chrom[:, :cumsum_len_segment[0]]
            else:
                Chrom_temp = Chrom[:, cumsum_len_segment[i - 1]:cumsum_len_segment[i]]
            X[:, i] = self.gray2rv(Chrom_temp)

        # 将分段比例转化成递增
        num = self.generator.subsection
        for i in range(2 * len(self.generator.generators)):
            if i == 0:
                X[:, :num] = np.cumsum(X[:, :num], axis=1) / X[:, :num].sum(axis=1).reshape(-1, 1)
            else:
                X[:, i*num:(i+1)*num] = np.cumsum(X[:, i*num:(i+1)*num], axis=1) / X[:, i*num:(i+1)*num].sum(axis=1).reshape(-1, 1)

        # 转化到上下限区间内
        if self.int_mode:
            X = self.lb + (self.ub_extend - self.lb) * X
            X = np.where(X > self.ub, self.ub, X)
            # the ub may not obey precision, which is ok.
            # for example, if precision=2, lb=0, ub=5, then x can be 5
        else:
            X = self.lb + (self.ub - self.lb) * X
        return X

    ranking = ranking.ranking
    selection = selection.selection_tournament_faster
    crossover = crossover.crossover_2point_bit
    mutation = mutation.mutation


if __name__ == '__main__':
    import time
    t1 = time.time()

    # 机组基础信息
    # file = "input.xlsx"
    file = r"C:\Users\<USER>\Desktop\input13.xlsx"
    generators = pd.read_excel(file, sheet_name='generators', index_col=None)  # 机组信息
    costs = pd.read_excel(file, sheet_name='costs', index_col=None)            # 机组成本曲线
    prices = pd.read_excel(file, sheet_name='prices', index_col=None)          # 节点电价
    elecs = pd.read_excel(file, sheet_name='elecs', index_col=None)            # 发电量约束

    generator_info = GeneratorInfoDaily(generators, costs, prices, elecs, func_mode='profit')
    lb = generator_info.lb
    ub = generator_info.ub
    n_dim = generator_info.n_dim

    ga = GADaily(func=generator_info.func, n_dim=n_dim, generator=generator_info, size_pop=1000, max_iter=300, lb=lb, ub=ub,
            constraint_ueq=(generator_info.constraint_max_power, generator_info.constraint_min_power), precision=0.1, prob_mut=0.005)
    result = ga.run()
    print(ga.generation_best_Y)
    print(ga.best_y)
    print(time.time() - t1)

    writer = pd.ExcelWriter(r"C:\Users\<USER>\Desktop\result24.xlsx")
    pd.DataFrame({'y': ga.generation_best_Y}).to_excel(writer, sheet_name='y')  # 查看每代最优解，正常不输出
    pd.DataFrame(result['subsection_declaration']).to_excel(writer, sheet_name='subsection')
    pd.DataFrame(result['bidded_power']).to_excel(writer, sheet_name='power')
    writer.save()
