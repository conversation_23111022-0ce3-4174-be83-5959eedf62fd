# -*- coding: utf-8 -*-
# @Time    : 2022/11/18 18:21
# <AUTHOR> darlene
# @FileName: price_trans_provincial_handler.py
# @Software: PyCharm
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_price_trans_provincial import OptimizeProvincial


class OptimizeProvincialEvalHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        # areas, node_area, line_data, start, price, way=[1, 2, 3]
        areas = params.get('areas')
        node_area = params.get('node_area')
        line_data = params.get('line_data')
        start = params.get('start')
        price = params.get('price')
        way = params.get('way', [1, 2, 3])
        ptp = OptimizeProvincial(areas=areas, node_area=node_area, line_data=line_data, start=start, price=price, way=way)
        self.write(ptp.result)

