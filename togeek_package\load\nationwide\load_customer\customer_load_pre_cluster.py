# -*- coding:utf-8 -*-
# valid license

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from json import loads


class CustomerLoadPre:
    def __init__(self, load, load_all, pre_days=1, weight_user=0.8):
        self.load = load
        self.load_all = load_all
        self.pre_days = pre_days
        self.weight_user = weight_user

    def _prepare_load(self):
        if isinstance(self.load, str):
            self.load = loads(self.load)
        if isinstance(self.load, dict):
            self.load = pd.DataFrame(self.load)
            self.load['ds'] = pd.to_datetime(self.load['ds'])

        # 最大值大于第二大值的两倍以上，用上限替换
        Q1, Q3 = self.load['y'].quantile([0.25, 0.75])
        upper_limit = Q3 + 1.5 * (Q3 - Q1)
        if self.load['y'].max() > (self.load['y'].sort_values().values[-2] * 2):
            self.load['y'].replace(self.load['y'].max(), upper_limit, inplace=True)

        self.load['date'] = self.load['ds'].map(lambda s: str(s).split(' ')[0])
        self.load['time'] = self.load['ds'].map(lambda s: str(s).split(' ')[1])
        load_sum = self.load.groupby('date')['y'].sum().reset_index()
        load_mean = self.load.groupby('time')['y'].mean().reset_index()
        load_std = load_mean.copy()
        load_std['y'] = load_mean['y'].map(lambda s: s / load_mean['y'].max())

        return load_sum, load_std

    def get_dist(self, m, n):
        assert len(m) == len(n)
        dist = np.sqrt(np.sum(np.square(m - n)) / len(m))
        return dist

    def predict(self):
        load_sum, load_std = self._prepare_load()
        # 所有数据，处理极值
        centers = np.array([[0.17546312, 0.13375277, 0.12757777, 0.11033438, 0.10404288, 0.10147967, 0.09111033, 0.08738203, 0.08307119, 0.08586741, 0.07409997, 0.07281836, 0.07235232, 0.06920657, 0.07002214, 0.05417686, 0.04706979, 0.06000233, 0.06186648, 0.04823488, 0.04567168, 0.04369102, 0.04765234, 0.05580799, 0.05149715, 0.04788535, 0.0384481 , 0.0348363 , 0.03099149, 0.03087499, 0.0384481 , 0.0384481 , 0.09926599, 0.13340324, 0.12990796, 0.14703484, 0.15542351, 0.1378306 , 0.15169521, 0.1353839 , 0.15216125, 0.155307  , 0.16241407, 0.15880228, 0.16823954, 0.2123966 , 0.24851451, 0.22288244, 0.20773622, 0.2123966 , 0.30117674, 0.39450076, 0.49528137, 0.54701153, 0.67051148, 0.83700338, 0.92310381, 0.92892928, 0.97075615, 0.9615519 , 1.        , 0.98834906, 0.93929861, 0.95094955, 0.93125947, 0.89968542, 0.82616801, 0.76103926, 0.68915298, 0.55493417, 0.3850635 , 0.24373762, 0.16066643, 0.14109286, 0.12885937, 0.1366655 , 0.13014098, 0.14447163, 0.14377257, 0.13025749, 0.12035419, 0.10975184, 0.11895608, 0.12408249, 0.13153909, 0.14353955, 0.1639287 , 0.16590936, 0.20738669, 0.20494   , 0.23138763, 0.22346499, 0.22777584, 0.22754282, 0.20936735, 0.19573576],
                            [0.77962723, 0.81023888, 0.81874301, 0.83153967, 0.82921348, 0.82709587, 0.82585596, 0.82572607, 0.81533076, 0.81956815, 0.8236036 , 0.80697525, 0.82134572, 0.81664869, 0.81568672, 0.81905572, 0.80600499, 0.79625643, 0.7977296 , 0.78239302, 0.77269912, 0.77852628, 0.77935643, 0.7721708 , 0.74207471, 0.74477616, 0.73288288, 0.68733488, 0.66467947, 0.63340009, 0.65096605, 0.66353217, 0.69433268, 0.70981514, 0.73561018, 0.75752409, 0.78614697, 0.80021935, 0.79466805, 0.82493396, 0.83319554, 0.8545128 , 0.84997948, 0.85266728, 0.84706092, 0.85241191, 0.82076819, 0.81198116, 0.79966354, 0.80819653, 0.84027216, 0.84900653, 0.85727159, 0.86054976, 0.85844851, 0.86958351, 0.84474708, 0.84716582, 0.85128277, 0.84720477, 0.82313139, 0.80752422, 0.77788501, 0.7770624 , 0.77646369, 0.78647686, 0.80756647, 0.82784397, 0.84552012, 0.87078104, 0.86285997, 0.8900158 , 0.87762199, 0.8649879 , 0.85742865, 0.8434721 , 0.81107246, 0.78444728, 0.82556392, 0.88513129, 0.88824188, 0.89741477, 0.90298337, 0.89200693, 0.90407682, 0.91149656, 0.89959795, 0.89679402, 0.88046814, 0.89022367, 0.87915664, 0.87942615, 0.85647567, 0.84077099, 0.81866203, 0.80017615],
                            [0.28974619, 0.30095819, 0.28014288, 0.28433735, 0.2766585 , 0.27799227, 0.27488816, 0.26844181, 0.26918831, 0.27064002, 0.26902575, 0.2629241 , 0.26845194, 0.25592684, 0.26239034, 0.26804668, 0.25476253, 0.26432004, 0.2577131 , 0.26402415, 0.27089109, 0.25794261, 0.26535273, 0.26960268, 0.28414986, 0.29604583, 0.31673472, 0.37504463, 0.38034095, 0.40737624, 0.43512783, 0.50556166, 0.57921848, 0.63841491, 0.70151785, 0.7554652 , 0.79097028, 0.80254564, 0.82405634, 0.84487521, 0.88725839, 0.93690139, 0.93114102, 0.9426638 , 0.93585926, 0.89212065, 0.87053468, 0.83914144, 0.79767719, 0.78608143, 0.77701608, 0.78064816, 0.80858726, 0.83076292, 0.85123324, 0.87605432, 0.90807186, 0.9223165 , 0.91398832, 0.91821989, 0.92589804, 0.90960562, 0.91054771, 0.91587647, 0.89264492, 0.88988175, 0.86320943, 0.8245821 , 0.78538419, 0.76345375, 0.72416814, 0.69321624, 0.68338164, 0.66036749, 0.64812497, 0.62675635, 0.62278727, 0.61406849, 0.60833811, 0.58990209, 0.56995181, 0.5666333 , 0.55153877, 0.55061074, 0.49389247, 0.49679409, 0.47262851, 0.4223438 , 0.41973946, 0.39342135, 0.38550583, 0.37303851, 0.34342834, 0.33511779, 0.32117735, 0.30697245],
                            [0.60873686, 0.60913829, 0.54871315, 0.62003932, 0.5945549 , 0.61514788, 0.58250418, 0.56487155, 0.60397262, 0.57656487, 0.58088426, 0.60559166, 0.61919628, 0.59035757, 0.71820455, 0.74688155, 0.86767518, 0.80162103, 0.93919648, 0.87351925, 0.88292803, 0.86435419, 0.92028183, 0.86792312, 0.92856599, 0.88369448, 0.94472748, 0.85754821, 0.97352098, 0.84983434, 0.94202198, 0.8150311 , 0.80307268, 0.612212  , 0.47733632, 0.35966719, 0.28972574, 0.22022636, 0.22147258, 0.21431844, 0.1946809 , 0.1922687 , 0.19272896, 0.20562067, 0.17895737, 0.16888002, 0.16996647, 0.15966404, 0.15884843, 0.16295494, 0.12640267, 0.14354144, 0.14795547, 0.14627334, 0.1523491 , 0.15272382, 0.14313723, 0.17891571, 0.232192  , 0.56418798, 0.56804845, 0.56901987, 0.46791613, 0.54301724, 0.55333991, 0.58695298, 0.54732636, 0.59923406, 0.54065169, 0.62586582, 0.58441636, 0.69075616, 0.7780962 , 0.918611  , 0.80163027, 0.91196905, 0.80194617, 0.97458894, 0.82135746, 0.92342939, 0.84985268, 0.89515702, 0.85954006, 0.94134709, 0.8359671 , 0.88812098, 0.82929171, 0.83357179, 0.69353544, 0.72666611, 0.66147071, 0.64751104, 0.61468614, 0.65954563, 0.59191455, 0.60400827],
                            [0.49579046, 0.50638733, 0.57439066, 0.55861975, 0.58987054, 0.4028598 , 0.61731309, 0.40399885, 0.66968027, 0.40653223, 0.56902277, 0.59019154, 0.57608781, 0.4048812 , 0.51656995, 0.57903448, 0.52849837, 0.50472918, 0.54482246, 0.56528704, 0.45746662, 0.50795341, 0.70094692, 0.49030593, 0.57104872, 0.63661256, 0.67133808, 0.66383984, 0.5963069 , 0.75385358, 0.61022462, 0.6445132 , 0.71763293, 0.78647515, 0.7317009 , 0.6614421 , 0.81207256, 0.7650933 , 0.71171284, 0.84042584, 0.73715401, 0.69423857, 0.71933379, 0.78927546, 0.83675427, 0.67117251, 0.71933489, 0.73192737, 0.63959921, 0.67119923, 0.75084248, 0.65215407, 0.65196065, 0.69612517, 0.70777403, 0.73996437, 0.61666866, 0.74447981, 0.71430451, 0.63941669, 0.8168264 , 0.68083692, 0.66576411, 0.65961433, 0.69257575, 0.71922617, 0.67706787, 0.65214886, 0.6036093 , 0.63485724, 0.57216475, 0.7021427 , 0.60001924, 0.58455841, 0.62665584, 0.53508829, 0.65078767, 0.5606977 , 0.60419666, 0.52255608, 0.56412329, 0.63392728, 0.55231663, 0.56111054, 0.5278675 , 0.57672445, 0.52410142, 0.59255616, 0.60553677, 0.49140001, 0.51831494, 0.52683282, 0.62407696, 0.51989512, 0.53074011, 0.56427422],
                            [0.96635534, 0.89034058, 0.98289591, 1.        , 0.66316324, 0.66342587, 0.56612024, 0.65726044, 0.67186062, 0.83339835, 0.76666233, 0.88866281, 0.765826  , 0.90156481, 0.87463633, 0.88165339, 0.76105786, 0.74393848, 0.65615637, 0.41101311, 0.46375836, 0.46936028, 0.35604189, 0.32072455, 0.25650519, 0.22760061, 0.20321938, 0.13844416, 0.15386538, 0.15217486, 0.15301629, 0.16029088, 0.16141025, 0.20513939, 0.18550844, 0.23682325, 0.25113275, 0.27664864, 0.28983623, 0.2934799 , 0.34789781, 0.37453052, 0.35293878, 0.39192018, 0.95876457, 0.90885215, 0.97110562, 0.8959323 , 0.86257321, 0.98767425, 0.96661797, 0.93747116, 0.74982088, 0.68222047, 0.71839709, 0.74756175, 0.7716829 , 0.81824232, 0.81347418, 0.82525173, 0.75345945, 0.86312397, 0.75064956, 0.7742123 , 0.62584685, 0.63874121, 0.56133171, 0.54170332, 0.52206728, 0.5035506 , 0.44942846, 0.44969874, 0.31733331, 0.3389123 , 0.29938524, 0.32686448, 0.2920826 , 0.23825369, 0.22870722, 0.23655552, 0.19281363, 0.1900114 , 0.19897651, 0.20963979, 0.25115315, 0.32826942, 0.31005362, 0.33274688, 0.45169014, 0.56499068, 0.48451122, 0.54086698, 0.87155107, 0.86735154, 0.90549151, 0.82611101],
                            [0.42308565, 0.74838347, 0.60981184, 0.52451536, 0.47175375, 0.51771784, 0.45347774, 0.54658801, 0.35593533, 0.46360807, 0.40361688, 0.39315496, 0.32609182, 0.47789189, 0.39575328, 0.47631234, 0.27009049, 0.39829842, 0.29167874, 0.30311956, 0.26239029, 0.4219886 , 0.37955693, 0.2935301 , 0.23741834, 0.35557475, 0.29586337, 0.30612183, 0.2686617 , 0.40537044, 0.36472149, 0.35620559, 0.28318937, 0.4888021 , 0.3862148 , 0.39795211, 0.35749678, 0.58149679, 0.43193313, 0.42440562, 0.33399558, 0.50736734, 0.43980505, 0.43204677, 0.37787054, 0.56124021, 0.42495772, 0.41466843, 0.29912506, 0.41250236, 0.3509025 , 0.36086348, 0.41341728, 0.43593235, 0.41656105, 0.42270045, 0.34131456, 0.55800839, 0.44953666, 0.5727829 , 0.42987109, 0.57004481, 0.4798634 , 0.47803774, 0.3811387 , 0.56850787, 0.45012128, 0.57401917, 0.3817032 , 0.47948635, 0.3823591 , 0.37256418, 0.3380712 , 0.42968849, 0.45615767, 0.34236388, 0.26156808, 0.30358999, 0.22270612, 0.22224464, 0.23449666, 0.35135126, 0.48071321, 0.344476  , 0.32871846, 0.37749987, 0.35606913, 0.36079444, 0.34679359, 0.60323131, 0.43794062, 0.44188569, 0.35470747, 0.49916648, 0.41764461, 0.44716434],
                            [0.90693572, 0.88992948, 0.90920764, 0.89273475, 0.91201048, 0.88311926, 0.89503302, 0.89808751, 0.90270519, 0.87288851, 0.90880874, 0.8964508 , 0.89727773, 0.89261035, 0.88808399, 0.90446656, 0.90642221, 0.9012578 , 0.90047654, 0.90604657, 0.91373813, 0.90948734, 0.91366256, 0.89840784, 0.91496848, 0.91623213, 0.92613339, 0.91121578, 0.90436318, 0.92470857, 0.93767122, 0.94452489, 0.92971895, 0.94672575, 0.95402819, 0.95377121, 0.96918324, 0.9451371 , 0.95484405, 0.95304793, 0.95418168, 0.95982549, 0.94684069, 0.95572543, 0.956375  , 0.94261146, 0.93621954, 0.94406891, 0.93652991, 0.93067665, 0.93309848, 0.93506577, 0.94500169, 0.93923974, 0.93945031, 0.93250108, 0.9186383 , 0.93171918, 0.93076668, 0.92565299, 0.92087045, 0.93159124, 0.94402965, 0.94206396, 0.94585876, 0.93130546, 0.96220688, 0.95024153, 0.95788905, 0.93961042, 0.94199398, 0.93601429, 0.94391734, 0.93726834, 0.93072655, 0.93155662, 0.92588438, 0.9294309 , 0.92862359, 0.90628698, 0.92562637, 0.91630542, 0.92545038, 0.92055547, 0.91951337, 0.91529899, 0.91686645, 0.91023849, 0.88933992, 0.88852872, 0.89239006, 0.89068767, 0.89283815, 0.89088852, 0.90465902, 0.90866722]])
        # 计算用户与各聚类中心的距离
        dist_dict = {}
        for i in range(8):
            dist = self.get_dist(load_std['y'].values, centers[i])
            dist_dict[i] = dist
        user_type = min(dist_dict, key=dist_dict.get)

        # 将聚类中心组合至load_std，并计算比例
        load_std['center'] = centers[user_type].tolist()
        load_std['percent_y'] = load_std['y'].map(lambda s: s / load_std['y'].sum())
        load_std['percent_center'] = load_std['center'].map(lambda s: s / load_std['center'].sum())
        load_std['percent'] = load_std['percent_y'] * self.weight_user + load_std['percent_center'] * (1 - self.weight_user)

        # 计算全部所需数据
        date_last3 = load_sum['date'].tolist()[-3]
        date_last2 = load_sum['date'].tolist()[-2]
        date_last1 = load_sum['date'].tolist()[-1]
        date_pre1 = (datetime.strptime(date_last1, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        date_pre2 = (datetime.strptime(date_last1, '%Y-%m-%d') + timedelta(days=2)).strftime('%Y-%m-%d')
        date_pre3 = (datetime.strptime(date_last1, '%Y-%m-%d') + timedelta(days=3)).strftime('%Y-%m-%d')

        load_user_pre1 = load_sum['y'].tolist()[-1] * (self.load_all[date_pre1] / self.load_all[date_last1])
        load_user_pre2 = load_sum['y'].tolist()[-2] * (self.load_all[date_pre2] / self.load_all[date_last2])
        load_user_pre3 = load_sum['y'].tolist()[-3] * (self.load_all[date_pre3] / self.load_all[date_last3])

        load_pre1 = (load_std['percent'].values * load_user_pre1).tolist()
        load_pre2 = (load_std['percent'].values * load_user_pre2).tolist()
        load_pre3 = (load_std['percent'].values * load_user_pre3).tolist()

        # 计算不同预测天数的预测值，以列表形式保存预测值
        if self.pre_days == 1:
            load_pre = load_pre1
        elif self.pre_days == 2:
            load_pre = load_pre1 + load_pre2
        elif self.pre_days == 3:
            load_pre = load_pre1 + load_pre2 + load_pre3

        # 组合时间和预测值，输出结果
        pre_time = pd.date_range(start=date_pre1 + ' 00:00:00', periods=96 * self.pre_days, freq='15min')
        result_temp = pd.DataFrame(pre_time)
        result_temp.columns = ['ds']
        result_temp['load_pre'] = load_pre

        result = {'ds': result_temp['ds'].astype(str).tolist(),
                  'load_pre': result_temp['load_pre'].tolist()
                  }
        return result


if __name__ == "__main__":
    load = pd.read_pickle('train_data/train_PreLoad_cluster.pkl')
    load_all = {'2021-06-14': 2518152.5, '2021-06-15': 2510136.6, '2021-06-16': 2448045.2,
                '2021-06-17': 2449434.5, '2021-06-18': 2393666.8, '2021-06-19': 2337167.3}
    p = CustomerLoadPre(load, load_all, pre_days=1, weight_user=0.8)
    load_pre = p.predict()
    print(load_pre)
