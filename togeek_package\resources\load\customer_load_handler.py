'''
Author: your name
Date: 2021-11-01 14:54:58
LastEditTime: 2021-11-01 17:52:55
LastEditors: your name
Description: In User Settings Edit
FilePath: \togeek\togeek_package\togeek_package\resources\load\customer_load_handler.py
'''
# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.nationwide.load_customer import Prediction
from togeek_package.load.nationwide.load_customer import CustomerLoadPre
from togeek_package.load.nationwide.load_customer import CustomerLoadFasterPre


class CustomerLoadHander(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        load = params.pop('load')
        holiday = params.pop('holiday', None)
        pre_days = params.pop('pre_days', 1)
        point_day = params.pop("point_day",24)
        # freq = params.pop("freq", "H")
        cap = params.pop('cap', None)
        floor = params.pop('floor', None)
        include_history = params.pop('include_history', False)
        flexibility = params.pop('flexibility', 5)
        pred = Prediction(load, holiday=holiday, pre_days=pre_days, point_day =point_day, cap=cap, floor=floor,
                          include_history=include_history, flexibility=flexibility)
        self.write(pred.predict(to_json=True))


class CustomerLoadPreHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        load = params.pop('load')
        load_all = params.pop('load_all')
        pre_days = params.pop('pre_days', 1)
        weight_user = params.pop('weight_user', 0.8)

        pred = CustomerLoadPre(load=load, load_all=load_all, pre_days=pre_days, weight_user=weight_user)
        self.write(pred.predict())


class CustomerLoadFasterPreHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        load = params.pop('load')
        holiday = params.pop('holiday', None)
        pre_days = params.pop('pre_days', 1)
        point_day = params.pop("point_day", 24)
        cap = params.pop('cap', None)
        floor = params.pop('floor', None)
        include_history = params.pop('include_history', False)
        flexibility = params.pop('flexibility', 5)
        hist_model = params.pop('hist_model', None)
        pred = CustomerLoadFasterPre(load, holiday=holiday, pre_days=pre_days, point_day=point_day, cap=cap, floor=floor,
                          include_history=include_history, flexibility=flexibility, hist_model=hist_model)
        self.write(pred.predict(to_json=True))