# -*- coding: utf-8 -*-
# valid license

import os
from tglibs.config import ConfigProperty, ConfigBase, ConfigParser


class ModelConfig(ConfigBase):
    def __new__(cls, *args, **kwargs):
#       当前目录获取方式：os.path.join(os.path.dirname(__file__)
#       os.path.abspath进行路径拼接
        cls.filename = os.path.abspath(os.path.join(os.path.dirname(__file__), 'models.ini'))
        cp = ConfigParser()
        cp.read(cls.filename, encoding='utf8')

        props = {'tongyijiesuan_keys': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'tongyijiesuan_all_keys': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'tongyijiesuan_beijianshu': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'tongyijiesuan_jianshu': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'tongyijiesuan_jianshu_02': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'tongyijiesuan_jiashu': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_keys': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_all_keys': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_beijianshu': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_jianshu': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_jianshu_02': lambda: ConfigProperty(list, default='系统负荷预测'),
                 'jiedian_jiashu': lambda: ConfigProperty(list, default='系统负荷预测')}
        for k in cp['DEFAULT']:
            name = '_'.join(k.split('_')[:-1])
            prop = props.get(name)

            if prop:
                setattr(cls, k, prop())
        return object.__new__(cls)

    def __init__(self):
        super(ModelConfig, self).__init__(self.filename)
        self.load()

    @property
    def provinces(self):
        return {name.split('_')[-1] for name, _ in self.iter_prop() if name.startswith('tongyijiesuan_keys')}

    def get_tongyijiesuan_keys(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_keys_{}'.format(province))

    def get_tongyijiesuan_all_keys(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_all_keys_{}'.format(province))

    def get_jiedian_keys(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_keys_{}'.format(province))

    def get_jiedian_all_keys(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_all_keys_{}'.format(province))

    def get_tongyijiesuan_beijianshu(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_beijianshu_{}'.format(province))

    def get_jiedian_beijianshu(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_beijianshu_{}'.format(province))

    def get_tongyijiesuan_jianshu(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_jianshu_{}'.format(province))

    def get_tongyijiesuan_jianshu_02(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_jianshu_02_{}'.format(province))

    def get_jiedian_jianshu(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_jianshu_{}'.format(province))

    def get_jiedian_jianshu_02(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_jianshu_02_{}'.format(province))

    def get_tongyijiesuan_jiashu(self, province):
        assert province in self.provinces
        return getattr(self, 'tongyijiesuan_jiashu_{}'.format(province))

    def get_jiedian_jiashu(self, province):
        assert province in self.provinces
        return getattr(self, 'jiedian_jiashu_{}'.format(province))