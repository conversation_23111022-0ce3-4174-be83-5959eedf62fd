#gengyifang 2025.06.25

from .xiongan_energy_storage_optimization import EnergyStorageOptimizer
from .xiongan_load_predict_xgb import XGBLoadPredict
from .xiongan_solar_predict_xgb import XGBSolarPowerPredict
import pandas as pd


class XionganTowerPVStorageOptimization:
    '''
    读取传入的字典，将字典转换为dataframe，
    调用负荷预测、光伏预测、优化算法，输出优化策略字典
    '''
    def __init__(
        self,
        load_data=None,
        solar_data=None,
        weather_data=None,
        price_data=None,
        target_date=None,
        soc_initial=50
    ):
        """
        初始化调度系统

        :param load_data: dict, 如 {timestamp: load}
        :param solar_data: dict, 如 {timestamp: solar_power}
        :param weather_data: dict, 如 {timestamp: {t_2m, tcc_atmo, ...}}
        :param price_data: dict, 如 {timestamp: price}
        """

        self.load_data = load_data
        self.solar_data = solar_data
        self.weather_data = weather_data
        self.price_data = price_data
        self.target_date = target_date
        self.soc_initial = soc_initial



    def run(self):
        """
        执行完整的预测 + 优化流程
        :return: None
        """
        print("---------开始执行---------")
        # 读取负载历史数据，训练预测模型，预测目标日当天的负载功率
        load_predictor = XGBLoadPredict(load_data=self.load_data, weather_data=self.weather_data)
        load_data_predicted = load_predictor.run(D=self.target_date, test_days=1)
        print("---------预测负载功率完成---------")

        # 读取天气历史数据、光伏发电功率历史数据，训练光伏功率预测模型，预测目标日当天的光伏功率
        solar_predictor = XGBSolarPowerPredict(solar_data=self.solar_data, weather_data=self.weather_data)
        solar_data_predicted = solar_predictor.run(D=self.target_date, test_days=1)
        print("---------预测光伏功率完成---------")

        # 读取电价，根据预测的目标日的负载功率、光伏功率，输出优化策略
        optimizer = EnergyStorageOptimizer(load_data_predicted=load_data_predicted,
                                           solar_data_predicted=solar_data_predicted,
                                           price_data=self.price_data,
                                           soc_initial=self.soc_initial)
        result_dict = optimizer.solve()

        print("---------优化策略完成---------")
        # optimizer.plot_results()
        return result_dict


if __name__ == "__main__":
    import json
    import pandas as pd

    #------准备数据---------
    target_date = '2025-04-18'
    soc_initial = 50

    # 文件路径
    load_file = 'load_data_apifox.csv'
    weather_file = 'weather_data_apifox.csv'
    solar_file = 'solar_data_apifox.csv'
    price_file = 'price_data_apifox.csv'

    # 读取负荷数据
    load_df = pd.read_csv(load_file)
    load_data = {
        'timestamp': load_df['Timestamp'].tolist(),
        'load': load_df['Load'].astype(float).tolist()
    }

    # 读取天气数据
    weather_df = pd.read_csv(weather_file)
    weather_data = {
        'timestamp': weather_df['t_datetime_cst'].tolist()
    }
    for col in weather_df.columns:
        if col != 't_datetime_cst':
            weather_data[col] = weather_df[col].astype(float).tolist()

    # 读取光伏数据
    solar_df = pd.read_csv(solar_file)
    solar_data = {
        'timestamp': solar_df['Timestamp'].tolist(),
        'solar': solar_df['Solar'].astype(float).tolist()
    }

    # 读取价格数据
    price_df = pd.read_csv(price_file)
    price_data = {
        'timestamp': price_df['Timestamp'].tolist(),
        'price': price_df['Price'].astype(float).tolist()
    }

    #--------------运行模型------------
    dispatch = XionganTowerPVStorageOptimization(
        load_data=load_data,
        solar_data=solar_data,
        weather_data=weather_data,
        price_data=price_data,
        target_date=target_date,
        soc_initial=soc_initial
    )

    result_dict = dispatch.run()
    print("优化结果：", result_dict)

    # ====== 保存输入数据为 input_data.json ======
    input_data = {
        "target_date": target_date,
        "soc_initial": soc_initial,
        "load_data": load_data,
        "solar_data": solar_data,
        "weather_data": weather_data,
        "price_data": price_data
    }

    with open('input_data_apifox.json', 'w', encoding='utf-8') as f:
        json.dump(input_data, f, indent=4, ensure_ascii=False)
    print("✅ 输入数据已保存至 input_data_apifox.json")

    # ====== 保存输出结果为 output_result.json ======

    with open('output_result_apifox.json', 'w', encoding='utf-8') as f:
        json.dump(result_dict, f, indent=4, ensure_ascii=False)
    print("✅ 输出结果已保存至 output_result_apifox.json")