#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/8/16 10:05
# <AUTHOR> Darlene

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.guangdong.gd_subsetction_declaration_profit_common import GDSubsectionDeclarationProfit
from togeek_package.optimize.guangdong.gd_subsection_declaration_profit_v2 import GDSubsectionDeclarProfitTS
from togeek_package.optimize.guangdong.gd_subsection_profit_contract import SubsectionDeclarProfitGD


class GDGAProfitHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 10)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        is_start_zero = params.pop("is_start_zero", 0)
        func_mode = params.pop('func_mode', 'eprofit')
        model = GDSubsectionDeclarationProfit(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                            lower_price=lower_price, upper_price=upper_price,
                                            min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                            default_subsection=default_subsection, price_decimal=price_decimal,
                                            is_start_zero=is_start_zero, func_mode=func_mode)
        result = model.predict()
        self.write(result)


class GDGAProfitHandlerTS(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        contracts = params.pop('contracts')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 10)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        price_gap = params.pop('price_gap', 1)
        is_start_zero = params.pop("is_start_zero", 0)
        func_mode = params.pop('func_mode', 'eprofit')
        model = GDSubsectionDeclarProfitTS(generators=generators, prices=prices, contracts=contracts, costs=costs,
                                           constraints=constraints, lower_price=lower_price, upper_price=upper_price,
                                           min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                           default_subsection=default_subsection, price_decimal=price_decimal,
                                           is_start_zero=is_start_zero, func_mode=func_mode, price_gap=price_gap)
        result = model.predict()
        self.write(result)

class GAProfitHandlerGD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 3)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_gap = params.pop('price_gap', 1)
        price_decimal = params.pop('price_decimal', 3)
        is_start_zero = params.pop("is_start_zero", 0)
        min_gen = params.pop("min_gen", 0)
        model = SubsectionDeclarProfitGD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                           lower_price=lower_price, upper_price=upper_price,
                                           min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                           default_subsection=default_subsection, price_gap=price_gap,
                                           price_decimal=price_decimal, is_start_zero=is_start_zero, min_gen=min_gen)
        result = model.predict()
        self.write(result)
