#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/28 13:58
# <AUTHOR> Darlene

import pyomo.environ as pyo
from pyomo.opt import SolverFactory
import pandas as pd
import numpy as np
import logging
logger = logging.getLogger()

'''
  1、需要数据：场站个数，96点的中长期持仓电量*n，96点持仓均价*n，96点的申报电量*n，96点的可交易电量*n，
             节点价格，统一结算点价格
  2、变量：策略计算后的中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量，
  
  3、输出结果：策略计算后的中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量，转移电量
'''

class ReplacementMxXny:
    def __init__(self, num_n, num_m, longterm_data, node_price, general_price, points, high_line=1.1, low_line=0.85):
        logger.info("---------------------开始计算新能源中长期置换策略---------------------")
        self.num_n = num_n  # 总输入场站
        self.num_m = num_m  # 目标场站
        self.points = points  # 点数
        self.high_line = high_line   # 补偿上限
        self.low_line = low_line    # 补偿下限


        self.longterm_data = self.pro_data(longterm_data)  #中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量
        self.node_price = self.pro_data(node_price)   # 场站+节点价格
        self.general_price = self.pro_data(general_price)   # 统一结算点价格


    def pro_data(self, data):
        '''
        数据处理
        '''
        data = pd.DataFrame(data)
        # if 'date_time' not in data.index:
        #     data = data.reset_index()
        #     data = data.rename(columns={'index': 'date_time'})
        data['date_time'] = data['date_time'].astype(str)
        return data

    def check_data(self):
        '''
        1、首先检查中长期持仓电量、中长期持仓均价，申报电量，结算价格，可交易电量等数据
        2、检查场站和节点价格是否对应
        3、检查统一结算点数据是否完整
        '''
        res = True
        logger.info("检查中长期合约数据")
        long_columns = ['plant', 'date_time', 'position_b', 'poprice_b', 'declare_b', 'accept_b']
        for gen in self.num_n:
            gen_col = self.longterm_data[self.longterm_data['plant'] == gen].dropna(axis=1).columns.to_list()
            if set(long_columns) - set(gen_col):
                logger.info(f'场站{gen}中长期合约数据不完整，请检查！')
                # print(f'场站{gen}中长期合约数据不完整，请检查！')
                res = False
        logger.info("检查节点价格数据")
        for gen in self.num_n:
            gen_price = self.node_price[self.node_price['plant'] == gen].dropna()
            if gen_price.empty:
                logger.info(f'场站{gen}节点价格数据不完整，请检查！')
                # print(f'场站{gen}节点价格数据不完整，请检查！')
                res = False
        logger.info("检查统一结算点数据")
        date_1 = self.longterm_data['date_time'].dropna().drop_duplicates().to_list()   # 中长期的时间点
        date_2 = self.general_price['date_time'].dropna().drop_duplicates().to_list()    # 统一结算点时间点
        if set(date_1) - set(date_2):
            logger.info("统一结算点价格数据不完整，请检查!")
            # print("统一结算点价格数据不完整，请检查!")
            res = False
        return res

    def get_model(self, z_data, node_tmp, general, m_list, n_list):
        '''
        position_b 中长期持仓电量
        poprice_b 中长期持仓均价
        declare_b 申报电量
        accept_b 可交易电量
        node_price 节点价格
        general_price 统一结算点价格
        '''
        model = pyo.ConcreteModel()
        model.N = pyo.RangeSet(len(n_list))
        model.M = m_list
        model.position_b = z_data['position_b'].to_dict()
        model.poprice_b = z_data['poprice_b'].to_dict()
        model.declare_b = z_data['declare_b'].to_dict()
        model.accept_b = z_data['accept_b'].to_dict()
        model.node_price = node_tmp['price'].to_dict()
        model.general_price = general

        model.position_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.poprice_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.declare_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.accept_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.jiesuan_a = pyo.Var(model.N, within=pyo.NonNegativeReals)

        # 初始化未知
        model.transfer_a = pyo.Var(model.N, model.N, within=pyo.NonNegativeReals)

        # 构造约束
        # 转入
        def rule_in(model, n):
            q_trans = sum(model.transfer_a[i, n] for i in model.N)
            return q_trans <= model.accept_b[n]
        model.rule_in = pyo.Constraint(model.N, rule=rule_in)

        # 转出
        def rule_out(model, n):
            c_trans = sum(model.transfer_a[n, i] for i in model.N)
            return c_trans <= model.position_b[n]
        model.rule_out = pyo.Constraint(model.N, rule=rule_out)

        # 可交易电量
        def rule_accept(model, n):
            return model.accept_a[n] == model.accept_b[n] - sum(model.transfer_a[i, n] for i in model.N) + sum(model.transfer_a[n, i] for i in model.N)
        model.rule_aaept = pyo.Constraint(model.N, rule=rule_accept)


        #  持仓电量
        def rule_chicang(model, n):
            return model.position_a[n] == model.position_b[n] - sum(model.transfer_a[n, i] for i in model.N) + sum(model.transfer_a[i, n] for i in model.N)
        model.rule_chicang = pyo.Constraint(model.N, rule=rule_chicang)

        # 持仓均价
        def rule_junjia(model, n):
            return model.poprice_a[n] == (model.position_b[n] * model.poprice_b[n] + sum(model.transfer_a[i, n] for i in model.N)) / model.declare_b[n]
        model.rule_junjia = pyo.Constraint(model.N, rule=rule_junjia)

        # 对角线
        def rule_diagonal(model, n):
            return model.transfer_a[n, n] == 0
        model.rule_diagonal = pyo.Constraint(model.N, rule=rule_diagonal)

        # 结算
        def rule_jiesuan(model, n):
            return model.jiesuan_a[n] == (model.position_a[n] * (model.poprice_a[n] - model.general_price) + model.declare_b[n] * model.node_price[n]) / model.declare_b[n]

        model.rule_jiesuan = pyo.Constraint(model.N, rule=rule_jiesuan)
        # 目标函数
        def obj_rule(model):
            obj_fun = sum(model.declare_b[m] * model.jiesuan_a[m] for m in model.M)
            return obj_fun
        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)
        # 求解问题
        solver = SolverFactory('ipopt')
        sol = solver.solve(model)
        if sol['Solver'][0]['Status'] != 'error':
            logger.info('求解成功！')
            longterm_result = pd.DataFrame()
            # 结果处理
            # 1、处理中长期持仓数据
            position_r = []
            poprice_r = []
            declare_r = []
            accept_r = []
            jiesuan_r = []
            for n in model.N:
                position_r.append(float(round(pyo.value(model.position_a[n]), 2)))
                poprice_r.append(float(round(pyo.value(model.poprice_a[n]), 2)))
                declare_r.append(float(round(pyo.value(model.declare_b[n]), 2)))
                accept_r.append(float(round(pyo.value(model.accept_a[n]), 2)))
                jiesuan_r.append(float(round(pyo.value(model.jiesuan_a[n]), 2)))
            longterm_result['id'] = n_list
            longterm_result['position'] = position_r
            longterm_result['poprice'] = poprice_r
            longterm_result['declare'] = declare_r
            longterm_result['accept'] = accept_r
            longterm_result['jiesuan'] = jiesuan_r
            transfer_result = pd.DataFrame()
            # 2、处理转让数据
            out_list = []
            out_id = []
            in_id = []
            for i in model.N:
                for j in model.N:
                    if i != j:
                        out_list.append(float(round(pyo.value(model.transfer_a[i, j]), 2)))
                        out_id.append(i)
                        in_id.append(j)
            transfer_result['转出站点'] = out_id
            transfer_result['转入站点'] = in_id
            transfer_result['转出电量'] = out_list
        else:
            longterm_result = pd.DataFrame()
            transfer_result = pd.DataFrame()
        return longterm_result, transfer_result


    def get_result(self, json):
        '''
        1、检查数据
        2、构造模型
        '''
        result = {}
        res = self.check_data()
        if not res:
            return result
        else:
            time_list = self.longterm_data['date_time'].dropna().drop_duplicates().to_list()
            n_data = pd.DataFrame()
            n_data['plant'] = self.num_n
            n_data.index = n_data.index + 1
            # n_data = n_data.reset_index()
            n_data['id'] = n_data.index
            m_data = n_data[n_data['plant'].isin(self.num_m)]
            longterm_data = self.longterm_data.merge(n_data, how='left', on='plant')
            node_data = self.node_price.merge(n_data, how='left', on='plant')
            n_list = n_data['id'].drop_duplicates().to_list()
            m_list = m_data['id'].drop_duplicates().to_list()
            longterm_result = pd.DataFrame()
            transfer_result = pd.DataFrame()
            for time in time_list:
                print(time)
                # position_b, poprice_b, declare_b, accept_b, node_price, general_price\
                # 取出这一时刻中长期数据
                z_data = longterm_data[longterm_data['date_time'] == time].set_index('id')
                node_tmp = node_data[node_data['date_time'] == time][['id', 'price']].set_index('id')
                general = self.general_price[self.general_price['date_time'] == time]['price'].values[0]
                longterm_tmp, transfer_tmp = self.get_model(z_data, node_tmp, general, m_list, n_list)
                longterm_tmp['date_time'] = time
                transfer_tmp['date_time'] = time
                if longterm_result.empty:
                    longterm_result = longterm_tmp.copy(deep=True)
                else:
                    longterm_result = pd.concat([longterm_result, longterm_tmp])
                if transfer_result.empty:
                    transfer_result = transfer_tmp.copy(deep=True)
                else:
                    transfer_result = pd.concat([transfer_result, transfer_tmp])
            longterm_result = longterm_result.merge(n_data, how='left', on='id')
            del longterm_result['id']
            transfer_result = transfer_result.merge(n_data, how='left', left_on='转出站点', right_on='id')
            del transfer_result['id']
            del transfer_result['转出站点']
            transfer_result = transfer_result.rename(columns={'plant': '转出站点'})
            transfer_result = transfer_result.merge(n_data, how='left', left_on='转入站点', right_on='id')
            del transfer_result['id']
            del transfer_result['转入站点']
            transfer_result = transfer_result.rename(columns={'plant': '转入站点'})
            # del longterm_result['id']
            if json == True:
                longterm_result = longterm_result.to_dict('list')
                transfer_result = transfer_result.to_dict('list')
            result['longterm_result'] = longterm_result
            result['transfer_result'] = transfer_result
            return result






if __name__ == '__main__':
    # longterm_data, node_price, general_price
    longterm_data = pd.read_excel(r"D:\02file\2023\05蒙西数据\中长期置换交易.xlsx", sheet_name='longterm_data')
    longterm_data['date_time'] = longterm_data['date_time'].astype(str)
    node_price = pd.read_excel(r"D:\02file\2023\05蒙西数据\中长期置换交易.xlsx", sheet_name='node_price')
    node_price['date_time'] = node_price['date_time'].astype(str)
    general_price = pd.read_excel(r"D:\02file\2023\05蒙西数据\中长期置换交易.xlsx", sheet_name='general_price')
    general_price['date_time'] = general_price['date_time'].astype(str)

    num_n = [1, 2, 3]
    num_m = [1, 2, 3]
    rep = ReplacementMxXny(num_n=num_n, num_m=num_m, longterm_data=longterm_data, node_price=node_price, general_price=general_price,
                           points=96)
    result = rep.get_result(json=False)
    print(result['longterm_result'])
    print(result['transfer_result'])
    # print(general_price.to_dict('list'))

