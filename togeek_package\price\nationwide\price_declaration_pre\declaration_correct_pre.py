"""
Author: Laney
Datetime: 2022/12/5/005 14:03
Info:
"""

import datetime
import numpy as np
import pandas as pd
from scipy.spatial import distance
from scipy.stats import pearsonr
import logging
import warnings


warnings.filterwarnings('ignore')
logger = logging.getLogger()


class DeclarationCorrect:
    def __init__(self, source_data, date_list, is_sim, sim_method, sim_cal_col, sim_type, sim_period, correct_col,
                 correct_period=4, correct_quantile=(0.75, 0.75), min_price=0, max_price=1500):  # periods-从历史多长时间中找相似日, 默认从历史7天中查找相似日
        self.source_data = self.pre_source(pd.DataFrame(source_data))
        self.date_list = date_list      # 预测日列表
        self.is_sim = is_sim            # 是否使用相似日
        self.sim_method = sim_method    # 相似日计算方式：1-最大最小值计算；0-96点计算
        if sim_cal_col == '竞价空间':
            self.sim_cal_col = 'bidding_space'  # 计算相似日采用的字段【竞价空间】
        elif sim_cal_col == '负荷率':
            self.sim_cal_col = 'load_ratio'  # 计算相似日采用的字段【负荷率】
        else:
            raise Exception(f"不支持使用{sim_cal_col}字段查找相似日")
        self.sim_type = sim_type        # 96点计算相似日时，采用的计算方法，0-cosine, 1-cityblock, 2-euclidean, 3-pearson
        self.sim_period = sim_period    # 相似日使用的历史日期长度
        if correct_col == '竞价空间':
            self.correct_col = 'bidding_space'  # 0值修正的参考字段-竞价空间
        elif correct_col == '负荷率':
            self.correct_col = 'load_ratio'     # 0值修正的参考字段-负荷率
        else:
            raise Exception(f"不支持使用{sim_cal_col}字段进行0值修正")
        self.correct_period = correct_period      # 修正使用的历史日期长度
        self.correct_quantile = correct_quantile  # 修正0值时的参考分位数，【0-7点，7-18点】的分位数
        self.min_price = min_price          # 电价下限
        self.max_price = max_price          # 电价上限

    def pre_source(self, source_data):
        """
        数据预处理
        :param source_data: 传入数据
        :return: dataframe, 处理后的基础数据
        """
        source_data = source_data.drop_duplicates()
        source_data.fillna(0, inplace=True)
        source_data['日期'] = source_data['date_time'].map(lambda s: str(s)[0:10])
        source_data['time'] = source_data['date_time'].map(lambda s: str(s).split(' ')[-1])
        source_data['hour'] = source_data['time'].map(lambda s: int(s.split(':')[0]))
        source_data['week'] = pd.to_datetime(source_data['日期']).dt.weekday.map(lambda s: int(s) + 1)
        source_data['时间'] = source_data['date_time'].map(lambda s: int(int(str(s)[11:13])*4+(int(str(s)[14:16])/15)+1))
        if 'bidding_space' not in source_data.columns:
            source_data['bidding_space'] = source_data['load_forecast'] - source_data['load_forecast_energy'] - source_data['load_forecast_delivery']
        source_data['load_ratio'] = source_data['bidding_space'] / source_data['installed_capacity']
        return source_data

    def acc(self, pred, real):
        predlist = pred.tolist()
        reallist = real.tolist()
        x = np.abs(np.array(predlist) - np.array(reallist)).sum() / real.sum()
        return 1 - x

    def select_similar_date(self, data, pred_date):
        """
        计算与预测日期(pred_date)的竞价空间/负荷率相似的日期
        :param data: 负荷预测数据
        :param pred_date: 预测日期
        :return: 相似日期
        """
        y, m, d = pred_date.split("-")
        com_start_date = str(datetime.date(int(y), int(m), int(d)) - datetime.timedelta(self.sim_period))
        hist_load = data[data['date_time'] >= com_start_date]

        # 通过“竞价空间”或“负荷率”选择相似日
        hist_load = hist_load.loc[hist_load['date_time'] < pred_date, ["date_time", self.sim_cal_col]]
        hist_load['date_time'] = pd.to_datetime(hist_load['date_time'])

        com_data = self.source_data.loc[self.source_data['日期'] == pred_date]  # 预测日

        if self.sim_method == 1:
            # 只比较每天的最大、最小竞价空间/负荷率
            hist_load.set_index('date_time', drop=True, inplace=True)
            hist_load = hist_load.resample('D').agg([min, max]).reset_index()
            hist_load.columns = ['date_time', self.sim_cal_col + '_min', self.sim_cal_col + '_max']

            pred_bd_max = com_data[self.sim_cal_col].values.max()  # 预测日的竞价空间/负荷率最大值
            pred_bd_min = com_data[self.sim_cal_col].values.min()  # 预测日的竞价空间/负荷率最小值

            hist_load["max_diff"] = abs(hist_load[self.sim_cal_col + '_max'] - pred_bd_max)
            hist_load["min_diff"] = abs(hist_load[self.sim_cal_col + '_min'] - pred_bd_min)
            hist_load['total_diff'] = hist_load["max_diff"] + hist_load["min_diff"]

            hist_load.sort_values('total_diff', inplace=True)
            hist_load['date_time'] = hist_load['date_time'].astype(str)
            similar_date = str(hist_load['date_time'].values[0])
        else:
            # 考虑全天的竞价空间/负荷率
            hist_load['date'] = hist_load['date_time'].astype(str).map(lambda s: s.split(' ')[0]).values
            hist_load['time'] = hist_load['date_time'].astype(str).map(lambda s: s.split(' ')[1]).values
            hist_ = hist_load.pivot(index='time', columns='date', values=self.sim_cal_col)
            # 空值填充
            hist_ = hist_.fillna(method="ffill").fillna(method='bfill')
            try:
                hist_date_lst = hist_.columns.to_list()
                tmp = []
                v = com_data[self.sim_cal_col].values
                for i, date in enumerate(hist_date_lst):
                    dis_lst = {'date': date}
                    u = hist_.loc[:, date].values
                    # 余弦距离
                    dis_lst['cosine'] = distance.cosine(u, v)
                    # 曼哈顿距离
                    dis_lst['cityblock'] = distance.cityblock(u, v)
                    # 欧氏距离
                    dis_lst['euclidean'] = distance.euclidean(u, v)
                    # 皮尔逊距离
                    dis_lst['pearson'] = 1 - pearsonr(u, v)[0]
                    tmp.append(dis_lst)
                res_ = pd.DataFrame(tmp)
                cols = ['cosine', 'cityblock', 'euclidean', 'pearson']
                res_ = res_.sort_values(cols[self.sim_type])
                similar_date = res_['date'].values[0]
            except Exception as e:
                raise Exception(e)
        return similar_date

    def fitting_price(self, curdata, curdatab):
        """
        竞价空间拟合算法，直接使用相似日的数据
        :param history_date: 相似日
        :param rdate: 预测日
        :return: 拟合结果
        """
        tmp_data = pd.DataFrame(columns=["date_time", "hour", "pred", "type", 'bidding_space'])

        # 多项式拟合
        parameter1 = np.polyfit(curdata['bidding_space'], curdata['price'], 1)
        p1 = np.poly1d(parameter1)
        parameter3 = np.polyfit(curdata['bidding_space'], curdata['price'], 3)
        p3 = np.poly1d(parameter3)

        p = p1
        if parameter3[0] > 0:
            delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
            if delta < 0:
                p = p3
            else:
                x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                y1 = p3(x1)
                y2 = p3(x2)
                if x1 > 0 and y1 < y2 * 1.2:
                    p = p3

        pre_data = np.maximum(p(curdatab['bidding_space']), 0)

        # 修正价格上下限
        pre_data = np.maximum(pre_data, self.min_price)
        pre_data = np.minimum(pre_data, self.max_price)

        tmp_data['date_time'] = curdatab['日期'] + ' ' + curdatab['time']
        tmp_data['hour'] = curdatab['time'].map(lambda s: int(s.split(':')[0]))
        tmp_data['pred'] = list(pre_data)
        tmp_data['type'] = [3] * curdatab.shape[0]
        tmp_data['bidding_space'] = curdatab['bidding_space']
        tmp_data['load_ratio'] = curdatab['load_ratio']
        return tmp_data

    def pred_price(self, to_json=False):
        logger.info("----------------三段拟合修正法预测出清电价开始-------------------")
        result = pd.DataFrame(columns=["date_time", "pred", "type"])
        for rdate in self.date_list:
            y, m, d = rdate.split("-")
            start_date = str(datetime.date(int(y), int(m), int(d)) - datetime.timedelta(self.correct_period))
            history_data = self.source_data[self.source_data['日期'] < rdate].dropna()

            if self.is_sim == 1:
                history_date = self.select_similar_date(history_data, rdate)
            else:
                history_date = history_data[history_data['日期'] < rdate].dropna()['日期'].max()
            logger.info(f'history_date: {history_date}')

            curdata = self.source_data[self.source_data['日期'] == history_date]
            curdatab = self.source_data[self.source_data['日期'] == rdate]

            # 模型拟合
            result_tmp = self.fitting_price(curdata, curdatab)

            # 极值修正
            correct_data = history_data[history_data['日期'] >= start_date]

            # 最小值修正
            train_zero = correct_data[correct_data['price'] == self.min_price]
            train_zero_01 = train_zero[(train_zero['hour'] >= 0) & (train_zero['hour'] <= 7)]
            train_zero_02 = train_zero[(train_zero['hour'] > 7) & (train_zero['hour'] <= 18)]

            threshold_value_01 = train_zero_01[self.correct_col].quantile(self.correct_quantile[0])
            threshold_value_02 = train_zero_02[self.correct_col].quantile(self.correct_quantile[1])

            if threshold_value_01 != np.NaN:
                result_tmp.loc[
                    ((result_tmp['hour'] <= 7) & (result_tmp[self.correct_col] <= threshold_value_01)), 'pred'] = self.min_price
            if threshold_value_02 != np.NaN:
                result_tmp.loc[((result_tmp['hour'] > 7) & (result_tmp['hour'] <= 18) & (
                            result_tmp[self.correct_col] <= threshold_value_02)), 'pred'] = self.min_price
            # result_tmp.loc[result_tmp['load_ratio'] <= 0.4, 'pred'] = self.min_price  # 山西负荷率低于0.4时，电价容易出现0价

            # 最高价格修正
            train_1500 = history_data[history_data['price'] == self.max_price]
            train_1500_03 = train_1500[train_1500['hour'] > 18]
            threshold_value_03 = train_1500_03[self.correct_col].quantile(0.9)
            result_tmp.loc[(result_tmp['hour'] > 18) & (result_tmp[self.correct_col] > threshold_value_03), 'pred'] = self.max_price

            result_tmp = result_tmp[["date_time", "pred", "type"]]
            result = pd.concat([result, result_tmp], axis=0)

        if to_json:
            result = result.to_dict('list')
        logger.info(f'result = {result}')
        logger.info("-----------------三段拟合修正法预测出清电价结束-------------------")
        return result


if __name__ == '__main__':
    # 南方电网测试
    data = pd.read_excel(r"C:\Users\<USER>\Desktop\nw_price_input.xlsx")
    # data['date'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[0])
    sim_type = 1
    correct_col = "负荷率"
    date_list = ["2024-11-09"]
    sim_cal_col = "竞价空间"
    sim_period = 7
    is_sim = 0
    sim_method = 1

    # p = DeclarationCorrect(data, date_list, is_sim, sim_method, sim_cal_col, sim_type, sim_period, correct_col)
    print(data.to_dict('list'))
    # tmp = p.pred_price()
    # print(tmp)

    # 历史
    # data = pd.read_excel(r"D:\ToGeek\work\10 京能\2_极值修正\负荷预测数据.xlsx")
    # data['date'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[0])
    # date_ = datetime.date(2022, 1, 1)
    #
    # correct_col = "负荷率"      # 极小值修正参考列
    # method = [1, 0, 0, 0, 0]   # 相似日计算方法， 1-最大最小值，0-96点值
    # cal_col = '竞价空间'        # 相似日计算采用列
    # type_ = [0, 0, 1, 2, 3]    # 相似日计算方法
    # cols = ['maxmin', 'cosine', 'cityblock', 'euclidean', 'pearson']
    # for c in range(5):
    #     result = pd.DataFrame()
    #     for i in range(316):
    #         dt = str(date_ + datetime.timedelta(i))
    #         dt_start = str(date_ + datetime.timedelta(i) - datetime.timedelta(8))
    #         source_data = data[(data['date'] >= dt_start) & (data['date'] <= dt)].reset_index(drop=True)
    #         try:
    #             p = DeclarationCorrect(source_data, [dt], correct_col, method[c], cal_col, type_[c])
    #             tmp = p.pred_price()
    #             # print(tmp)
    #             result = pd.concat([result, tmp], axis=0)
    #             print(i, dt_start, dt, source_data.shape, result.shape)
    #         except Exception as e:
    #             print(e)
    #     # result.to_excel(fr"D:\ToGeek\work\10 京能\2_极值修正\3_不同相似日比较\2_负荷率\3_预测结果_负荷率_相似日_%s.xlsx" % cols[c], index=False)
    #     print(result.shape)
    #     print(result.head())
