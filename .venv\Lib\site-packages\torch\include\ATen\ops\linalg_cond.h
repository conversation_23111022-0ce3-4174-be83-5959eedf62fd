#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/linalg_cond_ops.h>

namespace at {


// aten::linalg_cond(Tensor self, Scalar? p=None) -> Tensor
inline at::Tensor linalg_cond(const at::Tensor & self, const ::std::optional<at::Scalar> & p=::std::nullopt) {
    return at::_ops::linalg_cond::call(self, p);
}

// aten::linalg_cond.out(Tensor self, <PERSON><PERSON><PERSON>? p=None, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_cond_out(at::Tensor & out, const at::Tensor & self, const ::std::optional<at::Scalar> & p=::std::nullopt) {
    return at::_ops::linalg_cond_out::call(self, p, out);
}
// aten::linalg_cond.out(Tensor self, Scalar? p=None, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_cond_outf(const at::Tensor & self, const ::std::optional<at::Scalar> & p, at::Tensor & out) {
    return at::_ops::linalg_cond_out::call(self, p, out);
}

// aten::linalg_cond.p_str(Tensor self, str p) -> Tensor
inline at::Tensor linalg_cond(const at::Tensor & self, c10::string_view p) {
    return at::_ops::linalg_cond_p_str::call(self, p);
}

// aten::linalg_cond.p_str_out(Tensor self, str p, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_cond_out(at::Tensor & out, const at::Tensor & self, c10::string_view p) {
    return at::_ops::linalg_cond_p_str_out::call(self, p, out);
}
// aten::linalg_cond.p_str_out(Tensor self, str p, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_cond_outf(const at::Tensor & self, c10::string_view p, at::Tensor & out) {
    return at::_ops::linalg_cond_p_str_out::call(self, p, out);
}

}
