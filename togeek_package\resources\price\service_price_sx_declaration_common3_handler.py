"""
Author: <PERSON><PERSON>
Datetime: 2022/12/29/029 11:20
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shanxi.price_declaration_pre import CommonBiddingSpaceFitting3


class DeclarationValueEvalHandlerServiceCommon3SX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price = params.pop('price')
        run_date = params.pop('run_date', None)
        bsf = CommonBiddingSpaceFitting3(price, run_date=run_date)
        data = bsf.pred_price(to_json=True)
        self.write(data)
