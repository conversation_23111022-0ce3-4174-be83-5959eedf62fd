"""
Author: Laney
Datetime: 2022/12/1/001 10:12
Info:
"""

import numpy as np
import pandas as pd
import statsmodels.api as sm
import logging


logger = logging.getLogger()


class CoalConsumFunction:
    def __init__(self, hist_data, new_data, f, r, delete_r):
        logger.info("----------------------煤耗曲线拟合------------------------")
        logger.info("输入参数为: {" + f"'hist_data': {hist_data}, 'new_data': {new_data}, 'f': {f}, 'r': {r}, 'delete_r': {delete_r}" + "}")
        self.old = self._prepare_data(hist_data)  # 历史煤耗曲线的数据点
        self.new = self._prepare_data(new_data)  # 新煤耗曲线的数据点
        self.f = f                          # 旧煤耗曲线的系数
        self.r = r                          # 新煤耗数据的权重
        self.rp = delete_r['P']    # 旧煤耗数据删除的半径, 发电机负荷P,MW
        self.rd = delete_r['D']    # 旧煤耗数据删除的半径, 供热负荷D,GJ/h

    def _prepare_data(self, data):
        """
        将煤耗数据处理成DataFrame格式的数据
        :param data: 字典列表，形如：[{"P": 600, "D": 0, "C":179.53}, {"P": 450, "D": 0, "C":137.46}, {"P": 591.20, "D": 141.40, "C":179.53}, ...]
        :return: dataframe格式
        """
        # 二维数组，形如[[P, D, C], [P, D, C], [P, D, C], [P, D, C], ...]
        # data = pd.DataFrame(data, columns=['P', 'D', 'C'])  # 传入的数据为二维数组，且数据依次为P,D,C
        data = pd.DataFrame(data)  # 传入的数据为字典列表
        data = data.dropna()    # 删除缺失值
        return data

    # 煤耗计算函数
    def f_coal_consum(self, P, D, f):
        """
        根据数据的发电负荷P和供热量D，通过煤耗计算公式计算出煤耗C
        :param P: 发电机功率MW
        :param D: 供热量GJ/h
        :param f: 煤耗曲线的系数，如：[𝑎0,𝑎1,𝑎2,𝑎3,𝑎4,𝑎5,𝑎6,𝑎7,𝑎8,𝑎9]，煤耗曲线形式：𝐶=𝑎0𝑃3+𝑎1𝑃2𝐷+𝑎2𝑃𝐷2+𝑎3𝐷3+𝑎4𝑃2+𝑎5𝑃𝐷+𝑎6𝐷2+𝑎7𝑃+𝑎8𝐷+𝑎9
        :return: c, 总煤耗量t/h
        """
        f = list(f)
        if isinstance(f, list):
            c = f[0] * (P ** 3) + f[1] * P * P * D + f[2] * P * D * D + f[3] * (D ** 3) + f[4] * (P ** 2) + \
                f[ 5] * P * D + f[6] * D * D + f[7] * P + f[8] * D + f[9]
        elif isinstance(f, str):
            c = eval(f)  # f为传入的煤耗表达式，是关于P,D的表达式，为字符串
        else:
            raise Exception(f'不支持[{f}]煤耗函数类型')
        return c

    def correct_new_data(self):
        """
        根据传入的煤耗曲线及新增的数据点，修正新增数据的总煤耗量
        :return: True
        """
        P = np.array(self.new['P'])
        D = np.array(self.new['D'])
        # 使用历史煤耗曲线拟合新增数据点的煤耗
        self.new['old'] = self.f_coal_consum(P, D, self.f)

        # 修正新增煤耗数据点的煤耗
        self.new['C'] = self.new['C'] * self.r + self.new['old'] * (1-self.r)
        del self.new['old']
        return True

    def delete_old_data(self):
        """
        根据给定的删除半径，将旧数据中半径范围内的数据去除掉，避免数据的冗余
        :return: True
        """
        for d in self.new.itertuples():
            P_old = np.array(self.old['P'])
            D_old = np.array(self.old['D'])
            P_ = d.P
            D_ = d.D
            # print(abs(P_old - P_))
            P_diff = np.nonzero(abs(P_old - P_) <= self.rp)
            D_diff = np.nonzero(abs(D_old - D_) <= self.rd)
            # print(P_diff, D_diff)
            # 交集
            filter_idx = np.intersect1d(P_diff, D_diff)

            if filter_idx.any():
                self.old = self.old.drop(filter_idx, axis=0)
                self.old.reset_index(drop=True, inplace=True)  # 重置索引

        return True

    def coal_consum_fitting(self):
        """
        使用多元线性回归方法，对所有煤耗数据进行拟合，得出新的煤耗曲线
        :return: 煤耗曲线的系数，所有煤耗数据
        """
        data_new = pd.concat([self.old, self.new], axis=0)

        # 计算各系数项
        data_new['P0'] = data_new['P'] * data_new['P'] * data_new['P']
        data_new['P1'] = data_new['P'] * data_new['P'] * data_new['D']
        data_new['P2'] = data_new['P'] * data_new['D'] * data_new['D']
        data_new['P3'] = data_new['D'] * data_new['D'] * data_new['D']
        data_new['P4'] = data_new['P'] * data_new['P']
        data_new['P5'] = data_new['P'] * data_new['D']
        data_new['P6'] = data_new['D'] * data_new['D']
        data_new['P7'] = data_new['P']
        data_new['P8'] = data_new['D']

        # 新煤耗曲线拟合
        ols = sm.formula.ols('C ~ P0+P1+P2+P3+P4+P5+P6+P7+P8', data=data_new).fit()
        logger.info(ols.summary())
        logger.info(f'Parameters: {list(ols.params)}')
        logger.info(f'R2: {ols.rsquared}')

        # 煤耗曲线系数处理
        params = list(ols.params)[1:] + list(ols.params)[:1]
        return params, data_new[['P', 'D', 'C']]

    def run(self):
        """
        煤耗曲线的拟合
        :return: result
        """
        self.correct_new_data()
        self.delete_old_data()
        params, data = self.coal_consum_fitting()
        out = data.apply(lambda x: {"P": x.P, 'D': x.D, 'C': x.C}, axis=1).to_list()
        result = {"params": params, 'data': out}
        logger.info("----------------煤耗曲线拟合完成-----------------")
        logger.info(f"result:{result}")
        return result


if __name__ == '__main__':
    hist_data = [{'P': 133.88, 'D': 0.0, 'C': 45.6985992},
                 {'P': 105.93, 'D': 0.0, 'C': 37.3233762},
                 {'P': 77.26, 'D': 0.0, 'C': 28.352874800000006},
                 {'P': 127.873142825, 'D': 130.0, 'C': 45.6985992},
                 {'P': 99.78856965000001, 'D': 130.0, 'C': 37.3233762},
                 {'P': 72.09728365000001, 'D': 130.0, 'C': 28.352874800000006},
                 {'P': 121.86628565, 'D': 260.0, 'C': 45.6985992},
                 {'P': 93.6471393, 'D': 260.0, 'C': 37.3233762},
                 {'P': 66.93456730000001, 'D': 260.0, 'C': 28.352874800000006},
                 {'P': 115.85942847499999, 'D': 390.0, 'C': 45.6985992},
                 {'P': 87.50570895000001, 'D': 390.0, 'C': 37.3233762},
                 {'P': 61.77185095, 'D': 390.0, 'C': 28.352874800000006},
                 {'P': 109.8525713, 'D': 520.0, 'C': 45.6985992},
                 {'P': 81.3642786, 'D': 520.0, 'C': 37.3233762},
                 {'P': 56.609134600000004, 'D': 520.0, 'C': 28.352874800000006},
                 {'P': 103.845714125, 'D': 650.0, 'C': 45.6985992},
                 {'P': 75.22284825, 'D': 650.0, 'C': 37.3233762},
                 {'P': 51.44641825000001, 'D': 650.0, 'C': 28.352874800000006},
                 {'P': 97.83885695, 'D': 780.0, 'C': 45.6985992},
                 {'P': 69.0814179, 'D': 780.0, 'C': 37.3233762},
                 {'P': 46.283701900000004, 'D': 780.0, 'C': 28.352874800000006}]
    new_data = [{'P': 600.0, 'D': 0.0, 'C': 179.5299277075562},
                {'P': 450.0, 'D': 0.0, 'C': 137.46314244956946},
                 {'P': 300.0, 'D': 0.0, 'C': 95.94318666815997},
                 {'P': 240.0, 'D': 0.0, 'C': 78.80969935012195},
                 {'P': 180.0, 'D': 0.0, 'C': 62.20843926775484},
                 {'P': 591.203807925, 'D': 141.4, 'C': 179.5299277075562},
                 {'P': 441.644229225, 'D': 139.05, 'C': 137.46314244956946},
                 {'P': 292.549539675, 'D': 139.35, 'C': 95.94318666815997},
                 {'P': 231.799192275, 'D': 139.9333333333333, 'C': 78.80969935012195},
                 {'P': 171.799192275, 'D': 139.9333333333333, 'C': 62.20843926775484},
                 {'P': 582.40761585, 'D': 282.8, 'C': 179.5299277075562},
                 {'P': 433.28845845, 'D': 278.1, 'C': 137.46314244956946},
                 {'P': 285.09907935, 'D': 278.7, 'C': 95.94318666815997},
                 {'P': 223.59838455000002, 'D': 279.8666666666666, 'C': 78.80969935012195},
                 {'P': 163.59838455000002, 'D': 279.8666666666666, 'C': 62.20843926775484},
                 {'P': 573.611423775, 'D': 424.2, 'C': 179.5299277075562},
                 {'P': 424.932687675, 'D': 417.15, 'C': 137.46314244956946},
                 {'P': 277.648619025, 'D': 418.05, 'C': 95.94318666815997},
                 {'P': 215.39757682500002, 'D': 419.8, 'C': 78.80969935012195},
                 {'P': 155.39757682500002, 'D': 419.8, 'C': 62.20843926775484},
                 {'P': 564.8152317, 'D': 565.6, 'C': 179.5299277075562},
                 {'P': 416.5769169, 'D': 556.2, 'C': 137.46314244956946},
                 {'P': 270.1981587, 'D': 557.4, 'C': 95.94318666815997},
                 {'P': 207.1967691, 'D': 559.7333333333332, 'C': 78.80969935012195},
                 {'P': 147.1967691, 'D': 559.7333333333332, 'C': 62.20843926775484},
                 {'P': 556.019039625, 'D': 707.0, 'C': 179.5299277075562},
                 {'P': 408.221146125, 'D': 695.25, 'C': 137.46314244956946},
                 {'P': 262.747698375, 'D': 696.75, 'C': 95.94318666815997},
                 {'P': 198.99596137500004, 'D': 699.6666666666666, 'C': 78.80969935012195},
                 {'P': 138.99596137500004, 'D': 699.6666666666666, 'C': 62.20843926775484},
                 {'P': 547.22284755, 'D': 848.4, 'C': 179.5299277075562},
                 {'P': 399.86537535, 'D': 834.3, 'C': 137.46314244956946},
                 {'P': 255.29723805, 'D': 836.1, 'C': 95.94318666815997},
                 {'P': 190.79515365000003, 'D': 839.6, 'C': 78.80969935012195},
                 {'P': 130.79515365000003, 'D': 839.6, 'C': 62.20843926775484},
                 {'P': 538.426655475, 'D': 989.8, 'C': 179.5299277075562},
                 {'P': 391.509604575, 'D': 973.35, 'C': 137.46314244956946},
                 {'P': 247.846777725, 'D': 975.45, 'C': 95.94318666815997},
                 {'P': 182.59434592500003, 'D': 979.5333333333333, 'C': 78.80969935012195},
                 {'P': 122.59434592500001, 'D': 979.5333333333333, 'C': 62.20843926775484}]
    f = [2.62035442e-06, -1.86329900e-06, -1.70390088e-07, -3.63837059e-09, -1.00638635e-03,  4.13925120e-04,
         1.89755507e-05, 4.29128466e-01, -8.27666586e-03, 1.28001993e-02]
    r = 0.5
    delete_r = {'P': 5, 'D': 30}
    ccf = CoalConsumFunction(hist_data, new_data, f, r, delete_r)
    res = ccf.run()
    print(res['params'])
    print(res['data'])

