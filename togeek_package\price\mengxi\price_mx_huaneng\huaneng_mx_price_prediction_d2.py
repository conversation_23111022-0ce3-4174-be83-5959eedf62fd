#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/6/6 9:57
# <AUTHOR> Dar<PERSON>
'''
    给华能蒙西做的D+2价格预测模型

'''

import numpy as np
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
from sklearn.ensemble import RandomForestClassifier as RFC  # 随机森林
import datetime
from togeek_package.price.mengxi.price_mx_forest_pre.model_config import ModelConfig
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class HNMxElPriceValueDataset2:
    def __init__(self, data, province, date_list_yuce, jiedian_type='统一结算点', max_power=34660):
        logger.info("----------------------极端随机树ETR价格预测--------------------------")
        self.province = province
        self.date_list_yuce = date_list_yuce
        self.jiedian_type = jiedian_type
        self.max_power = max_power
        self.data, self.features = self._validate_data(self._pre_data(data))

    def _pre_data(self, data):
        d = pd.DataFrame(data)  # index为日期时间，str, 形如：'2020-08-08 00:15'
        if 'date_time' in d.columns:
            d.set_index('date_time', inplace=True)
        d.sort_index(inplace=True)  # 升序排列

        # 传入数据中若含有列"时间"和"日期"，将其删掉
        if '时间' in d.columns:
            d = d.drop('时间', axis=1)
        if '日期' in d.columns:
            d = d.drop('日期', axis=1)
        d = d.applymap(lambda x: float(x))  # 所有数据转为float类型
        return d

    def _validate_data(self, data):
        config = ModelConfig()

        if self.jiedian_type == '统一结算点':
            yuce_all_keys = config.get_tongyijiesuan_all_keys(self.province)
            beijianshu_keys = config.get_tongyijiesuan_beijianshu(self.province)
            jianshu_keys = config.get_tongyijiesuan_jianshu(self.province)
        elif self.jiedian_type == '节点':
            yuce_all_keys = config.get_jiedian_all_keys(self.province)
            beijianshu_keys = config.get_jiedian_beijianshu(self.province)
            jianshu_keys = config.get_jiedian_jianshu(self.province)
        else:
            print(self.jiedian_type, ' 该结算点类型暂不支持')
            raise Exception(self.jiedian_type + '该节点类型暂不支持')

        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        data['星期'] = pd.to_datetime(data['日期']).dt.weekday
        # 给东送取负值
        data['日前东送计划'] = data['日前东送计划'].map(lambda x: 0 - abs(x))
        data['火电竞价空间'] = data[beijianshu_keys].sum(axis=1) - data[jianshu_keys].sum(axis=1)
        if "非市场化出力" in yuce_all_keys:
            data['限电量预测'] = data['火电竞价空间'] - self.max_power * 0.5 - data['非市场化出力']
            features = yuce_all_keys + ['小时', '火电竞价空间', '限电量预测']
        else:
            # 特征字段，不包含价格
            features = yuce_all_keys + ['小时', '火电竞价空间']
        return data, features


    def fix_price(self, price, sign):
        '''
        按sign的标志修正价格数据
        Parameters
        ----------
        price：价格预测值
        sign：0-1预测值
        Returns
        -------
        '''
        s = price
        if sign == 1:
            if price >= 85:
                s = 40
        else:
            if price < 85:
                s = 100
        return s

    def fix_pre(self, x, y):
        '''
        x: 价格预测值
        y: 修正标识
        '''
        if x == 1:
            return 0
        else:
            return y

    def fix_2(self, train, pred, yuce_d1, pre_data):
        '''
        修正预测的0价点，取历史15天的数据，拿出低于50的新能源，排序后取十分位点为临界值，大于该值的点直接按小时赋0
        '''
        try:
            his_end = str(pd.to_datetime(yuce_d1) + datetime.timedelta(-1))[:10]
            his_date = str(pd.to_datetime(yuce_d1) + datetime.timedelta(-15))[:10]
            # if 'date' not in pred.columns:
            #     pred['date'] = pred['date_time'].map(lambda x:str(x).split(' ')[0])
            # if 'date' not in train.columns:
            #     train['date'] = train['date_time'].map(lambda x:str(x).split(' ')[0])
            his_data = train[(train['日期'] >= his_date) & (train['日期'] < his_end)]
            his_t = np.sort(np.array(his_data[his_data['价格'] < 50]['日前新能源负荷预测'].values))
            n = int(len(his_t) * 0.1)
            top_90_percenter = his_t[n]
            pred['xnyxz'] = pred['日前新能源负荷预测'].map(lambda x: 1 if x >= top_90_percenter else 0)
            # 按小时分状态
            pred['date_h'] = pred.index.map(lambda x: str(x)[:13])
            pred = pred[['date_h', 'xnyxz']].groupby('date_h').max().reset_index()
            pre_data['date_h'] = pre_data.index.map(lambda x:str(x)[:13])
            pre_data['date_time'] = pre_data.index
            pre_data = pre_data.merge(pred, how='left', on='date_h')
            pre_data['价格预测值'] = pre_data.apply(lambda row: self.fix_pre(row['xnyxz'], row['价格预测值']), axis=1)
            pre_data = pre_data.set_index('date_time')
        except Exception as err:
            logger.info(f"历史找不到低价点数据，不进行修正")
        return pre_data[['价格', '价格预测值']]

    def predict_day_price(self, days=15, n_est=100, max_depth=6, min_value=0, max_value=5180):
        all_data = self.data
        all_data['日期'] = all_data['日期'].astype(str)
        features_base = self.features
        date_list_yuce = self.date_list_yuce
        date_list_yuce.sort()  # 将预测日期升序排
        time_points = len(all_data['时间'].unique())  # 传入数据时点
        # 创建空DataFrame，用来预测结果存储
        pre_data = pd.DataFrame()
        for yuce_d1 in date_list_yuce:
            print('-------------------预测{}的数据-----------------'.format(yuce_d1))
            # 创建空DataFrame，用来预测结果存储
            pre_data1 = pd.DataFrame()
            data_input = all_data.dropna(subset=['价格'])
            last_date = str(datetime.datetime.strptime(yuce_d1, "%Y-%m-%d") + datetime.timedelta(-1)).split(" ")[0]
            train_all = data_input[data_input['日期'] < last_date]#.dropna()  # 所有实时价格非空的训练数据
            train = train_all[-days * time_points:]  # 取非空传入数据的最后30天数据作为训练数据
            pred = all_data[all_data['日期'] == yuce_d1]  # 预测数据
            # 划分数据集
            X_train = train[features_base]
            if X_train.shape[0] == 0:
                raise ValueError('删除null值后，训练数据为空，请检查传入数据')
            X_pred = pred[features_base]
            # 判断预测日训练数据是否含NaN值
            if not X_pred.isnull().sum().sum() == 0:
                raise ValueError('预测日数据含NaN值，请检查!')
            if X_pred.shape[0] == 0:
                raise ValueError('预测日无数据，请检查!')
            y_train = train[['价格']]
            train['价格1'] = train['价格'].map(lambda x:1 if x <= 85 else 0)
            y_train1 = train[['价格1']]
            del train['价格1']
            logger.info(f'---------------------------价格------------------------')
            logger.info(f'X_train：\n{X_train}')
            logger.info(f'训练数据字段：{list(X_train.columns)}')
            logger.info(f'训练数据NaN值数量：{X_train.isnull().sum().sum()}')

            # 模型训练、预测
            etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)
            etr.fit(X=X_train, y=y_train)
            res = etr.predict(X=X_pred)  # np.array类型
            # 预测结果处理
            result = pred[['价格']]
            result['价格预测值'] = res

            # 二分类修正
            md = RFC(n_estimators=100, random_state=1)
            md.fit(X_train, y_train1)
            res1 = md.predict(X_pred)
            result['价格85'] = res1
            result['价格预测值'] = result.apply(lambda row: self.fix_price(row['价格预测值'], row['价格85']), axis=1)

            # 0价点修正
            result = self.fix_2(train, pred, yuce_d1, result)

            # 对预测值进行最大最小值修正
            result['价格预测值'] = result['价格预测值'].map(
                lambda s: min_value if s < min_value else max_value if s > max_value else s)

            result = result[['价格预测值']]
            pre_data = pd.concat([pre_data, result])
        logger.info("-------------------------End of ETR ---------------------------------")
        logger.info(f"result: \n {pre_data}")
        return pre_data


if __name__ == '__main__':
    all_data_test = pd.read_excel(r"D:\02file\2024\05蒙西\data_input\模型输入.xlsx")
    all_data_test['date_time'] = pd.to_datetime(all_data_test['date_time'])
    all_data_test['date_time'] = all_data_test['date_time'].astype(str)
    all_data_test = all_data_test.set_index('date_time')
    all_data_test = all_data_test.fillna(method='ffill')
    print(all_data_test.to_dict())
    date_list_yuce_test = ['2024-05-30', '2024-05-31']
    province_test = '蒙西'
    jiedian_type_test = '统一结算点'  # default = '统一结算点'
    etr_test = HNMxElPriceValueDataset2(data=all_data_test,
                                   date_list_yuce=date_list_yuce_test,
                                   province=province_test,
                                   jiedian_type=jiedian_type_test)
    pre_data_test = etr_test.predict_day_price()

    print(f'result: \n {pre_data_test}')