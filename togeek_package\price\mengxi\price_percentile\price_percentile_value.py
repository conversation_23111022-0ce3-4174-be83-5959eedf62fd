#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/7 10:22
# <AUTHOR> Darlene
import pandas as pd
import math
import numpy as np
import warnings
import logging
warnings.filterwarnings('ignore')
warnings.simplefilter('ignore')
logger = logging.getLogger()


class PricePercentile:
    def __init__(self, price_data, q1=25, q3=75):
        '''
        q1: 第一四分位数
        q3: 第三四分位数
        返回六组q1和q3，4个小时一组
        '''
        logger.info("----------------------蒙西价格密集点计算模型--------------------------")
        self.q1 = q1
        self.q3 = q3
        self.price_data = self.pre_data(price_data)
        self.result = self.predict()


    def pre_data(self, data):
        data = pd.DataFrame(data)  # index为日期时间，str, 形如：'2020-08-08 00:15'
        if 'date_time' in data.columns:
            data.set_index('date_time', inplace=True)
        data.sort_index(inplace=True)  # 升序排列
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['时刻点'] = data['时间'].map(
            lambda s: int(int(str(s).split(":")[0]) * 4 + (int(str(s).split(":")[1]) / 15) + 1))

        data['段数'] = data['时刻点'].map(
            lambda x: int(math.floor(x / 16)) if x / 16 == math.floor(x / 16) else int(math.floor(x / 16)) + 1)
        return data

    def predict(self):
        result = {}
        for i in range(1, 7):
            price = self.price_data[self.price_data['段数'] == i]['price'].astype(float)
            if price.empty:
                result[i] = [0, 0]
            else:
                p1 = np.percentile(price, self.q1)
                p3 = np.percentile(price, self.q3)
                result[i] = [p1, p3]
        logger.info("----------------------蒙西价格密集点计算模型--------------------------")
        return result