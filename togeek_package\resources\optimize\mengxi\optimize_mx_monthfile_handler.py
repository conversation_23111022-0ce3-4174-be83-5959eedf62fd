#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/15 22:32
# <AUTHOR> <PERSON><PERSON>
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_monthfile import OptimizeMonthlyFile
class MXMonthlyFileHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        node_data = params.pop('node_data')
        con_data = params.pop('con_data')
        his_num = params.pop('his_num')
        eta_chao = params.pop('eta_chao')
        opt_date = params.pop('opt_date')
        f_helidu = params.pop('f_helidu')
        return_url = params.pop('return_url', {'url':'', 'authorization':''})
        model = OptimizeMonthlyFile(node_data=node_data, con_data=con_data, his_num=his_num, eta_chao=eta_chao,
                         opt_date=opt_date, f_helidu=f_helidu, return_url=return_url)
        result = model.get_result(json=True)
        self.write(result)