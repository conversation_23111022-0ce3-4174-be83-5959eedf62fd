#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/6/12 9:22 
@Info    ：多台机组参与深度调峰模型
多台机组之间按照额定机组容量比例分配发电负荷及供热负荷
机组台数不固定，深调档位不固定
'''
import numpy as np
import pandas as pd
import logging

logger = logging.getLogger()


class OptimizeDeepAdjustCommon:
    def __init__(self, param, generator_number=4, deep_shift=5):
        logger.info("---------------通用深度调峰模型开始---------------")
        logger.info(f"传参为：'generator_number': {generator_number}, 'deep_shift': {deep_shift}, 'param': {param}")
        self.number, self.shift, self.param, self.msg, self.flag = self.check_data(param, generator_number, deep_shift)

    def check_data(self, param, generator_number, deep_shift):
        """
        根据机组台数及深调档位数检查传入的参数是否符合要求
        :param generator_number: 机组数量
        :param deep_shift: 深调档位
        :param param: 传入模型的参数
        :return: 机组数量，深调档位，传入参数
        """
        msg = "开始检查param参数："
        flag = 1
        number = generator_number
        shift = deep_shift
        # 1 机组参数检查
        features1 = ['generator_min', 'generator_max', 'generator_const_min', 'generator_const_max',
                     'plant_elec_rate', 'coal_consum_func', 'generator_heat_loads_min', 'generator_heat_loads_max']
        for f1 in features1:
            if len(param[f1]) != number:
                msg += f"{f1}的长度为{len(param[f1])}， 应为{number},"
                flag *= 0
            else:
                msg += f"{f1}的长度为{number},检查通过，"
                flag *= 1
        # 检查机组负荷约束及厂用电率函数参数数据量
        for f in ['generator_const_min', 'generator_const_max', 'plant_elec_rate']:
            ln = [len(g) for g in param[f]]
            if np.array(ln).mean() != 3:
                msg += f"{f}中参数的长度分别为{ln}，不为3，"
                flag *= 0
            else:
                flag *= 1
        # 检查煤耗函数参数的数量是否为10
        ln1 = [len(c) for c in param['coal_consum_func']]
        if np.array(ln1).mean() != 10:
            msg += f"coal_consum_func中参数的长度分别为{ln1}，不为10，"
            flag *= 0
        else:
            flag *= 1
        msg += f"机组相关参数检查完成；"
        features2 = ['deep_adjust_base', 'deep_adjust_price']
        for f2 in features2:
            if len(param[f2]) != number:
                msg += f"{f2}的长度为{len(param[f2])}， 应为{shift},"
                flag *= 0
            else:
                msg += f"{f2}的长度为{shift},检查通过，"
                flag *= 1
        msg += f"深调档位相关参数检查完成！\n"
        logger.info(msg)
        return number, shift, param, msg, flag

    def eval_func(self, func, x):
        """
        一元二次函数的计算公式
        :param func: 方程的参数，依次为2次项系数，一次项系数和常数项
        :param x: 自变量
        :return: 函数的值
        """
        f = func[0] * x * x + func[1] * x + func[2]
        return f

    def f_coal_consum(self, coal_consum_func, P, D):
        """
        煤耗函数
        B：总煤耗，t/h, Bd：发电煤耗，g/kWh, Br：供热煤耗，kg/GJ, D：抽汽heat_load，GJ/h, P：发电机负荷，MW
        :param coal_consum_func: 煤耗函数的参数，分别为
        :param P: 发电机负荷
        :param D: 电锅炉负荷
        :return: 总煤耗 t/h
        """
        p = coal_consum_func
        if isinstance(p, list):
            c = p[0]*P*P*P + p[1]*P*P*D + p[2]*P*D*D + p[3]*D*D*D + p[4]*P*P + p[5]*P*D + p[6]*D*D + p[7]*P + p[8]*D + p[9]
        elif isinstance(p, str):
            c = eval(p)  # p为传入的煤耗表达式，是关于P, D的表达式，为字符串
        else:
            raise Exception(f'不支持[{p}]煤耗函数类型')
        return c

    def run(self, generator_step=1, eboiler_step=1):
        logger.info(f"其它参数为：'generator_step':, {generator_step}, 'eboiler_step': {eboiler_step}")
        pmin = sum(np.array(self.param['generator_min']))  # 最小发电负荷
        pmax = sum(np.array(self.param['generator_max']))  # 最大发电负荷
        f_ratio = np.array([a / pmax for a in self.param['generator_max']])  # 发电机组的发电分摊比例
        P_lst = range(pmin, pmax+1, generator_step)
        D_lst = list(range(self.param['eboiler_min'], self.param['eboiler_max'] + 1, eboiler_step))
        if self.param['eboiler_min'] != 0:
            D_lst.insert(0, 0)
        Pm, Dm = np.meshgrid(P_lst, D_lst)
        iters = len(Pm) * len(P_lst)  # 所有可能情况
        XP = Pm.flatten()  # 发电机负荷
        XD = Dm.flatten()  # 电锅炉负荷

        # P_计算深调 = P_发电 - P_电锅炉
        P_on_deep = XP - XD  # 参与深调的负荷

        # 计算单台发电机的负荷及电锅炉负荷
        f = np.dot(XP.reshape(iters, 1), f_ratio.reshape(1, self.number))  # 各机组的发电负荷

        # 计算厂用电量，厂用电率 = f_plant_elec_rate(f发电机负荷)
        plant_elec_rate = np.array([self.eval_func(rate, f[:, i]) for i, rate in enumerate(self.param['plant_elec_rate'])]).T  # 厂用电率
        plant_elec = (f * plant_elec_rate / 100).sum(axis=1)  # 厂用电量

        # 总抽汽负荷
        P_pump = self.param['heat_load'] - XD * self.param['eboiler_efficiency'] * 3.6
        f_pump = np.dot(P_pump.reshape(iters, 1), f_ratio.reshape(1, self.number))  # 各机组的抽汽负荷

        # 各机组发电负荷约束，受供热量的约束
        f_pump_min = np.array([self.eval_func(c, f_pump[:, i]) for i, c in enumerate(self.param['generator_const_min'])]).T
        f_pump_max = np.array([self.eval_func(c, f_pump[:, i]) for i, c in enumerate(self.param['generator_const_max'])]).T

        # 上网电量计算：P_上网 = P_发电 - P_电锅炉 - 厂用电   # 用来计算售电收入
        P_on_grid = XP - XD - plant_elec

        # 1 各档位的深调量计算
        deep_base = np.array(self.param['deep_adjust_base'])  # 深调档位基准
        deep_price = self.param['deep_adjust_price']  # 深调档位单价
        m_deep_base, _ = np.meshgrid(deep_base, P_on_deep)  # 深调档位基准矩阵

        L = []  # 自主深调量
        for i in range(self.shift):
            L_i = np.maximum(m_deep_base[:, i] - XP, 0)
            L.append(L_i)
        for i in range(self.shift - 2, -1, -1):  # 修正深调量
            L[i][L[i + 1] > 0] = deep_base[i] - deep_base[i + 1]

        LL = []  # 联合深调量
        for i in range(self.shift):
            LL_i = np.maximum(m_deep_base[:, i] - P_on_deep, 0)
            LL.append(LL_i)
        for i in range(self.shift - 2, -1, -1):
            LL[i][LL[i + 1] > 0] = deep_base[i] - deep_base[i + 1]

        DL = []  # 电锅炉深调量
        for i in range(self.shift):
            DL_i = np.maximum(LL[i] - L[i], 0)
            DL.append(DL_i)

        # 2 收入 成本计算
        elec_income = P_on_grid * self.param['elec_price'] / 10  # 售电收入,万元 = MWh * 元/kWh / 10
        heat_income = (P_pump + XD * self.param['eboiler_efficiency'] * 3.6) * self.param['heat_price'] / 1e4  # 售热收入
        eboiler_use_elec_income = XD * self.param['eboiler_use_elec_price'] / 10  # 电锅炉用电收入

        # 自主深调补偿收入
        s_zj_deep_adjust_income = []
        for i in range(self.shift):
            zj_i = L[i] * deep_price[i] / 10
            s_zj_deep_adjust_income.append(zj_i)
        zj_deep_adjust_income = np.array(s_zj_deep_adjust_income).T.sum(axis=1) * self.param['correct_coef']

        # 联合深调补偿收入分成
        s_lh_deep_adjust_income = []
        for i in range(self.shift):
            lh_i = DL[i] * deep_price[i] / 10
            s_lh_deep_adjust_income.append(lh_i)
        lh_deep_adjust_income = (np.array(s_lh_deep_adjust_income).T.sum(axis=1) * self.param['correct_coef'] -
                                 XD * (self.param['eboiler_use_elec_price'] + self.param['eboiler_other_cost']) / 10) \
                                 * self.param['eboiler_dividend_ratio']

        deep_adjust_income = zj_deep_adjust_income + lh_deep_adjust_income  # 深调补偿收入

        # 成本
        coal = np.array([self.f_coal_consum(c, f[:, i], f_pump[:, i]) for i, c in enumerate(self.param['coal_consum_func'])]).T.sum(axis=1)  # 煤耗
        cost = coal * self.param['scoal_price'] / 1e4 + XP * self.param['elec_other_cost'] / 10
        cost1 = coal * self.param['scoal_price'] / 1e4
        cost2 = XP * self.param['elec_other_cost'] / 10
        income = elec_income + heat_income + deep_adjust_income + eboiler_use_elec_income  # 收入
        profit = income - cost  # 总利润

        # 约束
        idx = 1
        idx *= P_on_grid >= 0  # 上网负荷约束,>=0
        # idx *= P_pump >= 0  # 抽汽负荷约束,>=0  # 改为for循环中的抽汽负荷约束

        # 发电机负荷约束, 满足最大最小负荷约束; 机组抽汽供热负荷约束，满足上下限约束;
        for i in range(self.number):
            idx *= f[:, i] >= f_pump_min[:, i]  # 发电机负荷约束
            idx *= f[:, i] >= self.param['generator_min'][i]  # 发电机负荷下限约束
            idx *= f_pump[:, i] >= self.param['generator_heat_loads_min'][i]  # 机组抽汽供热负荷约束
            idx *= f[:, i] <= f_pump_max[:, i]  # 发电机负荷约束
            idx *= f[:, i] <= self.param['generator_max'][i]  # 发电机负荷上限约束
            idx *= f_pump[:, i] <= self.param['generator_heat_loads_max'][i]  # 机组抽汽供热负荷约束

        # 3 结果整理
        dct = {'XP': XP, 'P_on_grid': P_on_grid, 'XD': XD, 'profit': profit, 'cost': cost, 'cost1': cost1, 'cost2': cost2,
               # 'fP1': f[:, 0], 'fP2': f[:, 1], 'D1': f_pump[:, 0], 'D2': f_pump[:, 1],  # 需注释
               'income': income, 'elec_income': elec_income, 'heat_income': heat_income,
               'eboiler_use_elec_income': eboiler_use_elec_income, 'deep_adjust_income': deep_adjust_income,
               'zj_deep_adjust_income': zj_deep_adjust_income,
               'lh_deep_adjust_income': lh_deep_adjust_income
               }
        Lcols, DLcols = [], []  # 深调字段列表
        for i in range(self.shift):  # 自主深调量
            col = "L" + str(i + 1)
            Lcols.append(col)
            dct[col] = L[i]

        for i in range(self.shift):  # 电锅炉深调量
            col = "DL" + str(i + 1)
            DLcols.append(col)
            dct[col] = DL[i]
        dct['flag'] = idx  # 约束条件
        data = pd.DataFrame(dct)
        res = data[data['flag'] == 1]
        # res.to_csv(r"C:\Users\<USER>\Desktop\deep_adjust.csv", encoding='utf8', index=False)
        del res['flag']
        return res, Lcols, DLcols

    def deal_result(self, generator_step=1, eboiler_step=1):
        data, Lcols, DLcols = self.run(generator_step, eboiler_step)
        Ccols = ['XP', 'P_on_grid', 'XD', 'profit', 'cost', 'cost1', 'cost2', 'income', 'elec_income', 'heat_income',
                 'eboiler_use_elec_income', 'deep_adjust_income', 'zj_deep_adjust_income', 'lh_deep_adjust_income']
        # print(data.columns)
        # data['deep_adjust'] = data[Lcols].sum(axis=1) + data[DLcols].sum(axis=1)
        result = {}
        # 前n-1档判断
        for i in range(self.shift-1):
            n1, n2 = "zj" + str(i+1), "lh" + str(i+1)
            zj = data[(data[Lcols[i]] > 0) & (data[Lcols[i+1:]].sum(axis=1) == 0) & (data[DLcols].sum(axis=1) == 0)]
            lh = data[(data[Lcols[i+1:]].sum(axis=1) == 0) & (data[DLcols[i]] > 0) & (data[DLcols[i+1:]].sum(axis=1) == 0)]

            zj.sort_values("profit", ascending=False, inplace=True)
            zj.reset_index(drop=True, inplace=True)
            lh.sort_values("profit", ascending=False, inplace=True)
            lh.reset_index(drop=True, inplace=True)

            for df, n in zip([zj, lh], [n1, n2]):
                if df.empty:
                    tmp = None
                else:
                    tmp = df.loc[0, Ccols].to_dict()
                    tmp['L'] = df.loc[0, Lcols].tolist()
                    tmp['DL'] = df.loc[0, DLcols].tolist()
                result[n] = tmp

            # result[n1] = None if zj.empty else zj.iloc[0, :].to_dict()
            # result[n2] = None if lh.empty else lh.iloc[0, :].to_dict()

        # 最后一档判断
        zj1 = data[(data[Lcols[-1]] > 0) & (data[DLcols].sum(axis=1) == 0)]
        lh1 = data[(data[DLcols[-1]] > 0)]

        zj1.sort_values("profit", ascending=False, inplace=True)
        zj1.reset_index(drop=True, inplace=True)
        lh1.sort_values("profit", ascending=False, inplace=True)
        lh1.reset_index(drop=True, inplace=True)

        if zj1.empty:
            tmp_zj = None
        else:
            tmp_zj = zj1.loc[0, Ccols].to_dict()
            tmp_zj['L'] = zj1.loc[0, Lcols].tolist()
            tmp_zj['DL'] = zj1.loc[0, DLcols].tolist()
        result["zj" + str(self.shift)] = tmp_zj

        if lh1.empty:
            tmp_lh = None
        else:
            tmp_lh = lh1.loc[0, Ccols].to_dict()
            tmp_lh['L'] = lh1.loc[0, Lcols].tolist()
            tmp_lh['DL'] = lh1.loc[0, DLcols].tolist()
        result["lh" + str(self.shift)] = tmp_lh

        # result["zj"+str(self.shift)] = None if zj1.empty else zj1.iloc[0, :].to_dict()
        # result["lh"+str(self.shift)] = None if lh1.empty else lh1.iloc[0, :].to_dict()
        logger.info(f"result:{result}")
        logger.info("---------------通用深度调峰模型运行结束---------------")
        return result


if __name__ == "__main__":
    # 1、generator_params
    generator_number = 4
    deep_shift = 5      # 深调档位
    generator_step = 1  # 发电机步长
    eboiler_step = 1    # 电锅炉步长
    param = {
        "generator_min": [0, 0, 0, 0],   # 机组最小负荷,MW
        "generator_max": [600, 600, 300, 300],  # 机组最大负荷,MW
        "eboiler_min": 0,  # 电锅炉最小负荷,MW
        "eboiler_max": 240,   # 电锅炉最大负荷,MW
        "heat_load": 643.25,  # 供热负荷, GJ/h
        "elec_price": 0.32469,  # 综合上网电价, 元/kWh
        "heat_price": 21.58,    # 供热单价, 元/GJ
        "scoal_price": 349.06,  # 入炉标煤单价, 元/t
        "elec_other_cost": 0.07862,  # 度电其他成本，元/kWh  （度电其他变动成本+度电固定成本）
        "eboiler_use_elec_price": 0.353,  # 电锅炉用电单价, 元/kWh
        "eboiler_other_cost": 0.148,  # 电锅炉度电固定成本，元/kWh
        "eboiler_dividend_ratio": 0.3,  # 电锅炉分成比例，电厂的分成比例
        "eboiler_efficiency":  0.95,     # 电锅炉效率, 分数，如：0.95代表95%
        "generator_heat_loads_min": [0, 0, 0, 0],  # 机组抽汽供热负荷下限,GJ/h
        "generator_heat_loads_max": [650, 650, 350, 350],  # 机组抽汽供热负荷上限,GJ/h
        "generator_const_min": [[0.0000469, -0.08006, 240], [0.0000469, -0.08006, 240], [-0.000000000001464, 0.05585, 100], [-0.000000000001464, 0.05585, 100]],  # 发电机最小负荷约束，MW
        "generator_const_max": [[0.0000026981, -0.08618, 600], [0.0000026981, -0.08618, 600], [-0.0000000000000002366, -0.05585, 300], [-0.0000000000000002366, -0.05585, 300]],  # 发电机最大负荷约束，MW
        "deep_adjust_base": [900, 720, 540, 360, 180],      # 深调补偿基准,MW
        "deep_adjust_price": [0.05, 0.22, 0.35, 0.5, 0.7],  # 深调补偿单价,元/kWh
        "correct_coef": 0.5,  # 全厂一个系数= 机组运行修正系数 * 深调补偿修正系数  run_correct_coef * deep_adjust_correct_coef
        "plant_elec_rate": [[0.0000095556, -0.0061666667, 8.07], [0.0000095556, -0.0061666667, 8.07], [0.0000095556, -0.0061666667, 8.07], [0.0000095556, -0.0061666667, 8.07]],  # 厂用电率函数的参数, %
        "coal_consum_func": [[-0.0000000474563558, 0.0000000145483857, 0.00000000213840137, 0.000000000045833548, 0.0000554337885, -0.00000823418551, -0.000000615648273, 0.259435718, 0.0170057089, 14.0767179],
                             [-0.0000000474563558, 0.0000000145483857, 0.00000000213840137, 0.000000000045833548, 0.0000554337885, -0.00000823418551, -0.000000615648273, 0.259435718, 0.0170057089, 14.0767179],
                             [-0.0000000474563558, 0.0000000145483857, 0.00000000213840137, 0.000000000045833548, 0.0000554337885, -0.00000823418551, -0.000000615648273, 0.259435718, 0.0170057089, 14.0767179],
                             [-0.0000000474563558, 0.0000000145483857, 0.00000000213840137, 0.000000000045833548, 0.0000554337885, -0.00000823418551, -0.000000615648273, 0.259435718, 0.0170057089, 14.0767179],
                            ]   # 煤耗曲线函数的参数，t/h
    }
    # param = {
    #     "generator_min": [0, 0],   # 机组最小负荷,MW
    #     "generator_max": [600, 600],  # 机组最大负荷,MW
    #     "eboiler_min": 0,  # 电锅炉最小负荷,MW
    #     "eboiler_max": 240,   # 电锅炉最大负荷,MW
    #     "heat_load": 643.25,  # 供热负荷, GJ/h
    #     "elec_price": 0.32469,  # 电单价, 元/kWh
    #     "heat_price": 21.58,    # 热单价, 元/GJ
    #     "scoal_price": 349.06,  # 标煤单价, 元/t
    #     "elec_other_cost": 0.07862,  # 度电其他成本，元/kWh
    #     "eboiler_use_elec_price": 0.353,  # 电锅炉用电单价, 元/kWh
    #     "eboiler_other_cost": 0.148,  # 电锅炉其他成本，元/kWh
    #     "eboiler_dividend_ratio": 0.3,  # 电锅炉分成比例，电厂的分成比例
    #     "eboiler_efficiency":  0.95,     # 电锅炉效率, 分数，如：0.95代表95%
    #     "generator_heat_loads_min": [0, 0],   # 机组抽汽供热负荷,GJ/h
    #     "generator_heat_loads_max": [650, 650],  # 机组抽汽供热负荷,GJ/h
    #     "generator_const_min": [[0.0000469, -0.08006, 240], [0.0000469, -0.08006, 240]],  # 发电机最小负荷约束，MW
    #     "generator_const_max": [[0.0000026981, -0.08618, 600], [0.0000026981, -0.08618, 600]],  # 发电机最大负荷约束，MW
    #     "deep_adjust_base": [600, 480, 360, 240, 120],      # 深调补偿基准,MW
    #     "deep_adjust_price": [0.05, 0.22, 0.35, 0.5, 0.7],  # 深调补偿单价,元/kWh
    #     "correct_coef": 0.5,  # 全厂一个系数=机组运行修正系数 * 深调补偿修正系数  run_correct_coef * deep_adjust_correct_coef
    #     "plant_elec_rate": [[0.0000095556, -0.0061666667, 8.07], [0.0000095556, -0.0061666667, 8.07]],  # 厂用电率函数的参数, %
    #     "coal_consum_func": [[-4.74563558e-08, 1.45483857e-08, 2.13840137e-09, 4.5833548e-11, 5.54337885e-05, -8.23418551e-06, -6.15648273e-07, 0.259435718, 0.0170057089, 14.0767179],
    #                          [-4.74563558e-08, 1.45483857e-08, 2.13840137e-09, 4.5833548e-11, 5.54337885e-05, -8.23418551e-06, -6.15648273e-07, 0.259435718, 0.0170057089, 14.0767179]
    #                         ]   # 煤耗曲线函数的参数，t/h
    # }

    m = OptimizeDeepAdjustCommon(param, generator_number=generator_number, deep_shift=deep_shift)
    res = m.deal_result(generator_step, eboiler_step)
    print(res)
