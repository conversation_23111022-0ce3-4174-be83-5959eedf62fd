#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/10 10:53
# <AUTHOR> Darlene
'''
    1、历史合约与出清电量取小值的和
    2、历史合约与出清电量取均值的和
    3、当日出清电量的小时值
    4、当日合约电量的小时值（置换后，年度、月度、基数）
    5、历史合约电量之和
    6、当日合约电价的小时值（置换后）
    7、当日节点电价的小时值
    8、曲线合理度基准
    9、超额回收电量比例
'''
import pyomo.environ as pyo
from pyomo.opt import SolverFactory
import pandas as pd
import requests
import time
import gc
from joblib import Parallel, delayed




class OptimizeMonthlyFile:
    def __init__(self, node_data, con_data, his_num, eta_chao, opt_date, f_helidu, return_url):
        self.opt_date = opt_date
        self.node_data, self.con_data = self.pre_data(node_data, con_data)
        self.his_num = pd.DataFrame(his_num)   # 包含历史合约与出清电量取小值的和min_power，取均值的和mean_power，历史合约电量之和con_power，历史计量电量之和jiliang_power
        self.eta_chao = eta_chao
        self.f_helidu = f_helidu
        self.return_url = return_url


    def pre_data(self, df_node, df_con):
        '''

        '''
        df_node = pd.DataFrame(df_node)
        if 'date_time' not in df_node.columns:
            df_node = df_node.reset_index().rename(columns={'index': 'date_time'})
        df_node['date'] = df_node['date_time'].map(lambda x: str(x).split(' ')[0])
        df_node = df_node[df_node['date'] == self.opt_date]
        df_node['t'] = df_node['date_time'].map(
            lambda s: int(pd.to_datetime(s).hour * 4 + pd.to_datetime(s).minute / 15 + 1)
        )
        del df_node['date']
        df_con = (
            pd.DataFrame(df_con)
            .assign(feiyong=lambda x: x['contract_power'] * x['contract_price'])
            .groupby(['node', 'date_time'])
            .agg({'contract_power': 'sum', 'feiyong': 'sum'})
            .reset_index()
        )
        df_con['contract_price'] = df_con['feiyong'] / df_con['contract_power']
        del df_con['feiyong']
        df_con['date'] = df_con['date_time'].map(lambda x: str(x).split(' ')[0])
        df_con = df_con[df_con['date'] == self.opt_date]
        del df_con['date']
        df_con['t'] = df_con['date_time'].map(
            lambda s: int(pd.to_datetime(s).hour * 4 + pd.to_datetime(s).minute / 15 + 1)
        )
        return df_node, df_con

    def create_power_allocation_model(self, n_data, c_data, his_n):
        # 创建模型
        model = pyo.ConcreteModel()


        # 时间周期集合
        model.T = n_data.index.to_list()

        # 出清电量
        model.chuqing_power = pyo.Param(model.T, within=pyo.NonNegativeReals,
                                initialize={t: n_data.loc[t, 'chuqing_power'] for t in model.T})
        # 计量电量
        model.jiliang_power = pyo.Param(model.T, within=pyo.NonNegativeReals,
                                        initialize={t: n_data.loc[t, 'jiliang_power'] for t in model.T})
        # 节点电价
        model.node_pirce = pyo.Param(model.T, within=pyo.Reals,
                                        initialize={t: n_data.loc[t, 'node_price'] for t in model.T})
        # 合约电量
        model.con_power = pyo.Param(model.T, within=pyo.NonNegativeReals,
                                     initialize={t: c_data.loc[t, 'contract_power'] for t in model.T})
        # 合约电价
        model.con_price = pyo.Param(model.T, within=pyo.NonNegativeReals,
                                    initialize={t: c_data.loc[t, 'contract_price'] for t in model.T})

        # 决策变量
        model.S_xt = pyo.Var(model.T, within=pyo.NonNegativeReals)  # 需优化的电量值
        # 中间变量  年度、月度、基数、月内
        model.heyue_power_alla = pyo.Var(model.T, within=pyo.NonNegativeReals)

        # 目标函数：最大化S_xt
        model.max_S_xt = pyo.Var(within=pyo.NonNegativeReals)  # 辅助变量：S_xt的最大值

        # 目标函数：最大化所有时刻S_xt中的最大值
        model.obj = pyo.Objective(expr=model.max_S_xt, sense=pyo.maximize)

        # 约束条件1：定义max_S_xt为所有S_xt的和
        def max_S_xt_definition_rule(model):
            return model.max_S_xt == pyo.quicksum(model.S_xt[t] for t in model.T)
        model.max_S_xt_definition = pyo.Constraint(rule=max_S_xt_definition_rule)

        def rule_heyue_power_alla(model, t):
            return model.heyue_power_alla[t] == model.con_power[t] + model.S_xt[t]
        model.rule_heyue_power_alla = pyo.Constraint(model.T, rule=rule_heyue_power_alla)
        # 约束条件1：曲线合理度约束
        def curve_reasonability_rule(model, t):
            #1、历史取小值 + 当前取小值
            p_min = his_n['min_power'] + pyo.quicksum(pyo.Expr_if(model.heyue_power_alla[k] >= model.chuqing_power[k],
                                model.chuqing_power[k], model.heyue_power_alla[k]) for k in model.T if k <= t)
            # 2、 历史取均值 + 当前取均值
            p_mean = his_n['mean_power'] + pyo.quicksum((model.chuqing_power[k] + model.heyue_power_alla[k]) / 2 for k in model.T if k <= t)
            return p_min / p_mean - self.f_helidu >= 0

        model.curve_reasonability_rule = pyo.Constraint(model.T, rule=curve_reasonability_rule)

        # 约束条件2：电量平衡约束
        def power_balance_rule(model, t):
            # 安全获取历史值和效率参数
            his_con = his_n['con_power']
            his_jiliang = his_n['jiliang_power']
            eta = self.eta_chao

            # 使用Pyomo的summation函数计算累积和
            con_cum = pyo.quicksum(model.con_power[k] + model.S_xt[k] for k in model.T if k <= t)
            jiliang_cum = pyo.quicksum(model.jiliang_power[k] for k in model.T if k <= t)

            return his_con + con_cum <= (his_jiliang + jiliang_cum) * eta

        model.power_balance = pyo.Constraint(model.T, rule=power_balance_rule)
        # 约束条件3：如果合约价格小于等于节点价格，s_xt为0
        def price_constraint_rule(model, t):
            if model.con_price[t] <= model.node_pirce[t]:
                return model.S_xt[t] == 0
            else:
                return model.S_xt[t] >= 0
        model.price_constraint = pyo.Constraint(model.T, rule=price_constraint_rule)
        return model


    def solve_model(self, node_name):
        print(node_name)
        n_data = self.node_data[self.node_data['node'] == node_name]
        c_data = self.con_data[self.con_data['node'] == node_name]
        his_n = self.his_num[self.his_num['node'] == node_name].to_dict(orient='records')[0]
        # 创建并求解模型
        model = self.create_power_allocation_model(n_data, c_data, his_n)
        # 选择求解器
        solver = SolverFactory('ipopt')
        solver.options['nlp_scaling_method'] = 'gradient-based'
        solver.options['tol'] = 1e-3
        solver.options['acceptable_tol'] = 1e-3
        solver.options['print_level'] = 0  # 不输出任何信息
        solver.options['max_iter'] = 2000  # 最大迭代次数
        try:
            # 求解模型
            sol = solver.solve(model, tee=True)
            # 检查求解状态
            if sol['Solver'][0]['Status'] != 'error':
                s_data = n_data[['date_time', 'node']]
                s_data['shenbao'] = [round(pyo.value(model.S_xt[t]), 4) for t in model.T]
                return s_data
            else:
                print(f"计算出错{node_name}")
                return None
        except Exception as e:
            print(e)
            return None

    def point_to_time(self, point):
        """
        根据96点的时刻点数反推时间点
        :param point: 96点的时刻点数（0-95）
        :return: 时间点（格式为HH:MM）
        """
        if point < 1 or point > 96:
            raise ValueError("时刻点数必须在0到95之间")
        point = point - 1
        # 每个点代表15分钟
        minutes = point * 15
        # 计算小时和分钟
        hours = minutes // 60
        minutes = minutes % 60

        # 格式化时间
        time_str = f"{hours:02d}:{minutes:02d}:{00:02d}"
        return time_str

    def make_request_with_retry(self, result, max_retries=3, timeout=20):
        """
        带重试机制的HTTP请求
        :param url: 请求URL
        :param max_retries: 最大重试次数 (默认3次)
        :param timeout: 请求超时时间(秒)
        :return: 响应对象或None
        """
        retry_count = 0
        headers = {
            "authorization": self.return_url['authorization']
        }

        while retry_count <= max_retries:
            try:
                # 使用上下文管理器确保连接关闭
                with requests.post(self.return_url['url'], headers=headers, timeout=timeout, json=result) as response:
                    response.raise_for_status()  # 检查HTTP错误状态
                    print(f"请求成功! 状态码: {response.status_code}")
                    return response

            except (requests.exceptions.RequestException, requests.exceptions.HTTPError) as e:
                retry_count += 1
                if retry_count > max_retries:
                    print(f"请求失败超过{max_retries}次，停止尝试。错误信息: {str(e)}")
                    return 0

                wait_time = 2 ** retry_count  # 指数退避策略
                print(f"请求失败 ({retry_count}/{max_retries})，{wait_time}秒后重试... 错误: {str(e)}")
                time.sleep(wait_time)

        return 0

    def get_result(self, json=True):
        '''
        处理数据，调用模型
        '''
        self.node_data = self.node_data.set_index('t')
        self.con_data = self.con_data.set_index('t')
        node_list = self.con_data['node'].drop_duplicates().to_list()
        results = pd.DataFrame()

        # 单线程
        for node in node_list:
            result_df = self.solve_model(node)
            # print(result_df)
            results = pd.concat([results, result_df])

        # results = []
        # max_workers = min(32, len(node_list) or 1)
        # results = Parallel(n_jobs=max_workers, timeout=300)(
        #     delayed(self.solve_model)(node) for node in node_list
        # )
        # results = pd.concat(results, ignore_index=True)

        if json == True:
            results = results.to_dict('list')
            if self.return_url['url'] == '':
                pass
            else:
                re = self.make_request_with_retry(results)
                # if re == 200:
            return results
        else:
            return results


if __name__ == '__main__':

    file_path = r'D:\02file\2025\01蒙西\中长期交易\模型输入输出\月内申报.json'
    import json, pathlib

    data = json.loads(pathlib.Path(file_path).read_text(encoding='utf-8'))
    opt = OptimizeMonthlyFile(node_data=data['node_data'], con_data=data['con_data'], his_num=data['his_num'],
                              eta_chao=data['eta_chao'], opt_date=data['opt_date'], f_helidu=data['f_helidu'],
                              return_url=data['return_url'])
    result = opt.get_result(json=True)
    print(result)




