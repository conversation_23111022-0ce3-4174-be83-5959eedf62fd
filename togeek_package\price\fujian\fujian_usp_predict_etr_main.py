import pandas as pd

from togeek_package.price.fujian.fujian_usp_ahead_price_d234_predict_etr import PriceAheadD234ETR
from togeek_package.price.fujian.fujian_usp_real_price_d234_predict_etr import PriceRealD234ETR



class PriceD234ETR():
    '''

    '''

    def __init__(self,
                 train_dict,
                 test_dict,
                 use_meta=True,
                 ):

        self.train_dict = train_dict
        self.test_dict = test_dict
        self.use_meta = use_meta

    def pred(self,):

        # 将 train_dict 转换为 DataFrame
        self.df_train = pd.DataFrame(self.train_dict)
        # 将 test_dict 转换为 DataFrame
        self.df_test = pd.DataFrame(self.test_dict)

        df_price_ahead_predicted = PriceAheadD234ETR(df_train=self.df_train.copy(), df_test=self.df_test.copy(), use_meta=self.use_meta).pred()
        df_price_real_predicted = PriceRealD234ETR(df_train=self.df_train.copy(), df_test=self.df_test.copy(), use_meta=self.use_meta).pred()

        # 将两个预测结果合并成一个字典
        result_dict = {
            'dateTime':  [ts.strftime('%Y-%m-%d %H:%M:%S') if isinstance(ts, pd.Timestamp) else ts
                        for ts in df_price_ahead_predicted.index.tolist()],
            '日前电价预测值': df_price_ahead_predicted['value'].tolist(),
            '实时电价预测值': df_price_real_predicted['value'].tolist()
        }

        return result_dict





if __name__ == '__main__':

    import json

    # D = "2025-07-21"
    use_meta = True
    train_dict = pd.read_csv('df_train.csv').to_dict('list')
    test_dict = pd.read_csv('df_test.csv').to_dict('list')

    result_dict = PriceD234ETR(train_dict=train_dict, test_dict=test_dict, use_meta=use_meta).pred()

    print(pd.DataFrame(result_dict))


    # 保存为 JSON 文件
    input_data = {
        '是否使用交易中心发布的预测值(True/False)': use_meta,
        '历史数据': train_dict,
        '未来数据': test_dict
    }

    with open('input_data.json', 'w', encoding='utf-8') as f:
        json.dump(input_data, f, ensure_ascii=False, indent=4)
        print("JSON 文件已保存为 input_data.json")

    with open('result_data.json', 'w', encoding='utf-8') as f:
        json.dump(result_dict, f, ensure_ascii=False, indent=4)
        print("结果已保存为 result_data.json")

    import winsound
    winsound.Beep(1000, 1000)

