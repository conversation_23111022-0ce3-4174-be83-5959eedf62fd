# -*- coding: utf-8 -*-

from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_forest_pre.mx_price_prediction_value import *
from togeek_package.price.mengxi.price_mx_forest_pre.mx_price_kmeans import MengxiPriceDtwKmeans
from tornado import gen, concurrent
from tglibs.easy_json import j2o


class PricePredictionValueEvalHandlerMX(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        province = data.get('province', '蒙西')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']
        days = data.get('days', 30)
        n_est = data.get('n_est', 100)
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 1500)
        max_power = data.get('max_power', 34660)
        r = yield self.predict(province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value, max_power)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value, max_power):
        elpd_val = ElPriceValueDataset(all_data, province, date_list_yuce, jiedian_type, max_power)
        result = elpd_val.predict_day_price(days=days, n_est=n_est, min_value=min_value, max_value=max_value)
        return result.to_dict()



class PricePredictionValueDtwHandlerMX(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        province = data.get('province', '蒙西')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']
        days = data.get('days', 14)
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 5180)
        time_list = data.get('time_list', [[0, 1, 2], [3, 4, 5, 6, 7], [8, 9], [10, 11, 12, 13, 14, 15, 16], [17, 18, 19, 20], [21, 22, 23]])
        r = yield self.predict(province, jiedian_type, date_list_yuce, days, all_data, min_value, max_value, time_list)

        self.write(r)


    @concurrent.run_on_executor
    def predict(self, province, jiedian_type, date_list_yuce, days, all_data, min_value, max_value, time_list):
        pre_kmeans = MengxiPriceDtwKmeans(all_data, date_list_yuce, time_list, province, jiedian_type, days, min_value, max_value)
        result = pre_kmeans.predict()
        return result.to_dict()

