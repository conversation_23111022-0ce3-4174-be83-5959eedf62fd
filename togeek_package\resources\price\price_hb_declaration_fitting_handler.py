# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.hebei.hb_price_bsf import PricePredBSF


class DeclarFittingHandlerHB(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        all_data = params.pop('all_data')
        price = params.pop('price')
        date_list_pred = params.pop('date_list_pred')
        points = params.get('points', 24)
        min_value = params.get("min_value", 0)
        max_value = params.get("max_value", 1500)
        m = PricePredBSF(all_data=all_data, price=price, date_list_pred=date_list_pred, points=points,
                         min_value=min_value, max_value=max_value)
        pred = m.predict()
        self.write(pred)
