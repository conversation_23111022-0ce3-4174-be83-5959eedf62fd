# -*- coding: utf-8 -*-

from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shaanxi.sax_etr_price_pred_7d import EtrPredict7d
from tglibs.easy_json import j2o


class EtrPred7dHandlerSAX(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    def put(self):
        data = j2o(self.request.body.decode())
        # print(data.keys())
        pred_list = data['pred_list']
        all_data = data['data']
        days = data.get('days', 30)
        n_est = data.get('n_est', 100)
        max_depth = data.get('max_depth', 8)
        min_price = data.get('min_value', 40)
        max_price = data.get('max_value', 1000)
        threshold_value = data.get('threshold_value', None)
        quantile_night = data.get('quantile_night', 0.5)
        quantile_day = data.get('quantile_day', 0.75)

        model = EtrPredict7d(data=all_data, pred_list=pred_list , threshold_value=threshold_value,
                             quantile_night=quantile_night, quantile_day=quantile_day)
        result = model.predict_day_price(days=days, n_est=n_est, max_depth=max_depth, min_price=min_price, max_price=max_price)
        self.write(result.to_dict())

