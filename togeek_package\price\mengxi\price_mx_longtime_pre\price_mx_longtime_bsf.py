#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/5/12 14:40
# <AUTHOR> Dar<PERSON>

# 三段拟合模型，可用于日前价格预测，实时价格预测


import numpy as np
import pandas as pd
import jenkspy
import warnings
import logging
from prophet import Prophet
from datetime import datetime, timedelta

from sklearn.ensemble import ExtraTreesRegressor
from tglibs.easy_date_time import construct_time_index


# logging.getLogger('prophet').setLevel(logging.WARNING)
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class MxPricePredBsf:

    def __init__(self, all_data, price, t_date, days, pro_params, points=96, min_value=0, max_value=1500):
        logger.info("---------------------- 蒙西中长期_三段拟合法_价格预测开始 --------------------------")
        self.points = points
        self.t_date = t_date
        self.date_list_pred = sorted(self.have_date(days))    # 预测日期列表
        self.price = pd.DataFrame(price).dropna()
        self.price['date_time'] = pd.to_datetime(self.price['date_time'])
        self.pro_params = pro_params
        self.all_data = self.data_process(all_data)
        self.all_data = self._pred_load()
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

    def have_date(self, days):
        end_date = datetime.strftime(datetime.strptime(self.t_date, "%Y-%m-%d") + timedelta(days=days - 1), '%Y-%m-%d')
        date_list = pd.Series(construct_time_index([self.t_date, end_date], freq='1d')).map(
            lambda x: x.strftime("%Y-%m-%d")).to_list()
        return date_list


    def data_process(self, all_data):
        """
        传入数据整理
        """
        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame.from_dict(all_data, orient='index').T  # 传入json数据
        data = data.sort_index()    # 日期索引升序排列

        # 字段检测
        for col in ['date_time', '负荷预测', '新能源预测', '东送计划']:
            if col not in data.columns:
                logger.info(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")
                raise ValueError(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")

        # 数据类型转换
        for col in ['负荷预测', '新能源预测', '东送计划']:
            data[col] = data[col].astype(float)

        # 添加日期字段
        data['date'] = data['date_time'].astype(str).map(lambda s: s.split(" ")[0])
        data['time'] = data['date_time'].astype(str).map(lambda s: s.split(" ")[-1])
        data = data.dropna().sort_values('date_time').reset_index(drop=True)
        return data

    def _stan_init(self, model):
        """
        从训练好的模型中检索参数
        以格式从训练过的模型中检索参数
        用于初始化一个新的Stan模型。
        Parameters     ----------
        m: A trained model of the Prophet class.
        Returns     -------
        A Dictionary containing retrieved parameters of m.
        """
        res = {}
        for pname in ['k', 'm', 'sigma_obs']:
            res[pname] = model.params[pname][0][0]
        for pname in ['delta', 'beta']:
            res[pname] = model.params[pname][0].tolist()
        return res

    def _pred_load(self, points=96):
        # 如果历史数据的最后一天不是run_date，需要将预测日期的[统调负荷、新能源、联络线]数据补齐
        run_date = self.date_list_pred[-1]  # 最大预测日期
        last_date = str(self.price.dropna()['date_time'].tolist()[-1]).split(" ")[0]  # 传入数据的截至日期
        # 对传入数据的第一天的日期进行修改，如果传入时间小于15天，则要把第一天改成15天前的日期
        if self.all_data.shape[0] / self.points < 15:
            start_15 = str(datetime.strptime(last_date, '%Y-%m-%d') + timedelta(-14)).split(" ")[0]
            self.all_data.loc[:96, 'date'] = start_15
            self.all_data['date_time'] = self.all_data['date'].astype(str) + ' ' + self.all_data['time'].astype(str)
            del(self.all_data['time'])
        start = str(datetime.strptime(last_date, '%Y-%m-%d') + timedelta(1)).split(" ")[0]  #
        n = (datetime.strptime(run_date, '%Y-%m-%d') - datetime.strptime(last_date, '%Y-%m-%d')).days
        if n > 0:
            future = pd.DataFrame({'date_time': pd.date_range(start=self.date_list_pred[0], periods=len(self.date_list_pred) * points, freq="15min")})
            self.all_data = pd.concat([self.all_data.dropna(), future], axis=0).drop_duplicates('date_time', keep='last')
        for col in ['负荷预测', '新能源预测', '东送计划']:
            tmp_df = self.all_data[['date_time', 'date', col]].dropna()
            last_date_i = tmp_df['date'].tolist()[-1]
            n = (datetime.strptime(run_date, '%Y-%m-%d') - datetime.strptime(last_date_i, '%Y-%m-%d')).days
            if n < 1:
                continue
            data = tmp_df[['date_time', col]][-15 * points:]
            data.columns = ['ds', 'y']
            data['ds'] = pd.to_datetime(data['ds'])
            data['y'] = data['y'].astype(float)
            params = {"seasonality_mode": 'multiplicative'}  # , "yearly_seasonality": True, "weekly_seasonality": True
            # 是否传入了模型参数，如果传入了，解析参数更新训练
            ts_model = Prophet(**params)
            if col in list(self.pro_params.keys()):
                param = self.pro_params[col]
                ts_model.fit(data, init=param)
            else:
                ts_model.fit(data)
            self.pro_params[col] = self._stan_init(ts_model)

            future = ts_model.make_future_dataframe(periods=n * points, include_history=False, freq='15min')
            pred = ts_model.predict(future)[['ds', 'yhat']]
            pred.columns = ['date_time', col]
            pred['date'] = pred['date_time'].map(lambda x: str(x).split(' ')[0])
            pred = pred[pred['date'].isin(self.date_list_pred)]
            self.all_data[col] = tmp_df[col].to_list() + pred[col].to_list()
        self.all_data['date_time'] = pd.to_datetime(self.all_data['date_time'])
        self.all_data.set_index('date_time', inplace=True)
        if self.points == 24:
            self.all_data = self.all_data.resample('H').mean().reset_index()
        elif self.points == 48:
            self.all_data = self.all_data.resample('30min').mean().reset_index()
        elif self.points == 96:
            self.all_data = self.all_data.reset_index()
        else:
            logger.info("仅支持【24，48，96】点数据，请检查输入参数【points】！")
            raise ValueError("仅支持【24，48，96】点数据，请检查输入参数【points】！")
        self.all_data['东送计划'] = self.all_data['东送计划'].map(lambda x: abs(x))
        self.all_data['bidding_space'] = self.all_data['负荷预测'] - self.all_data['新能源预测'] + self.all_data['东送计划']
        self.all_data['week'] = self.all_data['date_time'].map(lambda x: pd.to_datetime(x).weekday())
        self.all_data['date'] = self.all_data['date_time'].astype(str).map(lambda s: s.split(" ")[0])
        self.all_data['time'] = self.all_data['date_time'].astype(str).map(lambda s: s.split(" ")[-1])
        self.all_data = pd.merge(self.all_data, self.price, on='date_time', how='left')
        return self.all_data

    @staticmethod
    def get_history_date(history_data):
        """
        从传入历史数据 history_data 中选取价格不全为0的最大历史日期 history_date
        """
        history_date_list = history_data['date'].unique().tolist()   # 历史数据日期列表
        history_date_list = sorted(history_date_list, reverse=True)  # 历史日期倒序排列

        history_date = None     # 初始化
        for date in history_date_list:
            if history_data[history_data['date'] == date]['price'].sum() == 0:  # 若当前历史日期价格全为0，则不选取当天为训练数据(NaN值求和为0)
                continue
            elif history_data[history_data['date'] == date]['price'].nunique() == 1:    # 若当前历史日期价格只有1个取值，则不选取当天为训练数据(NaN值求和为0)
                continue
            else:
                history_date = date
                break

        if history_date is None:
            raise ValueError(f"传入历史数据中，没有满足预测需求的历史数据，请检查传入数据！")
        return history_date

    def predict(self, n_est=100, max_depth=6):
        """
        预测、输出
        """
        # 1. 数据处理
        source_data = self.all_data.copy()
        result_json = {}
        result = pd.DataFrame(columns=['date_time', 'pred_price', 'type'])
        # 把历史数据拿出来，不用每次都筛
        history_data = source_data[source_data['date'] < self.date_list_pred[0]]
        # 2. 遍历预测
        for date_pred in self.date_list_pred:
            print(f'-------------------预测{date_pred}的数据-----------------')
            # 筛选历史日期数据curdata和待预测日期数据curdatab
            curdatab = source_data[source_data['date'] == date_pred]
            weekb = curdatab['week'].drop_duplicates().to_list()[0]
            # 找历史数据里，week为weekb的最近一天
            curdata = history_data[history_data['week'] == weekb].sort_values('date_time')[-96:]
            # 删除空值
            curdata = curdata.dropna()

            # 将预测日的竞价空间缩放到[a, b]范围内
            a = curdata['bidding_space'].min()
            b = curdata['bidding_space'].max()

            mi = curdatab['bidding_space'].min()
            ma = curdatab['bidding_space'].max()

            curdatab['bidding_space'] = a + (curdatab['bidding_space'] - mi) / (ma - mi) * (b - a)

            # 价格乘竞价空间
            curdata['jenkspy'] = curdata['price'] * curdata['bidding_space']
            curdata.reset_index(inplace=True, drop=True)
            breaks = jenkspy.jenks_breaks(curdata['jenkspy'], 3)
            b2 = curdata[(curdata['jenkspy'] > breaks[1]) & (curdata['jenkspy'] <= breaks[2])]
            b3 = curdata[curdata['jenkspy'] > breaks[2]]

            b2jingjiamean = b2['bidding_space'].mean()
            b2rqrpmean = b2['price'].mean()

            k32_2m3all = ((b3['price'] - b2rqrpmean) / (b3['bidding_space'] - b2jingjiamean)).mean()

            # 第三段直线
            p13 = np.poly1d([k32_2m3all, np.mean(b3['price']) - k32_2m3all * np.mean(b3['bidding_space'])])

            # 多项式拟合
            parameter1 = np.polyfit(curdata['bidding_space'], curdata['price'], 1)
            p1 = np.poly1d(parameter1)
            parameter3 = np.polyfit(curdata['bidding_space'], curdata['price'], 3)
            p3 = np.poly1d(parameter3)

            todayuse = 1
            if parameter3[0] > 0:
                delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
                if delta < 0:
                    todayuse = 3
                else:
                    x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                    x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                    y1 = p3(x1)
                    y2 = p3(x2)
                    if x1 > 0 and y1 < y2 * 1.2:
                        todayuse = 33
            else:
                if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                    todayuse = 13
                else:
                    todayuse = 1

            pre_data = []
            if todayuse == 1:
                pre_data = p1(curdatab['bidding_space'])
            elif todayuse == 13:
                pre_data = np.maximum(np.maximum(p1(curdatab['bidding_space']), self.min_value),
                                      p13(curdatab['bidding_space']))
            elif todayuse == 3:
                pre_data = np.maximum(p3(curdatab['bidding_space']), self.min_value)
            elif todayuse == 33:
                pre_data = np.maximum(p3(curdatab['bidding_space']), y1)
                pre_data = np.where((curdatab['bidding_space'].values < x1), p3(curdatab['bidding_space']), pre_data)

            # 修正价格上下限
            pre_data = np.maximum(pre_data, self.min_value)
            pre_data = np.minimum(pre_data, self.max_value)

            result_tmp = pd.DataFrame(columns=['date_time', 'pred_price', 'type'])
            result_tmp['date_time'] = curdatab['date_time']
            result_tmp['pred_price'] = list(pre_data)
            result_tmp['type'] = [todayuse] * curdatab.shape[0]
            result = pd.concat([result, result_tmp], axis=0)

        # 3. 整理输出结构
        result['date_time'] = result['date_time'].astype(str)
        result = result.set_index('date_time')
        result_json['data'] = result.to_dict()
        result_json['model'] = self.pro_params


        # 4. 写入log
        logger.info("------------------ 蒙西中长期_三段拟合法_价格预测完成！ ---------------------")
        return result_json


if __name__ == '__main__':
    start = datetime.now()
    data = pd.read_excel(r"D:\02file\2023\05蒙西数据\data\边界条件.xlsx")
    price = pd.read_excel(r"D:\02file\2023\05蒙西数据\data\price.xlsx")
    data.columns = ['date_time', '负荷预测', '新能源预测', '东送计划']
    data['date_time'] = data['date_time'].apply(str)
    price['date_time'] = price['date_time'].apply(str)
    data = data[(data['date_time'] < '2023-02-26') & (data['date_time'] >= '2023-01-01')].dropna()
    price = price[(price['date_time'] < '2023-02-26') & (price['date_time'] >= '2023-01-01')].dropna()
    t_date = '2023-05-01'
    pro_params = {
        "负荷预测":{
            "k":0.11348270656631694,
            "m":0.8964328232301294,
            "sigma_obs":0.009389270443764272,
            "delta":[
                1.51913795e-01,
                -1.37124219e-03,
                -1.66106472e-01,
                1.46883329e-01,
                -7.56771358e-01,
                -3.52110200e-01,
                1.07524516e+00,
                -2.83268989e-01,
                1.27320707e-07,
                2.59038568e-01,
                -3.08478408e-01,
                3.61629710e-02,
                -1.23667210e-01,
                9.47366081e-01,
                -7.84234658e-01,
                -2.25701091e-01,
                2.53613088e-01,
                9.85644908e-02,
                -3.34006021e-01,
                5.85376275e-01,
                2.77764879e-02,
                -2.75343414e-01,
                -2.00330322e-01,
                6.81336254e-01,
                -5.58803443e-01
            ],
            "beta":[
                -0.00041838,
                0.0017103,
                -0.00206264,
                0.00036223,
                -0.00138887,
                -0.00037294,
                -0.01746786,
                0.00270741,
                -0.02103491,
                -0.01188637,
                0.0051788,
                -0.00484601,
                0.00018158,
                0.00098242
            ]
        },
        "新能源预测":{
            "k":-1.5795376722223664,
            "m":0.39800317851665423,
            "sigma_obs":0.13600427083989922,
            "delta":[
                -1.10009447e-07,
                2.95384370e-01,
                1.63113609e+00,
                1.47430901e-06,
                3.25908795e-06,
                -1.55282426e+00,
                1.47195907e+00,
                4.23702917e-06,
                9.11827982e-02,
                -7.82659478e-07,
                4.97254241e-01,
                -3.54255952e-07,
                -1.24910311e+00,
                -5.55748778e-03,
                -3.77308518e-01,
                -1.46170459e-06,
                3.24793769e+00,
                -2.81340287e-01,
                -2.75549307e+00,
                3.56630600e-01,
                -1.56422744e+00,
                3.36161052e+00,
                1.05688247e+00,
                -4.69988585e+00,
                1.92400288e+00
            ],
            "beta":[
                -0.03025574,
                -0.03600631,
                0.00159467,
                -0.01366118,
                0.01600653,
                -0.03244394,
                -0.05824457,
                -0.33743584,
                0.0725209,
                0.18031452,
                -0.02007757,
                -0.02871631,
                -0.01233584,
                -0.01951783
            ]
        },
        "东送计划":{
            "k":0.034219694365495386,
            "m":0.2578999441332277,
            "sigma_obs":0.07744990663422649,
            "delta":[
                -5.93901480e-07,
                7.20561679e-01,
                3.16859660e+00,
                1.27209930e-07,
                -3.76830652e+00,
                3.67377086e-05,
                -1.20649749e+01,
                1.15349175e+01,
                5.62922243e+00,
                -4.22987872e+00,
                -1.05748675e+00,
                4.46346642e-07,
                8.50316496e-08,
                -2.49508580e-01,
                -9.51108025e-02,
                5.98557164e-01,
                1.21313802e-07,
                1.44117957e+00,
                9.75734091e+00,
                -1.19544043e+01,
                1.64488774e-02,
                1.46097450e-07,
                -4.97303592e-01,
                5.75532127e-03,
                1.58791308e+00
            ],
            "beta":[
                0.0032902,
                0.00365517,
                -0.0012052,
                -0.00079373,
                0.00694089,
                0.00680942,
                -0.18852898,
                -0.19267256,
                -0.18124685,
                -0.05896202,
                0.13355083,
                -0.04451602,
                0.0074129,
                -0.0625037
            ]
        }
    }
    m = MxPricePredBsf(data.to_dict('list'), price.to_dict('list'), t_date, days=1, pro_params=pro_params)
    res = m.predict()
    pd.DataFrame(res['data']).to_excel(r"D:\02file\2023\05蒙西数据\data\pre15.xlsx")
    print(res)
    end = datetime.now()
    print(end - start)
