# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-10-20 14:14:57
Description :   储能系统调度模型：不同模式收益测算对比
"""

import os
import json
import numpy as np
import pandas as pd
import requests as rs
from datetime import timedelta
from calendar import monthrange
import pyomo.environ as pyo
import warnings
import logging
pd.set_option('display.max_columns', None)      # 显示所有列
warnings.filterwarnings("ignore")
logger = logging.getLogger()


def get_data_datasets(grid: str, key_dict: dict, date_start: str, date_end: str, path_save: str = None):
    """
    从 datasets 数据库查询数据
    Parameters
    ----------
    grid: 省份, 可选["SHANXI", "SHANDONG", "MENGXI", "GUANGDONG"]
    key_dict: 字段名称: 索引，示例： {"/日前/统一结算点电价": 399, "/实时/统一结算点电价": 405}。从表 datasets/ds_index_meta 中查询
    date_start: 查询起始日期
    date_end: 查询终止日期
    path_save: 查询数据保存地址: xxx.xlsx

    Returns data
    -------

    """
    # 1. 构造请求参数
    url = f'http://139.9.77.109/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
    params = {
        'grid': grid,
        'startTime': f"{date_start} 00:00:00",
        'endTime': f"{date_end} 23:45:00",
        'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
        'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
    }

    # 2. 获取数据
    res_dict = rs.get(url, params=params)

    # 3. 数据整理
    df_list = []

    for key in list(key_dict.keys()):
        df_tmp = pd.DataFrame(res_dict.json()['data'][key]['points'])
        df_tmp = df_tmp.rename(columns={'value': key})
        df_list.append(df_tmp)

    date_time_list = pd.date_range(f"{date_start} 00:00:00", f"{date_end} 23:45:00", freq="15T").astype(str).values
    df = pd.DataFrame({"time": date_time_list})
    for i in range(len(list(key_dict.keys()))):
        df = pd.merge(df, df_list[i], on='time', how="left")
    df = df.rename(columns={"time": "date_time"})
    df['date'] = df['date_time'].map(lambda s: s.split(" ")[0])
    df = df.set_index(["date_time", "date"])

    if path_save:
        df.to_excel(path_save)
        print(f"查询数据已保存，请查看：{os.path.abspath(path_save)}")

    return df


class OptimizeEnergyStorageV3:

    def __init__(self, method, type_price, capacity_transformer, price_capacity, price_max_demand, coef_charge, coef_max_transformer, attribute_storage, data_meter, data_storage):
        logger.info("---------------------- START: 广东古瑞瓦特智慧园区 储能需量调节&峰谷套利 收益优化 v3.0 --------------------------")

        self.method = method  # 模式：可选: ["demand", "arbitrage", "mix"]
        self.type_price = type_price  # 目标时段，储能价格类型, 可选: ["fixed", "spots"]
        self.capacity_transformer = capacity_transformer  # 变压器计费容量KVA, 6200
        self.price_capacity = price_capacity  # 容量月度单价（元/千伏安·月），即：基本电费单价
        self.price_max_demand = price_max_demand  # 需量月度单价（元/千瓦时·月）
        self.coef_charge = coef_charge  # 储能系统充放电效率，0-1之间的浮点数，一般为0.85-0.9之间
        self.coef_max_transformer = coef_max_transformer    # 变压器容量上限系数，一般为：0.8
        self.attribute_storage = attribute_storage  # 储能soc上下限， 0~1 之间

        self.data_meter = pd.DataFrame(data_meter)  # 历史实际电表"负荷"数据KW, json格式: ["meter_id", "date_time", "t_active_power"]
        self.data_meter["meter_id"] = self.data_meter["meter_id"].astype(int).astype(str)   # meter_id 转为 str 格式
        self.data_storage = pd.DataFrame(data_storage)  # 历史实际储能"电量"数据KWh, json格式: ["sys_id", "date_time", "use_elec"]
        self.data_storage["sys_id"] = self.data_storage["sys_id"].astype(int).astype(str)   # sys_id 转为 str 格式

        # 从储能数据中获取终止时刻，各PCS的可放电量
        self.dt_end_input = str(pd.to_datetime(self.data_storage["date_time"]).max())   # 传入数据终止时刻
        self.init_elec = self.data_storage[self.data_storage["date_time"] == self.dt_end_input].set_index("sys_id")["use_elec"].to_dict()
        # self.init_elec = {"1": 20.9, "2": 20.9, "20": 100, "30": 100, "40": 200}
        self.dt_start_output = str(pd.to_datetime(self.dt_end_input) + timedelta(minutes=15))   # 输出数据起始时刻
        self.year, self.month = self.dt_start_output.split(" ")[0].split("-")[:2]
        self.days_month = monthrange(int(self.year), int(self.month))[1]  # 目标日期所在月份的天数

        logger.info(f"价格类型: {self.type_price}")
        logger.info(f"模式：{self.method}")
        logger.info(f"输入数据终止时刻: {self.dt_end_input}")
        logger.info(f"终止时刻储能各PCS可放电量: {self.init_elec}")
        logger.info(f"输出数据起始时刻: {self.dt_start_output}")

        # 时刻列表
        self.time_list = pd.date_range("00:00:00", "23:45:00", freq="15T").map(lambda s: str(s).split(" ")[1]).values

        # 预测结果
        logger.info(f"start: 目标时段园区净负荷预测...")
        self.load_pred = self.get_load_pred()       # 目标时段园区净负荷预测
        logger.info(f"start: 目标时段价格预测...")
        self.price_pred = self.get_price_pred()     # 目标时段价格预测

    @staticmethod
    def calc_ema(values_array):
        """
        返回指定序列的指数移动平均值EMA，Exponential Moving Average
        array越靠后的数据权重越大
        """
        values_array = np.array(values_array)
        n_samples = len(values_array)  # 给定序列的数据量
        alpha = 2 / (n_samples + 1)  # 平滑因子
        weights = np.array([(1 - alpha) ** i for i in range(n_samples)][::-1])  # 权重序列
        ema = np.sum(weights * values_array) / np.sum(weights)
        return ema

    def get_load_pred(self):
        """
        根据传入历史数据, 预测目标日的园区净负荷 = 电表负荷 - 储能负荷
        """
        # 1. 储能PCS负荷计算
        elec_storage = self.data_storage.pivot(index="date_time", columns="sys_id", values="use_elec")  # 储能各时段的可放电量: KWh
        # 计算储能各时段的充放电量：KWh
        elec_storage_period = elec_storage.diff(periods=1).dropna()
        elec_storage_period.index = elec_storage.index[:-1]
        # 计算储能各时段的充放电功率
        power_storage = elec_storage_period * 4
        power_storage.columns = [f"storage_{i}" for i in power_storage.columns]     # 调整列名[storage_1, storage_2, storage_20, storage_30, storage_40]

        # 2. 电表负荷数据处理
        power_meter = self.data_meter.pivot(index="date_time", columns="meter_id", values="t_active_power")
        power_meter.columns = [f"meter_{i}" for i in power_meter.columns]

        # 3. 电表和储能数据组合
        dfc_load = pd.merge(power_meter.reset_index(), power_storage.reset_index())
        dfc_load["date"] = dfc_load["date_time"].map(lambda s: s.split(" ")[0])
        dfc_load["time"] = dfc_load["date_time"].map(lambda s: s.split(" ")[1])

        # 4. 计算电表对应区域的净负荷
        dfc_load["power_2003"] = dfc_load["meter_2003"] - dfc_load["storage_2030"]  # 电表3 对应 储能id 30
        dfc_load["power_2006"] = dfc_load["meter_2006"] - dfc_load["storage_2040"]  # 电表6 对应 储能id 40
        dfc_load["power_2007"] = dfc_load["meter_2007"] - dfc_load["storage_2001"] - dfc_load["storage_2002"] - dfc_load["storage_2020"]  # 电表7 对应 储能id 1, 2, 20
        dfc_load["power_2008"] = dfc_load["meter_2008"].copy()
        dfc_load["power_2009"] = dfc_load["meter_2009"].copy()

        # 5. 5个区域净负荷预测
        power_cols = ["power_2003", "power_2006", "power_2007", "power_2008", "power_2009"]
        pred_load_dict = {}
        for power_col in power_cols:
            pred_dict = {}
            tmp_power = dfc_load.pivot(index="time", columns="date", values=power_col)
            for t in self.time_list:
                tmp_values = tmp_power.loc[t].dropna().values  # t 时刻 不同日期的负荷列表
                power_pred_t = self.calc_ema(tmp_values)
                pred_dict[t] = power_pred_t
            pred_load_dict[power_col] = pred_dict

        # 6. 预测结果处理
        load_pred = pd.DataFrame({
            "date_time": pd.date_range(start=self.dt_start_output, periods=96, freq="15T").astype(str),
        })
        load_pred["date"] = load_pred["date_time"].map(lambda s: s.split(" ")[0])
        load_pred["time"] = load_pred["date_time"].map(lambda s: s.split(" ")[1])
        for key in pred_load_dict.keys():
            load_pred[f"pred_{key}"] = load_pred["time"].map(pred_load_dict[key])

        # # 数据保存
        # path_load_pred = rf"F:\ToGeek\data_research\储能收益优化_广东\20231018_zl\data_output\1_园区净负荷预测.xlsx"
        # load_pred.to_excel(path_load_pred, index=False, encoding="utf8")

        return load_pred

    def get_price_pred(self):
        # 1. 静态输配电价数据(固定值): price_td_dict
        data_price_dict = {
            "price_td": {"00:00:00": 0.0378, "00:15:00": 0.0378, "00:30:00": 0.0378, "00:45:00": 0.0378, "01:00:00": 0.0378, "01:15:00": 0.0378, "01:30:00": 0.0378, "01:45:00": 0.0378, "02:00:00": 0.0378, "02:15:00": 0.0378, "02:30:00": 0.0378, "02:45:00": 0.0378, "03:00:00": 0.0378, "03:15:00": 0.0378, "03:30:00": 0.0378, "03:45:00": 0.0378, "04:00:00": 0.0378, "04:15:00": 0.0378, "04:30:00": 0.0378, "04:45:00": 0.0378, "05:00:00": 0.0378, "05:15:00": 0.0378, "05:30:00": 0.0378, "05:45:00": 0.0378, "06:00:00": 0.0378, "06:15:00": 0.0378, "06:30:00": 0.0378, "06:45:00": 0.0378, "07:00:00": 0.0378, "07:15:00": 0.0378, "07:30:00": 0.0378, "07:45:00": 0.0378, "08:00:00": 0.0996, "08:15:00": 0.0996, "08:30:00": 0.0996, "08:45:00": 0.0996, "09:00:00": 0.0996, "09:15:00": 0.0996, "09:30:00": 0.0996, "09:45:00": 0.0996, "10:00:00": 0.1703, "10:15:00": 0.1703, "10:30:00": 0.1703, "10:45:00": 0.1703, "11:00:00": 0.1703, "11:15:00": 0.1703, "11:30:00": 0.1703, "11:45:00": 0.1703, "12:00:00": 0.0996, "12:15:00": 0.0996, "12:30:00": 0.0996, "12:45:00": 0.0996, "13:00:00": 0.0996, "13:15:00": 0.0996, "13:30:00": 0.0996, "13:45:00": 0.0996, "14:00:00": 0.1703, "14:15:00": 0.1703, "14:30:00": 0.1703, "14:45:00": 0.1703, "15:00:00": 0.1703, "15:15:00": 0.1703, "15:30:00": 0.1703, "15:45:00": 0.1703, "16:00:00": 0.1703, "16:15:00": 0.1703, "16:30:00": 0.1703, "16:45:00": 0.1703, "17:00:00": 0.1703, "17:15:00": 0.1703, "17:30:00": 0.1703, "17:45:00": 0.1703, "18:00:00": 0.1703, "18:15:00": 0.1703, "18:30:00": 0.1703, "18:45:00": 0.1703, "19:00:00": 0.0996, "19:15:00": 0.0996, "19:30:00": 0.0996, "19:45:00": 0.0996, "20:00:00": 0.0996, "20:15:00": 0.0996, "20:30:00": 0.0996, "20:45:00": 0.0996, "21:00:00": 0.0996, "21:15:00": 0.0996, "21:30:00": 0.0996, "21:45:00": 0.0996, "22:00:00": 0.0996, "22:15:00": 0.0996, "22:30:00": 0.0996, "22:45:00": 0.0996, "23:00:00": 0.0996, "23:15:00": 0.0996, "23:30:00": 0.0996, "23:45:00": 0.0996},
            "price_td_789": {"00:00:00": 0.0378, "00:15:00": 0.0378, "00:30:00": 0.0378, "00:45:00": 0.0378, "01:00:00": 0.0378, "01:15:00": 0.0378, "01:30:00": 0.0378, "01:45:00": 0.0378, "02:00:00": 0.0378, "02:15:00": 0.0378, "02:30:00": 0.0378, "02:45:00": 0.0378, "03:00:00": 0.0378, "03:15:00": 0.0378, "03:30:00": 0.0378, "03:45:00": 0.0378, "04:00:00": 0.0378, "04:15:00": 0.0378, "04:30:00": 0.0378, "04:45:00": 0.0378, "05:00:00": 0.0378, "05:15:00": 0.0378, "05:30:00": 0.0378, "05:45:00": 0.0378, "06:00:00": 0.0378, "06:15:00": 0.0378, "06:30:00": 0.0378, "06:45:00": 0.0378, "07:00:00": 0.0378, "07:15:00": 0.0378, "07:30:00": 0.0378, "07:45:00": 0.0378, "08:00:00": 0.0996, "08:15:00": 0.0996, "08:30:00": 0.0996, "08:45:00": 0.0996, "09:00:00": 0.0996, "09:15:00": 0.0996, "09:30:00": 0.0996, "09:45:00": 0.0996, "10:00:00": 0.1703, "10:15:00": 0.1703, "10:30:00": 0.1703, "10:45:00": 0.1703, "11:00:00": 0.2129, "11:15:00": 0.2129, "11:30:00": 0.2129, "11:45:00": 0.2129, "12:00:00": 0.0996, "12:15:00": 0.0996, "12:30:00": 0.0996, "12:45:00": 0.0996, "13:00:00": 0.0996, "13:15:00": 0.0996, "13:30:00": 0.0996, "13:45:00": 0.0996, "14:00:00": 0.1703, "14:15:00": 0.1703, "14:30:00": 0.1703, "14:45:00": 0.1703, "15:00:00": 0.2129, "15:15:00": 0.2129, "15:30:00": 0.2129, "15:45:00": 0.2129, "16:00:00": 0.2129, "16:15:00": 0.2129, "16:30:00": 0.2129, "16:45:00": 0.2129, "17:00:00": 0.1703, "17:15:00": 0.1703, "17:30:00": 0.1703, "17:45:00": 0.1703, "18:00:00": 0.1703, "18:15:00": 0.1703, "18:30:00": 0.1703, "18:45:00": 0.1703, "19:00:00": 0.0996, "19:15:00": 0.0996, "19:30:00": 0.0996, "19:45:00": 0.0996, "20:00:00": 0.0996, "20:15:00": 0.0996, "20:30:00": 0.0996, "20:45:00": 0.0996, "21:00:00": 0.0996, "21:15:00": 0.0996, "21:30:00": 0.0996, "21:45:00": 0.0996, "22:00:00": 0.0996, "22:15:00": 0.0996, "22:30:00": 0.0996, "22:45:00": 0.0996, "23:00:00": 0.0996, "23:15:00": 0.0996, "23:30:00": 0.0996, "23:45:00": 0.0996},
            "price_fixed_include_td": {"00:00:00": 0.2514, "00:15:00": 0.2514, "00:30:00": 0.2514, "00:45:00": 0.2514, "01:00:00": 0.2514, "01:15:00": 0.2514, "01:30:00": 0.2514, "01:45:00": 0.2514, "02:00:00": 0.2514, "02:15:00": 0.2514, "02:30:00": 0.2514, "02:45:00": 0.2514, "03:00:00": 0.2514, "03:15:00": 0.2514, "03:30:00": 0.2514, "03:45:00": 0.2514, "04:00:00": 0.2514, "04:15:00": 0.2514, "04:30:00": 0.2514, "04:45:00": 0.2514, "05:00:00": 0.2514, "05:15:00": 0.2514, "05:30:00": 0.2514, "05:45:00": 0.2514, "06:00:00": 0.2514, "06:15:00": 0.2514, "06:30:00": 0.2514, "06:45:00": 0.2514, "07:00:00": 0.2514, "07:15:00": 0.2514, "07:30:00": 0.2514, "07:45:00": 0.2514, "08:00:00": 0.6617, "08:15:00": 0.6617, "08:30:00": 0.6617, "08:45:00": 0.6617, "09:00:00": 0.6617, "09:15:00": 0.6617, "09:30:00": 0.6617, "09:45:00": 0.6617, "10:00:00": 1.126, "10:15:00": 1.126, "10:30:00": 1.126, "10:45:00": 1.126, "11:00:00": 1.126, "11:15:00": 1.126, "11:30:00": 1.126, "11:45:00": 1.126, "12:00:00": 0.6617, "12:15:00": 0.6617, "12:30:00": 0.6617, "12:45:00": 0.6617, "13:00:00": 0.6617, "13:15:00": 0.6617, "13:30:00": 0.6617, "13:45:00": 0.6617, "14:00:00": 1.126, "14:15:00": 1.126, "14:30:00": 1.126, "14:45:00": 1.126, "15:00:00": 1.126, "15:15:00": 1.126, "15:30:00": 1.126, "15:45:00": 1.126, "16:00:00": 1.126, "16:15:00": 1.126, "16:30:00": 1.126, "16:45:00": 1.126, "17:00:00": 1.126, "17:15:00": 1.126, "17:30:00": 1.126, "17:45:00": 1.126, "18:00:00": 1.126, "18:15:00": 1.126, "18:30:00": 1.126, "18:45:00": 1.126, "19:00:00": 0.6617, "19:15:00": 0.6617, "19:30:00": 0.6617, "19:45:00": 0.6617, "20:00:00": 0.6617, "20:15:00": 0.6617, "20:30:00": 0.6617, "20:45:00": 0.6617, "21:00:00": 0.6617, "21:15:00": 0.6617, "21:30:00": 0.6617, "21:45:00": 0.6617, "22:00:00": 0.6617, "22:15:00": 0.6617, "22:30:00": 0.6617, "22:45:00": 0.6617, "23:00:00": 0.6617, "23:15:00": 0.6617, "23:30:00": 0.6617, "23:45:00": 0.6617}
        }
        if self.month in ["07", "08", "09"]:
            price_td_dict = data_price_dict["price_td_789"]
        else:
            price_td_dict = data_price_dict["price_td"]

        if self.type_price == "fixed":
            # 读取固定价格(包含了输配电价，日前价格和实时价格相同)
            # columns = ['date_time', 'date', 'time', '购电价格(元/KWh)', 'pred_ahead_price', 'pred_real_price']
            price_fixed_include_td = data_price_dict["price_fixed_include_td"]
            price_pred = self.load_pred[["date_time", "date", "time"]]
            price_pred["购电价格(元/KWh)"] = price_pred["time"].map(price_fixed_include_td)
            price_pred["pred_ahead_price"] = price_pred["购电价格(元/KWh)"].values  # 日前价格(元/KWh)
            price_pred["pred_real_price"] = price_pred["购电价格(元/KWh)"].values  # 实时价格(元/KWh)

        elif self.type_price == "spots":
            # 现货价格预测：datasets查取历史30天数据，预测每个时刻的价格
            # columns = ['date_time', 'date', 'time', '日前电能量价格预测', '实时电能量价格预测', '输配电价(元/KWh)',
            #           'pred_ahead_price', 'pred_real_price']
            date_end_train = self.dt_start_output.split(" ")[0]
            date_start_train = str(pd.to_datetime(date_end_train) - timedelta(30)).split(" ")[0]
            df3 = get_data_datasets(grid="GUANGDONG",
                                    key_dict={"/日前/统一结算点电价": 1041, "/实时/统一结算点电价": 1038},
                                    date_start=date_start_train, date_end=date_end_train,
                                    path_save=None).reset_index().dropna()
            df3["time"] = df3["date_time"].map(lambda s: s.split(" ")[1])

            # 日前价格预测字典：data_price_ahead
            data_price_ahead = df3.pivot(index="time", columns="date", values="/日前/统一结算点电价")
            pred_ahead_price_dict = {}
            for hour in data_price_ahead.index.values:
                values = data_price_ahead.loc[hour].dropna().values   # hour小时 不同日期的价格列表
                pred_price_hour = self.calc_ema(values)    # 当前hour的预测价格
                pred_ahead_price_dict[hour] = pred_price_hour

            # 实时价格预测字典：data_price_real
            data_price_real = df3.pivot(index="time", columns="date", values="/实时/统一结算点电价")
            pred_real_price_dict = {}
            for hour in data_price_real.index.values:
                values = data_price_real.loc[hour].dropna().values  # hour小时 不同日期的价格列表
                pred_price_hour = self.calc_ema(values)  # 当前hour的预测价格
                pred_real_price_dict[hour] = pred_price_hour

            # 构造输出结构
            price_pred = self.load_pred[["date_time", "date", "time"]]
            price_pred["日前电能量价格预测"] = price_pred["time"].map(pred_ahead_price_dict)
            price_pred["实时电能量价格预测"] = price_pred["time"].map(pred_real_price_dict)
            price_pred = price_pred.fillna(method="ffill")  # 扩充为96点
            price_pred["输配电价(元/KWh)"] = price_pred["time"].map(price_td_dict)  # 添加输配电价列
            price_pred["pred_ahead_price"] = price_pred["日前电能量价格预测"] / 1000 + price_pred["输配电价(元/KWh)"]
            price_pred["pred_real_price"] = price_pred["实时电能量价格预测"] / 1000 + price_pred["输配电价(元/KWh)"]

        else:
            raise NotImplementedError(f"价格类型 '{self.type_price}' 未设定，请检查！")

        # # 价格预测数据保存
        # path_price_pred = rf"F:\ToGeek\data_research\储能收益优化_广东\20231018_zl\data_output\2_价格预测_{type_price}.xlsx"
        # price_pred.to_excel(path_price_pred, index=False, encoding="utf8")

        return price_pred

    def model_pyomo(self):
        """ 建模 """
        # 1. 全局参数计算
        pred_ahead_price = tuple(self.price_pred["pred_ahead_price"].values)  # 目标日，日前电价预测值
        pred_real_price = tuple(self.price_pred["pred_real_price"].values)  # 目标日，实时电价预测值
        load_pred_park = tuple(self.load_pred.set_index(["date_time", "date", "time"]).sum(axis=1).values)   # 园区净负荷=各区域负荷加总
        pred_lambda_list = tuple(np.array([1]).repeat(96))  # lambda 全部设置为1

        # 2. 创建模型
        model = pyo.ConcreteModel()

        # 3.添加参数：索引集合
        model.idx = pyo.RangeSet(0, 95)    # 首尾均包含

        # 4. 添加参数：日前电价预测(元/KWh) & 实时电价预测(元/KWh)
        model.pred_ahead_price = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(pred_ahead_price)})
        model.pred_real_price = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(pred_real_price)})

        # 5. 添加参数：预测园区净负荷(KW)
        model.load_pred_park = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(load_pred_park)})

        # 5. 添加参数：园区申报策略 lambda
        model.lambda_list = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(pred_lambda_list)})

        # 6. 添加决策变量：最大需量(KW)
        # upper_demand = self.capacity_transformer * self.price_capacity / self.price_max_demand
        model.max_demand = pyo.Var(domain=pyo.NonNegativeReals)    # 目标需量为非负整数

        # 7. 各储能未来96个时刻的充放电功率(KW)
        model.power_list_2001 = pyo.Var(model.idx, bounds=(-100, 100))
        model.power_list_2002 = pyo.Var(model.idx, bounds=(-100, 100))
        model.power_list_2020 = pyo.Var(model.idx, bounds=(-500, 500))
        model.power_list_2030 = pyo.Var(model.idx, bounds=(-500, 500))
        model.power_list_2040 = pyo.Var(model.idx, bounds=(-500, 500))
        # 储能总功率
        power_storage_sum = [model.power_list_2001[i] + model.power_list_2002[i] + model.power_list_2020[i] + model.power_list_2030[i] + model.power_list_2040[i] for i in model.idx]

        # 8. 各储能每个时段终点储能的电量值(KWh)
        model.elec_list_2001 = pyo.Var(model.idx, bounds=(self.attribute_storage["2001"]["soc_min"] * 209, self.attribute_storage["2001"]["soc_max"] * 209))
        model.elec_list_2002 = pyo.Var(model.idx, bounds=(self.attribute_storage["2002"]["soc_min"] * 209, self.attribute_storage["2002"]["soc_max"] * 209))
        model.elec_list_2020 = pyo.Var(model.idx, bounds=(self.attribute_storage["2020"]["soc_min"] * 1000, self.attribute_storage["2020"]["soc_max"] * 1000))
        model.elec_list_2030 = pyo.Var(model.idx, bounds=(self.attribute_storage["2030"]["soc_min"] * 1000, self.attribute_storage["2030"]["soc_max"] * 1000))
        model.elec_list_2040 = pyo.Var(model.idx, bounds=(self.attribute_storage["2040"]["soc_min"] * 2000, self.attribute_storage["2040"]["soc_max"] * 2000))

        # 9. 约束1： 若选择"需量调节"或者"混合"，则每个时刻的园区总负荷均要小于最大需量
        def const1(model, i):
            return load_pred_park[i] + power_storage_sum[i] <= model.max_demand
        if (self.method == "demand") | (self.method == "mix"):
            model.const1 = pyo.Constraint(model.idx, rule=const1)

        # 10. 约束2: 每个时段终点电量=该时段起点电量+该时段功率*该时段时长
        def const_PCS1(model, i):
            """ PCS1, 每个时段的终点电量=该时段起点电量+该时段功率*该时段时长 """
            init_elec = self.init_elec["2001"]
            if i == 0:
                return model.elec_list_2001[i] == init_elec + model.power_list_2001[i] * 0.25
            else:
                return model.elec_list_2001[i] == model.elec_list_2001[i - 1] + model.power_list_2001[i] * 0.25
        model.const_elec_1 = pyo.Constraint(model.idx, rule=const_PCS1)

        def const_PCS2(model, i):
            """ PCS2, 每个时段的终点电量=该时段起点电量+该时段功率*该时段时长 """
            init_elec = self.init_elec["2002"]
            if i == 0:
                return model.elec_list_2002[i] == init_elec + model.power_list_2002[i] * 0.25
            else:
                return model.elec_list_2002[i] == model.elec_list_2002[i - 1] + model.power_list_2002[i] * 0.25
        model.const_elec_2 = pyo.Constraint(model.idx, rule=const_PCS2)

        def const_PCS20(model, i):
            """ PCS20, 每个时段的终点电量=该时段起点电量+该时段功率*该时段时长 """
            init_elec = self.init_elec["2020"]
            if i == 0:
                return model.elec_list_2020[i] == init_elec + model.power_list_2020[i] * 0.25
            else:
                return model.elec_list_2020[i] == model.elec_list_2020[i - 1] + model.power_list_2020[i] * 0.25
        model.const_elec_20 = pyo.Constraint(model.idx, rule=const_PCS20)

        def const_PCS30(model, i):
            """ PCS30, 每个时段的终点电量=该时段起点电量+该时段功率*该时段时长 """
            init_elec = self.init_elec["2030"]
            if i == 0:
                return model.elec_list_2030[i] == init_elec + model.power_list_2030[i] * 0.25
            else:
                return model.elec_list_2030[i] == model.elec_list_2030[i - 1] + model.power_list_2030[i] * 0.25
        model.const_elec_30 = pyo.Constraint(model.idx, rule=const_PCS30)

        def const_PCS40(model, i):
            """ PCS40, 每个时段的终点电量=该时段起点电量+该时段功率*该时段时长 """
            init_elec = self.init_elec["2040"]
            if i == 0:
                return model.elec_list_2040[i] == init_elec + model.power_list_2040[i] * 0.25
            else:
                return model.elec_list_2040[i] == model.elec_list_2040[i - 1] + model.power_list_2040[i] * 0.25
        model.const_elec_40 = pyo.Constraint(model.idx, rule=const_PCS40)

        # 10. 约束3：储能总负荷 + 园区净负荷 >=0
        def const_power(model, i):
            """ 储能总负荷 + 园区净负荷 >= 0 """
            return model.load_pred_park[i] + power_storage_sum[i] >= 0
        model.const_power = pyo.Constraint(model.idx, rule=const_power)

        # 11. 约束4：电表3对应区域的园区净负荷 + 储能30 的功率 < 变压器容量 1600 * 变压器容量上限系数
        def const_3_30(model, i):
            power_2003 = tuple(self.load_pred["pred_power_2003"].values)  # 电表3对应区域的园区净负荷预测
            return power_2003[i] + model.power_list_2030[i] <= 1600 * self.coef_max_transformer
        model.const_3_30 = pyo.Constraint(model.idx, rule=const_3_30)

        # 12. 约束5：电表6对应区域的园区净负荷 + 储能40 的功率 < 变压器容量 1600 * 变压器容量上限系数
        def const_6_40(model, i):
            power_2006 = tuple(self.load_pred["pred_power_2006"].values)  # 电表6对应区域的园区净负荷预测
            return power_2006[i] + model.power_list_2040[i] <= 1600 * self.coef_max_transformer

        model.const_6_40 = pyo.Constraint(model.idx, rule=const_6_40)

        # 13. 约束6：电表7对应区域的园区净负荷 + 储能1+2+20 的功率 < 变压器容量 1000 * 变压器容量上限系数
        def const_7_1220(model, i):
            power_2007 = tuple(self.load_pred["pred_power_2007"].values)  # 电表7对应区域的园区净负荷预测
            return power_2007[i] + model.power_list_2001[i] + model.power_list_2002[i] + model.power_list_2020[i] <= 1000 * self.coef_max_transformer
        model.const_7_1220 = pyo.Constraint(model.idx, rule=const_7_1220)

        # 14. 目标日峰谷套利收益(元): - Q_储 * (λ * (P_日前 - P_实时) + P_实时)
        # # 储能盈利价格等效于：- (λ * (P_日前 - P_实时) + P_实时)
        storage_price_list = [model.lambda_list[i] * (model.pred_ahead_price[i] - model.pred_real_price[i]) + model.pred_real_price[i] for i in model.idx]
        income_fenggu_2001 = sum([model.power_list_2001[i] * 0.25 * storage_price_list[i] for i in model.idx])
        income_fenggu_2002 = sum([model.power_list_2002[i] * 0.25 * storage_price_list[i] for i in model.idx])
        income_fenggu_2020 = sum([model.power_list_2020[i] * 0.25 * storage_price_list[i] for i in model.idx])
        income_fenggu_2030 = sum([model.power_list_2030[i] * 0.25 * storage_price_list[i] for i in model.idx])
        income_fenggu_2040 = sum([model.power_list_2040[i] * 0.25 * storage_price_list[i] for i in model.idx])
        income_fenggu = - (income_fenggu_2001 + income_fenggu_2002 + income_fenggu_2020 + income_fenggu_2030 + income_fenggu_2040)

        # 15. 需量调节收益(元) = 容量电费 - 调节后的需量电费
        fee_capacity = self.capacity_transformer * self.price_capacity / self.days_month     # 目标日容量电费(元)
        fee_demand = model.max_demand * self.price_max_demand / self.days_month
        income_demand = fee_capacity - fee_demand

        # 16. 添加目标函数
        def obj_rule(model):
            if self.method == "demand":
                return income_demand
            elif self.method == "arbitrage":
                return income_fenggu
            elif self.method == "mix":
                return income_fenggu + income_demand
            else:
                raise NotImplementedError(f"模式{self.method}未定义")
        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)
        return model, income_demand, income_fenggu, power_storage_sum

    def optimize(self):
        # 1. 创建输出格式
        res_dict = {}

        # 2. 优化问题建模
        logger.info("start: 建模 ... ")
        model, income_demand, income_fenggu, power_storage_sum = self.model_pyomo()

        # 3. 调用求解器ipopt求解
        logger.info("start: 优化求解 ... ")
        opt = pyo.SolverFactory("ipopt")
        solution = opt.solve(model)

        # 4. 获取关键参数
        if (self.method == "demand") | (self.method == "mix"):
            opt_income_demand = pyo.value(income_demand)  # 需量调节收益
            opt_income_fenggu = pyo.value(income_fenggu)  # 峰谷套利收益
            opt_max_demand = model.max_demand()  # 优化后最大需量
        else:
            opt_income_fenggu = pyo.value(income_fenggu)    # 峰谷套利收益
            # 优化后最大需量计算
            opt_max_demand = max([pyo.value(model.load_pred_park[i] + power_storage_sum[i]) for i in range(96)])
            # 需量调节收益
            fee_capacity = self.capacity_transformer * self.price_capacity / self.days_month  # 目标日容量电费(元)
            fee_demand = opt_max_demand * self.price_max_demand / self.days_month
            opt_income_demand = fee_capacity - fee_demand

        res_dict["max_demand"] = opt_max_demand
        res_dict["profit_demand"] = opt_income_demand
        res_dict["profit_arbitrage"] = opt_income_fenggu

        logger.info(f"优化后最大需量(KW): {opt_max_demand}")
        logger.info(f"需量调节收益(元): {opt_income_demand}")
        logger.info(f"峰谷套利收益(元): {opt_income_fenggu}")

        # 对应参数提取
        df_res = self.price_pred[["date_time", "date", "time"]]
        df_res["power_2001"] = [pyo.value(model.power_list_2001[i]) for i in range(96)]
        df_res["power_2002"] = [pyo.value(model.power_list_2002[i]) for i in range(96)]
        df_res["power_2020"] = [pyo.value(model.power_list_2020[i]) for i in range(96)]
        df_res["power_2030"] = [pyo.value(model.power_list_2030[i]) for i in range(96)]
        df_res["power_2040"] = [pyo.value(model.power_list_2040[i]) for i in range(96)]

        df_res["elec_2001"] = [pyo.value(model.elec_list_2001[i]) for i in range(96)]
        df_res["elec_2002"] = [pyo.value(model.elec_list_2002[i]) for i in range(96)]
        df_res["elec_2020"] = [pyo.value(model.elec_list_2020[i]) for i in range(96)]
        df_res["elec_2030"] = [pyo.value(model.elec_list_2030[i]) for i in range(96)]
        df_res["elec_2040"] = [pyo.value(model.elec_list_2040[i]) for i in range(96)]

        df_res["load_storage_all"] = [pyo.value(power_storage_sum[i]) for i in range(96)]   # 储能总负荷
        df_res["load_park_all"] = [pyo.value(model.load_pred_park[i]) for i in range(96)]   # 园区净负荷预测
        df_res["load_all"] = df_res["load_storage_all"] + df_res["load_park_all"]           # 园区总负荷

        res_dict["strategy_storage"] = df_res.set_index("date_time").to_dict("records")

        # # 数据保存
        # path_out = rf"F:\ToGeek\data_research\储能收益优化_广东\20231018_zl\data_output\5_储能调度策略_{self.method}_{self.type_price}.xlsx"
        # df_res.to_excel(path_out, index=False)
        # print(f"储能调度策略已保存: {os.path.abspath(path_out)}")

        logger.info("---------------------- END: 广东古瑞瓦特智慧园区 储能需量调节&峰谷套利 收益优化 v3.0 --------------------------")
        return res_dict


if __name__ == '__main__':
    # 优化测试
    path_data_demo = r"E:\data_research\储能收益优化_广东\20231018_zl\data_input_demo\data_input_demo_v2.json"
    with open(path_data_demo, "r", encoding="utf-8") as f:
        data_input_demo = json.loads(f.read())

    # 初始化模型
    m = OptimizeEnergyStorageV3(
        method=data_input_demo["method"],                               # 模式：可选: ["demand", "arbitrage", "mix"]
        type_price=data_input_demo["type_price"],                       # 目标时段，储能价格类型, 可选: ["fixed", "spots"]
        capacity_transformer=data_input_demo["capacity_transformer"],   # 变压器计费容量KVA, 6200
        price_capacity=data_input_demo["price_capacity"],               # 容量月度单价（元/千伏安·月），即：基本电费单价
        price_max_demand=data_input_demo["price_max_demand"],           # 需量月度单价（元/千瓦时·月）
        coef_charge=data_input_demo["coef_charge"],                     # 储能系统充放电效率，0-1之间的浮点数，一般为0.85-0.9之间
        coef_max_transformer=data_input_demo["coef_max_transformer"],   # 变压器容量上限系数，一般为：0.8
        attribute_storage=data_input_demo["attribute_storage"],         # 储能soc上下限， 0~1 之间
        data_meter=data_input_demo["data_meter"],                       # 历史电表负荷
        data_storage=data_input_demo["data_storage"],                    # 历史储能电量
    )
    # 求解测试
    model_ = m.model_pyomo()[0]
    print(model_.pprint())
    print(m.optimize())
