# -*- coding: utf-8 -*-

import os
from tglibs.config import ConfigBase, ConfigProperty
from tglibs.easy_dir import join


class APIConfig(ConfigBase):
    host = ConfigProperty(str)
    process_pool = ConfigProperty(bool, default=True)
    pool_number = ConfigProperty(int, default=0)

    def __init__(self):
        super().__init__(join(__file__, '..', 'api.ini'))
        self.load()
        over_value = os.environ.get('DATA_SERVICE_IP')
        if over_value:
            self.host = over_value
        self.host = self.host.rstrip('/')
