# -*- coding: utf-8 -*-
# @Time    : 2022/4/21 14:33
# <AUTHOR> darlene
# @FileName: boot_capacity.py
# @Software: PyCharm
import pandas as pd
import numpy as np
import datetime
import logging
# 导入用于分割训练集和测试集的类
from sklearn.model_selection import train_test_split
# 导入线性回归类
from sklearn.linear_model import LinearRegression
logger = logging.getLogger()


class ForecastBoot:
    '''
    1、数据预处理，时间， 算竞价空间、取历史数据每天的竞价空间最大值,竞价空间差分结果,合并boot_data数据
    2、校验数据，待预测日jingjia_dif是否存在，
    3、用预测日竞价空间除以系数，
    '''
    def __init__(self, all_data, date_list_yuce, boot_data, coe=3.9, days=3, province='山西'):
        logger.info("-------------------负荷率预测开机容量-------------------------------")
        self.jingjia_data, self.input_data = self.pre_data(all_data, boot_data)
        self.result = self.get_result(date_list_yuce, coe, days)

    def pre_data(self, jingjia_data, boot_data):
        jingjia_data = pd.DataFrame(jingjia_data)
        boot_data = pd.DataFrame(boot_data)
        jingjia_data['date'] = jingjia_data['date_time'].map(lambda x: str(x).split(' ')[0])
        jingjia_data['竞价空间'] = jingjia_data['负荷预测'] - jingjia_data['日前新能源负荷预测'] - jingjia_data['日前联络线计划']
        jingjia_day = jingjia_data[['date', '负荷预测', '日前新能源负荷预测', '竞价空间']].groupby('date').max('竞价空间')
        jingjia_day = jingjia_day.reset_index()
        boot_data['date'] = boot_data['date'].map(lambda x: str(x).split(' ')[0])
        jingjia_day = pd.merge(jingjia_day, boot_data, how='left')
        jingjia_day['jingjia_diff'] = jingjia_day['竞价空间'].diff()
        jingjia_day['新能源diff'] = jingjia_day['日前新能源负荷预测'].diff()
        jingjia_day['新能源diff2'] = jingjia_day['日前新能源负荷预测'].diff(2)
        jingjia_day['负荷率'] = jingjia_day['竞价空间'] / jingjia_day['boot_capacity']
        return jingjia_data, jingjia_day

    def get_result(self, date_list_yuce, coe, days):
        '''
        循环预测日期，检查预测日竞价空间和jingjia_dif值是否存在，若存在，算竞价空间+dif/coe，不存在，打印日志，继续循环
        '''
        result = {}
        try:
            for date in date_list_yuce:
                yesterday = (datetime.datetime.strptime(date, "%Y-%m-%d") - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                data_tmp = self.input_data[self.input_data['date'] == date][['jingjia_diff', 'boot_capacity']]
                data_yesterday = self.input_data[self.input_data['date'] == yesterday][['jingjia_diff', 'boot_capacity']]
                if data_tmp[data_tmp[['jingjia_diff']].isnull().values==True].empty and \
                        data_yesterday[data_yesterday[['boot_capacity']].isnull().values==True].empty:
                    result[date] = data_tmp['jingjia_diff'][-1:].values[0] / coe + \
                                   data_yesterday['boot_capacity'][-1:].values[0]
                elif not data_yesterday[data_yesterday[['boot_capacity']].isnull().values==True].empty and \
                        data_tmp[data_tmp[['jingjia_diff']].isnull().values==True].empty and yesterday in result.keys():
                    result[date] = data_tmp['jingjia_diff'][-1:].values[0] / coe + \
                                   result[yesterday]
                else:
                    logger.info("{}日数据不全，无法预测".format(date))
                    continue
            # 判断有没有新能源diff2小于1000的情况，如果有，线性回归，没有就直接往下走
            admend_data = self.amendment_result(date_list_yuce)
            if not admend_data.empty:
                for date_x in admend_data['date'].to_list():
                    tmp = (result[date_x] + self.input_data[self.input_data['date'] == date_x]['竞价空间'].values[0] / admend_data[admend_data['date'] == date_x]['负荷率'].values[0]) / 2
                    result[date_x] = tmp
        except Exception as err:
            logger.info("模型出错：{}".format(err))
        logger.info("------end of boot-------")
        logger.info("result:{}".format(result))
        return result

    def amendment_result(self, date_list_yuce):
        '''
        1、取预测数据新能源，算diff和diff2， 找diff2小于1000的前一天
        2、如果有小于1000的，找历史同样diff2小于1000的数据，线性回归，没有的话直接返回
        3、线性回归后返回
        '''
        date_l1 = []
        admend_data = pd.DataFrame()
        try:
            # 找diff2小于1000的所有日期
            yuce_data = self.input_data[
                (self.input_data['date'].isin(date_list_yuce)) & (self.input_data['新能源diff2'] > -1000) & (
                            self.input_data['新能源diff2'] < 1000)]
            if not yuce_data.empty:
                diff_date = self.input_data[
                    (self.input_data['date'] < min(date_list_yuce)) & (
                            self.input_data['新能源diff2'] > -1000) & (
                            self.input_data['新能源diff2'] < 1000)]['date'].to_list()   # 找历史同样diff2小于1000的数据
                for date in diff_date:
                    date = datetime.datetime.strptime(date, '%Y-%m-%d')
                    date1 = date + datetime.timedelta(days=-1)
                    date2 = date + datetime.timedelta(days=-2)
                    date_l1 = date_l1 + [date2.strftime('%Y-%m-%d'), date1.strftime('%Y-%m-%d'), date.strftime('%Y-%m-%d')] # 历史日期
                histroy_data = self.input_data[self.input_data['date'].isin(date_l1)]  # 历史数据
                histroy_data = histroy_data.dropna(axis=0)
                X_nor = np.array(histroy_data['新能源diff'].to_list())
                x_train, y_train = X_nor.reshape(-1, 1), np.array(histroy_data['负荷率'].to_list())
                lr = LinearRegression()
                lr.fit(x_train, y_train)
                x_test = np.array(yuce_data['新能源diff'].to_list()).reshape(-1, 1)
                y_hat = lr.predict(x_test)
                admend_data['date'] = yuce_data['date'].to_list()
                admend_data['负荷率'] = y_hat
        except Exception as e:
            logger.info("修正过程出错：{}".format(e))
        return admend_data



if __name__ == '__main__':
    all_data = pd.read_excel(r"负荷预测.xlsx")
    boot_data = pd.read_excel(r"开机容量.xlsx")
    fore = ForecastBoot(all_data=all_data, boot_data=boot_data, date_list_yuce=['2022-04-13', '2022-04-15', '2022-04-19'])
    print(fore.result)
