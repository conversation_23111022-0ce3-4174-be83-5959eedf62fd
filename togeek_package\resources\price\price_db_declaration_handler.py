# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.dongbei.price_db_declaration_pre import Declaration


class DeclarationValueEvalHandlerDB(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        date_list = params.pop('date_list')
        province = params.get('province', '东北')
        jiedian_type = params.get('jiedian_type', '统一结算点')
        sj_sign = params.get('sj_sign', 0)
        sj_date = params.get('sj_date', [])
        pre_list = params.get('pre_list', ['日前价格', '实时价格'])
        pred = Declaration(source_data, date_list, province, jiedian_type, sj_sign=sj_sign, sj_date=sj_date, pre_list=pre_list)
        self.write(pred.pre_price(to_json=True))