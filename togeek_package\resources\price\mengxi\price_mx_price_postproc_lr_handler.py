#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
<AUTHOR>
@Date    ：2025/6/19
@Info    ：蒙西节点价格预测
@Update  ：2025/7/7 优化模型
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_node_etr.price_post_process_lr import PricePostProcessorLR


class PricePostProcLRHandlerMX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        df_ref=params.get('df_ref')
        df_adj = params.get("df_adj")
        high_threshold_percentile=params.get('high_threshold_percentile')
        low_threshold=params.get('low_threshold')
        pred = PricePostProcessorLR(
            df_ref=df_ref,
            df_adj=df_adj,
            high_threshold_percentile=high_threshold_percentile,
            low_threshold=low_threshold,
        )
        result = pred.fit_transform()
        self.write(result)
