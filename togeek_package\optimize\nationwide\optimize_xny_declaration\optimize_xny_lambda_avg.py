'''
Author: san
Date: 2022-05-27 11:11:54
Description: 
新能源申报，系数lambda，目前是0.6-1.4之间
依据历史该节点日前价格与实时价格的差的均值计算lambda
以均值在最大最小范围中的位置为依据，同比例置于lambda的约束范围中
输入：
    1、日前、实时统一结算点，实际电价;
    2、lambda的范围
输出：
    1、建议的lambda：
'''


import numpy as np
import pandas as pd
from datetime import datetime, timedelta


class Prediction:
    def __init__(self, price, lambda_min, lambda_max, periods):
        """
        初始化类
        :param price: 历史节点电价
        :param lambda_min: lambda最小值
        :param lambda_max: lambda最大值
        """
        self.lambda_min = lambda_min
        self.lambda_max = lambda_max
        self.price = pd.DataFrame(price).sort_values('date_time')
        self.periods = periods
        self.price['date_time'] = pd.to_datetime(self.price['date_time'])
        if self.periods == 24:
            self.price['时间'] = self.price['date_time'].map(
                lambda s: int(s.hour)
            )
        elif self.periods == 48:
            self.price['时间'] = self.price['date_time'].map(
                lambda s: int(s.hour * 2 + s.minute / 30)
            )
        else:
            self.price['时间'] = self.price['date_time'].map(
                lambda s: int(s.hour * 4 + s.minute / 15 + 1)
            )
        self.price['date_time'] = self.price['date_time'].astype(str)
        self.price['日期'] = self.price['date_time'].map(lambda s: str(s)[:10])
        self.price['差'] = self.price['ahead_price'] - self.price['real_price']

    def predict(self):
        """
        计算每个时点的lambda值
        :return: 建议的lambda,数组
        """
        df2 = self.price.pivot(index='时间', columns='日期', values='差')
        np1 = np.array(df2)
        d_avg1 = np.average(np1, axis=1)
        d_max1 = np.max(np1, axis=1)
        d_min1 = np.min(np1, axis=1)
        # lambda_point = self.lambda_min + (self.lambda_max - self.lambda_min) * (d_avg1 - d_min1) / (d_max1 - d_min1)
        lambda_point = 1 + (self.lambda_max - self.lambda_min) * d_avg1 / (d_max1 - d_min1)
        return lambda_point

    def deal_result(self):
        """
        将结果处理为字典格式
        :return: 建议的lambda,字典
        """
        result = {}
        res = self.predict()

        # 将数组中的nan、inf、-inf值修正为1
        res[np.isinf(res)] = 1
        res[np.isnan(res)] = 1

        date_ = max(set(self.price['日期']))
        date_ = datetime.strptime(date_, '%Y-%m-%d') + timedelta(1)
        if self.periods == 96:
            date_time =  pd.date_range(date_, periods=96, freq='15min')
        elif self.periods == 48:
            date_time =  pd.date_range(date_, periods=48, freq='30T')
        elif self.periods == 24:
            date_time = pd.date_range(date_, periods=24, freq='H')
        else:
            date_time = pd.date_range(date_, periods=96, freq='15min')
        result['date_time'] = [x.strftime('%Y-%m-%d %H:%M:%S') for x in date_time]
        result['lambda'] = list(res)
        return result


if __name__ == "__main__":
    # 1、日前、实时统一结算点，实际电价;
    # 2、lambda的范围
    price = pd.read_excel('C:/Users/<USER>/Desktop/input.xlsx', sheet_name='price', index_col=None)
    lambda_ = pd.read_excel('C:/Users/<USER>/Desktop/input.xlsx', sheet_name='lambda', index_col=None)
    lambda_min = lambda_['lambda_min'][0]
    lambda_max = lambda_['lambda_max'][0]
    print(lambda_min,lambda_max)

    p = Prediction(price, lambda_min, lambda_max)
    result = p.deal_result()
    print(result)

