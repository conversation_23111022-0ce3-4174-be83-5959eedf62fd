# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-07-18 13:30:41
Description :   山东价格预测及准确率计算
    内容：
        D+1：日前价格预测, 实时价格预测，价差方向准确率；通用随机森林模型
        D+2~D+8：日前价格预测，相似日
"""

import numpy as np
import pandas as pd
import requests as rs
from datetime import datetime, timedelta
from chinese_calendar import is_workday
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import ExtraTreesRegressor
import warnings
import logging

pd.set_option('display.max_columns', None)      # 显示所有列
warnings.filterwarnings("ignore")
logger = logging.getLogger()


key_dict = {
        "/日前/统一结算点电价": 399,
        "/实时/统一结算点电价": 405,
        "/日前/直调负荷": 357,
        "/日前/风电总加": 359,
        "/日前/光伏总加": 360,
        "/日前/联络线受电负荷": 358,
        "/日前/地方电厂发电总加": 363,
        "/日前/核电总加": 361,
        "/日前/自备机组总加": 362,
        "/日前/联络线/华北来电": 667,
        "/日前/联络线/银东直流": 668,
        "/日前/联络线/鲁固直流": 669,
        "/日前/联络线/昭沂直流": 670,
    }


class PricePredETR:

    def __init__(self, all_data, date_list_pred, days_train=30, min_value=0, max_value=1500):

        logger.info("---------------------- 通用价格预测方法：极端随机森林ETR --------------------------")

        self.all_data = all_data    # 传入json数据
        self.date_list_pred = date_list_pred    # 预测日期列表
        self.days_train = days_train    # 训练数据天数，默认30天
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

        logger.info(f"预测日期列表: {self.date_list_pred}")

    def data_process(self, data_input_json):
        """
        传入数据整理，划分数据集
        :param data_input_json: 传入dict类型的数据
        :return:
        """

        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame(data_input_json)
        # 日期索引升序排列
        data = data.sort_index()

        # 预测字段检测: label
        if "label" not in data.columns:
            logger.info(f"传入数据中不包含预测字段 'label', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段 'label', 请核对传入数据字段!")
        # 关键特征字段检测: "bidding_space"
        if "bidding_space" not in data.columns:
            logger.info(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")

        cols_input = data.columns.tolist()  # 传入数据的所有字段

        data['date'] = data.index.map(lambda s: str(s).split(' ')[0])    # 日期
        data['time'] = data.index.map(lambda s: str(s).split(' ')[1])    # 时间
        data['hour'] = data['time'].map(lambda s: int(s.split(":")[0]))    # 小时：作为特征之一

        # 每日数据点数
        time_point_list = [1, 2, 3, 4, 6, 8, 12, 24, 48, 96]
        time_point = data['time'].nunique()
        if time_point not in time_point_list:
            logger.info(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")
            raise ValueError(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")

        # 划分数据集
        cols_all = cols_input + ['hour']    # 含特征字段和目标字段
        cols_feature = [col for col in cols_all if col != "label"]    # 仅含特征字段

        # 训练集
        self.date_list_pred.sort()  # 将预测日期升序排
        pred_d1 = self.date_list_pred[0]
        data_train = data[data['date'] < pred_d1]
        data_train = data_train.dropna(axis=0)  # 舍弃含空值的训练数据
        data_train = data_train.sort_index()  # 按日期升序排序

        # 若训练数据最后一天的目标值全部相同（应对实时价格预测），则舍弃最后一日的训练数据
        date_last_train = data_train['date'].max()
        if data_train[data_train['date'] == date_last_train]["label"].nunique() == 1:
            data_train = data_train[data_train['date'].map(lambda s: s != date_last_train)]

        data_train = data_train.iloc[-self.days_train * time_point:, :]     # 筛选指定长度的训练数据

        # 预测集
        data_pred = data[data['date'].map(lambda s: s in self.date_list_pred)]

        # 预测集数据长度检查
        days_pred = len(self.date_list_pred)            # 预测天数
        num_should = days_pred * time_point             # 理论数据量
        num_true = data_pred.shape[0]                   # 实际数据量
        if num_should != num_true:
            raise ValueError(
                f"待预测日期合计{days_pred}天({self.date_list_pred})，相应数据长度应为{num_should}条，实为{num_true}条，请检查传入数据！")

        # 划分X, y
        X_train = data_train[cols_feature]
        y_train = data_train["label"]

        X_pred = data_pred[cols_feature]
        y_pred = data_pred[["label"]]      # 'label' 列应全为空值或全部为相同的填充值，作为输出DataFrame. 索引为日期

        # 预测日传入数据空值检测
        if X_pred.isnull().sum().sum() != 0:
            null_dict = X_pred.isnull().sum().to_dict()
            message = ""
            for k, v in null_dict.items():
                if v != 0:
                    message += f"'{k}'存在{v}个缺失值, "
            message += "请检查预测日数据! "

            logger.info(message)
            raise ValueError(message)

        logger.info(f"训练数据日期范围：'{data_train['date'].min()}' ~ '{data_train['date'].max()}'")
        logger.info(f"训练数据使用字段: {cols_feature}")

        return X_train, y_train, X_pred, y_pred

    def predict(self, n_est=100, max_depth=6):
        """
        模型训练、预测、输出
        """

        # 1. 数据处理
        X_train, y_train, X_pred, y_pred = self.data_process(data_input_json=self.all_data)

        # 2. 创建
        etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)

        # 3. 训练
        etr.fit(X=X_train, y=y_train)

        # 4. 预测及存储
        data_pred = etr.predict(X_pred)
        data_pred = np.maximum(data_pred, self.min_value)   # 最小值修正
        data_pred = np.minimum(data_pred, self.max_value)   # 最大值修正

        y_pred['label'] = data_pred
        y_pred = y_pred.rename(columns={'label': 'label_pred'})

        # 5. 输出为json格式
        result_json = y_pred.to_dict()

        # 6. 写入log
        logger.info("----------------- 通用价格预测方法：极端随机森林ETR 预测完成！ ---------------------")

        return result_json


class AheadPriceD2D3:

    def __init__(self, data, date_bidding: str, date_run: str, time_point: int = 24, min_price=-80, max_price=1300, num_lag=22, days_train=1):
        logger.info("---------------------- Start: 山东D+2、D+3 日前电价预测 --------------------------")
        self.data = data                        # 传入数据，json格式
        self.date_bidding = date_bidding        # 竞价日, 如"2023-06-12"
        self.date_run = date_run                # 运行日, 如"2023-06-14"
        self.time_point = time_point            # 每天的分段数量
        self.min_price = min_price              # 价格下限
        self.max_price = max_price              # 价格上限

        self.num_lag = num_lag                  # 构造lag特征的数量
        self.days_train = days_train            # 训练使用的历史数据天数
        self.message_error = ""                 # 错误信息

        self.days_diff = (pd.to_datetime(date_run) - pd.to_datetime(date_bidding)).days     # 竞价日和运行日之间的天数
        logger.info(f"竞价日: {date_bidding}, 运行日: {date_run}, 预测类型: D+{self.days_diff}")

    def data_verification(self):
        """
        数据校验：筛选过去30天(含)竞价日的日前价格实际值
        如果数据校验失败，只返回message
        否则，返回处理后的历史日前价格实际值
        :return:
        """

        # 读取数据
        df = pd.DataFrame(self.data)

        # 日期规范化，排序
        df.index = pd.to_datetime(df.index).astype(str)
        df = df.sort_index()

        # 添加字段： date, time
        df["date"] = df.index.map(lambda s: s.split(" ")[0])
        df["time"] = df.index.map(lambda s: s.split(" ")[1])

        # 筛选前30天数据
        date_train_start = str(pd.to_datetime(self.date_bidding) - timedelta(30)).split(" ")[0]
        df1 = df[(df['date'] > date_train_start) & (df['date'] <= self.date_bidding)]

        # 数据校验
        num_sample_should = self.time_point * 30
        num_sample_true = df1.shape[0]  # 传入的历史日前实际价格长度
        num_null = df1['ahead_price'].isnull().sum()  # 传入的历史日前实际价格缺失值数量

        # 数据错误信息保存至：self.message_error，若无错误，self.message=""
        if num_sample_true != num_sample_should:
            self.message_error += f"传入数据中，历史日前实际价格应为 {num_sample_should} 条，实为{num_sample_true}条, 请检查传入数据! "
        elif num_null != 0:
            self.message_error += f"传入数据中， 历史日前实际价格存在缺失值, 缺失值数量：{num_null}, 请检查传入数据! "
        else:
            logger.info(f"数据校验：历史日前实际价格应为 {num_sample_should} 条, 实为 {num_sample_true} 条, 数据校验通过.")

        return df1

    def feature_engineering(self, df_true):
        """
        特征工程
        :param df_true: 历史实际日前价格。
        :return:
        """
        # 构造预测日的日期，时间
        df_test = pd.DataFrame(index=pd.date_range(start=pd.to_datetime(self.date_bidding) + timedelta(1), periods=self.days_diff * self.time_point, freq=f"{1440 / self.time_point}T").astype(str))
        df_test['ahead_price'] = -1
        df_test['date'] = df_test.index.map(lambda s: s.split(" ")[0])
        df_test["time"] = df_test.index.map(lambda s: s.split(" ")[1])

        # 数据组合 --> dfc
        dfc = pd.concat([df_true, df_test])
        dfc = dfc.sort_index()

        # 峰平谷特征_山东: cols_fpg_list
        time_list = pd.date_range(start="00:00:00", periods=self.time_point, freq=f"{1440/self.time_point}T").map(lambda s: str(s).split(" ")[1]).to_list()

        if self.time_point == 24:
            feng_gu = ["gu"]*7 + ["ping"] + ["feng"]*3 + ["jian"] + ["ping"]*4 + ["feng"]*3 + ["jian"]*2 + ["ping"]*2 + ["gu"]
        elif self.time_point == 96:
            feng_gu = ["gu"]*28 + ["ping"]*6 + ["feng"]*8 + ["jian"]*4 + ["ping"]*18 + ["feng"]*12 + ["jian"]*8 + ["ping"]*8 + ["gu"]*4
        else:
            self.message_error += f"参数 time_point 不在 [24, 96]中，请核对数据!"

        feng_gu_dict = {k: v for k, v in zip(time_list, feng_gu)}
        dfc["period"] = dfc['time'].map(feng_gu_dict)
        cols_fpg_list = list(pd.get_dummies(dfc["period"]).columns)
        dfc[cols_fpg_list] = pd.get_dummies(dfc["period"])

        # 小时特征: cols1
        cols1 = [f"hour_{i:02}" for i in range(24)]
        dfc[cols1] = pd.get_dummies(dfc['time'].map(lambda s: s[:2]))

        # 周几特征: cols2
        cols2 = [f"dayofweek_{i}" for i in range(7)]
        dfc[cols2] = pd.get_dummies(pd.to_datetime(dfc.index).map(lambda s: s.dayofweek)).values

        # 是否工作日: cols3
        cols3 = ["is_workday"]
        dfc[cols3[0]] = pd.to_datetime(dfc.index).map(lambda s: is_workday(s)) * 1

        # lag特征: cols_lag_list
        idx_lag_list = np.arange(self.days_diff, self.num_lag + self.days_diff)
        cols_lag_list = []
        for idx_lag in idx_lag_list:
            col_lag = f"D_{idx_lag - self.days_diff}"
            dfc[col_lag] = dfc["ahead_price"].shift(idx_lag * self.time_point)
            cols_lag_list.append(col_lag)

        # 特征汇总
        features = cols_lag_list + cols1 + cols2 + cols3 + cols_fpg_list
        data_all = dfc[["date", "ahead_price"] + features].dropna()

        return data_all, features

    def predict(self):
        # 构造输出数据结构
        result = {
            "ahead_price_pred": {},
            "message_error": "",
        }

        # 数据校验
        df_true = self.data_verification()
        if len(self.message_error) != 0:
            result["message_error"] = self.message_error
        else:
            # 特征工程
            data_all, feature_list = self.feature_engineering(df_true)
            # 划分数据集
            data_train = data_all[data_all["date"] <= self.date_bidding]
            data_train = data_train.iloc[-self.days_train * self.time_point:, :]
            data_test = data_all[data_all["date"] == self.date_run]
            X_train = data_train[feature_list]
            X_test = data_test[feature_list]
            y_train = data_train["ahead_price"].values
            df_pred = data_test[["ahead_price"]]
            logging.info(f"训练数据范围：{data_train['date'].unique()[0]} ~ {data_train['date'].unique()[-1]}")
            logging.info(f"训练数据特征：{feature_list}")

            # 标准化
            scaler = StandardScaler()
            scaler.fit(X_train)
            X_train_std = scaler.transform(X_train)
            X_test_std = scaler.transform(X_test)

            # 模型预测
            model = ExtraTreesRegressor()
            model.fit(X_train_std, y_train)
            y_pred = model.predict(X_test_std)
            df_pred["ahead_price_pred"] = np.clip(y_pred, self.min_price, self.max_price)   # 极值修正
            result["ahead_price_pred"] = df_pred.to_dict()["ahead_price_pred"]

        logger.info("---------------------- End: 山东D+2、D+3 日前电价预测 --------------------------")

        return result


class DataPredSD:
    def __init__(self, date_bidding_start, date_bidding_end, method_acc=1):
        self.date_bidding_start = date_bidding_start  # 竞价日起始日期
        self.date_bidding_end = date_bidding_end  # 竞价日结束日期
        self.method_acc = method_acc    # 表示准确率计算方法，默认为1. 可选：[1, 2]， 1表示常用价格预测准确率，2表示国华计算方法

        self.date_selected_start = str(pd.to_datetime(self.date_bidding_start) - timedelta(61)).split(" ")[0]  # 数据查询起始日期
        self.date_selected_end = str(pd.to_datetime(self.date_bidding_end) + timedelta(8)).split(" ")[0]  # 数据查询结束日期

        logger.info(f"----------------------- START: 山东D1~D8价格预测 & 准确度接口, 竞价日期范围: {self.date_bidding_start} ~ {self.date_bidding_end} -------------------------")

    def get_data_shandong(self):
        """
        从datasets 接口查询山东交易中心数据， 返回并保存
        :return: df
                    index: "date_time
                    columns: ['date', 'hour', '日前价格', '实时价格', '/日前/直调负荷', '/日前/风电总加', '/日前/光伏总加',
                              '/日前/联络线受电负荷', '/日前/地方电厂发电总加', '/日前/核电总加', '/日前/自备机组总加',
                              '/日前/联络线/华北来电', '/日前/联络线/银东直流', '/日前/联络线/鲁固直流', '/日前/联络线/昭沂直流'])
        """

        # 1. 构造请求参数
        url = f'http://139.9.77.109/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
        params = {
            'grid': 'SHANDONG',
            'startTime': f"{self.date_selected_start} 00:00:00",
            'endTime': f"{self.date_selected_end} 23:45:00",
            'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
            'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
            }

        # 2. 获取数据
        res_dict = rs.get(url, params=params)

        # 3. 数据整理
        df_list = []

        for key in list(key_dict.keys()):
            df_tmp = pd.DataFrame(res_dict.json()['data'][key]['points'])
            df_tmp = df_tmp.rename(columns={'value': key})
            df_list.append(df_tmp)

        date_time_list = pd.date_range(f"{self.date_selected_start} 00:00:00", f"{self.date_selected_end} 23:45:00", freq="15T").astype(str).values
        df = pd.DataFrame({"time": date_time_list})
        for i in range(len(list(key_dict.keys()))):
            df = pd.merge(df, df_list[i], on='time', how="left")
        df = df.rename(columns={"time": "date_time"})
        df['date'] = df['date_time'].map(lambda s: s.split(" ")[0])
        df = df.set_index(["date_time", "date"]).reset_index()

        df = df.rename(columns={"/日前/统一结算点电价": "日前价格", "/实时/统一结算点电价": "实时价格", })

        # 4. 96 -> 24
        df['hour'] = df['date_time'].map(lambda x: x.split(" ")[1].split(":")[0])
        df = df.groupby(["date", "hour"], as_index=False).mean()
        df.index = df.apply(lambda item: f"{item['date']} {item['hour']}:00:00", axis=1)

        return df

    def get_pred_D1(self, data_input_D1):
        """
        D+1日前价格预测，D+1实时价格预测，D+1价差预测
        :param data_input_D1: 输入数据
        :return: df_pred_D1
        """
        # 新增字段：bidding_space
        data_input_D1["bidding_space"] = data_input_D1['/日前/直调负荷'] - data_input_D1["/日前/风电总加"] - data_input_D1["/日前/光伏总加"] - data_input_D1["/日前/联络线受电负荷"]

        # 日前、实时输入数据构造
        df_ahead = data_input_D1.copy()
        df_ahead = df_ahead.drop("实时价格", axis=1)
        df_ahead = df_ahead.rename(columns={"日前价格": "label"})

        df_real = data_input_D1.copy()
        df_real = df_real.drop("日前价格", axis=1)
        df_real = df_real.rename(columns={"实时价格": "label"})

        # 遍历调用模型进行计算
        df_pred_list = []
        date_bidding_list = pd.date_range(self.date_bidding_start, self.date_bidding_end).astype(str).values
        for date_bidding in date_bidding_list:
            print(f"D+1: {date_bidding}")
            date_pred = str(pd.to_datetime(date_bidding) + timedelta(1)).split(" ")[0]

            # 日前D+1预测
            model_ahead = PricePredETR(all_data=df_ahead, date_list_pred=[date_pred], days_train=30, min_value=-80, max_value=1300)
            pred_ahead = pd.DataFrame(model_ahead.predict())
            dfc_ahead = pd.merge(pred_ahead, df_ahead[['label']], left_index=True, right_index=True, how="left")
            dfc_ahead = dfc_ahead.rename(columns={"label": "日前价格实际值", "label_pred": "日前价格预测值"})

            # 实时D+1预测
            model_real = PricePredETR(all_data=df_real, date_list_pred=[date_pred], days_train=30, min_value=-80, max_value=1300)
            pred_real = pd.DataFrame(model_real.predict())
            dfc_real = pd.merge(pred_real, df_real[['label']], left_index=True, right_index=True, how="left")
            dfc_real = dfc_real.rename(columns={"label": "实时价格实际值", "label_pred": "实时价格预测值"})

            # 日前，实时组合
            dfc = pd.merge(dfc_ahead, dfc_real, left_index=True, right_index=True)
            dfc["竞价日期"] = date_bidding
            df_pred_list.append(dfc)

        # 日前&实时 预测结果组合
        df_pred_D1 = pd.concat(df_pred_list)
        df_pred_D1["运行日期"] = pd.to_datetime(df_pred_D1.index).astype(str)

        # 计算价差
        df_pred_D1["实际价差"] = df_pred_D1["日前价格实际值"] - df_pred_D1["实时价格实际值"]     # 实际价差
        df_pred_D1["预测价差"] = df_pred_D1["日前价格预测值"] - df_pred_D1["实时价格预测值"]     # 预测价差
        df_pred_D1["价差方向相同"] = df_pred_D1['实际价差'] * df_pred_D1['预测价差'] >= 0       # 是否价差同向
        # 若实际价格不存在，则价差方向设置为nan
        df_pred_D1.loc[np.isnan(df_pred_D1["日前价格实际值"]), "价差方向相同"] = np.nan
        df_pred_D1.loc[np.isnan(df_pred_D1["实时价格实际值"]), "价差方向相同"] = np.nan

        # 格式整理
        df_pred_D1["类型"] = "D+1"
        use_cols = ["竞价日期", "运行日期", "类型", "日前价格预测值", "日前价格实际值", "实时价格预测值", "实时价格实际值", "价差方向相同"]
        # use_cols = ["竞价日期", "运行日期", "类型", "日前价格预测值", "实时价格预测值"]
        df_pred_D1 = df_pred_D1[use_cols]
        df_pred_D1 = df_pred_D1.reset_index(drop=True)

        return df_pred_D1

    def get_pred_D2D3(self, data_input_D2D3):
        """
        D+2~D+8日前价格预测
        :param data_input_D2D3: index: date_time; columns=["ahead_price"]
        :return: df_pred_D2_D8
        """

        # 遍历回测
        df_pred_list = []
        date_bidding_list = pd.date_range(self.date_bidding_start, self.date_bidding_end).astype(str).values
        for date_bidding in date_bidding_list:
            print(f"D2D3: {date_bidding}")

            # D+{d}日预测
            for d in range(2, 9):
                date_pred = str(pd.to_datetime(date_bidding) + timedelta(d)).split(" ")[0]
                model = AheadPriceD2D3(
                    data=data_input_D2D3,
                    date_bidding=date_bidding,
                    date_run=date_pred,
                    time_point=24,
                    min_price=-80,
                    max_price=1300,
                )
                df_pred = pd.DataFrame(model.predict())
                df_pred['date_bidding'] = date_bidding
                df_pred['date_run'] = date_pred
                df_pred_list.append(df_pred)

        # 预测结果整理
        df_pred = pd.concat(df_pred_list).reset_index().rename(columns={"index": "date_time"})
        df_pred = df_pred[['date_bidding', "date_run", "date_time", "ahead_price_pred"]]

        # 实际/预测组合
        df_true = data_input_D2D3.reset_index().rename(columns={"index": "date_time"})
        dfc = pd.merge(df_pred, df_true, on="date_time", how="left")
        dfc["category"] = (pd.to_datetime(dfc['date_run']) - pd.to_datetime(dfc['date_bidding'])).map(lambda s: f"D+{s.days}")

        # 格式整理
        col_dict = {
            "date_bidding": "竞价日期",
            "date_time": "运行日期",
            "category": "类型",
            "ahead_price_pred": "日前价格预测值",
            "ahead_price": "日前价格实际值"
        }
        dfc = dfc.rename(columns=col_dict)
        dfc = dfc[col_dict.values()]
        dfc = dfc.reset_index(drop=True)

        return dfc

    def predict(self):
        """ D+1~D+8预测值获取 """
        # 1. 数据查询
        data_fixed_24 = self.get_data_shandong()

        # 2. D+1预测值
        data_input_D1 = data_fixed_24.drop(["date", "hour"], axis=1)
        df_pred_D1 = self.get_pred_D1(data_input_D1=data_input_D1)

        # 3. D+2~D+8预测值
        data_input_D2D8 = data_fixed_24[["日前价格"]].rename(columns={"日前价格": "ahead_price"})
        df_pred_D2D8 = self.get_pred_D2D3(data_input_D2D3=data_input_D2D8)

        # 4. 预测结果组合
        data_pred = pd.concat([df_pred_D1, df_pred_D2D8])
        data_pred = data_pred.sort_values(["竞价日期", "运行日期"]).reset_index(drop=True)

        # 5. 计算准确率
        data_acc = calc_acc_all(data_pred=data_pred, method_acc=self.method_acc)

        logger.info(f"----------------------- END: 山东D1~D8价格预测 & 准确度接口, 竞价日期范围: {self.date_bidding_start} ~ {self.date_bidding_end} -------------------------")

        return data_pred, data_acc


def calc_acc(y_true, y_pred):
    """
    整日准确度计算
    若当日实际值全为0,记准确率为0
    若准确率小于0, 置为0
    """
    y_true = np.array(y_true).flatten()
    y_pred = np.array(y_pred).flatten()
    if sum(y_true) == 0:
        acc = 0  # 若实际价格全为0，将准确度记为0
    else:
        acc = 1 - sum(abs(y_true - y_pred)) / sum(abs(y_true))
    acc = 0 if acc < 0 else acc  # 准确度小于0的置零
    return float(np.round(acc, 4))


def calc_acc_55(y_true, y_pred):
    """ 国华山东招标文件准确率计算方式 """

    y_true = np.array(y_true).flatten()
    y_pred = np.array(y_pred).flatten()

    # 若小于55，记为55
    y_true = np.clip(y_true, 55, 1300)
    y_pred = np.clip(y_pred, 55, 1300)

    acc = 1 - np.sum(np.abs(y_pred - y_true) / ((abs(y_pred) + abs(y_true)) / 2)) / len(y_true)

    acc = 0 if acc < 0 else acc  # 准确度小于0的置零

    return float(np.round(acc, 4))


def calc_acc_all(data_pred, method_acc=1):
    """
    准确率计算
    :param data_pred: 预测结果组合
    :param method_acc: 表示准确率计算方法，默认为1. 可选：[1, 2]， 1表示常用价格预测准确率，2表示国华计算方法
    :return:
    """
    # 从预测数据中获取竞价日期列表
    date_bid_list = data_pred["竞价日期"].unique()

    # 准确率计算
    cols = [
        "D1_日前价格准确率", "D1_实时价格准确率", "D1_价差方向准确率", "D2_日前价格准确率", "D3_日前价格准确率",
        "D4_日前价格准确率", "D5_日前价格准确率", "D6_日前价格准确率", "D7_日前价格准确率", "D8_日前价格准确率",
    ]
    df_acc = pd.DataFrame(columns=cols)

    for bid_date in date_bid_list:
        df = data_pred[data_pred["竞价日期"] == bid_date]

        # D+1日：日前、实时、价差 准确率计算
        df1 = df[df["类型"] == "D+1"]
        y_true_ahead = df1["日前价格实际值"].values
        y_pred_ahead = df1["日前价格预测值"].values
        y_true_real = df1["实时价格实际值"].values
        y_pred_real = df1["实时价格预测值"].values
        if method_acc == 1:
            acc_ahead_d1 = calc_acc(y_true=y_true_ahead, y_pred=y_pred_ahead)       # 日前价格准确率
            acc_real_d1 = calc_acc(y_true=y_true_real, y_pred=y_pred_real)          # 实时价格准确率
        elif method_acc == 2:
            acc_ahead_d1 = calc_acc_55(y_true=y_true_ahead, y_pred=y_pred_ahead)    # 日前价格准确率
            acc_real_d1 = calc_acc_55(y_true=y_true_real, y_pred=y_pred_real)       # 实时价格准确率
        else:
            acc_ahead_d1 = "准确率计算方法未定义"  # 日前价格准确率
            acc_real_d1 = "准确率计算方法未定义"  # 实时价格准确率

        # 价差准确率计算
        acc_diff_d1 = df1["价差方向相同"].sum() / len(df1)

        # D+1日准确率存储
        df_acc.loc[bid_date, "D1_日前价格准确率"] = acc_ahead_d1
        df_acc.loc[bid_date, "D1_实时价格准确率"] = acc_real_d1
        df_acc.loc[bid_date, "D1_价差方向准确率"] = acc_diff_d1

        # D+2 ~ D+8 日前价格准确率计算 & 存储
        for t in np.arange(2, 9):
            tmp = df[df["类型"] == f"D+{t}"]
            y_true_t = tmp["日前价格实际值"].values
            y_pred_t = tmp["日前价格预测值"].values
            if method_acc == 1:
                acc_ahead_t = calc_acc(y_true=y_true_t, y_pred=y_pred_t)  # 日前价格准确率
            elif method_acc == 2:
                acc_ahead_t = calc_acc_55(y_true=y_true_t, y_pred=y_pred_t)  # 日前价格准确率
            else:
                acc_ahead_t = "准确率计算方法未定义"  # 日前价格准确率
            df_acc.loc[bid_date, f"D{t}_日前价格准确率"] = acc_ahead_t

    df_acc.index.name = "竞价日期"
    df_acc = df_acc.reset_index()

    # 价差准确率修正
    yesterday = str(datetime.today() - timedelta(days=1)).split(" ")[0]
    df_acc.loc[df_acc["竞价日期"] >= yesterday, "D1_价差方向准确率"] = np.nan

    return df_acc


if __name__ == '__main__':
    # 参数设置
    _date_bidding_start = "2023-10-25"  # 竞价日起始日期
    _date_bidding_end = "2023-10-25"  # 竞价日结束日期
    _method_acc = 2

    m = DataPredSD(date_bidding_start=_date_bidding_start, date_bidding_end=_date_bidding_end, method_acc=_method_acc)
    _data_pred, _data_acc = m.predict()

    # # 结果数据保存
    # import os
    # from pandas import ExcelWriter
    # path_data_pred = rf"C:\Users\<USER>\Desktop\data_shandong_{_date_bidding_start}_{_date_bidding_end}.xlsx"
    # with ExcelWriter(path_data_pred, mode="w", engine="openpyxl") as writer:
    #     # _df_pred_D1.to_excel(writer, sheet_name="pred_D1", index=False, encoding="utf-8")
    #     _data_acc.to_excel(writer, sheet_name="准确率", index=False, encoding="utf-8")
    #     _data_pred.to_excel(writer, sheet_name="预测值", index=False, encoding="utf-8")
    # print(f"预测数据已保存，请查看：{os.path.abspath(path_data_pred)}")
    print(_data_pred)
    print(_data_acc)
