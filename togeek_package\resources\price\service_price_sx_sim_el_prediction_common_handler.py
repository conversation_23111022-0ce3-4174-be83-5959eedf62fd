"""
Author: <PERSON><PERSON>
Datetime: 2022/12/29/029 11:21
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shanxi.price_sim_el_price_prediction import CommonEtrPredictPrice


class PricePredictionValueEvalHandlerServiceCommonSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price = params.pop('price')
        run_date = params.pop('run_date', None)
        bsf = CommonEtrPredictPrice(price, run_date=run_date)
        data = bsf.pred_price(to_json=True)
        self.write(data)

