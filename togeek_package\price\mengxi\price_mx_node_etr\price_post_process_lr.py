import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
import logging

logger = logging.getLogger()

class PricePostProcessorLR:
    """价格后处理类，使用线性回归模型对预测价格进行动态校正"""
    
    def __init__(self, high_threshold_percentile=0.8, low_threshold=10,df_ref=None,df_adj=None):
        """
        初始化价格后处理类
        
        参数:
            high_threshold_percentile: 高价区阈值的分位数(0-1)
            low_threshold: 低价区阈值
        """
        if not high_threshold_percentile:
            self.high_threshold_percentile = 0.8
        else:
            self.high_threshold_percentile = high_threshold_percentile
        if not low_threshold:
            self.low_threshold = 10
        else:
            self.low_threshold = low_threshold
        self.models = {}  # 存储不同价格区间的校正模型
        self.high_price_threshold = None  # 高价区阈值
        self.df_ref=pd.DataFrame.from_dict(df_ref)
        self.df_adj=pd.DataFrame.from_dict(df_adj)
    
    def fit(self, df_ref):
        """
        使用参考数据训练价格校正模型
        
        参数:
            df_ref: 参考数据DataFrame，包含dateTime, pred_value, real_value三列
        """
        # 确保索引是datetime类型
        if not isinstance(df_ref.index, pd.DatetimeIndex):
            df_ref = df_ref.set_index('dateTime')
            df_ref.index = pd.to_datetime(df_ref.index)
        
        # 动态计算高价阈值
        self.high_price_threshold = df_ref['real_value'].quantile(self.high_threshold_percentile)
        logger.info(f"高价阈值(前{self.high_threshold_percentile*100}%): {self.high_price_threshold}")
        
        # 划分价格区间并训练模型
        high_price_data = df_ref[df_ref['real_value'] > self.high_price_threshold]
        mid_price_data = df_ref[(df_ref['real_value'] > self.low_threshold) & 
                              (df_ref['real_value'] <= self.high_price_threshold)]
        low_price_data = df_ref[df_ref['real_value'] <= self.low_threshold]
        
        # 训练各区间的线性回归模型
        for data, name in zip([low_price_data, mid_price_data, high_price_data], 
                             ['low', 'mid', 'high']):
            if not data.empty:
                model = LinearRegression()
                model.fit(data[['pred_value']], data['real_value'])
                self.models[name] = model
                logger.info(f"{name}区间模型训练完成，样本数: {len(data)}")
            else:
                logger.warning(f"{name}区间没有数据，无法训练模型")
    
    def transform(self, df_adj):
        """
        应用训练好的模型对预测数据进行校正
        
        参数:
            df_adj: 待校正数据DataFrame，包含dateTime, pred_value两列
            
        返回:
            DataFrame: 包含dateTime, adj_value两列的校正后数据
        """
        # 确保索引是datetime类型
        if not isinstance(df_adj.index, pd.DatetimeIndex):
            df_adj = df_adj.set_index('dateTime')
            df_adj.index = pd.to_datetime(df_adj.index)
        
        # 创建结果DataFrame
        result = df_adj[['pred_value']].copy()
        result['adj_value'] = np.nan
        
        # 应用高价区模型
        high_mask = df_adj['pred_value'] > self.high_price_threshold
        if 'high' in self.models and high_mask.any():
            result.loc[high_mask, 'adj_value'] = self.models['high'].predict(
                df_adj.loc[high_mask, ['pred_value']]
            )
        
        # 应用中价区模型
        mid_mask = (df_adj['pred_value'] > self.low_threshold) & (df_adj['pred_value'] <= self.high_price_threshold)
        if 'mid' in self.models and mid_mask.any():
            result.loc[mid_mask, 'adj_value'] = self.models['mid'].predict(
                df_adj.loc[mid_mask, ['pred_value']]
            )
        
        # 应用低价区模型
        low_mask = df_adj['pred_value'] <= self.low_threshold
        if 'low' in self.models and low_mask.any():
            result.loc[low_mask, 'adj_value'] = self.models['low'].predict(
                df_adj.loc[low_mask, ['pred_value']]
            )
        
        # 处理未被任何模型覆盖的数据点（如果有）
        no_model_mask = result['adj_value'].isna()
        if no_model_mask.any():
            logger.warning(f"有{no_model_mask.sum()}个数据点没有被任何模型覆盖，使用原始预测值")
            result.loc[no_model_mask, 'adj_value'] = result.loc[no_model_mask, 'pred_value']
        
        # 重置索引并返回结果
        return result.reset_index()[['dateTime', 'adj_value']]
    
    def fit_transform(self,tojson = True):
        """
        先训练模型，然后应用于待校正数据
        
        参数:
            df_ref: 参考数据DataFrame，包含dateTime, pred_value, real_value三列
            df_adj: 待校正数据DataFrame，包含dateTime, pred_value两列
            
        返回:
            DataFrame: 包含dateTime, adj_value两列的校正后数据
        """
        self.fit(self.df_ref)
        df_adj=self.transform(self.df_adj)
        df_adj.reset_index(inplace=True,drop=True)
        df_adj["dateTime"]=df_adj["dateTime"].astype(str)
        if tojson:
            df_adj=df_adj.to_dict(orient='list')
        return df_adj


if __name__ == "__main__":
    # 示例用法
    # 创建示例数据
    np.random.seed(42)
    dates = pd.date_range(start='2025-05-01', end='2025-06-30')
    
    # 参考数据（训练集）
    df_ref = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_pred_5.xlsx")
    
    # 待校正数据（测试集）
    df_adj = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_pred_6.xlsx")
    
    # 实例化后处理器并应用校正
    processor = PricePostProcessorLR(high_threshold_percentile=0.8, low_threshold=10,df_adj=df_adj,df_ref=df_ref)
    df_result = processor.fit_transform()
    # df_result.to_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_pred_adj.xlsx",index=False)
    print("校正结果示例:")
    print(df_result)    