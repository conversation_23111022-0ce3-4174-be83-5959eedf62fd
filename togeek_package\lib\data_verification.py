# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2023-03-02 14:49:47
# Description: 数据校验工具，用于价格预测传入数据修正，修正字段包括
                (必开机组容量, 必停机组容量, 检修机组容量, 日前联络线计划)
"""


import pandas as pd
import json


def data_verify(data_json, feature_list, threshold_null=0.35):
    """
    json格式转为pd.DataFrame格式，并进行指定数据缺失值填充
    :param data_json: json, 模型传入数据
    :param feature_list: list, 待校验字段列表
    :param threshold_null: float, 缺失值比例阈值。低于阈值向后填充补齐；高于阈值补0
    :return: df, message
    """
    # 1. json --> pd.DataFrame
    df = pd.DataFrame.from_dict(data_json, orient='index').T

    # 2. 遍历字段，缺失值填充
    num_should = len(df)    # 每个字段的理论数据量
    message = f"传入数据长度应为：{num_should} 条; \n"

    for feature in feature_list:
        # 1. 判断指定校验字段是否在传入字段中
        if feature not in df.columns:
            message += f"待检测字段: '{feature}' --> 不在传入数据中, 跳过检查该字段; \n"
            continue

        # 2. 统计当前字段缺失值数量，计算缺失值比例
        num_null = df[feature].isnull().sum()   # 当前字段的缺失值数量
        rate_null = num_null / num_should       # 当前字段缺失值比例

        # 3. 缺失值填充
        if rate_null == 0:
            message += f"待检测字段: '{feature}' --> 不存在缺失值; \n"
            continue
        elif rate_null < threshold_null:
            # 若缺失值比例低于指定阈值，向后填充缺失值
            df[feature] = df[feature].fillna(method='ffill').fillna(method='bfill')
            message += f"待检测字段: '{feature}' --> 缺失值数量{num_null}, 缺失值占比{rate_null:.2f}, 低于阈值{threshold_null:.2f}, 采用'向后向前法'填充; \n"
        else:
            # 若缺失值比例高于指定阈值，采用0值填充
            df[feature] = df[feature].fillna(0)
            message += f"待检测字段: '{feature}' --> 缺失值数量{num_null}, 缺失值占比{rate_null:.2f}, 高于阈值{threshold_null:.2f}, 0值填充; \n"

    message += f"检测处理完成后，df缺失值总数量：{df.isnull().sum().sum()}"

    return df, message


if __name__ == '__main__':
    # 读取数据
    path_data = r"F:\ToGeek\2023_model_fix\source_data_bsf_error_type2.json"    # 测试数据路径
    with open(path_data, "r", encoding="utf-8") as f:
        data_dict = json.loads(f.read())

    # 调用脚本填充空缺值
    df_test, message_test = data_verify(data_json=data_dict,
                                        feature_list=['日前必开机组', '日前必停机组', '检修总容量', '日前联络线计划'],
                                        threshold_null=0.4
                                        )
    print(message_test)
    print("--" * 50)
    print(df_test)
