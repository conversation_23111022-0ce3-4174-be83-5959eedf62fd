
import logging
from tornado import ioloop
from tornado import options
from tornado import web
from togeek_package.lib.tornado_config import init_tornado_config
from togeek_package.resources import register_resources
from togeek_package.lib.mul_proc_pool import ProcessPool

version = '2.3.5'
server_name = 'togeek_package'


def main():
    init_tornado_config()                           # 解析配置文件 tornado.ini, 配置 'port' 和 'log保存路径'
    app = web.Application(register_resources())     # 配置路由
    app.listen(options.options.port)                # 端口监听
    logger = logging.getLogger()
    logger.info('{}(v{}) is started. Welcome!!!'.format(server_name.replace("_", " ").capitalize(), version))
    logger.info('This service run on port {}, press Ctrl + C to exit...'.format(options.options.port))
    while True:
        try:
            ioloop.IOLoop.instance().start()
        except KeyboardInterrupt:
            k = input('Do you want to exit? (Yes|[No])').lower().strip()
            if k in ['y', 'yes']:
                ProcessPool().close()
                break
