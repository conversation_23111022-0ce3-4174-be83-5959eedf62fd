# -*- coding:utf-8 -*-
"""
Author      :   z<PERSON><PERSON>i
Date        :   2024-07-17 15:02:22
Description :   仅用于：山东发电侧现货申报系数
    只调整目标日, 日前<实时 对应时刻的lambda
    其它情况，lambda = 1
"""

import os
import pandas as pd
import requests as rs
from datetime import timedelta
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()


def get_data_datasets(grid: str, key_dict: dict, date_start: str, date_end: str, path_save: str = None):
    """
    从 datasets 数据库查询数据
    Parameters
    ----------
    grid: 省份, 可选["SHANXI", "SHANDONG", "MENGXI"]
    key_dict: 字段名称: 索引，示例： {"/日前/统一结算点电价": 399, "/实时/统一结算点电价": 405}。从表 datasets/ds_index_meta 中查询
    date_start: 查询起始日期
    date_end: 查询终止日期
    path_save: 查询数据保存地址: xxx.xlsx

    Returns data
    -------

    """
    # 1. 构造请求参数
    # url = f'http://datasets.togeek.cn/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
    url = f'http://139.9.77.109/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
    params = {
        "grid": grid,
        "startTime": f"{date_start} 00:00:00",
        "endTime": f"{date_end} 23:45:00",
        "appId": "XvBUb-pzgy0ZsXdPyaCz-",
        "token": "1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb"
    }

    # 2. 获取数据
    res_dict = rs.get(url, params=params)

    # 3. 数据整理
    df_list = []

    for key in list(key_dict.keys()):
        df_tmp = pd.DataFrame(res_dict.json()['data'][key]['points'])
        df_tmp = df_tmp.rename(columns={'value': key})
        df_list.append(df_tmp)

    date_time_list = pd.date_range(f"{date_start} 00:00:00", f"{date_end} 23:45:00", freq="15T").astype(str).values
    df = pd.DataFrame({"time": date_time_list})
    for i in range(len(list(key_dict.keys()))):
        df = pd.merge(df, df_list[i], on='time', how="left")
    df = df.rename(columns={"time": "date_time"})
    df['date'] = df['date_time'].map(lambda s: s.split(" ")[0])
    df = df.set_index(["date_time", "date"])

    if path_save:
        df.to_excel(path_save)
        print(f"查询数据已保存，请查看：{os.path.abspath(path_save)}")

    return df


class SpotDeclarationLambdaSD:

    def __init__(self, run_date, lambda_min, days_history):
        logger.info("----------- START: 山东发电侧，现货申报系数 -----------")
        self.run_date = run_date    # 目标日期
        self.lambda_min = lambda_min    # lambda最小值
        self.days_history = days_history      # 历史参照日期天数，不小于30

    def get_actual_price_diff_history(self):
        """
        1. 通过接口获取历史实际电价数据
        2. 获取价格差值 (实时-日前) 大于0的样本集合
        """

        date_end = str(pd.to_datetime(self.run_date) - timedelta(2)).split(" ")[0]
        date_start = str(pd.to_datetime(self.run_date) - timedelta(1 + self.days_history)).split(" ")[0]

        df = get_data_datasets(
            grid="SHANDONG",
            key_dict={"/日前/统一出清电价": 996, "/实时/统一出清电价": 995},
            date_start=date_start,
            date_end=date_end
        )

        # print(df[df["/实时/统一出清电价"].isnull()])

        df = df.dropna()

        # 历史样本量验证
        num_should = self.days_history * 96
        num_real = len(df)
        if num_should == num_real:
            message_error = ""
        else:
            message_error = f"{date_start} ~ {date_end}, 合计{self.days_history}天, 历史实际价格样本应为{num_should}条, 实为{num_real}, 验证失败, 请检查当前时段历史实际价格样本!!! "

        df["price_diff"] = df["/实时/统一出清电价"] - df["/日前/统一出清电价"]
        price_diff_history = df[df["price_diff"] > 0]["price_diff"].values

        return message_error, price_diff_history

    def get_pred_price_run_date(self):
        """ 获取目标日期的预测价格数据 """
        bid_date = str(pd.to_datetime(self.run_date) - timedelta(1)).split(" ")[0]
        url = f"http://192.168.1.203:8099/model_api/service/price/shandong/common_price_etr_8_v2.0"
        input_json = {"date_bidding_start": bid_date, "date_bidding_end": bid_date}

        try:
            r = rs.put(url, json=input_json)
            df = pd.DataFrame(r.json()["data_pred"])
            df = df[["运行日期", "日前电价预测值", "实时价格预测值"]]
            df.columns = ["date_time", "pred_ahead_price", "pred_real_price"]
            df["price_diff"] = df["pred_real_price"] - df["pred_ahead_price"]  # 价差定义: 实时电价 - 日前电价
            message_error = ""
        except:
            df = pd.DataFrame()
            message_error = f"获取价格预测数据失败, 请检查目标日{self.run_date}的价格预测值!!! "

        return message_error, df

    def get_lambda(self, values, price_diff):
        """ 计算 lambda
        :param values: np.array, 历史实际价差样本（实时实际电价 - 日前实际电价 > 0 的价差样本）
        :param price_diff: 目标时刻价差（实时预测电价 - 日前预测电价）
        """
        diff_min = min(values)
        diff_max = max(values)
        num_sample = len(values)

        if price_diff <= diff_min:
            _lambda = 1
        elif price_diff >= diff_max:
            _lambda = self.lambda_min
        else:
            num_exceed = sum(values > price_diff)
            _lambda = self.lambda_min + (1 - self.lambda_min) * (num_exceed / num_sample)

        return _lambda

    def predict(self):
        """ 计算目标日run_date 每个时刻的 lambda """

        # 0. 构造输出字典
        res_dict = {
            "date_time": [],
            "lambda": [],
            "message_error": "",
        }

        # 1. 获取历史统计价差样本
        message_error1, values = self.get_actual_price_diff_history()
        if len(message_error1) != 0:
            res_dict["message_error"] = message_error1
            return res_dict

        # 2. 通过接口获取目标日的价格预测值
        message_error2, df = self.get_pred_price_run_date()
        if len(message_error2) != 0:
            res_dict["message_error"] = message_error2
            return res_dict

        # 3. 计算lambda系数
        df["lambda"] = df["price_diff"].map(lambda s: self.get_lambda(values, s))

        # 4. 构造输出字典
        res_dict["date_time"] = df["date_time"].tolist()
        res_dict["lambda"] = df["lambda"].tolist()
        # res_dict["pred_ahead_price"] = df["pred_ahead_price"].tolist()  # 开发测试用
        # res_dict["pred_real_price"] = df["pred_real_price"].tolist()    # 开发测试用
        # res_dict["price_diff"] = df["price_diff"].tolist()              # 开发测试用

        logger.info("----------- END: 山东发电侧，现货申报系数 -----------")

        return res_dict


if __name__ == '__main__':
    m = SpotDeclarationLambdaSD(run_date="2024-07-17", lambda_min=0.7, days_history=30)
    result = m.predict()
    print(result)

    # # 开发测试用
    # path_res = "tmp_result.xlsx"
    # pd.DataFrame(result).to_excel(path_res, index=False, encoding="utf")
    # print(f"结果: {os.path.abspath(path_res)}")
