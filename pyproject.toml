[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "togeek_package"
version = "2.3.5"
description = "Togeek package Service"
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3.10"
]
dependencies = [
    "catboost>=1.2.8",
    "chinese-calendar>=1.10.0",
    "gpy>=1.13.2",
    "jenkspy>=0.4.1",
    "lightgbm>=4.6.0",
    "networkx>=3.1",
    "openpyxl>=3.1.5",
    "plotly>=6.1.2",
    "prophet>=1.1.7",
    "pwlf>=2.5.1",
    "pymysql>=1.1.1",
    "pyomo>=6.9.2",
    "PySCIPOpt>=5.5.0",
    "requests>=2.32.4",
    "scikit-learn>=1.3.2",
    "sktime>=0.29.1",
    "sqlalchemy>=2.0.41",
    "statsmodels>=0.14.1",
    "tglibs",
    "timedelta>=2020.12.3",
    "torch>=2.7.1",
    "tornado>=6.5.0",
    "xgboost>=2.1.4",
]

[project.scripts]
togeek_package = "togeek_package:main"

[tool.setuptools.packages.find]
include = ["togeek_package*"]

[tool.uv.sources]
tglibs = { path = "docker/tglibs-0.0.16-py3-none-any.whl" }

[dependency-groups]
dev = [
    "setuptools>=61.0",
    "wheel>=0.45.1",
]
