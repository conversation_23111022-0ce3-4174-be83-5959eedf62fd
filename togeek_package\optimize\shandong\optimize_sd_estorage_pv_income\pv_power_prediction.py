# -*- coding: utf-8 -*-            
# Author : lmm
# Time : 2024/1/31 9:27
# Description :  山东青岛光伏场站出力预测


import numpy as np
import pandas as pd
import joblib
from sklearn.ensemble import ExtraTreesRegressor
import logging
import warnings
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class PvPowerPrediction:

    def __init__(self, df_evalue, weather_train):
        logger.info("---------------------- START: 山东青岛光伏出力预测训练模型 --------------------------")
        self.df_evalue = df_evalue
        self.weather_train = weather_train

    def train_model(self):

        # 光伏训练预测特征
        feature_dict = {
            't_surface': '温度',
            'uswrf_surface': '向上短波辐射通量[W/m^2]',
            'dswrf_surface': '向下短波辐射通量[W/m^2]',
            'SUNSD_surface': '日照时长',
            'ulwrf_surface': '向上长波辐射通量[W/m^2]',
            'albedo_surface': '反射率',
            'dlwrf_surface': '向下长波辐射通量[W/m^2]',
            'q_80m': '80m比湿度',
            'pres_80m': '80m压力',
            't_80m': '80m温度',
            'uflx_surface': '动量通量, u分量[N/m^2]',
            'u_10m': '10m风速u分量',
            'v_10m': '10m风速v分量',
            'points': '时点'
        }

        df_evalue = pd.DataFrame(self.df_evalue)
        weather_train = pd.DataFrame(self.weather_train)

        # 给气象数据添加时点特征
        time_range_1 = pd.date_range("02:00:00", "23:45:00", freq="15min").astype(str).map(lambda s: s.split(' ')[1])
        time_dict_1 = pd.DataFrame({"time": time_range_1, "points": np.arange(1, 89)})
        time_range_2 = pd.date_range("00:00:00", "01:45:00", freq="15min").astype(str).map(lambda s: s.split(' ')[1])
        time_dict_2 = pd.DataFrame({"time": time_range_2, "points": np.arange(89, 97)})
        _time_dict = pd.concat([time_dict_1, time_dict_2])

        weather_train['time'] = weather_train['t_datetime_cst'].map(lambda s: str(s).split(' ')[1])
        weather_train = pd.merge(weather_train, _time_dict, on='time', how='left')

        # 训练预测
        df_train = df_evalue['实际电力'].values
        wea_train = weather_train[feature_dict.keys()].values

        # 训练、预测
        model = ExtraTreesRegressor(random_state=1).fit(wea_train, df_train)

        return joblib.dump(model, r".\model.pkl")


if __name__ == '__main__':

    path = r"D:/项目/2024/山东/青岛中石油/20240118_lm/weather_label.json"

    import json
    with open(path, 'r') as f:
        data = json.loads(f.read())

    _df_evalue = pd.DataFrame(data["pv_data"])
    _weather_train = pd.DataFrame(data["weather_data"])

    # 调用预测函数
    m = PvPowerPrediction(df_evalue=_df_evalue, weather_train=_weather_train)
    df_result = m.train_model()




