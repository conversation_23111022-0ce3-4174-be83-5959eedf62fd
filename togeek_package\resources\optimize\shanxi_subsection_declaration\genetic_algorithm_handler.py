# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.shanxi.optimize_subsection_declaration_sx import SubsectionDeclarProfitSXGN, \
    SubsectionDeclarProfitSXJN, SubsectDeclarIdealProfit


class GAProfitHandlerSXGN(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 3)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        is_start_zero = params.pop("is_start_zero", 0)
        model = SubsectionDeclarProfitSXGN(generators=generators, prices=prices, constraints=constraints,
                                           lower_price=lower_price, upper_price=upper_price,
                                           min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                           default_subsection=default_subsection, price_decimal=price_decimal,
                                           is_start_zero=is_start_zero)
        result = model.predict()
        self.write(result)


class GAProfitHandlerSXJN(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        default_subsection = params.pop('default_subsection', 10)
        POP_SIZE = params.pop('POP_SIZE', 5000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 10)
        price_decimal = params.pop('price_decimal', 0)
        is_start_zero = params.pop("is_start_zero", 0)
        out_n = params.pop("out_n", None)
        model = SubsectionDeclarProfitSXJN(generators=generators, prices=prices, constraints=constraints,
                                           lower_price=lower_price, upper_price=upper_price, POP_SIZE=POP_SIZE,
                                           N_GENERATIONS=N_GENERATIONS, default_subsection=default_subsection,
                                           price_decimal=price_decimal, is_start_zero=is_start_zero, out_n=out_n)
        result = model.predict()
        self.write(result)


class GAIdealProfitHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        engs = params.pop('engs')
        param = params.pop('param')
        price = params.pop('price')
        func_mode = params.pop('func_mode', 'profit')
        upward = params.pop('func_mode', 0.05)
        min_price = params.pop('min_price', 0)
        max_price = params.pop('max_price', 1500)
        size_pop = params.pop('size_pop', 1000)
        iter = params.pop('iter', 20)
        model = SubsectDeclarIdealProfit(engs=engs, param=param, price=price, upward=upward, func_mode=func_mode,
                                         min_price=min_price, max_price=max_price, size_pop=size_pop, iter=iter)
        result = model.run()
        self.write(result)
