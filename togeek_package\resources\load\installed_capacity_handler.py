from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.shanxi.installed_capacity import InstalledCapacityPred


class InstalledCapacityHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        load_data = params.get('load_data')
        reserve_load = params.get('reserve_load')
        capacity = params.get('capacity')
        pred_dates = params.get('pred_dates')
        n_estimators = params.get('days', 100)
        max_depth = params.get('max_depth', 6)
        days = params.get('days', 30)
        model = InstalledCapacityPred(load_data=load_data, capacity=capacity,
                                      pred_dates=pred_dates)
        res = model.run(to_json=True, n_estimators=n_estimators, max_depth=max_depth, days=days)
        self.write(res)
