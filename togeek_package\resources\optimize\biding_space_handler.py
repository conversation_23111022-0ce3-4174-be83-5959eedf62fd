# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_forelec_space import Prediction

class BiddingSpaceHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        holiday_data = params.pop('holiday_data')
        data_js = params.pop('data_js')
        give_data = params.pop('give_data')
        weight_list0 = params.get('weight_list0', 0.65)
        adjust = params.pop('adjust', 0.5)
        pred = Prediction(holiday_data=holiday_data,data_js=data_js,give_data=give_data, weight_list0=weight_list0, adjust=adjust)
        self.write(pred.result)