#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/5/6 15:09
# <AUTHOR> <PERSON><PERSON>
# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_bided_power import MxBidedPower


class MxBidedPowerHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        subsection_declaration = params.pop('subsection_declaration')
        approval_data = params.pop('approval_data')
        model = MxBidedPower(generators=generators, approval_data=approval_data, prices=prices, subsection_declaration=subsection_declaration)
        result = model.predict()
        self.write(result)
