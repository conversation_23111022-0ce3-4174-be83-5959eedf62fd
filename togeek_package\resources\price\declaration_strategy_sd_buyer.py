# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
# from togeek_package.price.nationwide.price_declaration_pre import Declaration
from togeek_package.price.shandong.declaration_strategy_buyer import DeclarationStrategySD


class DeclarationStrategyBuyerHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.pop("data")
        run_date = params.pop("run_date")
        time_point = params.pop("time_point", 96)
        days_train = params.pop("days_train", 30)
        scale = params.pop("scale", 0.2)

        m = DeclarationStrategySD(data=data,
                                  run_date=run_date,
                                  time_point=time_point,
                                  days_train=days_train,
                                  scale=scale,
                                  threshold_proba=0.6,
                                  )
        self.write(m.predict())
