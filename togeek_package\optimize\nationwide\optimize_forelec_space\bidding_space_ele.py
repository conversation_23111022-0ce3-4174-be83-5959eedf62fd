# -*- coding: utf-8 -*-
# valid license

import pandas as pd
import numpy as np
import logging

logger = logging.getLogger()

class Prediction:
    def __init__(self, holiday_data, data_js, give_data, weight_list0=0.65, adjust=0.5):

        logger.info("------------------------------------------")
        logger.info("----------套利空间-------------------")
        logger.info("holiday_data:{}, data_js:{}, give_data:{}, weight_list0={}, adjust={}".format(holiday_data, data_js, give_data, weight_list0, adjust))
        self.holiday_data = pd.DataFrame(holiday_data)
        self.data_js = self.pre_data_js(data_js)
        self.give_data = self.pre_data_js(give_data)
        self.weight0 = float(weight_list0)
        self.weight1 = round(1 - float(weight_list0), 3)
        self.adjust = float(adjust)
        self.result = []
        self.result_h = []
        self.give_date = list(set(self.give_data['日期'].to_list()))
        self.get_result()
        self.get_coefficient()

    def get_holiday_new(self):
        self.holiday_data['is_beforeholiday'] = self.holiday_data['is_holiday'].to_list()[1:] + [2]

    def pre_data_js(self, data_js):
        data_js = pd.DataFrame(data_js)
        data_js["日期"] = data_js["时间"].map(lambda x: x.split(" ")[0])
        data_js["时间"] = data_js["时间"].map(lambda x: x.split(" ")[1])
        return data_js

    def get_result(self):
        for date in self.give_date:
            date_min = min(self.data_js['日期'].tolist())
            max_day = max(self.data_js['日期'].tolist())
            date_max = max_day if date > max_day else date
            holi_data1 = self.data_js[(self.data_js['日期'] >= date_min) &
                                              (self.data_js['日期'] <= date_max)]
            date_list = sorted(list(set(holi_data1['日期'].to_list())))
            data_js2 = self.data_js[self.data_js['日期'].isin(date_list)]
            screening_give = self.give_data[self.give_data['日期'] == str(date)]
            time_list = screening_give["时间"].tolist()
            self.result = self.result + self.get_sameprobility(time_list, data_js2, screening_give, date)
            result_h = []
            for i in range(len(date_list) - 1, -1, -1):
                if i == 0:
                    continue
                else:
                    date_hlist = date_list[:i]
                    data_js3 = self.data_js[self.data_js['日期'].isin(date_hlist)]
                    screening_give = self.data_js[self.data_js['日期'] == str(date_list[i])]
                    result_h = result_h + self.get_sameprobility(time_list, data_js3, screening_give, date_list[i])
            self.result_h = result_h + self.result_h

    def get_sameprobility(self, time_list, data_h, screening_give, date1):
        score_list = []
        for time in time_list:
            time_result = data_h[data_h['时间'] == str(time)]
            count_all = time_result.shape[0]
            real_price = screening_give[screening_give["时间"] == time]["实时"].tolist()[0]
            dayahead_price = screening_give[screening_give["时间"] == time]["日前"].tolist()[0]
            price_diff = self.__get_differprice(real_price, dayahead_price)
            if count_all:
                if price_diff == 0:
                    count_diff = time_result[time_result['实时'] == time_result['日前']].shape[0]
                elif price_diff > 0:
                    count_diff = time_result[time_result['实时'] > time_result['日前']].shape[0]
                else:
                    count_diff = time_result[time_result['实时'] < time_result['日前']].shape[0]
                same_probility = count_diff / count_all
            else:
                same_probility = 0
            dict1 = {"date": str(date1) + ' ' + time,
                     "price_diff": price_diff,
                     "same_probility": same_probility}
            score_list.append(dict1)
        return score_list

    def __get_differprice(self, real_price, dayahead_price):
        # 实时-日前
        return float(real_price) - float(dayahead_price)

    def re_get_score(self):
        get_sign = lambda y: y / abs(y) if y != 0.0 else 0
        result_data = pd.DataFrame(self.result)
        result_data['sign'] = result_data[['price_diff']].applymap(get_sign)
        result_h = pd.DataFrame(self.result_h)
        result_h['sign'] = result_h[['price_diff']].applymap(get_sign)
        max_min_scaler = lambda x: (x - np.min(x)) / (np.max(x) - np.min(x)) if np.min(x) != np.max(x)  else x
        result_data['price_normal'] = result_data[['price_diff']].apply(max_min_scaler)
        result_data['score'] = self.weight0 * result_data['price_normal'] + self.weight1 * result_data['same_probility']
        result_h['price_normal'] = result_h[['price_diff']].apply(max_min_scaler)
        result_h['score'] = self.weight0 * result_h['price_normal'] + self.weight1 * result_h['same_probility']
        return result_data, result_h

    def get_coefficient(self):
        if self.result_h:
            result_data, result_h = self.re_get_score()
            max_min_scaler1 = lambda x: (x - np.min(x)) / (np.max(x) - np.min(x)) if np.min(x) != np.max(x)  else x
            result_list = result_data['date'].to_list()
            coefficient_list = []
            score_now = []
            for time in result_list:
                result_h["time"] = result_h["date"].map(lambda x: x.split(" ")[1]).to_list()
                time_day = time.split(" ")[1]
                sign = result_data[result_data['date'] == time]['sign'].to_list()[0]
                time_score = result_data[result_data['date'] == time]['score'].to_list()[0]
                score_tm = result_h[(result_h['sign'] == sign) & (result_h['time'] == time_day)]
                if not score_tm.empty:
                    score_l = pd.concat([score_tm[['date', 'score', 'same_probility']], result_data[result_data['date'] == time][['date', 'score', 'same_probility']]])
                    score_set = list(set(score_l['same_probility'].to_list()))
                    if len(score_set) == 1 and score_set[0] == 1.0:
                        score = 1.0
                        coefficient = 1.0 + sign * 1.0 * self.adjust
                    else:
                        score_l['score_1'] = score_l[['score']].apply(max_min_scaler1)
                        score_list = score_l['score_1'].to_list()
                        min_score = min(score_list)
                        max_score = max(score_list)
                        score = score_l[score_l['date'] == time]['score_1'].to_list()[0]
                        if score == min_score:
                            coefficient = 1.0
                        elif score == max_score:
                            coefficient = 1.0 + sign * 1.0 * self.adjust
                        else:
                            coefficient = 1.0 + sign * score * self.adjust
                    score_now.append(score)
                else:
                    coefficient = 1.0 + sign * time_score * self.adjust
                    score_now.append(time_score)
                coefficient_list.append(coefficient)
            self.result = pd.DataFrame(self.result)
            self.result['score'] = score_now
            self.result['coefficient'] = coefficient_list
        else:
            self.result = pd.DataFrame(self.result)
            get_sign = lambda y: y / abs(y) if y != 0.0 else 0
            self.result['score'] = self.result[['same_probility']]
            self.result['coefficient'] = self.result.apply(lambda x: 1.0 + x['score'] * self.adjust * x[['price_diff']].apply(get_sign),axis=1)
        logger.info("------end of space-------")
        logger.info("result:{}".format(self.result))
        self.result = self.result.to_dict()


if __name__ == '__main__':
    holiday_data = pd.read_pickle("train_data/holiday_data.pkl")
    data_js = pd.read_pickle("train_data/data_js.pkl")
    give_data = pd.read_pickle('train_data/give_data.pkl')
    data_js["时间"] = data_js["时间"].apply(str)
    p = Prediction(holiday_data, data_js, give_data, weight_list0=0.7)
    print(pd.DataFrame(p.result))