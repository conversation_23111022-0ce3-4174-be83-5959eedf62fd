import pandas as pd
import numpy as np
import xgboost as xgb


class XGBMengxiNewenergyLoadPredict:
    def __init__(self, load_data, weather_data):
        """
        初始化模型，接受列式字典格式的数据

        :param load_data: {'timestamp': [...], '实测值': [...]}
        :param weather_data: {'timestamp': [...], 't_2m': [...], ...}
        """
        self.load_data = load_data
        self.weather_data = weather_data
        self.model_wind = None
        self.model_pv = None


        # 特征定义
        self.features_wind = [
            'time_of_day', 'day_of_year', 'month', 'season',
            't_2m', 'rh_2m', 'u_10m', 'v_10m', 'sp_surface',
            'air_density', 'total_wind_speed',
        ]

        self.features_pv = [
            'time_of_day', 'day_of_year', 'month', 'season',
            't_2m', 'tmax_2m', 'tmin_2m', 'dswrf_surface', 'tcc_atmo',
        ]

    def _dict_to_dataframe(self):
        """将 column-oriented 字典转换为 DataFrame 并合并"""
        # 构建负荷数据 DataFrame
        load_df = pd.DataFrame({
            'timestamp': pd.to_datetime(self.load_data['timestamp']),
            '风电实测值': self.load_data['风电实测值'],
            '光伏实测值': self.load_data['光伏实测值']
        })

        # 构建天气数据 DataFrame
        weather_dict = {k: v for k, v in self.weather_data.items() if k != 'timestamp'}
        weather_df = pd.DataFrame({
            'timestamp': pd.to_datetime(self.weather_data['timestamp']),
            **weather_dict
        })

        # 合并数据
        df = pd.merge(load_df, weather_df, on='timestamp', how='outer')
        return df

    def preprocess_data(self, D='2025-05-27', test_days=20):
        """预处理：构造特征 & 标准化时间格式"""
        df = self._dict_to_dataframe()
        self.df = df  # 保存原始 df 供 predict 使用

        # 构造新特征
        df['time_of_day'] = (df['timestamp'].dt.hour * 4) + (df['timestamp'].dt.minute // 15) + 1
        df['month'] = df['timestamp'].dt.month
        df['season'] = df['month'].apply(self.month_to_season)
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        # 增加空气密度特征
        df['air_density'] = self.calculate_air_density(temp_c=df['t_2m'],
                                                       pressure_pa=df['sp_surface'],
                                                       relative_humidity=df['rh_2m'])
        # 增加总风速特征
        df['total_wind_speed'] = np.sqrt(df['u_10m'] ** 2 + df['v_10m'] ** 2)

        # 自动过滤掉缺失或空的天气特征
        available_features_wind = []
        for feat in self.features_wind:
            if feat in df.columns and pd.notna(df[feat]).any():
                available_features_wind.append(feat)

        available_features_pv = []
        for feat in self.features_pv:
            if feat in df.columns and pd.notna(df[feat]).any():
                available_features_pv.append(feat)

        skipped_wind = [f for f in self.features_wind if f not in available_features_wind]
        skipped_pv = [f for f in self.features_pv if f not in available_features_pv]

        print("⚠️ 风电特征被跳过:", skipped_wind)
        print("⚠️ 光伏特征被跳过:", skipped_pv)

        # 提取训练集/测试集
        D = pd.to_datetime(D)
        test_end = D + pd.Timedelta(days=test_days)

        train_mask = df['timestamp'] < D
        test_mask = (df['timestamp'] >= D) & (df['timestamp'] < test_end)

        # 风电训练数据
        X_train_wind = df.loc[train_mask, available_features_wind].copy()
        y_train_wind = df.loc[train_mask, '风电实测值']

        # 光伏训练数据
        X_train_pv = df.loc[train_mask, available_features_pv].copy()
        y_train_pv = df.loc[train_mask, '光伏实测值']

        # 测试数据
        X_test_wind = df.loc[test_mask, available_features_wind].copy()
        X_test_pv = df.loc[test_mask, available_features_pv].copy()

        # 筛选有效样本
        valid_wind = X_train_wind.notna().all(axis=1) & y_train_wind.notna()
        valid_pv = X_train_pv.notna().all(axis=1) & y_train_pv.notna()

        X_train_wind = X_train_wind[valid_wind]
        y_train_wind = y_train_wind[valid_wind]
        X_train_pv = X_train_pv[valid_pv]
        y_train_pv = y_train_pv[valid_pv]

        return (
            X_train_wind, X_train_pv,
            X_test_wind, X_test_pv,
            y_train_wind, y_train_pv
        )

    #计算空气密度
    def calculate_air_density(self, temp_c, pressure_pa, relative_humidity):
        # 转换到正确单位
        temp_k = temp_c + 273.15  # 温度转为开尔文
        rh = relative_humidity / 100  # 相对湿度转为小数

        # 计算饱和水汽压力
        es = 6.112 * np.exp((17.67 * (temp_c)) / (temp_c + 243.5))

        # 计算实际水汽压力
        e = rh * es

        # 干燥空气分压
        pd = pressure_pa - e

        # 虚拟温度
        tv = temp_k * (1 + 0.61 * e / pressure_pa)

        # 空气密度
        Rd = 287.058  # 干空气气体常数 J/(kg·K)
        air_density = pd / (Rd * tv)

        return air_density

    # 定义映射函数：将月份映射到季节
    def month_to_season(self, month):
        if 3 <= month <= 5:
            return 1  # 春季
        elif 6 <= month <= 8:
            return 2  # 夏季
        elif 9 <= month <= 11:
            return 3  # 秋季
        else:
            return 4  # 冬季（12, 1, 2）

    def train_model(self, X_train_wind, X_train_pv, y_train_wind, y_train_pv):
        """分别训练风电和光伏的 XGBoost 模型"""
        # 训练风电模型
        self.model_wind = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=5,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        self.model_wind.fit(X_train_wind, y_train_wind)

        # 训练光伏模型
        self.model_pv = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=5,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        self.model_pv.fit(X_train_pv, y_train_pv)

    def predict(self, X_test_wind, X_test_pv):
        """返回列式字典结构：{'timestamp': [...], '风电预测值': [...], '光伏预测值': [...], '总预测值': [...]}"""
        y_pred_wind = self.model_wind.predict(X_test_wind)
        y_pred_pv = self.model_pv.predict(X_test_pv)
        y_pred_pv = np.clip(y_pred_pv, 0, None)

        total_pred = y_pred_wind + y_pred_pv

        # 提取 timestamp 列并转换为字符串格式列表
        timestamps = self.df.loc[X_test_wind.index, 'timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()

        result = {
            'timestamp': timestamps,
            # '风电预测值': y_pred_wind.tolist(),
            # '光伏预测值': y_pred_pv.tolist(),
            '预测值': total_pred.tolist()
        }

        return result

    def run(self, D='2025-05-27', test_days=20):
        """执行整个流程，返回预测结果"""
        data = self.preprocess_data(D=D, test_days=test_days)
        X_train_wind, X_train_pv, X_test_wind, X_test_pv, y_train_wind, y_train_pv = data
        self.train_model(X_train_wind, X_train_pv, y_train_wind, y_train_pv)
        predictions = self.predict(X_test_wind, X_test_pv)
        return predictions


if __name__ == "__main__":
    import json
    from datetime import datetime

    # 配置参数
    D = '2025-05-25'
    test_days = 20

    # ----------加载数据---------
    load_df = pd.read_csv('real_data_2_apifox.csv')
    weather_df = pd.read_csv('weather_apifox.csv')
    # load_df = pd.read_csv('real_data_20250101_20250615_2.csv')
    # weather_df = pd.read_csv('weather_蒙西_风电坐标.csv')
    # weather_df = pd.read_csv('weather.csv')

    # 转换为 column-oriented 字典结构
    load_data = {
        'timestamp': load_df['时刻'].tolist(),
        '风电实测值': load_df['风电实测值'].astype(float).tolist(),
        '光伏实测值': load_df['光伏实测值'].astype(float).tolist()
    }

    weather_data = {
        'timestamp': weather_df['t_datetime_cst'].tolist()
    }
    for col in weather_df.columns:
        if col != 't_datetime_cst':
            weather_data[col] = weather_df[col].astype(float).tolist()



    # -----------进行预测------------
    forecaster = XGBMengxiNewenergyLoadPredict(load_data=load_data, weather_data=weather_data)
    predictions = forecaster.run(D=D, test_days=test_days)

    # print("🔮 预测结果：")
    # for t, w, p, t_p in zip(
    #         predictions['timestamp'],
    #         predictions['风电预测值'],
    #         predictions['光伏预测值'],
    #         predictions['总预测值']
    # ):
    #     print(f"{t}: 风电={w:.2f}, 光伏={p:.2f}, 总={t_p:.2f}")

    # -------保存输入输出json-----------



    # 自定义 JSON 序列化函数，用于处理 numpy 类型和 Timestamp
    def default_serializer(obj):
        if isinstance(obj, (np.integer, np.floating, np.number)):
            return float(obj)
        if isinstance(obj, (np.ndarray, list)):
            return list(obj)
        if isinstance(obj, (pd.Timestamp, datetime)):
            return obj.strftime('%Y/%m/%d %H:%M')  # 统一格式：2025/1/1 0:00
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


    # 构建包含 D 和 test_days 的输入字典
    input_data = {
        "D": D,  # 你的主程序中已经定义了 D
        "test_days": test_days,  # 也已经定义
        "load": {},
        "weather": {}
    }

    # 将 load_data 写入 input_data["load"]（包含 timestamp）
    for key in load_data:
        input_data["load"][key] = load_data[key]

    # 将 weather_data 写入 input_data["weather"]（包含 timestamp）
    for key in weather_data:
        input_data["weather"][key] = weather_data[key]

    # 保存为 JSON 文件
    with open('input_data.json', 'w', encoding='utf-8') as f:
        json.dump(input_data, f, ensure_ascii=False, indent=4, default=default_serializer)

    print("✅ 输入数据已保存为 input_data.json")

    # 保存 predictions 为 JSON 文件
    with open('predictions.json', 'w', encoding='utf-8') as f:
        json.dump(predictions, f, ensure_ascii=False, indent=4, default=default_serializer)
    print(" - predictions.json")


    # -----------计算准确率-----------

    def accuracy_for_trade(y_true, y_pred, epsilon=0.01):
        y_true = np.asarray(y_true)
        y_pred = np.asarray(y_pred)

        accuracies = np.zeros_like(y_true, dtype=float)

        near_zero_mask = (np.abs(y_true) <= epsilon) & (np.abs(y_pred) <= epsilon)
        accuracies[near_zero_mask] = 1.0

        non_zero_mask = ~near_zero_mask
        if np.any(non_zero_mask):
            y_true_nonzero = y_true[non_zero_mask]
            y_pred_nonzero = y_pred[non_zero_mask]
            mape_values = np.abs((y_pred_nonzero - y_true_nonzero) / np.clip(y_true_nonzero, a_min=1e-8, a_max=None))
            accuracies[non_zero_mask] = 1 - mape_values
            accuracies[non_zero_mask] = np.clip(accuracies[non_zero_mask], 0, 1)

        return np.mean(accuracies)

    # 获取真实值
    real_df = pd.read_csv('real_data_20250101_20250615_2.csv')
    real_df['时刻'] = pd.to_datetime(real_df['时刻'])
    real_wind_dict = dict(zip(real_df['时刻'], real_df['风电实测值']))
    real_solar_dict = dict(zip(real_df['时刻'], real_df['光伏实测值']))

    pred_timestamps = pd.to_datetime(predictions['timestamp'])
    pred_wind_values = np.array(predictions['风电预测值'])
    pred_solar_values = np.array(predictions['光伏预测值'])
    pred_total_values = np.array(predictions['总预测值'])

    # 提取真实值
    y_true_wind = np.array([real_wind_dict.get(t, np.nan) for t in pred_timestamps])
    y_true_solar = np.array([real_solar_dict.get(t, np.nan) for t in pred_timestamps])
    y_true_total = y_true_wind + y_true_solar

    # 过滤掉 NaN 值（避免影响准确率计算）
    valid_mask = ~np.isnan(y_true_wind) & ~np.isnan(y_true_solar)
    y_true_wind = y_true_wind[valid_mask]
    y_true_solar = y_true_solar[valid_mask]
    y_true_total = y_true_total[valid_mask]

    pred_wind_values = pred_wind_values[valid_mask]
    pred_solar_values = pred_solar_values[valid_mask]
    pred_total_values = pred_total_values[valid_mask]

    # 计算准确率
    acc_wind = accuracy_for_trade(y_true_wind, pred_wind_values)
    acc_solar = accuracy_for_trade(y_true_solar, pred_solar_values)
    acc_total = accuracy_for_trade(y_true_total, pred_total_values)

    # 输出准确率
    print("\n📊 准确率统计：")
    print(f"风电预测准确率: {acc_wind * 100:.2f}%")
    print(f"光伏预测准确率: {acc_solar * 100:.2f}%")
    print(f"总发电量预测准确率: {acc_total * 100:.2f}%")

    #------------画图---------
    import matplotlib.pyplot as plt
    from matplotlib.dates import DateFormatter

    # 假设 real_df 是你之前加载的真实数据 DataFrame
    real_df = pd.read_csv('real_data_20250101_20250615_2.csv')
    real_df['时刻'] = pd.to_datetime(real_df['时刻'])
    real_wind_dict = dict(zip(real_df['时刻'], real_df['风电实测值']))
    real_solar_dict = dict(zip(real_df['时刻'], real_df['光伏实测值']))

    # 获取预测的时间戳和预测值
    pred_timestamps = pd.to_datetime(predictions['timestamp'])  # datetime 类型
    pred_wind_values = predictions['风电预测值']
    pred_solar_values = predictions['光伏预测值']
    pred_total_values = predictions['总预测值']

    # 每天96个点（每15分钟一个）
    num_points_per_day = 96
    days_to_plot = [6, 14, 19]  # 第7天、第15天、第20天（索引从0开始）

    # 设置中文支持
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False


    # 定义通用绘图函数
    def plot_comparison(fig, axes, pred_values, real_dict, title_prefix):
        for i, day_idx in enumerate(days_to_plot):
            start_idx = day_idx * num_points_per_day
            end_idx = (day_idx + 1) * num_points_per_day

            times = pred_timestamps[start_idx:end_idx]
            preds = pred_values[start_idx:end_idx]

            truths = [real_dict.get(t, np.nan) for t in times]

            ax = axes[i]
            ax.plot(times, preds, label='预测值', color='blue', linestyle='--', marker='o', markersize=3)
            ax.plot(times, truths, label='真实值', color='green')

            ax.set_title(f'{title_prefix} - 第 {day_idx + 1} 天 ({times[0].strftime("%Y-%m-%d")})')
            ax.set_xlabel('时间')
            ax.set_ylabel('发电量')
            ax.legend()
            ax.grid(True)

            date_format = DateFormatter("%H:%M")
            ax.xaxis.set_major_formatter(date_format)
            fig.autofmt_xdate()


    # 绘制风电预测 vs 真实值
    fig_wind, axes_wind = plt.subplots(3, 1, figsize=(12, 10), sharex=False)
    fig_wind.suptitle('风电预测 vs 真实值对比', fontsize=16)
    plot_comparison(fig_wind, axes_wind, pred_wind_values, real_wind_dict, '风电预测')
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 绘制光伏预测 vs 真实值
    fig_solar, axes_solar = plt.subplots(3, 1, figsize=(12, 10), sharex=False)
    fig_solar.suptitle('光伏预测 vs 真实值对比', fontsize=16)
    plot_comparison(fig_solar, axes_solar, pred_solar_values, real_solar_dict, '光伏预测')
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 绘制总发电量预测 vs 真实值
    fig_total, axes_total = plt.subplots(3, 1, figsize=(12, 10), sharex=False)
    fig_total.suptitle('总发电量预测 vs 真实值对比', fontsize=16)

    truth_total_values = [real_wind_dict.get(t, np.nan) + real_solar_dict.get(t, np.nan) for t in pred_timestamps]

    for i, day_idx in enumerate(days_to_plot):
        start_idx = day_idx * num_points_per_day
        end_idx = (day_idx + 1) * num_points_per_day

        times = pred_timestamps[start_idx:end_idx]
        preds = pred_total_values[start_idx:end_idx]
        truths = truth_total_values[start_idx:end_idx]

        ax = axes_total[i]
        ax.plot(times, preds, label='预测值', color='blue', linestyle='--', marker='o', markersize=3)
        ax.plot(times, truths, label='真实值', color='green')

        ax.set_title(f'总发电量预测 - 第 {day_idx + 1} 天 ({times[0].strftime("%Y-%m-%d")})')
        ax.set_xlabel('时间')
        ax.set_ylabel('发电量')
        ax.legend()
        ax.grid(True)

        date_format = DateFormatter("%H:%M")
        ax.xaxis.set_major_formatter(date_format)
        fig_total.autofmt_xdate()

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()


