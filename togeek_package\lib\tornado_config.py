# -*- coding: utf-8 -*-

import os
from tglibs.easy_dir import join, mkdir
from tornado import options

_init_is_done = False


def init_tornado_config():
    """
    解析配置文件 tornado.ini, 配置 'port' 和 'log保存路径'
    """
    global _init_is_done
    if not _init_is_done:
        config_file = join(__file__, '..', 'tornado.ini')
        options.define('port', default=80, type=int, help='Run on the given port')
        options.parse_config_file(config_file, final=False)
        if options.options.log_file_prefix:
            mkdir(os.path.dirname(options.options.log_file_prefix))
        options.options.parse_command_line()
        if options.options.log_file_prefix:
            mkdir(os.path.dirname(options.options.log_file_prefix))
    _init_is_done = True
