#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/7 10:18
# <AUTHOR> Darlene


from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_percentile import PricePercentile


class PricePercentileHandlerMx(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price_data = params.get('price_data')
        q1 = params.get('q1', 25)
        q3 = params.get('q3', 75)
        pred = PricePercentile(price_data=price_data, q1=q1, q3=q3)
        self.write(pred.result)
