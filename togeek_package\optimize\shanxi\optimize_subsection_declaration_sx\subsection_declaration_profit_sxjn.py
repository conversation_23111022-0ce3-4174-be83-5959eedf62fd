# -*- coding: utf-8 -*
"""
Author: laney
Date: 2023-04-10 10:43:23
LastEditTime: 2023-04-16 14:15:06
Description: 山西京能最优分段报价项目 -- 针对发电厂的报价方案
输入：
    1、机组ID、装机容量、最小技术出力、最小步长、起初出力、分段数量、允许停机、最小停机时长、最小运行时长、连续运行时长、连续停机时长；
    2、机组ID、时段、出力下限、出力上限、爬坡速率
    2、机组ID、时间、日前节点电价、实时节点电价
    3、机组ID、负荷率、单位变动成本
    4、报价下限、报价上限、最小收益、种群大小、迭代次数、默认分段数量
输出：
    1、总收益：profit
    2、最优分段报价：subsection_declaration,第一段从0开始
    3、分时点中标出力：bidded_power
注：计算收益时，考虑固定成本和变动成本（燃料成本+其他）；
    停机时的收益由系统平台计算，模型内部不进行计算
"""
import numpy as np
import pandas as pd
import operator
import warnings
import logging
import os
from multiprocessing import Pool

warnings.filterwarnings("ignore")
logger = logging.getLogger()
cpu_num = os.cpu_count()


class SubsectionDeclarProfitSXJN:
    def __init__(self, generators, prices, constraints, lower_price=0, upper_price=1500, default_subsection=10,
                 POP_SIZE=5000, N_GENERATIONS=2, price_decimal=0, is_start_zero=0, out_n=None):
        # 初始化输入信息
        self.generators = pd.DataFrame.from_dict(generators)    # 机组信息
        self.prices = pd.DataFrame.from_dict(prices)            # 机组节点电价
        self.constraints = pd.DataFrame.from_dict(constraints)  # 机组分段约束条件-爬坡速率、出力上下限
        self.default_subsection = default_subsection  # 默认申报分段数量
        self.lower_price = lower_price                # 报价下限
        self.upper_price = upper_price                # 报价上限
        self.price_decimal = price_decimal            # 价格保留位数，默认保留小数点后3位

        # 默认值
        self.freqs = [24, 48, 96]                     # 每日的时间点的数量只能是这些数字
        self.gap = 15                                 # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        self.subsection = 10                          # 分段数量，这里仅做初始化
        self.is_start_zero = is_start_zero            # 第一段起始出力是否从0开始
        self.out_n = out_n                            # 最终输出策略的数量，默认为None，输出全部结果

        # 遗传算法基本参数
        self.gene_size = 10                 # 基因大小，代表每个数字的精度，10个位表示1024
        self.DNA_SIZE = self.gene_size * 2 * self.subsection  # 基因组大小
        self.POP_SIZE = POP_SIZE            # 种群数量
        self.N_GENERATIONS = N_GENERATIONS  # 进化代数
        self.CROSSOVER_RATE = 0.6           # 杂交比例，按照基因杂交；将来应该可以选择按照染色体、基因、点位三种方式都可以杂交
        self.MUTATION_RATE = 0.1            # 变异可能性
        self.hybridization_rate = 0.4       # 子代从亲代获取基因的比例，0.4表示父6母4，循环是放回式采样，所以母不到4

        # 数据格式化
        self.installed_capacity = 0         # 装机容量
        self.min_power = 0                  # 最小技术出力
        self.beginning_power = 0            # 初始出力
        self.min_step = 0                   # 最小步长
        self.subsection_gap = 1             # 每段出力的最小间隔
        self.auxiliary_power_ratio = 0      # 厂用电率，百分数
        self.variable_cost = None           # 平均变动成本（包括燃料变动成本 + 其他（含二次费用、水、材料）成本），元/MWh
        self.fixed_cost = None              # 固定成（单台机组，每天的固定成本），元/每台机组每天

        self.lower_power = None             # 出力下限
        self.upper_power = None             # 出力上限
        self.upward_speed = None            # 爬坡速率：MW/min

        # 写入log
        logger.info("------------------分段报价_收益最大_遗传算法_模型开始运行------------------------")
        msg = self._prepare_load()          # 数据预处理 + 数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        self.generators['generator'] = self.generators['generator'].astype('str')

        # 填充空值
        # 起始出力如果是空值则用最小出力代替；分段数量如果是空值则用默认值代替；事前/事后厂用电率如果是空值则用0代替
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['subsection'].fillna(self.default_subsection, inplace=True)
        self.generators['auxiliary_power_ratio'].fillna(0, inplace=True)
        if 'subsection_gap' not in self.generators.columns:  # 如果没有实时价格数据，则设置实时价格=日前价格，等价于计算利润时只考虑日前市场
            self.generators['subsection_gap'] = self.generators['min_step']
        self.generators['subsection_gap'].fillna(self.generators['min_step'], inplace=True)

        # 修正 generators 数据类型
        for col in ['installed_capacity', 'min_power', 'subsection']:
            self.generators[col] = self.generators[col].astype('int')
        for col in ['min_step', 'subsection_gap', 'variable_cost', 'fixed_cost', 'auxiliary_power_ratio', 'beginning_power']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 constriants 数据类型
        self.constraints['generator'] = self.constraints['generator'].astype('str')
        self.constraints['period'] = self.constraints['period'].astype('str')
        for col in ['lower_power', 'upper_power', 'upward_speed']:
            self.constraints[col] = self.constraints[col].astype('float')

        # 修正 prices 数据类型并排序
        if 'real_price' not in self.prices.columns:  # 如果没有实时价格数据，则设置实时价格=日前价格，等价于计算利润时只考虑日前市场
            self.prices['real_price'] = self.prices['ahead_price']
        if self.prices['real_price'].isnull().all():
            self.prices['real_price'] = self.prices['ahead_price']
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        for col in ['ahead_price', 'real_price', 'mid_long_term_elec', 'mid_long_term_price']:  # 修正数据类型
            self.prices[col] = self.prices[col].astype('float')

        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、installed_capacity、min_power、min_step、gap、variable_cost、fixed_cost)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + hasnull['min_step'] \
                   + hasnull['variable_cost'] + hasnull['fixed_cost']
        if num_null > 0:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、variable_cost、fixed_cost中有" \
                    + str(num_null) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、variable_cost、fixed_cost中没有空值；"

        # 2、循环generator，检查爬坡速率及出力上下限分段数量符合要求
        hasnull2 = self.constraints.isnull().sum()
        num_null2 = hasnull2['generator'] + hasnull2['period'] + hasnull2['lower_power'] + \
                    hasnull2['upper_power'] + hasnull2['upward_speed']
        if num_null2 > 0:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中有" + \
                   str(num_null2) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中没有空值；\n"

        # 2、循环generator，检查连续运行时长和连续停机时长，一个必须为0值
        # 3、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freqs中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中;  "
                else:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中;  "
                    logger.info(msg)
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                # gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 96):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 48):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 24):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                else:
                    msg = msg + "3.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    logger.info(msg)
                    raise Exception()
            except:
                msg += "价格数据有异常，请检查传入的价格数据。"
                logger.info(msg)
                raise Exception()

        return msg

    def _prepare_constraints(self, g):
        """
        将出力上下限及爬坡速率设置为长度=freq的列表
        :param g: 机组编号
        :return: 出力下限、出力上限、爬坡速率，列表
        """
        constraint = self.constraints[self.constraints['generator'] == g]
        constraint = constraint[['generator', 'period', 'lower_power', 'upper_power', 'upward_speed']]
        constraint['start'] = constraint['period'].apply(lambda x: x.split('-')[0]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
        constraint['end'] = constraint['period'].apply(lambda x: x.split('-')[1]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))

        lower_power = []
        upper_power = []
        upward_speed = []
        for i in range(constraint.shape[0]):
            lst = constraint.iloc[i, :].tolist()
            for j in range(lst[5], lst[6] + 1):
                lower_power.append(lst[2])
                upper_power.append(lst[3])
                upward_speed.append(lst[4])

        if len(lower_power) not in self.freqs:
            logger.info("机组" + str(g) + "的时间段不在" + str(self.freqs) + "中，请检查传入数据。")
            raise Exception()
        return lower_power, upper_power, upward_speed

    # 分拆x和p，并转化到上下限内
    def _splitxy(self, xp_line):
        # 将分段转换为累加值，作为递增的分段和递增的价格
        x = xp_line[:self.subsection]
        x = x * (self.installed_capacity - self.min_power) + self.min_power
        p = xp_line[self.subsection:]
        p = p * (self.upper_price - self.lower_price) + self.lower_price
        # 修正起始出力
        if self.is_start_zero == 1:
            x[0] = self.min_power               # 如果出力从0开始，则修正第一段出力为出力下限
        else:
            x[0] = self.min_power + 1           # 如果出力不是从0开始，则修正第一段出力为出力下限 + 1
        # 修正起始报价
        p_ = np.insert(p, 0, self.lower_price)  # 修正第一段报价为0价
        p_ = p_[:-1]
        return x, p_

    # 根据最小步长及爬坡速率，修正分段报价x, p
    def _correct_xp(self, x, p):  # x表示分段出力，p表示当前分段的报价
        # 考虑最小步长及两段报价之间出力间隔≥1MW，规整到最小步长的间隔，间隔太小就减少分段，所以最终报价段数可能小于默认分段数
        del_x = []
        last_xi = x[0]
        self.min_step = np.ceil(self.min_step)
        for i in range(1, len(x)):
            if x[i] - last_xi < max(self.min_step, self.subsection_gap):
                del_x.append(i)
                continue
            if (x[i] - last_xi) % self.min_step >= self.min_step / 2:
                x[i] = int(x[i] + self.min_step - (x[i] - last_xi) % self.min_step)
                last_xi = x[i]
            else:
                if x[i] - last_xi >= self.min_step:
                    x[i] = int(x[i] - (x[i] - last_xi) % self.min_step)
                    last_xi = x[i]
                else:
                    x[i] = int(x[i] - (x[i] - last_xi) % self.min_step)
                    del_x.append(i)
        x_ = np.delete(x, del_x)
        x_[-1] = self.installed_capacity                       # 修正最后一段出力必须为最大出力
        p_ = np.delete(p, del_x)
        p_ = [round(x, int(self.price_decimal)) for x in p_]   # 修正价格的小数点位数
        return x_, p_

    # 依据出清电价，模拟中标出力
    def _simulate_bidded_power(self, x, p, col):   # col = ['ahead_price', 'real_price']
        xs = []  # 中标出力
        last_point_power = max(self.min_power, self.beginning_power)  # 前一天的最后一个点的出力

        # 爬坡及下坡速率修正
        for p_t, lower_power, upper_power, upward_speed in zip(self.price[col], self.lower_power, self.upper_power, self.upward_speed):
            x_t = 0  # 初始化：当前时刻出力
            i = len(p) - 1  # - 1

            # 1 出清价格大于报价，才能中标，最小到p[0]=0, 一定中标，中了最小出力
            while i >= 0:
                if p_t >= p[i]:
                    x_t = x[i]
                    break
                else:
                    i = i - 1
            # 判断机组中标出力是否为0，如果是0，则不再进行爬坡速率及出力上下限的约束，暂时不考虑停机时长约束及允许开机
            if x_t != 0:
                # 2 爬坡速率修正，且需要修正为整数
                if x_t > last_point_power + upward_speed * self.gap:
                    last_point_power = int(last_point_power + upward_speed * self.gap)
                    x_t = last_point_power
                elif x_t < last_point_power - upward_speed * self.gap:
                    last_point_power = np.ceil(last_point_power - upward_speed * self.gap)
                    x_t = last_point_power
                else:
                    last_point_power = x_t

                # 3 每个时点出力的上下限修正
                if x_t > upper_power:
                    last_point_power = upper_power
                    x_t = last_point_power
                elif x_t < lower_power:
                    last_point_power = lower_power
                    x_t = last_point_power
            xs.append(x_t)
        return np.array(xs)

    # 评价函数： 依据分段出力x和对应的分段报价p，计算日前和实时市场各时点的中标出力和总收益
    def _income(self, x, p):
        x_, p_ = self._correct_xp(x, p)
        xas = self._simulate_bidded_power(x_, p_, 'ahead_price')  # 日前市场中标出力
        xrs = self._simulate_bidded_power(x_, p_, 'real_price')   # 实时市场中标出力

        # 中长期边际贡献 = 中长期电量 * （中长期电价 - 单位燃料成本）
        long_margin_profit = self.long_elec * (self.long_price - self.variable_cost)

        # 日前现货电量 = （日前中标上网出力 - 中长期合约电力）* 0.25
        spot_ahead_elec = xas * (self.gap / 60) * (1 - self.auxiliary_power_ratio) - self.long_elec
        # 实时现货电量 = （实际上网出力 - 日前中标上网出力）* 0.25
        spot_real_elec = (xrs - xas) * (self.gap / 60) * (1 - self.auxiliary_power_ratio)

        # 现货边际贡献 = 省内日前边际收益 + 省内实时边际收益
        # 省内日前边际收益 = 日前现货电量 * （日前现货电价 - 单位燃料成本）
        # 省内实时边际收益 = 实时现货电量  * （实时现货电价 - 单位燃料成本）
        spot_margin_profit = spot_ahead_elec * (self.price.ahead_price - self.variable_cost) + \
                             spot_real_elec * (self.price.real_price - self.variable_cost)

        # 收益 = 中长期边际收益 + 现货边际收益 - 折旧费用 - 固定费用
        profit = sum(long_margin_profit + spot_margin_profit) - self.fixed_cost

        return profit, x_, p_, xas, xrs

    # 计算每个染色体
    def cal_xp(self, xp):
        x, p = self._splitxy(xp)
        profit, x_, p_, xas, xrs = self._income(x, p)
        return profit, x_, p_, xas, xrs

    # 基因组表达
    def gray2rv(self, gray_code):
        # Gray Code to real value: one piece of a whole chromosome
        # input is a 2-dimensional numpy array of 0 and 1.
        # output is a 1-dimensional numpy array which convert every row of input into a real number.
        _, len_gray_code = gray_code.shape
        b = gray_code.cumsum(axis=1) % 2
        mask = np.logspace(start=1, stop=len_gray_code, base=0.5, num=len_gray_code)
        return (b * mask).sum(axis=1) / mask.sum()

    def _translateDNA(self, pop):
        cumsum_len_segment = [self.gene_size * i for i in range(1, self.subsection * 2 + 1)]
        X = np.zeros(shape=(self.POP_SIZE, self.subsection * 2))
        for i, j in enumerate(cumsum_len_segment):
            if i == 0:
                pop_temp = pop[:, :cumsum_len_segment[0]]
            else:
                pop_temp = pop[:, cumsum_len_segment[i - 1]:cumsum_len_segment[i]]
            X[:, i] = self.gray2rv(pop_temp)

        # 将分段比例转化成递增
        num = self.subsection
        X[:, :num] = np.cumsum(X[:, :num], axis=1) / X[:, :num].sum(axis=1).reshape(-1, 1)
        X[:, num:] = np.cumsum(X[:, num:], axis=1) / X[:, num:].sum(axis=1).reshape(-1, 1)
        return X

    # 单点变异
    def _mutation(self, child):
        if np.random.rand() < self.MUTATION_RATE:               # 以MUTATION_RATE的概率进行变异
            mutate_point = np.random.randint(0, self.DNA_SIZE)  # 随机产生一个实数，代表要变异基因的位置
            child[mutate_point] = child[mutate_point] ^ 1       # 将变异点的二进制为反转：按位异或，相同为0，相异为1

    # 杂交，按照基因杂交，基因排布为量价交叉 1 mother-[shape]
    def _crossover_and_mutation(self, pop):
        new_pop = []
        for father in pop:  # 遍历种群中的每一个个体，将该个体作为父亲
            child = father  # 孩子先得到父亲的全部基因
            if np.random.rand() < self.CROSSOVER_RATE:  # 产生子代时不是必然发生交叉，而是以一定的概率发生交叉
                mother = pop[np.random.randint(pop.shape[0]-1)]  # 在种群中选择另一个个体，并将该个体作为母亲
                # 子代从亲代获取基因的比例，0.4表示父6母4，循环是放回式采样，所以母不到4
                for i in range(int(2 * self.subsection * self.hybridization_rate)):
                    cross_points = np.random.randint(low=0, high=pop.shape[1])  # 随机产生杂交基因的位点
                    # 孩子得到位于交叉点所在位置的母亲的一个基因
                    child[(int(cross_points / self.gene_size)) * self.gene_size:(int(cross_points / self.gene_size)) * self.gene_size + self.gene_size] \
                    = mother[(int(cross_points / self.gene_size)) * self.gene_size:(int(cross_points / self.gene_size)) * self.gene_size + self.gene_size]
            self._mutation(child)  # 每个后代有一定的机率发生变异
            new_pop.append(child)
        return new_pop

    # 选择新种群，根据收益越大，被选中的概率越大
    def _select(self, pop, profit):  # nature selection wrt pop's fitness
        profit = profit.astype(float)
        p_min = min(profit)
        p_max = max(profit)
        if p_max == p_min:
            idx = np.arange(len(profit))
        else:
            fit_ = (profit - p_min) / (p_max - p_min)   # 标准化处理
            # 过滤掉np.nan值
            fit_ = filter(lambda x: x >= 0, fit_)
            fit = np.array(list(fit_))
            # logger.info(fit.sum(), sorted(fit))
            # 从一维数组(a)中随机抽取数字，组成指定大小(size)的数组，replace=True表示可以取相同数字，数组p与数组a对应，表示数组a中每个元素被抽到的概率
            # 在这里，profit越大, 所在样本被抽到的概率越大
            idx = np.random.choice(a=np.arange(fit.shape[0]), size=fit.shape[0], replace=True, p=fit / (fit.sum()))
        return pop[idx]

    # 计算日前市场和实时市场的中标出力、总收益，返回该种群中的最优解
    def _get_fitness(self, pop):
        pop_weight = self._translateDNA(pop)

        # 多进程
        pool = Pool(processes=cpu_num)
        result = pool.map(self.cal_xp, pop_weight)
        pool.close()
        result = np.asarray(result)
        profits, xs, ps, xas, xrs = result[:, 0], result[:, 1], result[:, 2], result[:, 3], result[:, 4]
        return profits, xas, xrs, xs, ps

        # 单进程
        # profits = []
        # xas = []
        # xrs = []
        # xs = []
        # ps = []
        # # 按行取个体，进行出力
        # for xp_line in pop_weight:
        #     # 按照规则，将权重分配到具体出力和价格
        #     x, p = self._splitxy(xp_line)
        #     # 依据出力、价格、成本计算收益
        #     profit, x_, p_, xa, xr = self._income(x, p)
        #     profits.append(profit)
        #     xas.append(xa)
        #     xrs.append(xr)
        #     xs.append(x_)
        #     ps.append(p_)
        # return np.array(profits), np.array(xas), np.array(xrs), np.array(xs), np.array(ps)

    def predict(self):
        result = {}    # 模型输出结果汇总
        # 1、循环generator；
        for g in self.generators['generator']:
            print("---第%s台机组---" % g)
            generator = self.generators[self.generators['generator'] == g]
            # 1.1、获取当前机组边界信息及节点电价
            self.price = self.prices[self.prices['generator'] == g].copy()
            freq = self.price.shape[0]
            gap = 1440 / freq
            self.gap = gap
            self.price['时间'] = self.price['time'].map(
                lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap) + 1))

            # 1.2、获取基本参数
            self.subsection = int(generator['subsection'])
            self.DNA_SIZE = int(self.gene_size * 2 * self.subsection)
            self.lower_power, self.upper_power, self.upward_speed = self._prepare_constraints(g)
            self.subsection_gap = float(generator['subsection_gap'])
            self.installed_capacity = int(generator['installed_capacity'])
            self.min_power = float(generator['min_power'])
            self.min_step = float(generator['min_step'])
            self.beginning_power = float(generator['beginning_power'])

            self.variable_cost = float(generator['variable_cost'])
            self.fixed_cost = float(generator['fixed_cost'])
            self.auxiliary_power_ratio = float(generator['auxiliary_power_ratio']) / 100
            self.long_elec = np.array(self.price['mid_long_term_elec'])  # 中长期电量，单位MWh
            self.long_price = np.array(self.price['mid_long_term_price'])  # 中长期电价，单位元/MWh

            # if max(np.ceil(self.min_step), self.subsection_gap) * (self.subsection-1) > self.installed_capacity - self.min_power - 1:
            #     self.subsection = int(np.floor((self.installed_capacity - self.min_power - 1) / (max(np.ceil(self.min_step), self.subsection_gap)))+1)
            #     logger.info(f"最小步长及最大最小出力计算得到的分段数量不满足要求，重新设置段数为:{self.subsection}")
            pop_ = np.random.randint(2, size=(self.POP_SIZE, self.DNA_SIZE))  # 二维 01 矩阵
            t_profits, t_xas, t_xrs, t_xs0, t_xs, t_ps = [], [], [], [], [], []
            # 1.3，每个机组迭代N代
            for n in range(self.N_GENERATIONS):  # 迭代N代
                print(f"-------第{n}次迭代-----")
                profits_, xas_, xrs_, xs_, ps_ = self._get_fitness(pop_)  # 经过基因组表达和拟合，返回预测结果

                # 筛选符合段数的染色体的索引
                seg_idx = []
                for i, s in enumerate(ps_):
                    if len(s) == self.subsection:
                        seg_idx.append(i)

                # 筛选符合段数的染色体
                if not seg_idx:
                    logger.info("没有符合段数的报价方案，请检查最小步长与最小最大出力是否满足段数要求")
                    pop_ = np.random.randint(2, size=(self.POP_SIZE, self.DNA_SIZE))  # 二维 01 矩阵
                    continue
                pop = pop_[seg_idx]
                profits = profits_[seg_idx]
                xas = xas_[seg_idx]
                xrs = xrs_[seg_idx]
                xs = xs_[seg_idx]
                ps = ps_[seg_idx]

                top_index = profits.argsort()[::-1][0:int(profits.shape[0] / 10)]  # 保留最优的10%
                top_pop = pop[top_index]

                # 生成新的种群，这时候不删除最优的10%，以便于可以遗传后代
                pop = self._select(pop, profits)  # 选择生成新的种群
                # 补充新的随机样本，至总数减去保留10%，如果总数大于此数字，则把尾巴剪掉
                if pop.shape[0] > (self.POP_SIZE - top_pop.shape[0]):
                    pop = pop[0:(self.POP_SIZE - top_pop.shape[0])]
                else:
                    pop_append = np.random.randint(2, size=(
                    self.POP_SIZE - pop.shape[0] - top_pop.shape[0], self.DNA_SIZE))
                    pop = np.concatenate((pop, pop_append), axis=0)
                # 进行杂交、变异
                pop = np.array(self._crossover_and_mutation(pop))
                # 将最优的10%加回来
                pop = np.concatenate((pop, top_pop), axis=0)
                # print("补充完成之后，pop.shape为：", pop.shape)

                # 保存每一代最优的前10个最大收益
                profits = list(profits)
                profits_ = list(set(profits))
                max_profit_lst = []
                n_ = min(self.out_n, len(profits_))
                for _ in range(n_):
                    max_val = max(profits_)
                    max_idx = profits_.index(max_val)
                    max_profit_lst.append(max_val)
                    profits_[max_idx] = float('-inf')
                # print(max_profit_lst)
                for pro in max_profit_lst:
                    idx = profits.index(pro)
                    t_profits.append(profits[idx])
                    t_xas.append(xas[idx])
                    t_xrs.append(xrs[idx])
                    t_xs0.append([self.min_power] + list(xs[idx][:-1]))  # 起始出力
                    t_xs.append(xs[idx])                                 # 终止出力
                    t_ps.append(ps[idx])

            # 输出最优的前10个最大收益或所有可能结果
            pro_ = list(set(t_profits))
            # print(self.out_n)
            if not pro_:
                result[g] = {'profit': None,
                             'ahead_power': None,
                             'real_power': None,
                             'step_wise_power_start': None,
                             'step_wise_power_end': None,
                             'step_wise_price': None,
                             }
            else:
                if self.out_n:
                    max_pro_lst = []
                    for _ in range(min(self.out_n, len(pro_))):
                        max_val = max(pro_)
                        max_idx = pro_.index(max_val)
                        max_pro_lst.append(max_val)
                        pro_[max_idx] = float('-inf')
                else:
                    max_pro_lst = pro_

                t_max_idx = []
                for p_ in max_pro_lst:
                    idx = t_profits.index(p_)
                    t_max_idx.append(idx)

                # 输出收益最大的前10个结果
                result[g] = {'profit': max_pro_lst,
                             'ahead_power': [list(t_xas[i])for i in t_max_idx],
                             'real_power': [list(t_xrs[i]) for i in t_max_idx],
                             'step_wise_power_start': [list(t_xs0[i]) for i in t_max_idx],
                             'step_wise_power_end': [list(t_xs[i]) for i in t_max_idx],
                             'step_wise_price': [list(t_ps[i]) for i in t_max_idx],
                             }
        # 2、输出结果 
        logger.info(f'result = {result}')
        logger.info("---------------------------分段报价_收益最大_遗传算法_模型运行结束--------------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta
    time0 = time.time()
    file = r"C:\Users\<USER>\Desktop\input.xlsx"
    generators = pd.read_excel(file, sheet_name='generators', index_col=None)
    prices = pd.read_excel(file, sheet_name='prices', index_col=None)
    constraints = pd.read_excel(file, sheet_name='constraints', index_col=None)
    p = SubsectionDeclarProfitSXJN(generators=generators, prices=prices, constraints=constraints,
                                   lower_price=0, upper_price=1500, POP_SIZE=2000, N_GENERATIONS=50)
    result = p.predict()

    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1-time0))}")
    print(result)

