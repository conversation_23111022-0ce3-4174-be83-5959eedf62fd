#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/20 16:21
# <AUTHOR> <PERSON><PERSON>
# -*- coding: utf-8 -*
# valid license

import pandas as pd
from prophet import Prophet
from json import loads
import logging
import datetime

logger = logging.getLogger()


class PredProvinces:
    def __init__(self, data, pre_start, pre_days=1, point_day=96, cap=None, floor=None, include_history=False):
        """
        :param data: dict, 历史数据
        :param holiday: dict, 假期数据
        :param pre_days: int, 预测天数，默认为: 1
        :param point_day: int, 每天的样本数，可选[24, 48, 96]，默认为: 96
        :param cap: float or list, 预测值的上限， 默认为: None
        :param floor: float or list, 预测值的下限, 默认为: None
        """
        # 写入log
        logger.info("---------------------------------------------------------")
        logger.info("------------------蒙西省间-自相关时间序列预测-------------------------")
        logger.info(
            f"data:{data}, pre_days:{pre_days}, point_day={point_day}, cap={cap}, floor={floor}")

        # 季节模式设为乘法模型
        self.params = {"seasonality_mode": 'multiplicative'}
        self.data = self._prepare_load(data)
        self.pre_days = pre_days
        self.pre_start = pre_start
        self.point_day = point_day
        self.pre_length = pre_days * point_day
        self.inc_his = include_history
        self.freq = {24: 'H', 48: '30min', 96: '15min'}[point_day]
        self.cap = cap
        self.floor = floor

    @staticmethod
    def _prepare_load(data):
        if isinstance(data, str):
            data = loads(data)
        if isinstance(data, dict):
            data['date_time'] = pd.to_datetime(data['date_time'])
            data = pd.DataFrame(data)
        data['date_time'] = pd.to_datetime(data['date_time'])
        return data


    def train_model(self, train_data, pre_length):
        '''
        不同历史数据下训练模型，预测结果
        Parameters
        ----------
        train_data ： 历史数据
        pre_length ： 预测长度
        res_list ： 预测结果

        Returns
        -------

        '''
        # params中包含['seasonality_mode', 'growth', 'holidays']
        model = Prophet(**self.params)
        model.fit(train_data)
        future = model.make_future_dataframe(periods=pre_length, include_history=self.inc_his, freq=self.freq)
        pred = model.predict(future)[['ds', 'yhat']]
        # 上下限限制
        if (self.cap is not None) and (self.floor is not None):
            pred["yhat"] = pred["yhat"].map(lambda s: self.floor if s < self.floor else s)
            pred["yhat"] = pred["yhat"].map(lambda s: self.cap if s > self.cap else s)
        # # 若预测值小于0，则设置为0
        # pred.loc[pred[pred["yhat"] < self.floor].index, "yhat"] = self.floor

        return pred

    def inter_datasets(self, df1, date):
        start_date = date
        end_date = str(pd.to_datetime(start_date) + datetime.timedelta(1))[:10]
        tmp_df = pd.DataFrame()
        tmp_df['date_time'] = pd.date_range(start_date, end_date, freq='15t', closed='left')
        tmp_df = pd.merge(tmp_df, df1, how='left')
        tmp_result = tmp_df.copy(deep=True)
        tmp_result['price'] = tmp_result['price'].interpolate()
        return tmp_result

    def predict(self, to_json=True):
        """
        :param to_json: 为了测试而设置的参数，默认为True，输出为dict
        """
        end_date = str(pd.to_datetime(self.pre_start) + datetime.timedelta(days=self.pre_days))[:10]
        self.data = self.data.sort_values('date_time')
        self.data['date'] = self.data['date_time'].map(lambda x:str(x).split(' ')[0])
        date_list = self.data['date'].unique()
        train_data = pd.DataFrame()
        for date in date_list:
            # 检查够不够96个点，如果不够就线性插值
            on_data = self.data[self.data['date'] == date]
            if on_data.shape[0] == 96:
                out_data = on_data.copy(deep=True)
            else:
                out_data = self.inter_datasets(on_data, date)
            train_data = pd.concat([train_data, out_data])
        # 插值完成，预测
        train_data = train_data.rename(columns={'date_time': 'ds', 'price': 'y'})[['ds', 'y']]
        result = self.train_model(train_data, self.pre_days * self.point_day)
        pre_datetime = pd.date_range(self.pre_start, end_date, closed='left', freq='15t')
        if to_json:
            result = {'ds': pre_datetime.astype(str).tolist(),
                      'pred': result['yhat'].tolist()}
        # 将结果写入log
        logger.info("---------------------------预测完成--------------------------")
        logger.info(f'result: {result}')
        return result


if __name__ == "__main__":
    data = pd.read_csv(r"D:\02file\0000文档\03模型类资料\模型API测试\data\common\data_train.csv")
    holiday = pd.read_csv(r"D:\02file\0000文档\03模型类资料\模型API测试\data\common\holiday.csv")
    p = PredProvinces(data=data, holiday=holiday, pre_days=5, point_day=96, include_history=False)
    # p = Prediction(data=data, holiday=holiday, pre_days=2, point_day=96, include_history=False)
    pre = p.predict(False)
    print(pre)
    pd.DataFrame(pre).to_excel(r"D:\3_images\prophet1.xlsx")
