#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/28 13:58
# <AUTHOR> Darlene

import pyomo.environ as pyo
from pyomo.opt import SolverFactory
import pandas as pd
import numpy as np
from tglibs.easy_date_time import construct_time_index
import logging
logger = logging.getLogger()

'''
  1、需要数据：场站个数，96点的中长期持仓电量*n，96点持仓均价*n，96点的申报电量*n，96点的可交易电量*n，
             节点价格一个月，统一结算点价格一个月
  2、变量：策略计算后的中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量，
  
  3、输出结果：策略计算后的中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量，转移电量
'''

class ReplacementMxHD:
    def __init__(self, generals, longterm_data, node_price, general_price, points, op_time=[],
                 high_line=1.1, low_line=0.9, num_m=[]):
        logger.info("---------------------开始计算新能源中长期置换策略---------------------")
        self.generals = generals  # 总输入场站
        if num_m == []:
            self.num_m = self.generals.keys()
        else:
            self.num_m = num_m  # 目标场站

        self.points = points  # 点数
        self.high_line = high_line   # 补偿上限
        self.low_line = low_line    # 补偿下限


        self.longterm_data = self.pro_data(longterm_data)  #中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量
        self.node_price = self.pro_data(node_price)   # 场站+节点价格
        self.general_price = self.pro_data(general_price)   # 统一结算点价格

        # 如果起止时间不给，那么取longterm的最大和最小时间
        if len(op_time) == 2:
            if pd.to_datetime(op_time[0]) < pd.to_datetime(op_time[1]):
                self.start_time = op_time[0]
                self.end_time = op_time[1]
            elif pd.to_datetime(op_time[0]) > pd.to_datetime(op_time[1]):
                self.start_time = op_time[1]
                self.end_time = op_time[0]
            else:
                self.start_time = self.longterm_data['date_time'].min()
                self.end_time = self.longterm_data['date_time'].max()
        else:
            self.start_time = self.longterm_data['date_time'].min()
            self.end_time = self.longterm_data['date_time'].max()


    def pro_data(self, data):
        '''
        数据处理
        '''
        data = pd.DataFrame(data)
        # if 'date_time' not in data.index:
        #     data = data.reset_index()
        #     data = data.rename(columns={'index': 'date_time'})
        data['date_time'] = data['date_time'].astype(str)
        if 'plant' in data.columns:
            data['plant'] = data['plant'].astype(str)
        return data

    def get_generals(self):
        '''
        按场站名称处理：上网电量、合约总量、月度持仓均价、节点均价
        '''


    def check_data(self):
        '''
        1、首先检查中长期持仓电量、中长期持仓均价，申报电量，结算价格，可交易电量等数据
        2、检查场站和节点价格是否对应
        3、检查统一结算点数据是否完整
        '''
        res = True
        logger.info("检查中长期合约数据")
        long_columns = ['plant', 'date_time', 'position_b', 'poprice_b', 'declare_b', 'accept_b']
        for gen in self.generals:
            gen_col = self.longterm_data[self.longterm_data['plant'] == gen].dropna(axis=1).columns.to_list()
            if set(long_columns) - set(gen_col):
                logger.info(f'场站{gen}中长期合约数据不完整，请检查！')
                # print(f'场站{gen}中长期合约数据不完整，请检查！')
                res = False
        logger.info("检查节点价格数据")
        for gen in self.generals:
            gen_price = self.node_price[self.node_price['plant'] == gen].dropna()
            if gen_price.empty:
                logger.info(f'场站{gen}节点价格数据不完整，请检查！')
                # print(f'场站{gen}节点价格数据不完整，请检查！')
                res = False
        logger.info("检查统一结算点数据")
        date_1 = self.longterm_data['date_time'].dropna().drop_duplicates().to_list()   # 中长期的时间点
        date_2 = self.general_price['date_time'].dropna().drop_duplicates().to_list()    # 统一结算点时间点
        if set(date_1) - set(date_2):
            logger.info("统一结算点价格数据不完整，请检查!")
            # print("统一结算点价格数据不完整，请检查!")
            res = False
        return res

    def get_model(self, z_data, node_tmp, general, n_list, n_data, m_list, mean_price):
        '''
        position_b 中长期持仓电量
        poprice_b 中长期持仓均价
        declare_b 申报电量
        accept_b 可交易电量
        node_price 节点价格
        general_price 统一结算点价格
        n_data 月度总量
        '''
        model = pyo.ConcreteModel()
        model.N = pyo.RangeSet(len(n_list))
        model.M = m_list
        model.position_b = z_data['position_b'].to_dict()
        model.poprice_b = z_data['poprice_b'].to_dict()
        model.declare_b = z_data['declare_b'].to_dict()
        model.accept_b = z_data['accept_b'].to_dict()
        model.node_price = node_tmp['price'].to_dict()
        model.general_price = general
        model.mean_price = mean_price

        # 处理月度数据
        n_data = n_data.set_index('id')
        model.in_power = n_data['in_power'].to_dict()   # 月度上网电量
        model.con_power = n_data['con_power'].to_dict()   # 月度持仓总电量
        model.con_price = n_data['con_price'].to_dict()   # 月度持仓均价
        model.n_price = n_data['n_price'].to_dict()   # 月度节点均价

        model.position_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.poprice_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.declare_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.accept_a = pyo.Var(model.N, within=pyo.NonNegativeReals)
        model.jiesuan_a = pyo.Var(model.N, within=pyo.NonNegativeReals)

        # 初始化未知
        model.transfer_a = pyo.Var(model.N, model.N, within=pyo.NonNegativeReals)

        # 构造约束
        # 转入
        def rule_in(model, n):
            q_trans = sum(model.transfer_a[i, n] for i in model.N)
            return q_trans <= model.accept_b[n] - 1
        model.rule_in = pyo.Constraint(model.N, rule=rule_in)

        # 转出
        def rule_out(model, n):
            c_trans = sum(model.transfer_a[n, i] for i in model.N)
            return c_trans <= model.position_b[n] - 1
        model.rule_out = pyo.Constraint(model.N, rule=rule_out)

        # 可交易电量
        def rule_accept(model, n):
            return model.accept_a[n] == model.accept_b[n] - sum(model.transfer_a[i, n] for i in model.N) + sum(model.transfer_a[n, i] for i in model.N)
        model.rule_aaept = pyo.Constraint(model.N, rule=rule_accept)


        #  持仓电量
        def rule_chicang(model, n):
            return model.position_a[n] == model.position_b[n] - sum(model.transfer_a[n, i] for i in model.N) + sum(model.transfer_a[i, n] for i in model.N)
        model.rule_chicang = pyo.Constraint(model.N, rule=rule_chicang)

        # 持仓均价
        def rule_junjia(model, n):
            if model.declare_b[n] == 0:
                return model.poprice_a[n] ==0
            else:
                return model.poprice_a[n] == (model.position_b[n] * model.poprice_b[n] + sum(model.transfer_a[i, n] for i in model.N)) / model.declare_b[n]
        model.rule_junjia = pyo.Constraint(model.N, rule=rule_junjia)

        # 对角线
        def rule_diagonal(model, n):
            return model.transfer_a[n, n] == 0
        model.rule_diagonal = pyo.Constraint(model.N, rule=rule_diagonal)

        # 缺额考核
        def rule_under1(model, m):
            con_powerm = model.con_power[m] - sum(model.transfer_a[m, i] for i in model.N) + sum(model.transfer_a[i, m] for i in model.N)
            return con_powerm >= model.in_power[m] * 0.9
        model.rule_under1 = pyo.Constraint(model.M, rule=rule_under1)

        # (月度加权均价*月度持仓电量-转出电量*持仓价+转入价格*转入电量)/(月持仓电量-转出+转入)
        # def rule_under2(model, m):
        #     con_feem = model.con_price[m] * model.con_power[m] - \
        #                  sum(model.transfer_a[m, i] for i in model.N) * model.poprice_b[m] + \
        #                  sum(model.transfer_a[i, m] * model.poprice_b[i] for i in model.N)
        #     con_power = model.con_power[m] - sum(model.transfer_a[m, i] for i in model.N) + \
        #                  sum(model.transfer_a[i, m] for i in model.N)
        #     return (con_feem / con_power) >= model.n_price[m]
        # model.rule_under2 = pyo.Constraint(model.M, rule=rule_under2)

        # 超额考核
        def rule_over1(model, m):
            con_powerm = model.con_power[m] - sum(model.transfer_a[m, i] for i in model.N) + sum(
                model.transfer_a[i, m] for i in model.N)
            return con_powerm <= model.in_power[m] * 1.1
        model.rule_over1 = pyo.Constraint(model.M, rule=rule_over1)

        # (月度加权均价*月度持仓电量-转出电量*持仓价+转入价格*转入电量)/(月持仓电量-转出+转入)
        # def rule_over2(model, m):
        #     con_feem = model.con_price[m] * model.con_power[m] - \
        #                sum(model.transfer_a[m, i] for i in model.N) * model.poprice_b[m] + \
        #                sum(model.transfer_a[i, m] * model.poprice_b[i] for i in model.N)
        #     con_power = model.con_power[m] - sum(model.transfer_a[m, i] for i in model.N) + \
        #                 sum(model.transfer_a[i, m] for i in model.N)
        #     return (con_feem / con_power) <= mean_price
        # model.rule_over2 = pyo.Constraint(model.M, rule=rule_over2)

        # 结算
        def rule_jiesuan(model, n):
            return model.jiesuan_a[n] == (model.position_a[n] * (model.poprice_a[n] - model.general_price) + model.declare_b[n] * model.node_price[n]) / model.declare_b[n]
        model.rule_jiesuan = pyo.Constraint(model.N, rule=rule_jiesuan)
        # 目标函数
        def obj_rule(model):
            obj_fun = sum(model.declare_b[n] * model.jiesuan_a[n] for n in model.N)
            return obj_fun
        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)
        # 求解问题
        solver = SolverFactory('ipopt')
        sol = solver.solve(model)
        if sol['Solver'][0]['Status'] != 'error':
            logger.info('求解成功！')
            longterm_result = pd.DataFrame()
            # 结果处理
            # 1、处理中长期持仓数据
            position_r = []
            poprice_r = []
            declare_r = []
            accept_r = []
            jiesuan_r = []
            for n in model.N:
                position_r.append(float(pyo.value(model.position_a[n])))
                poprice_r.append(float(pyo.value(model.poprice_a[n])))
                declare_r.append(float(pyo.value(model.declare_b[n])))
                accept_r.append(float(pyo.value(model.accept_a[n])))
                jiesuan_r.append(float(pyo.value(model.jiesuan_a[n])))
            longterm_result['id'] = n_list
            longterm_result['position'] = position_r
            longterm_result['poprice'] = poprice_r
            longterm_result['declare'] = declare_r
            longterm_result['accept'] = accept_r
            longterm_result['jiesuan'] = jiesuan_r
            transfer_result = pd.DataFrame()
            # 2、处理转让数据
            out_list = []
            out_id = []
            in_id = []
            for i in model.N:
                for j in model.N:
                    if i != j:
                        out_list.append(float(pyo.value(model.transfer_a[i, j])))
                        out_id.append(i)
                        in_id.append(j)
            transfer_result['转出站点'] = out_id
            transfer_result['转入站点'] = in_id
            transfer_result['转出电量'] = out_list
        else:
            longterm_result = pd.DataFrame()
            transfer_result = pd.DataFrame()
        return longterm_result, transfer_result


    def get_result(self, json):
        '''
        1、检查数据
        2、构造模型
        '''
        result = {}
        res = self.check_data()
        if not res:
            return result
        else:
            tmptime = pd.DataFrame()
            tmptime['date_time'] = list(construct_time_index([self.start_time, self.end_time], freq='15t').astype(str))
            time_list = tmptime[(tmptime['date_time'] >= self.start_time) &
                                (tmptime['date_time'] <= self.end_time)]['date_time'].drop_duplicates().to_list()
            n_data = pd.DataFrame()
            # 获取场站名称
            n_data['plant'] = self.generals.keys()
            # 给场站编号
            n_data.index = n_data.index + 1
            n_data['id'] = n_data.index
            longterm_data = self.longterm_data.merge(n_data, how='left', on='plant').sort_values('date_time')
            node_data = self.node_price.merge(n_data, how='left', on='plant').sort_values('date_time')
            # 把数据存入表里(上网电量、合约总量、持仓月度均价、节点月度均价、统一结算点月度均价)
            in_power = []    # 月度上网电量
            con_power = []   # 月度合约总量
            con_price = []   # 月度持仓均价
            n_price = []    # 节点月度均价
            gen_list = n_data['plant'].to_list()
            for gen in gen_list:
                in_power.append(self.generals[gen]['in_power'])
                con_power.append(self.generals[gen]['con_power'])
                con_price.append(self.generals[gen]['con_price'])
                n_fee = np.array(longterm_data[longterm_data['plant'] == gen]['declare_b'] * node_data[node_data['plant'] == gen]['price']).sum()
                n_pow = longterm_data[longterm_data['plant'] == gen]['declare_b'].sum()
                n_price.append(n_fee / n_pow)
            n_data['in_power'] = in_power
            n_data['con_power'] = con_power
            n_data['con_price'] = con_price
            n_data['n_price'] = n_price
            n_list = n_data['id'].drop_duplicates().to_list()
            m_data = n_data[n_data['plant'].isin(self.num_m)]
            m_list = m_data['id'].drop_duplicates().to_list()
            longterm_result = pd.DataFrame()
            transfer_result = pd.DataFrame()
            for time in time_list:
                # position_b, poprice_b, declare_b, accept_b, node_price, general_price\
                # 取出这一时刻中长期数据
                z_data = longterm_data[longterm_data['date_time'] == time].set_index('id')
                node_tmp = node_data[node_data['date_time'] == time][['id', 'price']].set_index('id')
                mean_price = self.general_price['price'].mean()
                general = self.general_price[self.general_price['date_time'] == time]['price'].values[0]
                longterm_tmp, transfer_tmp = self.get_model(z_data, node_tmp, general, n_list, n_data, m_list, mean_price)
                longterm_tmp['date_time'] = time
                transfer_tmp['date_time'] = time
                if longterm_result.empty:
                    longterm_result = longterm_tmp.copy(deep=True)
                else:
                    longterm_result = pd.concat([longterm_result, longterm_tmp])
                if transfer_result.empty:
                    transfer_result = transfer_tmp.copy(deep=True)
                else:
                    transfer_result = pd.concat([transfer_result, transfer_tmp])

                # 循环所有场站，更新月度持仓电量和持仓均价
                for gen in gen_list:
                    tmp_mon = n_data[n_data['plant'] == gen]
                    idx = tmp_mon['id'].values[0]
                    lon_a = longterm_tmp[longterm_tmp['id'] == idx]  # 转置后
                    lon_b = z_data[z_data.index == idx]  # 转置前
                    dif_power = lon_b['position_b'].values[0] - lon_a['position'].values[0]
                    # 持仓电量。
                    conpow = tmp_mon['con_power'].values[0] + dif_power
                    dif_fee = lon_a['position'].values[0] * lon_a['poprice'].values[0] - \
                              lon_b['position_b'].values[0] * lon_b['poprice_b'].values[0]
                    # 持仓电费
                    conpri = (tmp_mon['con_power'].values[0] * tmp_mon['con_price'].values[0] + dif_fee) / conpow
                    n_data.loc[n_data['plant'] == gen, 'con_power'] = conpow
                    n_data.loc[n_data['plant'] == gen, 'con_price'] = conpri

            longterm_result = longterm_result.merge(n_data[['id', 'plant']], how='left', on='id')
            del longterm_result['id']
            transfer_result = transfer_result.merge(n_data[['id', 'plant']], how='left', left_on='转出站点', right_on='id')
            del transfer_result['id']
            del transfer_result['转出站点']
            transfer_result = transfer_result.rename(columns={'plant': '转出站点'})
            transfer_result = transfer_result.merge(n_data[['id', 'plant']], how='left', left_on='转入站点', right_on='id')
            del transfer_result['id']
            del transfer_result['转入站点']
            transfer_result = transfer_result.rename(columns={'plant': '转入站点'})
            # del longterm_result['id']
            if json == True:
                longterm_result = longterm_result.to_dict('list')
                transfer_result = transfer_result.to_dict('list')
            result['longterm_result'] = longterm_result
            result['transfer_result'] = transfer_result
            return result






if __name__ == '__main__':
    # longterm_data, node_price, general_price
    longterm_data = pd.read_excel(r"C:\Users\<USER>\Downloads\中长期置换交易.xlsx", sheet_name='longterm_data')
    longterm_data['date_time'] = longterm_data['date_time'].astype(str)
    node_price = pd.read_excel(r"C:\Users\<USER>\Downloads\中长期置换交易.xlsx", sheet_name='node_price')
    node_price['date_time'] = node_price['date_time'].astype(str)
    general_price = pd.read_excel(r"C:\Users\<USER>\Downloads\中长期置换交易.xlsx", sheet_name='general_price')
    general_price['date_time'] = general_price['date_time'].astype(str)

    num_n = [1, 2, 3]
    #num_m = [1, 2, 3]
    rep = ReplacementMxHD(num_n=num_n, longterm_data=longterm_data, node_price=node_price, general_price=general_price,
                           points=96)
    result = rep.get_result(json=False)
    print(result['longterm_result'])
    print(result['transfer_result'])
    # print(general_price.to_dict('list'))

