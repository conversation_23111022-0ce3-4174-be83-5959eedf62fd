#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
<AUTHOR>
@Date    ：2025/6/19
@Info    ：蒙西节点价格预测
@Update  ：2025/7/7 优化模型
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_node_etr.price_real_extreme_etr import PriceRealExtremeETR


class PriceNodeETRHandlerMX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        D = params.get('D')
        print("D_Param:", D)
        df_real=params.get('df_real')
        df_ahead = params.get("df_ahead")
        df_overhaul = params.get("df_overhaul")
        df_node_gfs = params.get("df_node_gfs")
        df_all_gfs = params.get("df_all_gfs")
        df_price=params.get('df_price')
        df_holiday=params.get('df_holiday')
        pred_day = params.get("pred_day")
        name_list = params.get("name_list")
        pred = PriceRealExtremeETR(
            D=D,
            df_real=df_real,
            df_price=df_price,
            df_holiday=df_holiday,
            pred_day=pred_day,
            df_ahead=df_ahead,
            df_overhaul=df_overhaul,
            df_node_gfs=df_node_gfs,
            df_all_gfs=df_all_gfs,
            name_list=name_list,
        )
        result = pred._pred()
        self.write(result)
