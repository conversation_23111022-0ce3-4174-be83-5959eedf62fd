import datetime

import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import ExtraTreesRegressor,ExtraTreesClassifier
import numpy as np

import logging
logger = logging.getLogger()
# # logger全部输出到控制台
# logger.setLevel(logging.DEBUG)
from togeek_package.price.mengxi.price_mx_node_etr.lmp_pred_mixin import (
    DataFetcherMixin,
    DEF_FEATURES,
    D1_FEATS_LIST,
    D2_FEATS_LIST,
    D3_FEATS_LIST,
)


class PriceRealExtremeETR( DataFetcherMixin):

    def __init__(
        self, D, df_real, df_price, df_holiday, df_ahead, df_overhaul, df_node_gfs, df_all_gfs, pred_day, name_list
    ):
        logger.info(f"----------开始运行：蒙西节点价格预测-----------")
        self.scalar = StandardScaler()
        self.D = D
        self.df_real = df_real
        self.df_ahead = df_ahead
        self.df_price = df_price
        self.df_holiday = df_holiday
        self.df_overhaul = df_overhaul
        self.df_node_gfs = df_node_gfs
        self.df_all_gfs = df_all_gfs
        self.pred_day = pred_day
        self.name_list = name_list
        self.best_model = self._default_model()

    def _standalone_train(self) -> bool:
        return False

    def _default_model(self) -> bool:
        return ExtraTreesRegressor(
            n_estimators=300, max_depth=15, min_samples_leaf=3, max_features="sqrt", random_state=80  # 避免过拟合
        )

    def _train(self, D: str):
        raise NotImplementedError

    def _pred(self,tojson = True) -> pd.DataFrame:
        """
        :param D: 交易日
        :return:
        """

        train, test = self._data(
            df_price=self.df_price,
            D=self.D,
            df_real=self.df_real,
            df_holiday=self.df_holiday,
            df_ahead=self.df_ahead,
            df_overhaul=self.df_overhaul,
            df_node_gfs=self.df_node_gfs,
            df_all_gfs=self.df_all_gfs,
            pred_day=self.pred_day,
            name_list=self.name_list,
        )
        # 处理缺失值
        mask = ~train.isnull().any(axis=1)
        train = train[mask]
        if train.empty:
            logger.debug(
                f"---------------注意！交易日{D} train 清洗后没有数据，跳过预测---------------")
            return None

        if test.empty:
            logger.debug(
                f"---------------注意！交易日{D} test 没有数据，跳过预测---------------")
            return None

        if self.pred_day == 1:
            feats_list = D1_FEATS_LIST
        elif self.pred_day == 2:
            feats_list = D2_FEATS_LIST
        elif self.pred_day == 3:
            feats_list = D3_FEATS_LIST
        # 检查特征是否存在
        valid_features = [feat for feat in feats_list if feat in train.columns]
        missing_features = set(feats_list) - set(valid_features)

        if missing_features:
            logger.warning(f"[WARNING] 缺失以下特征列，已自动剔除: {missing_features}")

        # 直接预测价格
        X_train = train[valid_features]
        y_train = train['value']
        X_test = test[valid_features]

        X_train_scaled = self.scalar.fit_transform(X_train)
        self.best_model.fit(X_train_scaled, y_train)

        X_test_scaled = self.scalar.transform(X_test)
        y_pred = self.best_model.predict(X_test_scaled)

        # 预测零价
        y_zero_train = train['zero_value']
        model=ExtraTreesClassifier(n_estimators=100, max_depth=10, random_state=80)
        model.fit(X_train_scaled, y_zero_train)
        y_zero_prob = model.predict_proba(X_test_scaled)
        y_zero_pred = np.argmax(y_zero_prob, axis=1)
        # y_prob = [y_proba[i][y_pred[i]] for i in range(len(y_proba))]
        y_zero_prob = [y_zero_prob[i][y_zero_pred[i]] for i in range(len(y_zero_prob))]

        # 合并预测结果：若预测为1，则置y_pred为0
        for i in range(len(y_zero_pred)):
            if y_zero_pred[i]==1:
                y_pred[i]=0

        if y_pred is not None:
            df_pred = pd.DataFrame()
            df_pred["dateTime"]=test["dateTime"] # str
            df_pred["dateTime"]=df_pred["dateTime"].astype(str)
            df_pred["value"]=y_pred
            df_pred.reset_index(inplace=True, drop=True)
            if tojson:
                return df_pred.to_dict(orient='list')
            return df_pred

        return None


if __name__=='__main__':
    D = "2025-06-01"
    df_real = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\real.xlsx")
    df_ahead = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\ahead.xlsx")
    df_price = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_price.xlsx")
    df_overhaul = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\t_equipment_overhaul.xlsx")
    df_node_gfs = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_node_gfs.xlsx")
    df_all_gfs = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_all_gfs.xlsx")
    df_holiday = pd.read_csv(r"fwx\typical_day.csv")
    df_price=df_price[['dateTime','value']]
    name_list = [
        "红塔站",
        "百灵站",
        "望海站",
        "固北站",
        "百望Ⅰ线",
        "百望Ⅱ线",
        "百固Ⅰ线",
        "百红Ⅰ线",
        "百红Ⅱ线",
        "茂百线",
        "茂明线",
    ]
    pred_days = [1, 2, 3]
    for pred_day in pred_days:
        df_pred = PriceRealExtremeETR(
            D=D,
            df_real=df_real,
            df_price=df_price,
            df_holiday=df_holiday,
            df_ahead=df_ahead,
            df_overhaul=df_overhaul,
            df_node_gfs=df_node_gfs,
            df_all_gfs=df_all_gfs,
            pred_day=pred_day,
            name_list=name_list,
        )._pred(tojson=False)
        print(df_pred)
