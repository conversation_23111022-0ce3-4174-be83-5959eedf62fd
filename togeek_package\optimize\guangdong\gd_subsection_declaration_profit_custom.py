# -*- coding: utf-8 -*
"""
Author: laney
Date: 2025-03-19 10:43:23
LastEditTime: 2025-03-19 14:15:06
Description: 广东添加自定义出力段的分段报价模型，模型只需要优化各个出力段的报价
输入：
    1、机组ID、装机容量、最小技术出力、最小步长、起初出力、分段数量、允许停机、最小停机时长、最小运行时长、连续运行时长、连续停机时长；
    2、机组ID、时段、出力下限、出力上限、爬坡速率
    2、机组ID、时间、日前节点电价、实时节点电价
    3、机组ID、负荷率、单位变动成本
    4、报价下限、报价上限、最小收益、种群大小、迭代次数、默认分段数量
输出：
    1、总收益：profit
    2、最优分段报价：subsection_declaration,第一段从0开始
    3、分时点中标出力：bidded_power
"""
import numpy as np
import pandas as pd
import operator
import warnings
import logging
import os
from multiprocessing import Pool

warnings.filterwarnings("ignore")
logger = logging.getLogger()
cpu_num = os.cpu_count()


class GDCustomSubsectionDeclarProfitTS:
    def __init__(self, generators, prices, contracts, costs, constraints, powers, lower_price=0, upper_price=1500,
                 min_profit=0, POP_SIZE=1000, N_GENERATIONS=30, price_gap=1, price_decimal=3, func_mode='eprofit'):
        # print(f"generators: {generators.to_dict('list')}, prices: {prices.to_dict('list')}, contracts: {contracts.to_dict('list')}, "
        #       f"costs: {costs.to_dict('list')}, constraints: {constraints.to_dict('list')}, lower_price: {lower_price}, upper_price: {upper_price}")
        # 初始化输入信息
        self.generators = pd.DataFrame(generators)    # 机组信息
        self.prices = pd.DataFrame(prices)            # 机组节点电价
        self.powers = pd.DataFrame(powers)            # 自定义出力的出力点，如[120, 150, 170, 210, 230],分段数量则为4
        self.contracts = pd.DataFrame(contracts)      # 中长期合约
        self.costs = pd.DataFrame(costs)              # 机组变动成本
        self.constraints = pd.DataFrame(constraints)  # 机组分段约束条件-爬坡速率、出力上下限
        self.min_profit = min_profit                  # 机组停机的最小收益
        self.lower_price = lower_price                # 报价下限
        self.upper_price = upper_price                # 报价上限
        self.price_gap = price_gap                    # 两段间报价的最小差值
        self.price_decimal = price_decimal            # 价格保留位数，默认保留小数点后3位

        # 默认值
        self.freqs = [24, 48, 96]                     # 每日的时间点的数量只能是这些数字
        self.gap = 15                                 # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正

        # 遗传算法基本参数
        self.size_pop = POP_SIZE            # 种群数量
        self.N_GENERATIONS = N_GENERATIONS  # 进化代数
        self.CROSSOVER_RATE = 0.6           # 杂交比例，按照基因杂交；将来应该可以选择按照染色体、基因、点位三种方式都可以杂交
        self.MUTATION_RATE = 0.04           # 变异可能性
        self.hybridization_rate = 0.4       # 子代从亲代获取基因的比例，0.4表示父6母4，循环是放回式采样，所以母不到4

        # 数据格式化
        self.installed_capacity = 0         # 装机容量
        self.min_power = 0                  # 最小技术出力
        self.beginning_power = 0            # 初始出力
        self.allowed_stop = 0               # 是否允许停机，默认为0-不允许停机，1-允许停机
        self.f_auxiliary_power_ratio = 0    # 事前厂用电率，小数
        self.b_auxiliary_power_ratio = 0    # 事后厂用电率，小数
        self.elec_purchase_coefficient = 0  # 代购电系数
        self.elec_purchase_price = 0        # 代购电电价

        self.p1 = []                        # 一次项成本公式x1
        self.p2 = []                        # 二次项成本公式x2
        self.lower_power = None             # 出力下限
        self.upper_power = None             # 出力上限
        self.upward_speed = None            # 爬坡速率：MW每分钟
        self.min_boot_hour = 0              # 最小开机时长
        self.min_stop_hour = 0              # 最小停机时长
        self.running_hour = 0               # 连续开机时长
        self.stopping_hour = 0              # 连续停机时长
        self.func_mode = func_mode          # 目标函数

        # 写入log
        logger.info("------------------分段报价_收益最大_遗传算法_模型开始运行------------------------")
        msg = self._prepare_load()          # 数据预处理 + 数据校验
        print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 判断预测日是否要停机
        if 'to_stop' not in self.generators.columns:  # 如果没有该列，则设置为默认值0，即不停机
            self.generators['to_stop'] = 0
        self.generators['to_stop'].fillna(0, inplace=True)  # 空值填充为0

        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        for col in ['installed_capacity', 'allowed_stop', 'min_stop_hour', 'min_boot_hour', 'running_hour',
                    'stopping_hour', 'to_stop']:
            self.generators[col] = self.generators[col].astype('int')
        for col in ['min_power', 'beginning_power', 'f_auxiliary_power_ratio', 'b_auxiliary_power_ratio',
                    'elec_purchase_coefficient', 'elec_purchase_price']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 constriants 数据类型
        self.constraints['generator'] = self.constraints['generator'].astype('str')
        self.constraints['period'] = self.constraints['period'].astype('str')
        for col in ['lower_power', 'upper_power', 'upward_speed']:
            self.constraints[col] = self.constraints[col].astype('float')

        # 修正 prices 数据类型并排序
        if 'real_price' not in self.prices.columns:  # 如果没有实时价格数据，则设置实时价格=日前价格，等价于计算利润时只考虑日前市场
            self.prices['real_price'] = self.prices['ahead_price']
        if self.prices['real_price'].isnull().all():
            self.prices['real_price'] = self.prices['ahead_price']
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        for col in ['ahead_price', 'real_price']:  # 修正数据类型
            self.prices[col] = self.prices[col].astype('float')
        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        # 修正 contracts 数据类型并排序
        self.contracts['generator'] = self.contracts['generator'].astype('str')
        self.contracts['time'] = self.contracts['time'].astype('str')
        for col in ['mid_long_term_elec', 'mid_long_term_price', 'purchase_elec', 'purchase_elec_curve']:
            self.contracts[col] = self.contracts[col].astype('float')

        msg = ""
        # 检查powers自定义分段出力数据
        hasnull = self.powers.isnull().sum()
        num_null = hasnull['generator'] + hasnull['power_start'] + hasnull['power_end']
        if num_null > 0:
            msg += f"powers中存在{num_null}个空值"
            raise Exception(msg)
        self.powers.sort_values(['generator', 'power_start', 'power_end'], inplace=True)
        self.powers['generator'] = self.powers['generator'].astype('str')

        # 1、检测机组信息表中前10个字段(generator、installed_capacity、min_power、allowed_stop、min_stop_hour、min_boot_hour、
        # running_hour、stopping_hour、elec_purchase_coefficient、elec_purchase_price)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + \
                   hasnull['allowed_stop'] + hasnull['min_stop_hour'] + hasnull['min_boot_hour'] + \
                   hasnull['running_hour'] + hasnull['stopping_hour'] + \
                   hasnull['elec_purchase_coefficient'] + hasnull['elec_purchase_price']
        if num_null > 0:
            msg += "1.1, generator中generator、installed_capacity、min_power、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour、elec_purchase_coefficient、elec_purchase_price中有" \
                   + str(num_null) + "个空值，请检查传入数据。"
            raise Exception(msg)
        else:
            msg += "1.1, generator中generator、installed_capacity、min_power、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour、elec_purchase_coefficient、elec_purchase_price中没有空值；"

        # 2、循环generator，检查爬坡速率及出力上下限分段数量符合要求
        hasnull2 = self.constraints.isnull().sum()
        num_null2 = hasnull2['generator'] + hasnull2['period'] + hasnull2['lower_power'] + \
                    hasnull2['upper_power'] + hasnull2['upward_speed']
        if num_null2 > 0:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中有" + \
                   str(num_null2) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中没有空值；\n"

        # 2、循环generator，检查连续运行时长和连续停机时长，一个必须为0值
        for g in self.generators['generator']:
            try:
                running_hour = int(self.generators[self.generators['generator'] == g]['running_hour'])
                stopping_hour = int(self.generators[self.generators['generator'] == g]['stopping_hour'])
                if running_hour + stopping_hour != 0:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值不同时为0, "
                    if running_hour * stopping_hour == 0:
                        msg += "其中一个值为0，"
                    else:
                        msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                               + str(stopping_hour) + ", 不符合规定，请检查传入数据; "
                        raise Exception()
                else:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值同时为0，不符合规定，请检查传入数据; "
                    raise Exception()
            except:
                msg += "2, 机组数据有异常，请检查传入的机组数据。"
                raise Exception()
            finally:
                msg += "机组数据验证通过；\n"

        # 起始出力如果是空值则用最小出力代替；事前/事后厂用电率如果是空值则用0代替
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['f_auxiliary_power_ratio'].fillna(0, inplace=True)
        self.generators['b_auxiliary_power_ratio'].fillna(0, inplace=True)

        # 3、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格为96点数据
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq == 96:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 符合要求;  "
                else:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不符合要求;  "
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                # gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif operator.eq(list(price['时间']), curtimes1):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                else:
                    msg = msg + "3.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    raise Exception()
            except:
                msg += "价格数据有异常，请检查传入的价格数据。"
                logger.info(msg)
                raise Exception(msg)

        # 4、循环generator，检查变动成本
        self.costs['generator'] = self.costs['generator'].astype('str')
        generators = self.costs['generator'].unique()
        if len(generators) != len(self.generators['generator']):
            msg += "4, costs中应有" + str(len(self.generators['generator'])) + "台机组，costs表中有" + \
                   str(len(generators)) + "台机组，机组数量不一致，请检查传入数据。"
            raise Exception(msg)
        else:
            for g in generators:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null = hasnull['generator'] + hasnull['load_rate'] + hasnull['cost']
                    if num_null > 0:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中有" + \
                              str(num_null) + "个空值，请检查传入数据。"
                        raise Exception()
                    else:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中没有空值,"
                except:
                    msg += "4, 机组" + str(g) + "的输入数据有异常。"
                    raise Exception(msg)
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。\n"
        return msg

    def _prepare_constraints(self, g):
        """
        将出力上下限及爬坡速率设置为长度=freq的列表
        :param g: 机组编号
        :return: 出力下限、出力上限、爬坡速率，列表
        """
        constraint = self.constraints[self.constraints['generator'] == g]
        constraint = constraint[['generator', 'period', 'lower_power', 'upper_power', 'upward_speed']]
        lower_power = constraint['lower_power'].values[0]
        upper_power = constraint['upper_power'].values[0]
        upward_speed = constraint['upward_speed'].values[0]
        return lower_power, upper_power, upward_speed

    def crtbp(self):
        # 创建种群
        self.Chrom = np.random.randint(low=0, high=2, size=(self.size_pop, self.DNA_SIZE))
        return self.Chrom

    def gray2rv(self, gray_code):
        # 格雷码转换为真实值: 染色体的一个片段
        # input is a 2-dimensional numpy array of 0 and 1.
        # output is a 1-dimensional numpy array which convert every row of input into a real number.
        _, len_gray_code = gray_code.shape
        b = gray_code.cumsum(axis=1) % 2
        mask = np.logspace(start=1, stop=len_gray_code, base=0.5, num=len_gray_code)
        return (b * mask).sum(axis=1) / mask.sum()

    def chrom2x(self, Chrom):
        cumsum_len_segment = np.array([self.gene_size] * self.subsection).cumsum()
        X = np.zeros(shape=(self.size_pop, self.subsection))
        for i, j in enumerate(cumsum_len_segment):
            if i == 0:
                Chrom_temp = Chrom[:, :cumsum_len_segment[0]]
            else:
                Chrom_temp = Chrom[:, cumsum_len_segment[i - 1]:cumsum_len_segment[i]]
            X[:, i] = self.gray2rv(Chrom_temp)
        return X

    def crossover_2point_bit(self, pop):
        '''
        3 times faster than `crossover_2point`, but only use for 0/1 type of Chrom
        :param self:
        :return:
        '''
        Chrom, size_pop, len_chrom = pop, pop.shape[0], self.DNA_SIZE
        half_size_pop = int(size_pop / 2)
        Chrom1, Chrom2 = Chrom[:half_size_pop], Chrom[half_size_pop:]
        mask = np.zeros(shape=(half_size_pop, len_chrom), dtype=int)
        for i in range(half_size_pop):
            n1, n2 = np.random.randint(0, self.DNA_SIZE, 2)
            if n1 > n2:
                n1, n2 = n2, n1
            mask[i, n1:n2] = 1
        mask2 = (Chrom1 ^ Chrom2) & mask
        Chrom1 ^= mask2
        Chrom2 ^= mask2
        return Chrom

    def selection_tournament_faster(self, pop, profit, tourn_size=3):
        '''
        Select the best individual among *tournsize* randomly chosen
        Same with `selection_tournament` but much faster using numpy
        individuals,
        :param self:
        :param tourn_size:
        :return:
        '''
        aspirants_idx = np.random.randint(pop.shape[0], size=(pop.shape[0], tourn_size))
        aspirants_values = profit[aspirants_idx]
        winner = aspirants_values.argmax(axis=1)  # winner index in every team
        sel_index = [aspirants_idx[i, j] for i, j in enumerate(winner)]
        pop = pop[sel_index, :]
        return pop

    def mutation(self, pop):
        '''
        mutation of 0/1 type chromosome
        faster than `self.Chrom = (mask + self.Chrom) % 2`
        :param self:
        :return:
        '''
        #
        mask = (np.random.rand(pop.shape[0], self.DNA_SIZE) < self.MUTATION_RATE)
        pop ^= mask
        return pop

    # 分拆x和p，并转化到上下限内
    def _limit_price(self, line):
        # 将分段转换为累加值，作为递增的价格
        # 价格
        p = np.cumsum(line) / sum(line)
        p = p * (self.upper_price - self.lower_price) + self.lower_price
        return p

    # 根据最小报价间隔，修正报价p
    def _correct_price(self, p):  # x表示分段出力，p表示当前分段的报价
        p = np.insert(p, 0, self.lower_price)      # 修正第一段报价为0价
        p = p[:-1]

        # 判断每个出力段对应的价格是否相等，如果相等则加上最小报价间隔
        for j in range(1, len(p)):
            if p[j] - p[j-1] < self.price_gap:
                p[j] += self.price_gap
        p_ = [round(_, int(self.price_decimal)) for _ in p]   # 修正价格的小数点位数
        return p_

    # 依据出清电价，模拟中标出力
    def _simulate_bidded_power(self, x, p, col):   # col = ['ahead_price', 'real_price']
        xs = []  # 中标出力
        last_point_power = max(self.lower_power, self.beginning_power)  # 前一天的最后一个点的出力

        # 爬坡及下坡速率修正
        for p_t in self.price[col]:
            x_t = 0  # 初始化：当前时刻出力
            i = len(p) - 1  # - 1

            # 1 出清价格大于报价，才能中标，最小到p[0]=0, 一定中标，中了最小出力
            while i >= 0:
                if p_t >= p[i]:
                    x_t = x[i]
                    break
                else:
                    i = i - 1
            # 判断机组中标出力是否为0，如果是0，则不再进行爬坡速率及出力上下限的约束，暂时不考虑停机时长约束及允许开机
            if x_t != 0:
                # 2 爬坡速率修正，且需要修正为整数
                if x_t > last_point_power + self.upward_speed * self.gap:
                    last_point_power = int(last_point_power + self.upward_speed * self.gap)
                    x_t = last_point_power
                elif x_t < last_point_power - self.upward_speed * self.gap:
                    last_point_power = np.ceil(last_point_power - self.upward_speed * self.gap)
                    x_t = last_point_power
                else:
                    last_point_power = x_t
            xs.append(x_t)
        return np.array(xs)

    # 评价函数： 依据分段出力x和对应的分段报价p，计算日前和实时市场各时点的中标出力和总收益
    def _income(self, x, p):
        p_ = self._correct_price(p)
        xas = self._simulate_bidded_power(x, p_, 'ahead_price')  # 日前市场中标出力
        xrs = self._simulate_bidded_power(x, p_, 'real_price')   # 实时市场中标出力

        xas_elec = xas * (self.gap / 60)  # 日前中标电量 = 日前出力 / 4
        xrs_elec = xrs * (self.gap / 60)  # 实时中标电量 = 实时出力 / 4
        ahead_price = self.price['ahead_price'].values
        real_price = self.price['real_price'].values
        xrs1 = xrs
        if (self.freq1 == 96) & (self.freq2 == 24):
            xrs1 = np.array([np.mean(xrs1[4 * i:4 * (i + 1)]) for i in range(24)])
            xas_elec = np.array([sum(xas_elec[4 * i:4 * (i + 1)]) for i in range(24)])
            xrs_elec = np.array([sum(xrs_elec[4 * i:4 * (i + 1)]) for i in range(24)])
            ahead_price = np.array([np.mean(ahead_price[4 * i:4 * (i + 1)]) for i in range(24)])
            real_price = np.array([np.mean(real_price[4 * i:4 * (i + 1)]) for i in range(24)])

        # 变动成本计算
        load_rate = xrs1 / self.installed_capacity  # 负荷率
        point_cost = self.p2[0] * load_rate * load_rate + self.p2[1] * load_rate + self.p2[2]  # 变动成本

        # 日前上网电量 = 日前中标电量 * （1-事前厂用电率）
        # 实时上网电量 = 实时中标电量 * （1-事后厂用电率）
        # 单位转换，将功率转换位电能：间隔15分钟，相当于除以4；间隔30分钟，相当于除以2；间隔1小时，相当于除以1
        xas_up_grid_elec = xas_elec * (1 - self.f_auxiliary_power_ratio)  # 日前上网电量
        xrs_up_grid_elec = xrs_elec * (1 - self.b_auxiliary_power_ratio)  # 实时上网电量

        # 收入 = 中长期市场化电量 * 中长期市场化电价 + 代购电电量* 代购电电价 + （日前上网电量-中长期市场化电量-代购电电量）*日前电价 + （实时上网电量-日前上网电量）* 实时电价  192.168.1.215:8988
        point_revenue = self.long_elec * self.long_price + self.correct_purchase_elec * self.elec_purchase_price + \
                        (xas_up_grid_elec - self.long_elec - self.correct_purchase_elec) * ahead_price + \
                        (xrs_up_grid_elec - xas_up_grid_elec) * real_price

        # 收益 = 收入 - 成本
        profit = sum(point_revenue - point_cost * xrs_elec)
        if self.func_mode == 'eprofit':
            if sum(xrs) <= 0:  # 排除不发电的情况
                eprofit = 0
            else:
                eprofit = profit / sum(xrs_up_grid_elec)
            return eprofit, x, p_, xas, xrs
        else:
            return profit, x, p_, xas, xrs

    # 计算每个染色体的报价
    def cal_price(self, line):
        try:
            p = self._limit_price(line)
            profit, x_, p_, xas, xrs = self._income(self.x[1:], p)
            return (profit, x_, p_, xas, xrs)
        except Exception as e:
            logger.error(f"Error in cal_xp: {e}")
            # 返回默认值确保结构一致
            return (-np.inf, [], [], [], [])


    def _get_fitness(self, pop):
        pop_weight = self._translateDNA(pop)

        pool = Pool(processes=cpu_num)
        result = pool.map(self.cal_xp, pop_weight)
        pool.close()

        # 提取收益并找到最优个体
        profits = []
        best_profit = -np.inf
        best_x = []
        best_p = []
        best_xa = []
        best_xr = []

        for item in result:
            if len(item) == 5:
                profit, x_, p_, xa, xr = item
                profits.append(profit)

                # 记录当前代最优个体
                if profit > best_profit:
                    best_profit = profit
                    best_x = x_
                    best_p = p_
                    best_xa = xa
                    best_xr = xr
            else:
                profits.append(-np.inf)  # 无效结果设为负无穷

        # 保存当前代最优个体
        self.current_best_profit = best_profit
        self.current_best_x = best_x
        self.current_best_p = best_p
        self.current_best_xa = best_xa
        self.current_best_xr = best_xr

        return np.array(profits)


        # 单进程
        # profits = []
        # xas = []
        # xrs = []
        # x_s = []
        # p_s = []
        # # 按行取个体，进行出力
        # for line in pop_weight:
        #     # 按照规则，将权重分配到具体的报价
        #     p = self._limit_price(line)
        #     # 依据出力、价格、成本计算收益
        #     profit, x_, p_, xa, xr = self._income(self.x[1:], p)
        #     profits.append(profit)
        #     xas.append(xa)
        #     xrs.append(xr)
        #     x_s.append(x_)
        #     p_s.append(p_)
        # return np.array(profits), np.array(xas), np.array(xrs), np.array(x_s), np.array(p_s)

    # 输出结果处理：判断最终收益，如果小于设定收益，给出保开机方案或停机方案
    def deal_result(self, g, max_idx, tmp_result):  # g为机组编号， best_subsection为N代之后的最佳结果
        # 结果拆解
        profits, xas, xrs, xs, ps = tmp_result
        profit = profits[max_idx]

        # 最终收益与最小收益对比，小于最小收益的启用手动报价方案
        if profit < self.min_profit:
            # 判断状态
            if (self.running_hour >= self.min_boot_hour) & self.allowed_stop:  # 给出停机方案
                # print("机组" + str(g) + "可以停机")
                logger.info("机组" + str(g) + "可以停机")
                p = []
                for i in range(self.subsection):
                    j = self.subsection - i - 1
                    p.append(self.upper_price - 0.01 * j)
                p.append(self.upper_price)
            elif self.running_hour > 0:                 # 给出保开机方案
                # print("机组" + str(g) + "以最低出力运行")
                logger.info("机组" + str(g) + "以最低出力运行")
                p = [self.lower_price]
                for i in range(self.subsection - 1):
                    j = self.subsection - i - 1
                    p.append(self.upper_price - 0.01 * j)
            else:   # 机组处于停机状态，给出停机方案
                # print("机组" + str(g) + "处于停机状态")
                logger.info("机组" + str(g) + "处于停机状态，给出停机方案")
                p = []
                for i in range(self.subsection):
                    j = self.subsection - i - 1
                    p.append(self.upper_price - 0.01 * j)
            xa = self._simulate_bidded_power(self.x, p, 'ahead_price')
            xr = self._simulate_bidded_power(self.x, p, 'real_price')
        else:
            xa = xas[max_idx]  # 分时刻中标出力 - 日前
            xr = xrs[max_idx]  # 分时刻中标出力 - 实时
            p = ps[max_idx]    # 分段报价方案 - 价格

        # 返回最大收益
        tmp_profit = pd.DataFrame(columns=['generator', 'profit'])
        tmp_profit['generator'] = [g]
        tmp_profit['profit'] = [profit]    # 最大收益

        # 返回分段报价方案
        tmp_subsection = pd.DataFrame(columns=['generator', 'subsection', 'power_start', 'power_end', 'price'])
        tmp_subsection['subsection'] = range(1, self.subsection + 1)
        tmp_subsection['generator'] = g
        tmp_subsection['power_start'] = self.x[:-1]  # 每段的起始出力
        tmp_subsection['power_end'] = self.x[1:]     # 每段的终止出力
        tmp_subsection['price'] = p                  # 每个出力段的报价

        # 返回分时刻中标出力
        tmp_bided_power = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        tmp_bided_power['time'] = self.price['time'].copy()
        tmp_bided_power['generator'] = g
        tmp_bided_power['ahead_power'] = xa
        tmp_bided_power['real_power'] = xr
        return tmp_profit, tmp_subsection, tmp_bided_power

    # 停机方案
    def shut_down_scheme(self, g):
        logger.info("机组" + str(g) + "需要停机，给出停机方案")
        p = []
        for i in range(self.subsection - 1):
            j = self.subsection - i - 1
            p.append(self.upper_price - 0.01 * j)
        p.append(self.upper_price)

        # 返回最大收益
        tmp_profit = pd.DataFrame(columns=['generator', 'profit'])
        tmp_profit['generator'] = [g]
        tmp_profit['profit'] = [0]  # 最大收益

        # 返回分段报价方案
        tmp_subsection = pd.DataFrame(
            columns=['generator', 'subsection', 'power_start', 'power_end', 'price'])
        tmp_subsection['subsection'] = range(1, self.subsection + 1)
        tmp_subsection['generator'] = g
        tmp_subsection['power_start'] = self.x[:-1]  # 分段报价方案-每段的起始出力
        tmp_subsection['power_end'] = self.x[1:]     # 分段报价方案-每段的终止出力
        tmp_subsection['price'] = p  # 分段报价方案 - 报价

        # 返回分时刻中标出力
        tmp_bided_power = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        tmp_bided_power['time'] = self.price['time'].copy()
        tmp_bided_power['generator'] = g
        tmp_bided_power['ahead_power'] = 0
        tmp_bided_power['real_power'] = 0
        return tmp_profit, tmp_subsection, tmp_bided_power

    def predict(self):
        result = {}                    # 模型输出结果汇总
        profit = []                    # 最大收益
        subsection_declaration = []    # 分段报价结果
        bided_power = []               # 分时刻中标出力
        self.gene_size = np.ceil(np.log2(self.upper_price - self.lower_price + 1)).astype(int)

        # 1、循环generator；
        for g in self.generators['generator']:
            print("---第%s台机组---" % g)
            # self.to_stop = int(self.generators[self.generators['generator'] == g]['to_stop'])
            generator = self.generators[self.generators['generator'] == g]
            # 1.1、获取当前机组边界信息及节点电价
            self.stopping_hour = int(generator['stopping_hour'])
            if self.stopping_hour > 0:
                continue

            self.price = self.prices[self.prices['generator'] == g].copy()
            self.freq1 = self.price.shape[0]
            self.gap = 1440 / self.freq1

            contract = self.contracts[self.contracts['generator'] == g].copy()
            self.freq2 = contract.shape[0]

            power = self.powers[self.powers['generator'] == g].copy()

            # 1.2、获取基本参数
            self.lower_power, self.upper_power, self.upward_speed = self._prepare_constraints(g)
            self.allowed_stop = int(generator['allowed_stop'])
            self.installed_capacity = int(generator['installed_capacity'])
            self.min_power = float(generator['min_power'])
            self.beginning_power = float(generator['beginning_power'])
            self.min_boot_hour = int(generator['min_boot_hour'])
            self.min_stop_hour = int(generator['min_stop_hour'])
            self.running_hour = int(generator['running_hour'])
            self.f_auxiliary_power_ratio = float(generator['f_auxiliary_power_ratio'])
            self.b_auxiliary_power_ratio = float(generator['b_auxiliary_power_ratio'])
            self.elec_purchase_coefficient = float(generator['elec_purchase_coefficient'])
            self.elec_purchase_price = float(generator['elec_purchase_price'])
            self.x = list(power['power_start'].values)
            self.x.append(power['power_end'].values[-1])

            # 分段报价的段数
            self.subsection = power.shape[0]
            self.DNA_SIZE = int(self.gene_size * self.subsection)  # 基因组大小

            self.long_elec = np.array(contract['mid_long_term_elec'])    # 中长期市场化电量，单位MWh
            self.long_price = np.array(contract['mid_long_term_price'])  # 中长期市场化电价，单位元/MWh
            purchase_elec = np.array(contract['purchase_elec'])          # 原始代购电电量，单位MWh
            purchase_curve = np.array(contract['purchase_elec_curve'])   # 代购电参照曲线
            pred_curve = purchase_curve / np.mean(purchase_curve) * self.elec_purchase_coefficient  # 代购电预计曲线

            # 转化后代购电
            pred_curve1 = np.array([min(max(c, 0), 2) for c in pred_curve])
            self.correct_purchase_elec = purchase_elec * pred_curve1

            # if self.to_stop == 1:
            #     tmp_profit, tmp_subsection_declaration, tmp_bided_power = self.shut_down_scheme(g)
            # else:
            # 保存最优解
            self.gene_best_profit = []  # 每一代最优的收益
            self.gene_best_xas = []     # 每一代最优收益对应的日前中标出力
            self.gene_best_xrs = []     # 每一代最优收益对应的实时中标出力
            self.gene_best_xs = []      # 每一代最优收益对应的分段报价-出力
            self.gene_best_ps = []      # 每一代最优收益对应的分段报价-价格

            self.p2 = np.polyfit(self.costs[self.costs['generator'] == g]['load_rate'].values,
                                 self.costs[self.costs['generator'] == g]['cost'].values, 2)
            pop = self.crtbp()  # 二维 01 矩阵

            # 1.3 每个机组迭代N代
            for n in range(self.N_GENERATIONS):  # 迭代N代
                # print(f"-------第{n}次迭代-----", time.time())
                profits = self._get_fitness(pop)  # 现在只返回profits数组

                # 保存当前代的最优个体信息
                self.gene_best_profit.append(self.current_best_profit)
                self.gene_best_xas.append(self.current_best_xa)
                self.gene_best_xrs.append(self.current_best_xr)
                self.gene_best_xs.append(self.current_best_x)
                self.gene_best_ps.append(self.current_best_p)

                # 保留最优的10%
                top_index = profits.argsort()[::-1][0:int(profits.shape[0] / 10)]
                top_pop = pop[top_index]

                # 生成新的种群，这时候不删除最优的10%，以便于可以遗传后代
                pop = self.selection_tournament_faster(pop, profits)  # 选择生成新的种群
                # 补充新的随机样本，至总数减去保留10%，如果总数大于此数字，则把尾巴剪掉
                if pop.shape[0] > (self.size_pop - top_pop.shape[0]):
                    pop = pop[0:(self.size_pop - top_pop.shape[0])]
                else:
                    pop_append = np.random.randint(2, size=(
                    self.size_pop - pop.shape[0] - top_pop.shape[0], self.DNA_SIZE))
                    pop = np.concatenate((pop, pop_append), axis=0)
                # 进行杂交、变异
                pop = self.crossover_2point_bit(pop)
                pop = self.mutation(pop)
                # 将最优的10%加回来
                pop = np.concatenate((pop, top_pop), axis=0)
                # print("补充完成之后，pop.shape为：", pop.shape)

            tmp_max_profit_idx = np.argmax(self.gene_best_profit)

            tmp_result = [self.gene_best_profit, self.gene_best_xas, self.gene_best_xrs, self.gene_best_xs,
                          self.gene_best_ps]
            # 获取并存储各机组结果
            tmp_profit, tmp_subsection_declaration, tmp_bided_power = self.deal_result(g, tmp_max_profit_idx,
                                                                                           tmp_result)

            profit.append(tmp_profit)
            subsection_declaration.append(tmp_subsection_declaration)
            bided_power.append(tmp_bided_power)
        # 2、输出结果
        # 2.1、整合各机组结果: 由list转为DataFrame
        profit = pd.concat(profit)
        subsection_declaration = pd.concat(subsection_declaration)
        bided_power = pd.concat(bided_power)

        # 2.2、输出结果汇总为result
        result['profit'] = profit.to_dict('list')
        result['subsection_declaration'] = subsection_declaration.to_dict('list')
        result['bided_power'] = bided_power.to_dict('list')

        logger.info(f'result = {result}')
        logger.info("---------------------------分段报价_收益最大_遗传算法_模型运行结束--------------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta
    time0 = time.time()
    file = r"D:\1_Togeek\02_广东\7_台山二期\2_自定义分段报价\custom_input.xlsx"
    generators = pd.read_excel(file, sheet_name='generators', index_col=None)
    prices = pd.read_excel(file, sheet_name='prices', index_col=None)
    prices['time'] = prices['time'].astype(str)
    contracts = pd.read_excel(file, sheet_name='contracts', index_col=None)
    contracts['time'] = contracts['time'].astype(str)
    costs = pd.read_excel(file, sheet_name='costs', index_col=None)
    constraints = pd.read_excel(file, sheet_name='constraints', index_col=None)
    powers = pd.read_excel(file, sheet_name='powers', index_col=None)
    p = GDCustomSubsectionDeclarProfitTS(generators=generators, prices=prices, contracts=contracts, costs=costs,
                                         constraints=constraints, powers=powers, lower_price=0, upper_price=783,
                                         min_profit=0, POP_SIZE=3000, N_GENERATIONS=20, price_gap=1,
                                         func_mode='eprofit')
    result = p.predict()

    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1-time0))}")
    print(pd.DataFrame(result['profit']))
    print('*'*80)
    print(pd.DataFrame(result['subsection_declaration']))
    print('*' * 80)
    print(pd.DataFrame(result['bided_power']))
    print('*' * 80)
