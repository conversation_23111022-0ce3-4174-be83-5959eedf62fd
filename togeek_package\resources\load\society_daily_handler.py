from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.nationwide.load_society_daily import Prediction


class SocietyDailyHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        elec = params.pop('elec')
        holiday = params.pop('holiday', None)
        pred_days = params.pop('pred_days', 30)
        cap = params.pop('cap', None)
        floor = params.pop('floor', None)
        include_history = params.pop('include_history', False)
        flexibility = params.pop('flexibility', 5)
        pred = Prediction(elec, holiday=holiday, pred_days=pred_days, cap=cap, floor=floor,
                          include_history=include_history, flexibility=flexibility)
        self.write(pred.predict(to_json=True))
