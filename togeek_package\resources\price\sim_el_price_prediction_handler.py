# -*- coding: utf-8 -*-

from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.nationwide.price_sim_el_price_prediction.sim_el_price_prediction import *
from togeek_package.price.nationwide.price_sim_el_price_prediction.sim_el_price_prediction_value import *
from tornado import gen, concurrent
from tglibs.easy_json import j2o

class ElPricePredictionEvalHandler(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        pre_fun_type = data.get('pre_fun_type', '相似日')
        province = data['province']
        jiedian_type = data.get('jiedian_type', '统一结算点')
        jiage_name = data.get("jiage_name", "日前价格")
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']


        r = yield self.predict(pre_fun_type, province, jiedian_type, date_list_yuce, all_data, jiage_name)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, pre_fun_type, province, jiedian_type, date_list_yuce, all_data, jiage_name):
        elpd = ElPriceDataset(all_data, province, date_list_yuce, jiedian_type, jiage_name)
        result = {}

        if pre_fun_type == '相似日':
            result = elpd.sim_day_prediction()
        elif pre_fun_type == '相似时刻':
            result = elpd.sim_minute_prediction()
        else:
            result = {'error': 'pre_fun_type 暂不支持该计算类型：' + pre_fun_type}

        return result


class ElPricePredictionValueEvalHandler(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        province = data.get('province', '通用')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']
        days = data.get('days', 30)
        n_est = data.get('n_est', 130)
        max_deepth = data.get('max_depth', 11)
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 1500)
        threshold_value = data.get('threshold_value', None)
        quantile_night = data.get('quantile_night', 0.5)
        quantile_day = data.get('quantile_day', 0.75)
        special_sign = data.get('special_sign', 0)
        special_days = data.get('special_days', [])
        r = yield self.predict(province, jiedian_type, date_list_yuce, days, all_data, n_est, max_deepth, min_value, max_value,
                               threshold_value, quantile_night, quantile_day, special_sign, special_days)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, province, jiedian_type, date_list_yuce, days, all_data, n_est, max_deepth, min_value, max_value,
                threshold_value, quantile_night, quantile_day, special_sign, special_days):
        elpd_val = ElPriceValueDataset(all_data, province, date_list_yuce, special_sign,
                                       special_days, jiedian_type, threshold_value, quantile_night, quantile_day)
        result = elpd_val.predict_day_price(days=days, n_est=n_est, max_depth=max_deepth, min_value=min_value, max_value=max_value)

        return result.to_dict()
