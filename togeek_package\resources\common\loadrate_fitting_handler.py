# -*- coding: utf-8 -*-
# @Time    : 2022/12/15 18:19
# <AUTHOR> darlene
# @FileName: loadrate_fitting_handler.py
# @Software: PyCharm
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_loadrate_fitting import LoadRate


class LoadRateFittingHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        fit_x = params.get('fit_x')
        fit_y = params.get('fit_y')
        n = params.get('n')
        model = LoadRate(fit_x=fit_x, fit_y=fit_y, n=n)
        self.write(model.fitting())