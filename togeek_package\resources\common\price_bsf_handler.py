# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_price_bsf import PricePredBSF


class PricePredBSFHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        all_data = params.pop('all_data')
        date_list_pred = params.pop('date_list_pred')
        min_value = params.pop('min_value', 0)
        max_value = params.pop('max_value', 1500)

        model = PricePredBSF(all_data=all_data,
                             date_list_pred=date_list_pred,
                             min_value=min_value,
                             max_value=max_value)
        result_json = dict(model.predict())
        self.write(result_json)
