#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/4/12 10:23
# <AUTHOR> Darlene

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.liaoning.ln_segmentation_common import *


class LNAdaptMCSCommonHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        default_subsection = params.pop('default_subsection', 10)
        price_decimal = params.pop('price_decimal', 3)
        n_sampling = params.pop('n_sampling', 1e5)
        min_split_scale = params.pop('min_split_scale', 0.1)
        is_start_zero = params.pop("is_start_zero", 0)
        min_price = params.pop("min_price", 1)
        model = AdaptMCS(generators=generators, prices=prices, costs=costs, constraints=constraints,
                         lower_price=lower_price, upper_price=upper_price, default_subsection=default_subsection,
                         price_decimal=price_decimal, n_sampling=n_sampling, min_split_scale=min_split_scale,
                          is_start_zero=is_start_zero, min_price=min_price)
        result = model.predict()
        self.write(result)


class LNDistAdaptMCSCommonHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        default_subsection = params.pop('default_subsection', 10)
        price_decimal = params.pop('price_decimal', 3)
        n_sampling = params.pop('n_sampling', 1e5)
        min_split_scale = params.pop('min_split_scale', 0.1)
        is_start_zero = params.pop("is_start_zero", 0)
        dists = params.pop('dists', None)
        min_price = params.pop('min_price', 1)
        model = DistAdaptMCS(generators=generators, prices=prices, costs=costs, constraints=constraints,
                         lower_price=lower_price, upper_price=upper_price, default_subsection=default_subsection,
                         price_decimal=price_decimal, n_sampling=n_sampling, min_split_scale=min_split_scale,
                          is_start_zero=is_start_zero, min_price=min_price, dists=dists)
        result = model.predict()
        self.write(result)
