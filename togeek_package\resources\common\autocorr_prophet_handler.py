# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_autocorr_prophet import Prediction


class AutoCorrProphetHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.pop('data')
        holiday = params.pop('holiday', None)
        pre_days = params.pop('pre_days', 1)
        point_day = params.pop("point_day", 96)
        cap = params.pop('cap', None)
        floor = params.pop('floor', None)
        special_sign = params.pop('special_sign', 0)
        special_days = params.pop('special_days', [])
        include_history = params.pop('include_history', False)
        pred = Prediction(data=data, holiday=holiday, pre_days=pre_days, point_day=point_day, cap=cap, floor=floor,
                          include_history=include_history, special_sign=special_sign, special_days=special_days)
        self.write(pred.predict(to_json=True))
