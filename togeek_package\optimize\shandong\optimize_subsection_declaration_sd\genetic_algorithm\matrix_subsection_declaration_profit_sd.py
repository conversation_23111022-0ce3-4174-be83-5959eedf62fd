# -*- coding: utf-8 -*
"""
Author: laney
Date: 2023-03-27 10:43:23
LastEditTime: 2022-04-06 14:15:06
Description:
报价要求：
    1、报价的第一点出力P1≤最低技术出力
    2、报价的最后一点出力PN=额定有功功率
    3、随着出力增加，发电机组市场报价应单调非递减，即C1≤C2≤...≤CN
    4、发电机各出力点报价不可超过申报价格的上下限限制
    5、每连续两个出力点的长度≥（额定有功功率-最低技术出力）*10%
    6、报价点数 N≤7
    7、机组的市场报价应包含环保电价，机组市场化电量对应的环保电价不再另行计算
输入：
    1、机组ID、装机容量、最小技术出力、最小步长、起初出力、分段数量、允许停机、最小停机时长、最小运行时长、连续运行时长、连续停机时长；
    2、机组ID、时段、出力下限、出力上限、爬坡速率
    2、机组ID、时间、日前节点电价、实时节点电价
    3、机组ID、负荷率、单位变动成本
    4、报价下限、报价上限、最小收益、种群大小、迭代次数、默认分段数量
输出：
    1、总收益：profit
    2、最优分段报价：subsection_declaration,第一段从0开始, 最后需要根据具体需求，选取第一段（从0开始）/第二段（从最小技术出力）作为报价起始段
    3、分时点中标出力：bidded_power
"""
import numpy as np
import pandas as pd
import operator
import warnings
import logging
import os
from multiprocessing import Pool

warnings.filterwarnings("ignore")
logger = logging.getLogger()
cpu_num = os.cpu_count()


class MatrixSubsectionDeclarationSD:
    def __init__(self, generators, prices, costs, constraints, lower_price=-80, upper_price=1300, min_profit=0,
                 POP_SIZE=1000, N_GENERATIONS=100, default_subsection=7, price_decimal=3):
        # 初始化输入信息
        self.generators = pd.DataFrame(generators)    # 机组信息
        self.prices = pd.DataFrame(prices)            # 机组节点电价
        self.costs = pd.DataFrame(costs)              # 机组变动成本
        self.constraints = pd.DataFrame(constraints)  # 机组分段约束条件-爬坡速率、出力上下限
        self.default_subsection = default_subsection  # 默认申报分段数量
        self.min_profit = min_profit                  # 机组停机的最小收益
        self.lower_price = lower_price                # 报价下限
        self.upper_price = upper_price                # 报价上限
        self.price_decimal = price_decimal            # 价格保留位数，默认保留小数点后3位

        # 默认值
        self.freqs = [24, 48, 96]                     # 每日的时间点的数量只能是这些数字
        self.gap = 15                                 # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        self.subsection = 10                          # 分段数量，这里仅做初始化

        # 遗传算法基本参数
        self.size_pop = POP_SIZE            # 种群数量
        self.iter = N_GENERATIONS           # 进化代数
        self.prob_mut = 0.04                # 变异可能性

        # 数据格式化
        self.installed_capacity = 0         # 装机容量
        self.min_power = 0                  # 最小技术出力
        self.beginning_power = 0            # 初始出力
        self.min_step = 0                   # 最小步长
        self.subsection_gap = 1             # 每段出力的最小间隔
        self.allowed_stop = 0               # 是否允许停机，默认为0-不允许停机，1-允许停机

        self.p1 = []                        # 一次项成本公式x1
        self.p2 = []                        # 二次项成本公式x2
        self.lower_power = None             # 出力下限
        self.upper_power = None             # 出力上限
        self.upward_speed = None            # 爬坡速率：MW每分钟
        self.min_boot_hour = 0              # 最小开机时长
        self.min_stop_hour = 0              # 最小停机时长
        self.running_hour = 0               # 连续开机时长
        self.stopping_hour = 0              # 连续停机时长

        # 写入log
        logger.info("------------------分段报价_GA收益最大模型_开始运行------------------------")
        msg = self._prepare_load()          # 数据预处理 + 数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 判断预测日是否要停机
        if 'to_stop' not in self.generators.columns:  # 如果没有该列，则设置为默认值0，即不停机
            self.generators['to_stop'] = 0
        self.generators['to_stop'].fillna(0, inplace=True)  # 空值填充为0
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        for col in ['installed_capacity', 'min_power', 'beginning_power', 'subsection', 'allowed_stop', 'min_stop_hour',
                    'min_boot_hour', 'running_hour', 'stopping_hour', 'to_stop']:
            self.generators[col] = self.generators[col].astype('int')
        for col in ['min_step', 'subsection_gap']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 constriants 数据类型
        self.constraints['generator'] = self.constraints['generator'].astype('str')
        self.constraints['period'] = self.constraints['period'].astype('str')
        for col in ['lower_power', 'upper_power', 'upward_speed']:
            self.constraints[col] = self.constraints[col].astype('float')

        # 修正 prices 数据类型并排序
        if 'real_price' not in self.prices.columns:  # 如果没有实时价格数据，则设置实时价格=日前价格，等价于计算利润时只考虑日前市场
            self.prices['real_price'] = self.prices['ahead_price']
        if self.prices['real_price'].isnull().all():
            self.prices['real_price'] = self.prices['ahead_price']
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
        self.prices['real_price'] = self.prices['real_price'].astype('float')
        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        # 修正 costs 数据类型
        self.costs['generator'] = self.costs['generator'].astype('str')
        self.costs['load_rate'] = self.costs['load_rate'].astype('float')
        self.costs['cost'] = self.costs['cost'].astype('float')

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、installed_capacity、min_power、min_step、gap、allowed_stop、min_stop_hour、
        # min_boot_hour、running_hour、stopping_hour)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + \
                   hasnull['min_step'] + hasnull['subsection_gap'] + hasnull['allowed_stop'] + hasnull['min_stop_hour'] + \
                   hasnull['min_boot_hour'] + hasnull['running_hour'] + hasnull['stopping_hour']
        if num_null > 0:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour中有" + str(num_null) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour中没有空值；"

        # 2、循环generator，检查爬坡速率及出力上下限分段数量符合要求
        hasnull2 = self.constraints.isnull().sum()
        num_null2 = hasnull2['generator'] + hasnull2['period'] + hasnull2['lower_power'] + \
                    hasnull2['upper_power'] + hasnull2['upward_speed']
        if num_null2 > 0:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中有" + \
                   str(num_null2) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中没有空值；\n"

        # 2、循环generator，检查连续运行时长和连续停机时长，一个必须为0值
        for g in self.generators['generator']:
            try:
                running_hour = int(self.generators[self.generators['generator'] == g]['running_hour'])
                stopping_hour = int(self.generators[self.generators['generator'] == g]['stopping_hour'])
                if running_hour + stopping_hour != 0:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值不同时为0, "
                    if running_hour * stopping_hour == 0:
                        msg += "其中一个值为0，"
                    else:
                        msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                               + str(stopping_hour) + ", 不符合规定，请检查传入数据; "
                        raise Exception()
                else:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值同时为0，不符合规定，请检查传入数据; "
                    raise Exception()
            except:
                msg += "2, 机组数据有异常，请检查传入的机组数据。"
                raise Exception()
            finally:
                msg += "机组数据验证通过；\n"

        # 起始出力如果是空值则用最小出力代替；分段数量如果是空值则用默认值代替；
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['subsection'].fillna(self.default_subsection, inplace=True)

        # 3、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freqs中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中;  "
                else:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中;  "
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                # gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 96):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 48):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 24):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                else:
                    msg = msg + "3.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    raise Exception()
            except:
                msg += "价格数据有异常，请检查传入的价格数据。"
                logger.info(msg)
                raise Exception()

        # 4、循环generator，检查变动成本
        generators = self.costs['generator'].unique()
        if len(generators) != len(self.generators['generator']):
            msg += "4, costs中应有" + str(len(self.generators['generator'])) + "台机组，costs表中有" + \
                   str(len(generators)) + "台机组，机组数量不一致，请检查传入数据。"
            raise Exception()
        else:
            for g in generators:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null = hasnull['generator'] + hasnull['load_rate'] + hasnull['cost']
                    if num_null > 0:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中有" + \
                              str(num_null) + "个空值，请检查传入数据。"
                        raise Exception()
                    else:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中没有空值,"
                except:
                    msg += "4, 机组" + str(g) + "的输入数据有异常。"
                    raise Exception()
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。\n"
        return msg

    def _prepare_constraints(self, g):
        """
        将出力上下限及爬坡速率设置为长度=freq的列表
        :param g: 机组编号
        :return: 出力下限、出力上限、爬坡速率，列表
        """
        constraint = self.constraints[self.constraints['generator'] == g]
        constraint = constraint[['generator', 'period', 'lower_power', 'upper_power', 'upward_speed']]
        constraint['start'] = constraint['period'].apply(lambda x: x.split('-')[0]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
        constraint['end'] = constraint['period'].apply(lambda x: x.split('-')[1]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))

        lower_power = []
        upper_power = []
        upward_speed = []
        for i in range(constraint.shape[0]):
            lst = constraint.iloc[i, :].tolist()
            for j in range(lst[5], lst[6] + 1):
                lower_power.append(lst[2])
                upper_power.append(lst[3])
                upward_speed.append(lst[4])

        if len(lower_power) not in self.freqs:
            logger.info("机组" + str(g) + "的时间段不在" + str(self.freqs) + "中，请检查传入数据。")
            raise Exception()
        return lower_power, upper_power, upward_speed

    def _get_lb_ub(self):
        """
        根据传入的机组信息（机组数量、出力上下限、报价上下限），得到遗传算法需要传入参数的维度及上下限
        :return: None
        """
        self.lb = []  # 出力及报价的下限
        self.ub = []  # 出力及报价的上限

        for s in range(self.subsection):
            self.lb.append(self.min_power)
            self.ub.append(self.installed_capacity)
        self.lb.extend([self.lower_price] * self.subsection)
        self.ub.extend([self.upper_price] * self.subsection)
        self.lb, self.ub = np.array(self.lb), np.array(self.ub)
        self.n_dim = len(self.ub)  # GA传入参数的维度
        return True

    def crtbp(self):
        # 创建种群
        self.Chrom = np.random.randint(low=0, high=2, size=(self.size_pop, self.len_chrom))
        return self.Chrom

    def gray2rv(self, gray_code):
        # Gray Code to real value: one piece of a whole chromosome
        # input is a 2-dimensional numpy array of 0 and 1.
        # output is a 1-dimensional numpy array which convert every row of input into a real number.
        _, len_gray_code = gray_code.shape
        b = gray_code.cumsum(axis=1) % 2
        mask = np.logspace(start=1, stop=len_gray_code, base=0.5, num=len_gray_code)
        return (b * mask).sum(axis=1) / mask.sum()

    def chrom2x(self, Chrom):
        """
        将染色体编码转换成值
        :param Chrom:
        :return:
        """
        cumsum_len_segment = self.Lind.cumsum()
        X = np.zeros(shape=(self.size_pop, self.n_dim))
        for i, j in enumerate(cumsum_len_segment):
            if i == 0:
                Chrom_temp = Chrom[:, :cumsum_len_segment[0]]
            else:
                Chrom_temp = Chrom[:, cumsum_len_segment[i - 1]:cumsum_len_segment[i]]
            X[:, i] = self.gray2rv(Chrom_temp)

        # 将分段比例转化成递增
        num = self.subsection
        X[:, :num] = np.cumsum(X[:, :num], axis=1) / X[:, :num].sum(axis=1).reshape(-1, 1)
        X[:, num:] = np.cumsum(X[:, num:], axis=1) / X[:, num:].sum(axis=1).reshape(-1, 1)

        # 转化到上下限区间内
        X = self.lb + (self.ub - self.lb) * X

        # 设置数据类型
        X.astype('float16')

        return X

    def cal_xp_matrix(self):
        # 前一天的最后一个点的出力
        point_xa_power = max(self.min_power, self.beginning_power)
        ahead_price = self.price['ahead_price'].copy()
        real_price = self.price['real_price'].copy()

        # 成本曲线拟合
        p2 = np.polyfit(self.cost['load_rate'], self.cost['cost'], 2)

        num = self.subsection

        # 选取机组对应的分段出力报价
        x = self.X[:, :num]
        p = self.X[:, num:]
        x[:, 0] = self.min_power               # 第一段为最小出力值

        p = np.insert(p, 0, self.lower_price, 1)   # 第一段报价为0值
        p = p[:, :-1]

        # 1 考虑最小步长，规整到最小步长的间隔，间隔太小就减少分段，所以分段数可能小于subsection
        # 修正分段报价的步长 = 最小出力 + round（（分段出力 - 最小出力）/ 间隔）* 间隔
        # 间隔 = max{最小步长（山东为（装机容量-最小技术出力）* 10%，蒙西为装机容量*5%）， 给定间隔（一般为1）}
        # 修正后的出力和价格为x_c和p_c, 此时分段数量为subsection，当段数不符合时设置为与上一段的值相同
        x_c = self.min_power + np.around(np.around((x - self.min_power) / self.min_step) * self.min_step)
        x_c[:, -1] = self.installed_capacity  # 将最后一段修正为最大出力，即装机容量
        # x_c = np.insert(x_c, -1, self.installed_capacity, 1)  # 最后一段增加1列装机容量值，计算中标出力时使用
        x_c = np.append(x_c, np.array([self.installed_capacity] * self.size_pop).reshape(self.size_pop, 1), axis=1)

        x_diff = np.diff(x_c)
        x_diff = np.minimum(x_diff, 1)  # 将价格差大于0的值修改为1，0：与上一段的差值为0即相等，1：与上一段的差值大于0，即不相等

        p_diff = np.diff(p)
        p_diff = p_diff * x_diff[:, :-1]  # 价差跟出力差（0或1）相乘，将不符合的段数的出力与价格更改为与前一段相同，从而保证矩阵的大小
        p_diff = np.insert(p_diff, 0, self.lower_price, axis=1)  # 插入第一列的值 最低电价
        p_c = np.cumsum(p_diff, axis=1)
        p_c = np.around(p_c, 2)
        p_c = np.append(p_c, np.array([self.upper_price] * self.size_pop).reshape(self.size_pop, 1), axis=1)  # 最后一段增加1列最高报价值，计算中标出力时使用

        # 2 模拟中标出力
        # 分段价格数据扩展维度为 size_pop * freq * subsection
        p3 = np.tile(p_c[:, :-1], self.freq)
        p3 = p3.reshape(self.size_pop, self.freq, num)

        # 日前价格扩展维度为 size_pop * freq * subsection
        ap = np.array(ahead_price.tolist())
        ap1 = np.tile(ap, (num, 1)).T
        ap3 = np.array([ap1] * self.size_pop)
        ap2 = np.array([ap] * self.size_pop)  # size_pop * freq

        # 实时价格扩展维度为 size_pop * freq * subsection
        rp = np.array(real_price.tolist())
        rp1 = np.tile(rp, (num, 1)).T
        rp3 = np.array([rp1] * self.size_pop)
        rp2 = np.array([rp] * self.size_pop)   # size_pop * freq

        # 爬坡速率维度扩展 size_pop * (freq - 1)  # 爬坡速率用来计算两段之间的差值，最后一个时刻的值不计入计算
        up = np.array(self.upward_speed[:-1])
        up1 = np.array([up] * self.size_pop)  # size_pop * freq

        # 计算日前价格与分段价格的差异
        diff = ap3 - p3                # 日前价格-分段价格
        np.putmask(diff, diff > 0, 1)  # 将大于0的值设置为1
        diff = np.maximum(diff, 0)     # 将小于0的值设置为0

        # 计算实时价格与分段价格的差异
        r_diff = rp3 - p3                  # 实时价格-分段价格
        np.putmask(r_diff, r_diff > 0, 1)  # 将大于0的值设置为1
        r_diff = np.maximum(r_diff, 0)     # 将小于0的值设置为0

        # 计算中标出力的索引
        diff1 = diff.sum(axis=2, dtype=int) - 1       # 日前中标索引
        r_diff1 = r_diff.sum(axis=2, dtype=int) - 1   # 实时中标索引

        # 根据索引值，计算出力值
        mask = np.arange(self.size_pop)
        X1 = x_c[:, diff1 + 1][mask, mask]
        X0 = x_c[:, diff1][mask, mask]
        P1 = p_c[:, diff1 + 1][mask, mask]
        P0 = p_c[:, diff1][mask, mask]
        a_res = (X1 - X0) * (ap2 - P0) / (P1 - P0) + X0

        RX1 = x_c[:, r_diff1 + 1][mask, mask]
        RX0 = x_c[:, r_diff1][mask, mask]
        RP1 = p_c[:, r_diff1 + 1][mask, mask]
        RP0 = p_c[:, r_diff1][mask, mask]
        r_res = (RX1 - RX0) * (rp2 - RP0) / (RP1 - RP0) + RX0
        # r_res = (x_c[:, r_diff1 + 1][mask, mask] - x_c[:, r_diff1][mask, mask]) * (rp2 - p_c[:, r_diff1]) \
        #         / (p_c[:, r_diff1 + 1][mask, mask] - p_c[:, r_diff1][mask, mask]) + x_c[:, r_diff1][mask, mask]

        # 考虑  AX+B的计算方式
        # res = x_c[:, diff3]
        # res = res[mask, mask]

        # 爬坡速率修正--该过程需要循环检查直至爬坡速率符合要求
        a_res_c = np.around(a_res, 0)  # 日前出力取整
        while True:
            res_diff = np.diff(a_res_c)
            idx = np.where(abs(res_diff) > up1)[1]
            if idx.size > 0:
                num = min(idx) + 1
                res_i = res_diff[:, :num]
                res_i = np.maximum(res_i, -np.floor(up1[:, :num]))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                res_i = np.minimum(res_i, np.floor(up1[:, :num]))   # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                res_ci = np.insert(res_i, 0, point_xa_power, axis=1)
                res_ci = np.cumsum(res_ci, axis=1)
                a_res_c[:, :num + 1] = res_ci
            else:
                break

        r_res_c = np.around(r_res, 0)  # 实时出力取整
        while True:
            res_diff = np.diff(r_res_c)
            idx = np.where(abs(res_diff) > up1)[1]
            if idx.size > 0:
                num = min(idx) + 1
                res_i = res_diff[:, :num]
                res_i = np.maximum(res_i, -np.floor(up1[:, :num]))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                res_i = np.minimum(res_i, np.floor(up1[:, :num]))   # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                res_ci = np.insert(res_i, 0, point_xa_power, axis=1)
                res_ci = np.cumsum(res_ci, axis=1)
                r_res_c[:, :num + 1] = res_ci
            else:
                break

        # 3 计算成本 收入 收益
        a_elec = a_res_c / (60 / self.gap)                             # 日前发电量
        r_elec = r_res_c / (60 / self.gap)                             # 实时发电量
        load_rate = r_res_c / self.installed_capacity                # 负荷率
        cost = p2[0] * (load_rate ** 2) + p2[1] * load_rate + p2[2]  # 成本

        # 收益
        profit = ap2 * a_elec + rp2 * (r_elec - a_elec) - cost * r_elec   # 考虑日前市场和实时市场的收益

        return profit

    def cal_best_x_matrix(self, xp, g):
        """
        根据最后得到的最好染色体，计算得到分段出力和报价，并模拟中标出力情况
        :param xp: 最好的染色体 - self.best_X
        :return: 机组ID、时间、中标出力的列表，分段报价的结果
        """

        # 前一天的最后一个点的出力
        point_xa_power = max(self.min_power, self.beginning_power)
        ahead_price = self.price['ahead_price'].copy()
        real_price = self.price['real_price'].copy()
        time = self.price['time'].copy()

        # 成本曲线拟合
        p2 = np.polyfit(self.cost['load_rate'], self.cost['cost'], 2)

        num = self.subsection

        # 修正第一段出力和报价
        x = xp[: num]            # 第一段为最小出力值
        x[0] = self.min_power    # 第一段为最小出力值

        p = np.insert(xp[num:], 0, self.lower_price)   # 第一段报价为0值
        p = p[:-1]
        # 1 考虑最小步长，规整到最小步长的间隔，间隔太小就减少分段，所以分段数可能小于subsection
        # 修正分段报价的步长 = 最小出力 + round（（分段出力 - 最小出力）/ 间隔）* 间隔
        # 间隔 = max{最小步长，一般为（装机容量-最小技术出力）* 5%， 给定间隔，一般为1，蒙西为装机容量*5%}
        # 修正后的出力和价格为x_c和p_c, 此时分段数量为subsection，当段数不符合时设置为与上一段的值相同

        x_c = self.min_power + np.around(np.around((x - self.min_power) / self.min_step) * self.min_step)
        x_c[-1] = self.installed_capacity              # 将最后一段修正为最大出力，即装机容量
        x_c = np.append(x_c, self.installed_capacity)  # 最后一段增加装机容量值，计算中标出力时使用

        x_diff = np.diff(x_c)
        x_diff = np.minimum(x_diff, 1)   # 将价格差大于0的值修改为1，0：与上一段的差值为0即相等，1：与上一段的差值大于0，即不相等

        p_diff = np.diff(p)
        p_diff = p_diff * x_diff[:-1]  # 价差跟出力差（0或1）相乘，将不符合的段数的出力与价格更改为与前一段相同，从而保证矩阵的大小
        p_diff = np.insert(p_diff, 0, self.lower_price)  # 插入第一列的值0
        p_c = np.cumsum(p_diff)
        p_c = np.around(p_c, self.price_decimal)
        p_c = np.append(p_c, self.upper_price)  # 最后一段增加最高报价值，计算中标出力时使用

        # 2 模拟中标出力
        # 分段价格数据扩展维度为 freq * subsection
        p3 = np.tile(p_c, self.freq).reshape(self.freq, num + 1)
        x3 = np.tile(x_c, self.freq).reshape(self.freq, num + 1)

        # 日前价格扩展维度为 freq * subsection

        # 日前价格扩展维度为 freq * subsection
        ap = np.array(ahead_price.tolist())
        ap1 = np.tile(ap.reshape(-1, 1), num)

        # 实时价格扩展维度为 freq * subsection
        rp = np.array(real_price.tolist())
        rp1 = np.tile(rp.reshape(-1, 1), num)

        # 爬坡速率维度扩展 (freq - 1)  # 爬坡速率用来计算两段之间的差值，最后一个时刻的值不计入计算
        up = np.array(self.upward_speed[:-1])

        # 计算日前价格与实时价格的差异
        diff = ap1 - p3[:, :-1]        # 日前价格-实际价格
        np.putmask(diff, diff > 0, 1)  # 将大于0的值设置为1
        diff2 = np.maximum(diff, 0)    # 将小于0的值设置为0

        # 计算实时价格与分段价格的差异
        r_diff = rp1 - p3[:, :-1]          # 实时价格-分段价格
        np.putmask(r_diff, r_diff > 0, 1)  # 将大于0的值设置为1
        r_diff2 = np.maximum(r_diff, 0)    # 将小于0的值设置为0

        # # 根据索引值，计算出力值 -- 矩阵计算
        # diff3 = diff2.sum(axis=1, dtype=int) - 1  # 计算中标出力的索引
        # res = x_c[diff3]

        # 计算中标出力的索引
        diff3 = diff2.sum(axis=1, dtype=int) - 1      # 日前中标索引
        r_diff3 = r_diff2.sum(axis=1, dtype=int) - 1  # 实时中标索引

        # 根据索引值，计算出力值 -- 矩阵计算
        mask = np.arange(self.freq)
        X1 = x3[:, diff3 + 1][mask, mask]
        X0 = x3[:, diff3][mask, mask]
        P1 = p3[:, diff3 + 1][mask, mask]
        P0 = p3[:, diff3][mask, mask]
        a_res = (X1 - X0) * (ap - P0) / (P1 - P0) + X0

        RX1 = x3[:, r_diff3 + 1][mask, mask]
        RX0 = x3[:, r_diff3][mask, mask]
        RP1 = p3[:, r_diff3 + 1][mask, mask]
        RP0 = p3[:, r_diff3][mask, mask]
        r_res = (RX1 - RX0) * (rp - RP0) / (RP1 - RP0) + RX0

        # a_res = (x3[:, diff3 + 1][mask, mask] - x3[:, diff3][mask, mask]) * (ap - p3[:, diff3][mask, mask]) / (p3[:, diff3 + 1][mask, mask] - p3[:, diff3][mask, mask]) + x3[:, diff3][mask, mask]
        # r_res = (x3[:, r_diff3 + 1][mask, mask] - x3[:, r_diff3][mask, mask]) * (rp - p3[:, r_diff3][mask, mask]) / (p3[:, r_diff3 + 1][mask, mask] - p3[:, r_diff3][mask, mask]) + x3[:, r_diff3][mask, mask]

        # 爬坡速率修正--该过程需要循环检查直至爬坡速率符合要求
        a_res_c = np.around(a_res, 0)
        while True:
            res_diff = np.diff(a_res_c)
            idx = np.where(abs(res_diff) > up)[0]
            if idx.size > 0:
                num = min(idx) + 1
                res_i = res_diff[:num]
                res_i = np.maximum(res_i, -np.floor(up[:num]))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                res_i = np.minimum(res_i, np.floor(up[:num]))   # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                res_ci = np.insert(res_i, 0, point_xa_power)
                res_ci = np.cumsum(res_ci)
                a_res_c[:num + 1] = res_ci
            else:
                break

        # 爬坡速率修正--该过程需要循环检查直至爬坡速率符合要求
        r_res_c = np.around(r_res, 0)
        while True:
            res_diff = np.diff(r_res_c)
            idx = np.where(abs(res_diff) > up)[0]
            if idx.size > 0:
                num = min(idx) + 1
                res_i = res_diff[:num]
                res_i = np.maximum(res_i, -np.floor(up[:num]))  # 将小于爬坡速率（-141.75）的数据修改为爬坡速率-141
                res_i = np.minimum(res_i, np.floor(up[:num]))   # 将大于爬坡速率（141.75）的数据修改为爬坡速率141
                res_ci = np.insert(res_i, 0, point_xa_power)
                res_ci = np.cumsum(res_ci)
                r_res_c[:num + 1] = res_ci
            else:
                break

        # 3 计算发电量, 成本/收入
        a_elec = a_res_c / (60 / self.gap)                             # 日前发电量
        r_elec = r_res_c / (60 / self.gap)                             # 实时发电量
        load_rate = r_res_c / self.installed_capacity                  # 负荷率
        cost = p2[0] * (load_rate ** 2) + p2[1] * load_rate + p2[2]    # 成本
        profit = np.sum(ap * a_elec + rp * (r_elec - a_elec) - cost * r_elec)   # 收益

        bid = pd.DataFrame({'date_time': time, 'ahead_power': a_res_c, 'real_power': r_res_c})
        bid['generator'] = g
        xc_ = list(sorted(set(x_c[:-1])))
        decl = {'power': xc_, 'price': sorted(set(p_c[:-1])), 'profit': profit}
        return decl, bid

    def x2y_matrix(self):
        """
        根据种群中染色体对应的值计算目标函数的值
        :return:目标函数值
        """
        profit = self.cal_xp_matrix()
        self.Y = np.sum(profit, axis=1)
        return self.Y

    def selection(self, tourn_size=3):
        '''
        Select the best individual among *tournsize* randomly chosen
        Same with `selection_tournament` but much faster using numpy
        individuals,
        :param self:
        :param tourn_size:
        :return:
        '''
        aspirants_idx = np.random.randint(self.size_pop, size=(self.size_pop, tourn_size))
        aspirants_values = self.Y[aspirants_idx]
        winner = aspirants_values.argmax(axis=1)  # winner index in every team
        sel_index = [aspirants_idx[i, j] for i, j in enumerate(winner)]
        self.Chrom = self.Chrom[sel_index, :]
        return self.Chrom

    def crossover(self):
        '''
        3 times faster than `crossover_2point`, but only use for 0/1 type of Chrom
        :param self:
        :return:
        '''
        Chrom, size_pop, len_chrom = self.Chrom, self.size_pop, self.len_chrom
        half_size_pop = int(size_pop / 2)
        Chrom1, Chrom2 = Chrom[:half_size_pop], Chrom[half_size_pop:]
        mask = np.zeros(shape=(half_size_pop, len_chrom), dtype=int)
        for i in range(half_size_pop):
            n1, n2 = np.random.randint(0, self.len_chrom, 2)
            if n1 > n2:
                n1, n2 = n2, n1
            mask[i, n1:n2] = 1
        mask2 = (Chrom1 ^ Chrom2) & mask
        Chrom1 ^= mask2
        Chrom2 ^= mask2
        return self.Chrom

    def mutation(self):
        '''
        mutation of 0/1 type chromosome
        faster than `self.Chrom = (mask + self.Chrom) % 2`
        :param self:
        :return:
        '''
        #
        mask = (np.random.rand(self.size_pop, self.len_chrom) < self.prob_mut)
        self.Chrom ^= mask
        return self.Chrom

    def deal_result(self, g):
        """
        对得到的分段报价结果进行整理
        :return: 分段报价及中标出力
        """
        subsection, bidded_power = self.cal_best_x_matrix(self.best_x, g)
        bidded_power = bidded_power.astype(str)
        return subsection, bidded_power

    def run(self):
        gener_lsts = self.generators['generator']
        result = {}
        for g in gener_lsts:
            self.generator = self.generators[self.generators['generator'] == g]
            self.cost = self.costs[self.costs['generator'] == g]
            self.price = self.prices[self.prices['generator'] == g]
            self.price['时间'] = self.price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap) + 1))
            # print("---第%s台机组---" % g)
            # 1.1、获取当前机组边界信息及节点电价
            self.stopping_hour = int(self.generator['stopping_hour'])          # 连续停机时长
            if self.stopping_hour > 0:
                continue

            self.freq = self.price.shape[0]     # 时点数
            self.gap = 1440 / self.freq

            # 1.2、获取基本参数
            self.subsection = int(self.generator['subsection'])
            self.lower_power, self.upper_power, self.upward_speed = self._prepare_constraints(g)  # 处理成96点数据
            self.subsection_gap = float(self.generator['subsection_gap'])         # 每两段出力之间的间隔
            self.allowed_stop = int(self.generator['allowed_stop'])               # 是否允许停机
            self.installed_capacity = int(self.generator['installed_capacity'])   # 装机容量
            self.min_power = float(self.generator['min_power'])                   # 最小技术出力
            self.min_step = float(self.generator['min_step'])                     # 最小步长
            self.beginning_power = float(self.generator['beginning_power'])       # 起始出力
            self.min_boot_hour = int(self.generator['min_boot_hour'])             # 最短开机时长
            self.min_stop_hour = int(self.generator['min_stop_hour'])             # 最短停机时长
            self.running_hour = int(self.generator['running_hour'])               # 连续运行时长

            self.Chrom = None
            self.X = None  # shape = (size_pop, n_dim)
            self.Y = None  # shape = (size_pop,) , value is f(x)

            self.generation_best_X = []  # 每一代最优的x
            self.generation_best_Y = []  # 每一代最优的y

            self.all_history_Y = []  # 所有的y,即适应度

            self.best_x, self.best_y = None, None

            # 计算染色体长度
            self._get_lb_ub()
            Lind_raw = np.log2(self.ub - self.lb + 1)
            self.Lind = np.ceil(Lind_raw).astype(int)
            self.len_chrom = sum(self.Lind)
            self.crtbp()  # 创建初始种群
            tmp_res = {}

            for i in range(self.iter):
                # print(f"第{i}次迭代---", time.time())
                self.X = self.chrom2x(self.Chrom)
                self.Y = self.x2y_matrix()  # 运行时间长

                self.selection()
                self.crossover()
                self.mutation()

                # record the best ones
                generation_best_index = self.Y.argmax()
                self.generation_best_X.append(self.X[generation_best_index, :])
                self.generation_best_Y.append(self.Y[generation_best_index])
                self.all_history_Y.append(self.Y)

            global_best_index = np.array(self.generation_best_Y).argmax()
            self.best_x = self.generation_best_X[global_best_index]
            subsection, res_power = self.deal_result(g)
            tmp_res['generator'] = g
            tmp_res['subsection_declaration'] = subsection
            tmp_res['bidded_power'] = res_power.to_dict('list')

            result[g] = tmp_res

        logger.info(f'result = {result}')
        logger.info("---------------分段报价模型运行结束----------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta
    time0 = time.time()
    file = r"D:\Togeek\work\model_data\optimize\subsection_declaration_using\input.xlsx"
    generators = pd.read_excel(file, sheet_name='generators', index_col=None)
    prices = pd.read_excel(file, sheet_name='prices', index_col=None)
    costs = pd.read_excel(file, sheet_name='costs', index_col=None)
    constraints = pd.read_excel(file, sheet_name='constraints', index_col=None)
    p = MatrixSubsectionDeclarationSD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                lower_price=-80, upper_price=1300, min_profit=0, POP_SIZE=1000, N_GENERATIONS=5)
    result = p.run()

    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1-time0))}")
    # writer = pd.ExcelWriter(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\test.xlsx")
    # pd.DataFrame(result['subsection_declaration']).to_excel(writer, sheet_name='subsection')
    # pd.DataFrame(result['bided_power']).to_excel(writer, sheet_name='bided_power')
    # # pd.DataFrame(result['profit']).to_excel(writer, sheet_name='profit')
    # writer.save()
    print('*' * 80)
    print(result)


