#!/usr/bin/env python
# coding: utf-8

import numpy as np
import pandas as pd
from json import loads
# from sklearn.linear_model import LinearRegression
import logging
logger = logging.getLogger()

class Xun:
    def __init__(self, data_xun, xianjia):
        self.data_xun = data_xun
        self.xianjia = self.prepare_xianjia(xianjia)

    def prepare_xianjia(self, xianjia):
        if isinstance(xianjia, str):
            xianjia = loads(xianjia)
        else:
            xianjia = pd.DataFrame(xianjia).drop_duplicates().sort_values(by=['year', 'month'])
        type_dict = {'year': int, 'month': int, 'xun': int, 'time_point': int, 'refer': float, 'lower': float, 'upper': float}
        xianjia = xianjia.astype(type_dict)
        xianjia = xianjia.sort_values(['year', 'month', 'xun', 'time_point'])
        return xianjia

    def prepare_train(self, data):
        if isinstance(data, str):
            data = loads(data)
        if isinstance(data, dict):
            data = pd.DataFrame(data)
        data = data.fillna(method='ffill')
        data = data.fillna(method='bfill')
        data = data.fillna(method='bfill')
        data = data.fillna(method='ffill')
        type_dict = {'year': int, 'month': int, 'xun': int, 'time_point': int, 'xjz_jiage': float, 'xgd_jiage': float}
        data = data.astype(type_dict)
        xianjia = self.xianjia
        data_train = pd.merge(data, xianjia, on=['year', 'month', 'xun', 'time_point'])
        data_train = data_train.drop_duplicates().reset_index(drop=True)
        data_train = data_train.sort_values(['year', 'month', 'xun', 'time_point'])
        return data_train

    def prepare_pred(self):
        """
        根据限价数据，得到标的旬的信息。限价数据的最后一旬即为预测旬
        """
        data_pred = self.xianjia.reset_index(drop=True)[-24:]
        data_pred = data_pred.drop_duplicates().reset_index(drop=True)
        data_pred = data_pred.sort_values(['year', 'month', 'xun', 'time_point'])
        return data_pred

    def predict_xun(self, to_json=True):
        logger.info("-------------中长期旬度价格预测开始-------------------")
        logger.info(self.data_xun)
        logger.info(self.xianjia)
        train_xun = self.prepare_train(self.data_xun)
        pred_xun = self.prepare_pred()

        train_xun['xjz_rate'] = (train_xun['xjz_jiage'] - train_xun['lower']) / (
                    train_xun['upper'] - train_xun['lower'])
        train_xun['xgd_rate'] = (train_xun['xgd_jiage'] - train_xun['lower']) / (
                    train_xun['upper'] - train_xun['lower'])
        train_xun['xjz_rate'] = train_xun['xjz_rate'].map(lambda s: 0 if s < 0 else 1 if s > 1 else s)
        train_xun['xgd_rate'] = train_xun['xgd_rate'].map(lambda s: 0 if s < 0 else 1 if s > 1 else s)

        rate = train_xun.groupby('time_point').mean().reset_index()[['time_point', 'xjz_rate', 'xgd_rate']]

        pred_xun = pd.merge(pred_xun, rate, on='time_point')
        pred_xun.sort_values(['year', 'month', 'time_point'], inplace=True)
        pred_xun.reset_index(drop=True, inplace=True)
        pred_xun['xjz_jiage'] = pred_xun['lower'] + (pred_xun['upper'] - pred_xun['lower']) * pred_xun['xjz_rate']
        pred_xun['xgd_jiage'] = pred_xun['lower'] + (pred_xun['upper'] - pred_xun['lower']) * pred_xun['xgd_rate']
        
        """ 
        线性回归方法，适用于上下限值不为一条直线的情况 
        features = ['refer', 'lower', 'upper']
        train_x = train_xun[features]
        train_y_xjz = train_xun[['xjz_jiage']]
        train_y_xgd = train_xun[['xgd_jiage']]
        pred_x = pred_xun[features]
        lr_xjz = LinearRegression().fit(train_x, train_y_xjz)
        lr_xgd = LinearRegression().fit(train_x, train_y_xgd)
        pred_xun['xjz_jiage'] = lr_xjz.predict(pred_x)
        pred_xun['xgd_jiage'] = lr_xgd.predict(pred_x)
        """
        
        if to_json:
            result = {'year': pred_xun['year'].tolist(),
                      'month': pred_xun['month'].tolist(),
                      'xun': pred_xun['xun'].tolist(),
                      'time_point': pred_xun['time_point'].tolist(),
                      'xjz_jiage': pred_xun['xjz_jiage'].tolist(),
                      'xgd_jiage': pred_xun['xgd_jiage'].tolist()}
        else:
            result = pred_xun[['year', 'month', 'xun', 'time_point', 'xjz_jiage', 'xgd_jiage']]
        logger.info(f'result = {result}')
        logger.info("-------------中长期旬度价格预测结束-------------------")
        return result


if __name__ == '__main__':
    import warnings
    warnings.filterwarnings('ignore')
    from sklearn.metrics import r2_score
    xianjia = pd.read_csv(r"D:\ToGeek\work\model_data\price\zcqfsdjg\xun_xianjia.csv")
    data_xun_train = pd.read_csv(r"D:\ToGeek\work\model_data\price\zcqfsdjg\xun.csv")
    # data_xun_test = pd.read_csv(r"D:\ToGeek\work\model_data\price\zcqfsdjg\data_xun_test.csv")
    p = Xun(data_xun=data_xun_train, xianjia=xianjia)
    pred = p.predict_xun(to_json=False)
    # 评估
    # acc_xjz = 1 - abs((data_xun_test['xjz_jiage'] - pred['xjz_jiage'])/data_xun_test['xjz_jiage']).mean()
    # r2_xjz = r2_score(data_xun_test['xjz_jiage'], pred['xjz_jiage'])
    # acc_xgd = 1 - abs((data_xun_test['xgd_jiage'] - pred['xgd_jiage'])/data_xun_test['xgd_jiage']).mean()
    # r2_xgd = r2_score(data_xun_test['xgd_jiage'], pred['xgd_jiage'])
    # print(f'acc_xjz: {acc_xjz}\nr2_xjz: {r2_xjz}')
    # print(f'acc_xgd: {acc_xgd}\nr2_ygd: {r2_xgd}')
    print(pd.DataFrame(pred))



