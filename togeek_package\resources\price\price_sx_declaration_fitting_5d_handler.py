# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shanxi.sx_declaration_fitting_5d import DeclarationFit5d


class DeclarFitting5dHandlerSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        pred_list = params.pop('pred_list')
        sj_sign = params.get('sj_sign', 0)
        sj_date = params.get('sj_date', [])
        min_price = params.get("min_price", 0)
        max_price = params.get("max_price", 1500)
        model = DeclarationFit5d(source_data=source_data, pred_list=pred_list, sj_sign=sj_sign, sj_date=sj_date,
                                 min_price=min_price, max_price=max_price)
        self.write(model.pred_price(to_json=True))

