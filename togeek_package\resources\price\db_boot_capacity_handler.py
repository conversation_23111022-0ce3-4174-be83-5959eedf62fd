# -*- coding: utf-8 -*-
# @Time    : 2022/4/22 14:35
# <AUTHOR> darlene
# @FileName: boot_capacity_handler.py
# @Software: PyCharm
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.dongbei.price_db_boot_capacity import ForecastBoot


class BootValueEvalHandlerDB(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        all_data = params.get('all_data')
        boot_data = params.get('boot_data')
        date_list_yuce = params.get('date_list_yuce')
        coe = params.get('coe', 3.92)
        days = params.get('days', 3)
        province = params.get('province', "东北")
        pred = ForecastBoot(all_data=all_data, boot_data=boot_data, date_list_yuce=date_list_yuce, coe=coe, days=days, province=province)
        self.write(pred.result)
