# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_xny_declaration import Prediction
import logging
logger = logging.getLogger()

class XnyLambdaAvgHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price = params.pop('price')
        lambda_min = params.pop('lambda_min')
        lambda_max = params.pop('lambda_max')
        periods = params.pop('periods', 96)
        logger.info("----------------新能源申报系数预测模型--------------------------")
        model = Prediction(price, lambda_min, lambda_max, periods)
        result = model.deal_result()
        logger.info("------------end----------------")
        self.write(result)