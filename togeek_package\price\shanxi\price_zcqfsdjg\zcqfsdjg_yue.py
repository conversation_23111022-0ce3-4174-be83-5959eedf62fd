#!/usr/bin/env python
# coding: utf-8

import numpy as np
import pandas as pd
from json import loads
# from sklearn.linear_model import LinearRegression
import logging
logger = logging.getLogger()

class Yue:
    def __init__(self, data_yue, xianjia):
        self.data_yue = data_yue
        self.xianjia = xianjia

    def prepare_xianjia(self):
        if isinstance(self.xianjia, str):
            self.xianjia = loads(self.xianjia)
        if isinstance(self.xianjia, dict):
            self.xianjia = pd.DataFrame(self.xianjia).drop_duplicates()
        type_dict = {'year': int, 'month': int, 'time_point': int, 'refer': float, 'lower': float, 'upper': float}
        self.xianjia = self.xianjia.astype(type_dict)
        return self.xianjia

    def prepare_train(self, data):
        if isinstance(data, str):
            data = loads(data)
        if isinstance(data, dict):
            data = pd.DataFrame(data).sort_values(by=['year', 'month'])
        data = data.fillna(method='ffill')
        data = data.fillna(method='bfill')
        data = data.fillna(method='bfill')
        data = data.fillna(method='ffill')
        type_dict = {'year': int, 'month': int, 'time_point': int, 'yjz_jiage': float, 'ygd_jiage': float}
        data = data.astype(type_dict)
        xianjia = self.prepare_xianjia()
        data_train = pd.merge(data, xianjia, on=['year', 'month', 'time_point'])
        return data_train

    def prepare_pred(self, data):
        if data['month'].to_list()[-1] == 12:
            yuce_year = data['year'].to_list()[-1] + 1
            yuce_month = 1
        else:
            yuce_year = data['year'].to_list()[-1]
            yuce_month = data['month'].to_list()[-1] + 1

        data_pred = pd.DataFrame({'year': [yuce_year] * 24,
                                  'month': [yuce_month] * 24,
                                  'time_point': np.arange(24)})
        xianjia = self.prepare_xianjia()
        data_pred = pd.merge(data_pred, xianjia, on=['year', 'month', 'time_point'])
        return data_pred

    def predict_yue(self, to_json=True):
        logger.info("-------------中长期月度价格预测开始-------------------")
        logger.info(self.data_yue)
        logger.info(self.xianjia)
        train_yue = self.prepare_train(self.data_yue)
        pred_yue = self.prepare_pred(train_yue)
        train_yue['yjz_rate'] = (train_yue['yjz_jiage'] - train_yue['lower']) / (train_yue['upper'] - train_yue['lower'])
        train_yue['ygd_rate'] = (train_yue['ygd_jiage'] - train_yue['lower']) / (train_yue['upper'] - train_yue['lower'])
        train_yue['yjz_rate'] = train_yue['yjz_rate'].map(lambda s: 0 if s < 0 else 1 if s > 1 else s)
        train_yue['ygd_rate'] = train_yue['ygd_rate'].map(lambda s: 0 if s < 0 else 1 if s > 1 else s)

        rate = train_yue.groupby('time_point').mean().reset_index()[['time_point', 'yjz_rate', 'ygd_rate']]

        pred_yue = pd.merge(pred_yue, rate, on='time_point')
        pred_yue.sort_values(['year', 'month', 'time_point'], inplace=True)
        pred_yue.reset_index(drop=True, inplace=True)
        pred_yue['yjz_jiage'] = pred_yue['lower'] + (pred_yue['upper'] - pred_yue['lower']) * pred_yue['yjz_rate']
        pred_yue['ygd_jiage'] = pred_yue['lower'] + (pred_yue['upper'] - pred_yue['lower']) * pred_yue['ygd_rate']

        """ 
        线性回归方法，适用于上下限值不为一条直线的情况 
        
        features = ['refer', 'lower', 'upper']
        train_x = train_yue[features]
        train_y_yjz = train_yue[['yjz_jiage']]
        train_y_ygd = train_yue[['ygd_jiage']]
        pred_x = pred_yue[features]
        lr_yjz = LinearRegression().fit(train_x, train_y_yjz)
        lr_ygd = LinearRegression().fit(train_x, train_y_ygd)
        pred_yue['yjz_jiage'] = lr_yjz.predict(pred_x)
        pred_yue['ygd_jiage'] = lr_ygd.predict(pred_x)
        """

        if to_json:
            result = {'year': pred_yue['year'].tolist(),
                      'month': pred_yue['month'].tolist(),
                      'time_point': pred_yue['time_point'].tolist(),
                      'yjz_jiage': pred_yue['yjz_jiage'].tolist(),
                      'ygd_jiage': pred_yue['ygd_jiage'].tolist()}
        else:
            result = pred_yue[['year', 'month', 'time_point', 'yjz_jiage', 'ygd_jiage']]
        logger.info(f'result = {result}')
        logger.info("-------------中长期月度价格预测结束-------------------")
        return result


if __name__ == '__main__':
    import warnings
    warnings.filterwarnings('ignore')
    from sklearn.metrics import r2_score
    xianjia = pd.read_csv(r'D:\ToGeek\work\model_data\price\zcqfsdjg\yue_xianjia.csv')
    data_yue_train = pd.read_csv(r'D:\ToGeek\work\model_data\price\zcqfsdjg\yue.csv')
    # data_yue_test = pd.read_csv(r'D:\ToGeek\work\model_data\price\zcqfsdjg\data_yue_test.csv')
    p = Yue(data_yue=data_yue_train, xianjia=xianjia)
    pred = p.predict_yue(to_json=False)
    # 评估
    # acc_yjz = 1 - abs((data_yue_test['yjz_jiage'] - pred['yjz_jiage'])/data_yue_test['yjz_jiage']).mean()
    # r2_yjz = r2_score(data_yue_test['yjz_jiage'], pred['yjz_jiage'])
    # acc_ygd = 1 - abs((data_yue_test['ygd_jiage'] - pred['ygd_jiage'])/data_yue_test['ygd_jiage']).mean()
    # r2_ygd = r2_score(data_yue_test['ygd_jiage'], pred['ygd_jiage'])
    # print(f'acc_yjz: {acc_yjz}\nr2_yjz: {r2_yjz}')
    # print(f'acc_ygd: {acc_ygd}\nr2_ygd: {r2_ygd}\n')
    print(pd.DataFrame(pred))



