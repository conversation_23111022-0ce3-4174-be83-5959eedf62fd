# !/usr/bin/env python
# -*- coding:utf-8 -*-

# 三段拟合模型，可用于日前价格预测，实时价格预测


import numpy as np
import pandas as pd
import jenkspy
import warnings
import logging
from prophet import Prophet
from datetime import datetime, timedelta


# logging.getLogger('prophet').setLevel(logging.WARNING)
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class PricePredBSF:

    def __init__(self, all_data, price, date_list_pred, points=24, min_value=0, max_value=1500):
        logger.info("---------------------- 河北_三段拟合法_价格预测开始 --------------------------")
        self.points = points
        self.date_list_pred = sorted(date_list_pred)    # 预测日期列表
        self.price = pd.DataFrame(price)
        self.price['date_time'] = pd.to_datetime(self.price['date_time'])
        self.all_data = self.data_process(all_data)
        self.all_data = self._pred_load()
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

    def data_process(self, all_data):
        """
        传入数据整理
        """
        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame.from_dict(all_data, orient='index').T  # 传入json数据
        data = data.sort_index()    # 日期索引升序排列

        # 字段检测
        for col in ['date_time', '统调负荷预测', '新能源预测', '联络线计划']:
            if col not in data.columns:
                logger.info(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")
                raise ValueError(f"传入数据中不包含目标字段'{col}', 请核对传入数据字段!")

        # 数据类型转换
        for col in ['统调负荷预测', '新能源预测', '联络线计划']:
            data[col] = data[col].astype(float)

        # 添加日期字段
        data['date'] = data['date_time'].astype(str).map(lambda s: s.split(" ")[0])
        data = data.sort_values('date_time')
        return data

    def _pred_load(self, points=96):
        # 如果历史数据的最后一天不是run_date，需要将预测日期的[统调负荷、新能源、联络线]数据补齐
        run_date = self.date_list_pred[-1]  # 最大预测日期
        last_date = str(self.all_data['date'].tolist()[-1])  # 传入数据的截至日期
        start = str(datetime.strptime(last_date, '%Y-%m-%d') + timedelta(1)).split(" ")[0]  #
        n = (datetime.strptime(run_date, '%Y-%m-%d') - datetime.strptime(last_date, '%Y-%m-%d')).days
        if n > 0:
            future = pd.DataFrame({'date_time': pd.date_range(start=start, periods=n * points, freq="15min")})
            self.all_data = pd.concat([self.all_data, future], axis=0).drop_duplicates('date_time', keep='last')
            self.all_data['date'] = self.all_data['date_time'].astype(str).map(lambda s: s.split(" ")[0])
        for col in ['统调负荷预测', '新能源预测', '联络线计划']:
            tmp_df = self.all_data[['date_time', 'date', col]].dropna()
            if tmp_df.shape[0] < points * 5:
                self.all_data[col] = self.all_data[col].fillna(tmp_df[col].mean())
                continue
            last_date_i = tmp_df['date'].tolist()[-1]
            n = (datetime.strptime(run_date, '%Y-%m-%d') - datetime.strptime(last_date_i, '%Y-%m-%d')).days
            if n < 1:
                continue
            data = tmp_df[['date_time', col]]
            data.columns = ['ds', 'y']
            data['ds'] = pd.to_datetime(data['ds'])
            params = {"seasonality_mode": 'multiplicative'}  # , "yearly_seasonality": True, "weekly_seasonality": True
            ts_model = Prophet(**params)
            ts_model.fit(data)

            future = ts_model.make_future_dataframe(periods=n * points, include_history=False, freq='15min')
            pred = ts_model.predict(future)[['ds', 'yhat']]
            pred.columns = ['date_time', col]
            tmp_total = pd.concat([tmp_df[['date_time', col]], pred], axis=0)
            del self.all_data[col]
            self.all_data = pd.merge(self.all_data, tmp_total, on='date_time', how='left')
        self.all_data['date_time'] = pd.to_datetime(self.all_data['date_time'])
        self.all_data.set_index('date_time', inplace=True)
        if self.points == 24:
            self.all_data = self.all_data.resample('H').mean().reset_index()
        elif self.points == 48:
            self.all_data = self.all_data.resample('30min').mean().reset_index()
        elif self.points == 96:
            self.all_data = self.all_data.reset_index()
        else:
            logger.info("仅支持【24，48，96】点数据，请检查输入参数【points】！")
            raise ValueError("仅支持【24，48，96】点数据，请检查输入参数【points】！")
        self.all_data['bidding_space'] = self.all_data['统调负荷预测'] - self.all_data['新能源预测'] - self.all_data['联络线计划']
        self.all_data['date'] = self.all_data['date_time'].astype(str).map(lambda s: s.split(" ")[0])
        self.all_data['time'] = self.all_data['date_time'].astype(str).map(lambda s: s.split(" ")[-1])
        self.all_data = pd.merge(self.all_data, self.price, on='date_time', how='left')
        return self.all_data

    @staticmethod
    def get_history_date(history_data):
        """
        从传入历史数据 history_data 中选取价格不全为0的最大历史日期 history_date
        """

        history_date_list = history_data['date'].unique().tolist()   # 历史数据日期列表
        history_date_list = sorted(history_date_list, reverse=True)  # 历史日期倒序排列

        history_date = None     # 初始化
        for date in history_date_list:
            if history_data[history_data['date'] == date]['price'].sum() == 0:  # 若当前历史日期价格全为0，则不选取当天为训练数据(NaN值求和为0)
                continue
            elif history_data[history_data['date'] == date]['price'].nunique() == 1:    # 若当前历史日期价格只有1个取值，则不选取当天为训练数据(NaN值求和为0)
                continue
            else:
                history_date = date
                break

        if history_date is None:
            raise ValueError(f"传入历史数据中，没有满足预测需求的历史数据，请检查传入数据！")
        return history_date

    def predict(self):
        """
        预测、输出
        """

        # 1. 数据处理
        source_data = self.all_data.copy()

        # 2. 遍历预测
        y1 = 0
        result = pd.DataFrame(columns=['date_time', 'pred_price', 'type'])

        for date_pred in self.date_list_pred:
            history_data = source_data[source_data['date'] < date_pred].dropna()    # 历史数据
            # history_date = self.get_history_date(history_data)  # 筛选用于预测的历史日期
            #
            # logger.info(f"date_pred: {date_pred}, date_history: {history_date}")

            # 筛选历史日期数据curdata和待预测日期数据curdatab
            curdata = source_data[source_data['date'] < date_pred]
            curdatab = source_data[source_data['date'] == date_pred]

            # 删除空值
            curdata = curdata.dropna()

            # 将预测日的竞价空间缩放到[a, b]范围内
            a = curdata['bidding_space'].min()
            b = curdata['bidding_space'].max()

            mi = curdatab['bidding_space'].min()
            ma = curdatab['bidding_space'].max()

            curdatab['bidding_space'] = a + (curdatab['bidding_space'] - mi) / (ma - mi) * (b - a)

            # 价格乘竞价空间
            curdata['jenkspy'] = curdata['price'] * curdata['bidding_space']
            curdata.reset_index(inplace=True, drop=True)
            breaks = jenkspy.jenks_breaks(curdata['jenkspy'], 3)
            b2 = curdata[(curdata['jenkspy'] > breaks[1]) & (curdata['jenkspy'] <= breaks[2])]
            b3 = curdata[curdata['jenkspy'] > breaks[2]]

            b2jingjiamean = b2['bidding_space'].mean()
            b2rqrpmean = b2['price'].mean()

            k32_2m3all = ((b3['price'] - b2rqrpmean) / (b3['bidding_space'] - b2jingjiamean)).mean()

            # 第三段直线
            p13 = np.poly1d([k32_2m3all, np.mean(b3['price']) - k32_2m3all * np.mean(b3['bidding_space'])])

            # 多项式拟合
            parameter1 = np.polyfit(curdata['bidding_space'], curdata['price'], 1)
            p1 = np.poly1d(parameter1)
            parameter3 = np.polyfit(curdata['bidding_space'], curdata['price'], 3)
            p3 = np.poly1d(parameter3)

            todayuse = 1
            if parameter3[0] > 0:
                delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
                if delta < 0:
                    todayuse = 3
                else:
                    x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                    x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                    y1 = p3(x1)
                    y2 = p3(x2)
                    if x1 > 0 and y1 < y2 * 1.2:
                        todayuse = 33
            else:
                if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                    todayuse = 13
                else:
                    todayuse = 1

            pre_data = []
            if todayuse == 1:
                pre_data = p1(curdatab['bidding_space'])
            elif todayuse == 13:
                pre_data = np.maximum(np.maximum(p1(curdatab['bidding_space']), self.min_value), p13(curdatab['bidding_space']))
            elif todayuse == 3:
                pre_data = np.maximum(p3(curdatab['bidding_space']), self.min_value)
            elif todayuse == 33:
                pre_data = np.maximum(p3(curdatab['bidding_space']), y1)
                pre_data = np.where((curdatab['bidding_space'].values < x1), p3(curdatab['bidding_space']), pre_data)

            # 修正价格上下限
            pre_data = np.maximum(pre_data, self.min_value)
            pre_data = np.minimum(pre_data, self.max_value)

            result_tmp = pd.DataFrame(columns=['date_time', 'pred_price', 'type'])
            result_tmp['date_time'] = curdatab['date_time']
            result_tmp['pred_price'] = list(pre_data)
            result_tmp['type'] = [todayuse] * curdatab.shape[0]
            result = pd.concat([result, result_tmp], axis=0)

        # 3. 整理输出结构
        result['date_time'] = result['date_time'].astype(str)
        result = result.set_index('date_time')
        result_json = result.to_dict()

        # 4. 写入log
        logger.info("------------------ 河北_三段拟合法_价格预测完成！ ---------------------")
        return result_json


if __name__ == '__main__':
    data = pd.read_excel(r"D:\Togeek\work\17_河北\冀南.xlsx")
    price = pd.read_excel(r"D:\Togeek\work\17_河北\冀南.xlsx", sheet_name='price')
    data.columns = ['date_time', '统调负荷预测', '非市场化出力', '新能源预测', '联络线计划', 'price']
    m = PricePredBSF(data.to_dict('list'), price.to_dict('list'),
                     ['2023-04-25', '2023-04-26', '2023-04-27', '2023-04-28', '2023-04-29', '2023-04-30', '2023-05-01',
                      '2023-05-02', '2023-05-03', '2023-05-04', '2023-05-05', '2023-05-06'])
    res = m.predict()
    pd.DataFrame(res).to_excel(r"D:\Togeek\work\17_河北\res2.xlsx")
    print(res)
