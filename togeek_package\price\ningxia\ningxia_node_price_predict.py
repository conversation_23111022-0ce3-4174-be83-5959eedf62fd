import pandas as pd
import numpy as np
from sklearn.ensemble import ExtraTreesRegressor
from datetime import datetime


class ETRNingxiaNodePricePredict:
    def __init__(self, price_dict, weather_dict, grid_dict=None):
        self.price_dict = price_dict
        self.weather_dict = weather_dict
        self.grid_dict = grid_dict  # 接收外部传入的 grid 数据 dict

        self.model = None
        self.feature_model = None

        # 特征原始列定义
        self.raw_weather_features = [
            't_2m', 'rh_2m', 'tmax_2m', 'tmin_2m',
            'u_10m', 'v_10m', 'sp_surface', 'tp_surface',
            'csnow_surface', 'cicep_surface', 'cfrzr_surface', 'crain_surface',
            'dswrf_surface', 'dlwrf_surface', 'uswrf_surface', 'ulwrf_surface', 'tcc_atmo'
        ]
        self.raw_grid_features = [
            '实际负荷', '新能源实际总出力', '省间联络线输电情况',
            '非市场机组实际总出力', '发电实际总出力'
        ]
        self.time_features = ['time_of_day', 'day_of_week', 'day_of_year', 'is_weekend']

        # 后续会更新为实际存在的列
        self.weather_features = []
        self.grid_features = []

    def _dict_to_dataframe(self):
        """将 price_dict、weather_dict 和 grid_dict 合并为一个 DataFrame"""
        # 将价格数据转为 DataFrame
        price_df = pd.DataFrame(self.price_dict)
        price_df['timestamp'] = pd.to_datetime(price_df['timestamp'])

        # 将天气数据转为 DataFrame
        weather_df = pd.DataFrame(self.weather_dict)
        weather_df['timestamp'] = pd.to_datetime(weather_df['timestamp'])

        #将电网边界条件数据转为Dataframe
        grid_df = pd.DataFrame(self.grid_dict)
        grid_df['timestamp'] = pd.to_datetime(grid_df['timestamp'])

        # 合并 price  weather grid
        merged_df = pd.merge(price_df, weather_df, on='timestamp', how='outer')
        merged_df = pd.merge(merged_df, grid_df, on='timestamp', how='left')

        merged_df.sort_values('timestamp', inplace=True)
        self.df = merged_df.reset_index(drop=True)

        # 构造时间特征
        self.df['time_of_day'] = (self.df['timestamp'].dt.hour * 4) + (self.df['timestamp'].dt.minute // 15) + 1
        self.df['day_of_week'] = self.df['timestamp'].dt.dayofweek + 1
        self.df['day_of_year'] = self.df['timestamp'].dt.dayofyear
        self.df['is_weekend'] = self.df['timestamp'].dt.weekday >= 5

        # 更新 weather_features 和 grid_features 为真实存在的列
        self.weather_features = [f for f in self.raw_weather_features if f in self.df.columns]
        self.grid_features = [f for f in self.raw_grid_features if f in self.df.columns]

        # 记录缺失的特征字段
        self.missing_weather_features = [f for f in self.raw_weather_features if f not in self.df.columns]
        self.missing_grid_features = [f for f in self.raw_grid_features if f not in self.df.columns]
        # 输出缺失字段信息
        if self.missing_weather_features:
            print("天气特征中缺失字段:", self.missing_weather_features)
        if self.missing_grid_features:
            print("电网特征中缺失字段:", self.missing_grid_features)

        # 检查是否至少存在一个 weather 或 grid 特征
        if not self.weather_features and not self.grid_features:
            raise ValueError("天气特征和电网特征均为空，请检查输入数据是否包含任何特征字段")

        return self.df

    def train_feature_predictor(self, D):
        """训练模型预测未来 grid 特征"""

        feature_X = self.df[self.time_features + self.weather_features].copy()
        feature_y = self.df[self.grid_features].copy()

        D = pd.to_datetime(D)
        train_mask = self.df['timestamp'] < D
        valid_train_mask = train_mask & feature_X[train_mask].notna().all(axis=1) & feature_y[train_mask].notna().all(axis=1)
        X_train = feature_X[valid_train_mask]
        y_train = feature_y[valid_train_mask]

        self.feature_model = ExtraTreesRegressor(
            n_estimators=150,
            max_depth=8,
            min_samples_split=8,
            min_samples_leaf=3,
            random_state=42
        )
        self.feature_model.fit(X_train, y_train)

    def predict_grid_features(self, X_future):
        """使用训练好的模型预测未来 grid 特征"""
        if self.feature_model is None:
            raise ValueError("请先调用 train_feature_predictor()")

        pred_features = self.feature_model.predict(X_future)
        pred_df = pd.DataFrame(pred_features, columns=self.grid_features)
        return pred_df

    def preprocess_data(self, D, test_days=15):
        """构造特征、划分训练集与测试集：D开始共test_days天为测试集"""

        # 目标变量
        y = self.df[['dayahead_price', 'realtime_price']].copy()

        # 可用特征（包括天气、时间）,用于grid预测
        X_available = self.df[self.time_features + self.weather_features].copy()
        #全体特征，用于价格预测
        X_all = self.df[self.time_features + self.weather_features + self.grid_features].copy()

        # 划分训练集和测试集
        D = pd.to_datetime(D)
        test_end = D + pd.Timedelta(days=test_days)

        train_mask = self.df['timestamp'] < D
        valid_train_mask = train_mask & X_all[train_mask].notna().all(axis=1) & y[train_mask].notna().all(axis=1)
        test_mask = (self.df['timestamp'] >= D) & (self.df['timestamp'] < test_end)

        X_train = X_all[valid_train_mask]
        y_train = y[valid_train_mask]
        X_test_for_grid = X_available[test_mask]

        # 使用模型预测测试集上的 grid 特征
        predicted_features = self.predict_grid_features(X_test_for_grid)

        # 将预测的 grid 特征拼接到 X_test_for_grid 上
        predicted_features = predicted_features.set_index(X_test_for_grid.index)
        X_test_with_grid = pd.concat([X_test_for_grid, predicted_features], axis=1)

        return X_train, X_test_with_grid, y_train

    def train_model(self, X_train, y_train):
        """训练 ExtraTreesRegressor 多输出模型"""
        self.model = ExtraTreesRegressor(
            n_estimators=200,
            max_depth=10,
            min_samples_split=10,
            min_samples_leaf=4,
            random_state=42
        )
        self.model.fit(X_train, y_train)

    def predict(self, X_test):
        """将预测结果转为 {'列名': [值列表]} 的扁平化格式字典"""
        preds = self.model.predict(X_test)
        timestamps = self.df.loc[X_test.index, 'timestamp']

        result_dict = {
            'timestamp': [t.strftime('%Y-%m-%d %H:%M') for t in timestamps],
            'dayahead_price': [round(p[0], 2) for p in preds],
            'realtime_price': [round(p[1], 2) for p in preds]
        }
        return result_dict

    def run(self, D, test_days=15):
        """主流程：加载 -> 训练预测特征 -> 预处理 -> 训练 -> 预测 -> 输出"""
        self._dict_to_dataframe()
        self.train_feature_predictor(D)
        X_train, X_test, y_train = self.preprocess_data(D=D, test_days=test_days)
        self.train_model(X_train, y_train)
        result_dict = self.predict(X_test)
        return result_dict


if __name__ == '__main__':
    import json

    #-------------准备输入数据-----------

    # 读取价格数据，并转换为dict
    price_path = 'node_no5.xlsx'
    price_df = pd.read_excel(price_path, engine='openpyxl')
    price_df['timestamp'] = pd.to_datetime(price_df['timestamp'].astype(str))

    price_dict = {
        'timestamp': price_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M').tolist(),
        'dayahead_price': price_df['dayahead_price'].tolist(),
        'realtime_price': price_df['realtime_price'].tolist()
    }

    # 读取天气数据，并转换为dict
    weather_df = pd.read_csv('weather_ningxia.csv')
    weather_df['timestamp'] = pd.to_datetime(weather_df['t_datetime_cst'].astype(str))

    weather_dict = {
        'timestamp': weather_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M').tolist()
    }
    for col in weather_df.columns:
        if col != 't_datetime_cst' and col != 'timestamp':
            weather_dict[col] = weather_df[col].fillna(0).tolist()

    # -------------读取 grid 数据-------------
    grid_path = 'grid_ningxia.xlsx'
    grid_df = pd.read_excel(grid_path)
    grid_df['timestamp'] = pd.to_datetime(grid_df['timestamp'])

    grid_dict = {
        'timestamp': grid_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M').tolist()
    }
    for col in grid_df.columns:
        if col != 'timestamp':
            grid_dict[col] = grid_df[col].fillna(0).tolist()

    #===============执行模型==================
    # D = '2025-04-16'
    D = '2025-05-01'
    test_days = 15

    predictor = ETRNingxiaNodePricePredict(
        price_dict=price_dict,
        weather_dict=weather_dict,
        grid_dict=grid_dict
    )

    result_dict = predictor.run(D=D, test_days=test_days)
    #================================

    # 打印部分预测结果
    print("\n🔮 预测结果示例：")
    for i in range(min(5, len(result_dict['timestamp']))):
        print(f"{result_dict['timestamp'][i]} → 日前价: {result_dict['dayahead_price'][i]}, 实时价: {result_dict['realtime_price'][i]}")

    #----------保存为JSON文件----------

    #  保存输入数据和参数到 input_output_data.json
    input_json = {
        "D": D,
        "test_days": test_days,
        "price_data": price_dict,
        "weather_data": weather_dict,
        "grid_data": grid_dict
    }

    with open('input_data.json', 'w', encoding='utf-8') as f:
        json.dump(input_json, f, ensure_ascii=False, indent=4)

    print("\n💾 已将输入数据保存为 input_data.json")

    #  保存预测结果到 prediction_output.json
    with open('prediction_output.json', 'w', encoding='utf-8') as f:
        json.dump(result_dict, f, ensure_ascii=False, indent=4)

    print("💾 已将预测结果单独保存为 prediction_output.json")