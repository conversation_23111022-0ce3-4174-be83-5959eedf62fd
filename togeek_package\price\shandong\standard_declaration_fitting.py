# -*- coding: utf-8 -*-
"""
Author: Laney
Datetime: 2023/1/12/012 13:11
Info:
"""

import pandas as pd
import numpy as np
import logging
import warnings
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler


logger = logging.getLogger()
warnings.filterwarnings('ignore')


class StandardDeclarationFitting:
    def __init__(self, running_generator, standard_declaration, load, price, pred_date,
                 sim_dates=14, points=24, min_price=0, max_price=1500):
        freqs = {96: '15min', 48: '30min', 24: '1h'}
        logger.info("----------------------标准报价方案申报分布拟合电价开始--------------------------")
        self.msg = ""
        self.flag = True
        self.pred_date = pred_date  # 运行日
        self.sim_dates = int(sim_dates)
        self.points = int(points)
        self.freq = freqs[self.points]
        self.min_price = min_price
        self.max_price = max_price
        self.jingjia_date = str(datetime.strptime(self.pred_date, "%Y-%m-%d") - timedelta(1))[:10]  # 竞价日
        self.running_generator = self._prepare_generator(running_generator)
        self.standard_declaration = self._prepare_declaration(standard_declaration)
        self.load = self._prepare_load(load)
        self.price = self._prepare_price(price)

    def _prepare_generator(self, data):
        self.msg += f"--开始准备【全网运行机组】数据,"
        data = pd.DataFrame(data)
        data.dropna(inplace=True)
        data['日期'] = data['日期'].astype(str)
        # 判断【集团、电厂、机组、装机容量、运行状态】是否在字段名称中
        a = ['机组名称', '装机容量', '运行状态']  # '集团', '电厂',
        b = data.columns.tolist()
        r = set(a) <= set(b)
        if r:
            data = data[(data['运行状态'] == '运行') | (data['运行状态'] == 1)]
            data.sort_values(['日期', '装机容量'], inplace=True)
            data = data.reset_index(drop=True)
            self.msg += f"准备完成\n"
        else:
            self.flag *= False
            self.msg += f'【全网运行机组】数据的字段为{b}，需求的字段为{a}，不满足需求！\n'
        return data

    def _prepare_declaration(self, data):
        self.msg += f"--开始准备【标准申报方案】数据,"
        try:
            data = pd.DataFrame(data)
            data.dropna(inplace=True)
            data.sort_values(['日期', '机组容量'], inplace=True)
            cols = data.columns.tolist()
            cols.remove('日期')
            cols.remove('机组容量')
            data1 = data[cols]
            load_rate = list(map(float, cols))
            data1.columns = load_rate
            data1['日期'] = data['日期']
            data1['机组容量'] = data['机组容量']
            # for col in cols:
            #     if isinstance(col, (float, int)):
            #     load_rate.append(col)
            # data.columns = ['日期', '机组容量'] + load_rate
            load_rate.sort()
            data = data1[['日期', '机组容量'] + load_rate]
            data['日期'] = data['日期'].astype(str)
            self.msg += f"准备完成\n"
        except Exception as e:
            self.flag *= False
            self.msg += f"运行报错{e}, 【标准申报方案】数据异常，请检查！\n"
        return data

    def _prepare_load(self, df):
        self.msg += f"--开始准备【全网负荷预测】数据,"
        data = pd.DataFrame(df)
        data.dropna(inplace=True)
        b = data.columns.tolist()  # 负荷数据的所有字段
        flag = True
        a = ['date_time', '竞价空间']
        # 判断字段是否包含【'date_time', '竞价空间'】，如果包含，则直接使用传入的竞价空间字段，否则计算竞价空间字段
        if set(a) <= set(b):
            flag *= True
        else:
            # 判断字段是否包括【'date_time', '直调负荷', '地方电厂发电总加', '联络线受电负荷', '风电负荷', '光伏负荷', '核电总加', '自备机组总加'】
            a = ['date_time', '直调负荷', '联络线受电负荷', '风电负荷', '光伏负荷', '核电总加', '自备机组总加']  # , '地方电厂发电总加'
            r = set(a) <= set(b)
            if r:
                data['竞价空间'] = data['直调负荷'] - data['联络线受电负荷'] - data['风电负荷'] - data['光伏负荷'] - data['核电总加'] - data[
                    '自备机组总加']
                flag *= True
            else:
                flag *= False

        if flag:
            data.sort_values('date_time', inplace=True)
            data['date'] = data['date_time'].astype(str).map(lambda x: x.split(' ')[0])
            data['minute'] = data['date_time'].astype(str).map(lambda x: x.split(' ')[1][3:5])
            if self.points == 24:
                data = data.loc[data['minute'] == '00']
            elif self.points == 48:
                data = data.loc[(data['minute'] == '00') & (data['minute'] == '30')]
            else:
                data = data.reset_index(drop=True)
            self.msg += f"准备完成\n"
        else:
            self.flag *= False
            self.msg += f'负荷数据中的字段为{b}，需求的字段为{a}，不满足需求！\n'
        return data

    def _prepare_dist_table(self, dist_date):
        self.msg += f"--开始统计{dist_date}的【标准报价方案的申报分布】数据,"
        try:
            standard_declaration = self.standard_declaration[self.standard_declaration['日期'] == dist_date]
            del standard_declaration['日期']
            capa_level = standard_declaration['机组容量'].tolist()
            load_rate = standard_declaration.columns.tolist()
            load_rate.remove('机组容量')
            logger.info(f"标准报价方案中的机组容量为{capa_level}，负荷率为{load_rate}")
            running_capa = self.running_generator[self.running_generator['日期'] == dist_date]

            result = pd.DataFrame()
            for i, capa in enumerate(capa_level):
                tmp = pd.DataFrame()
                if i == 0:
                    capa_data = running_capa[running_capa['装机容量'] < capa]  # 第一等级为小于300mw的机组
                elif i < len(capa_level) - 1:
                    capa_data = running_capa[(running_capa['装机容量'] >= capa) & (running_capa['装机容量'] < capa_level[i + 1])]
                else:
                    capa_data = running_capa[running_capa['装机容量'] >= capa]  # 最后一级为大于等于1000MW的机组
                capa_ = np.array(capa_data['装机容量'].tolist()).reshape(-1, 1)
                dec_power = capa_ * np.array(load_rate)
                dec_power1 = np.insert(np.diff(dec_power), 0, dec_power[:, 0], 1)
                dec_price = standard_declaration.loc[standard_declaration['机组容量'] == capa, load_rate].values[0].tolist()
                tmp['price'] = np.array(dec_price * capa_data.shape[0]).flatten()
                tmp['power'] = dec_power1.flatten()
                result = pd.concat([result, tmp], axis=0)
            result.sort_values('price', inplace=True)

            # 计算申报分布表
            declare = result.groupby('price').sum().reset_index()
            declare['cumsum_power'] = declare['power'].cumsum()
            declare_price = np.array(declare['price'])
            declare_power = np.array(declare['cumsum_power'])

            self.msg += f"统计结束\n"
        except Exception as e:
            self.flag *= False
            declare_price = np.array([])
            declare_power = np.array([])
            self.msg += f"运行报错{e}, 请检查出错原因！\n"
        return declare_price, declare_power

    def _prepare_price(self, data):
        self.msg += f"--开始准备【日前统一结算点电价】数据,"
        price = pd.DataFrame(data)
        try:
            price['date'] = price['date_time'].astype(str).map(lambda x: x.split(' ')[0])
        except Exception as e:
            self.flag *= False
            self.msg += f"运行报错{e}, 【日前统一结算点电价】数据异常，请检查！\n"
        self.msg += f"准备完成\n"
        return price

    def select_similar_date(self, com_date):
        """
        计算与com_date竞价空间相似的日期
        :param com_date: 比对日期
        :return: 相似日期
        """
        load = self.load.copy()
        # 选取历史数据
        self.com_start_d = str(datetime.strptime(com_date, "%Y-%m-%d") - timedelta(self.sim_dates))[:10]
        pred_load = load.loc[load['date'] == com_date, ['date_time', '竞价空间']]
        hist_load = load.loc[(load['date'] >= self.com_start_d) & (load['date'] < com_date), ['date_time', '竞价空间']]
        hist_load['date_time'] = pd.to_datetime(hist_load['date_time'])
        hist_load.set_index('date_time', drop=True, inplace=True)
        hist_load = hist_load.resample('D').agg([min, max]).reset_index()
        hist_load.columns = ['date_time', '竞价空间_min', '竞价空间_max']

        pred_bd_max = pred_load['竞价空间'].values.max()  # 预测日的竞价空间最大值
        pred_bd_min = pred_load['竞价空间'].values.min()  # 预测日的竞价空间最小值

        hist_load["max_diff"] = abs(hist_load['竞价空间_max'] - pred_bd_max)
        hist_load["min_diff"] = abs(hist_load['竞价空间_min'] - pred_bd_min)

        # for col in ['max_diff', 'min_diff']:  # 标准化特征
        #     hist_load[col] = MinMaxScaler().fit_transform(np.array(hist_load[col]).reshape(-1, 1))

        hist_load['total_diff'] = hist_load["max_diff"] + hist_load["min_diff"]
        hist_load.sort_values('total_diff', inplace=True)
        hist_load['date_time'] = hist_load['date_time'].astype(str)
        similar_date = str(hist_load['date_time'].values[0])

        i = 1
        date_lst = self.standard_declaration['日期'].astype(str).values
        date_lst2 = self.running_generator['日期'].astype(str).values
        while (similar_date not in date_lst) or (similar_date not in date_lst2):
            similar_date = str(hist_load['date_time'].values[i])
            i += 1
        return similar_date

    def price_fitting(self, to_json=False):
        jingjia0 = np.array(self.load.loc[self.load['date'] == self.jingjia_date, '竞价空间'])
        jingjia = np.array(self.load.loc[self.load['date'] == self.pred_date, '竞价空间'])

        # 准备竞价日的标准报价方案的申报分布表
        # 判断竞价日的标准报价方案是否公布,如果已公布,则使用竞价日的报价方案,如果未公布,则使用相似日的报价方案
        # if self.jingjia_date in self.standard_declaration['日期'].values:
        #     sim_date0 = self.jingjia_date
        # else:
        # sim_date0 = self.select_similar_date(self.jingjia_date)
        sim_date0 = self.jingjia_date
        declare_price0, declare_power0 = self._prepare_dist_table(sim_date0)

        # 计算竞价日前一日(D-2)的价格,拟合定价出力,从而计算出非定价出力
        price0 = self.price.loc[self.price['date'] == self.jingjia_date, 'price'].tolist()
        make_power0 = np.array([])
        for i, p in enumerate(price0):
            idx = len(declare_price0) - 1

            if len(np.nonzero((declare_price0 - p) >= 0)[0]) > 0:
                idx = np.nonzero((declare_price0 - p) >= 0)[0][0]
            make_power0 = np.append(make_power0,
                                    round((declare_power0[idx] - declare_power0[idx - 1]) /
                                          (declare_price0[idx] - declare_price0[idx - 1]) *
                                          (p - declare_price0[idx]) + declare_power0[idx], 0))
        no_make_power = np.array(jingjia0) - make_power0

        # 准备运行日的标准报价方案的申报分布表
        sim_date = self.select_similar_date(self.pred_date)
        declare_price, declare_power = self._prepare_dist_table(sim_date)

        # 通过竞价日的竞价空间(D-1)在申报分布表上拟合价格
        make_power = np.array(jingjia) - no_make_power
        result = pd.DataFrame(columns=['date_time', 'price'])
        result['date_time'] = pd.date_range(start=self.pred_date, periods=self.points, freq=self.freq)
        result['price'] = np.nan
        if self.flag:
            pred_price = np.array([])
            for i, c in enumerate(make_power):
                idx = len(declare_power) - 1

                if len(np.nonzero((declare_power - c) >= 0)[0]) > 0:
                    idx = np.nonzero((declare_power - c) >= 0)[0][0]

                value = round((declare_price[idx] - declare_price[idx-1]) /
                              (declare_power[idx] - declare_power[idx-1]) *
                              (c - declare_power[idx]) + declare_price[idx], 2)
                pred_price = np.append(pred_price, max(min(self.max_price, value), self.min_price))
            result['price'] = pred_price
        logger.info(self.msg)
        # print(self.msg)
        if to_json:
            result['date_time'] = result['date_time'].astype(str)
            result = result.to_dict('list')
            result["message"] = self.msg
        logger.info("----------------------标准报价方案申报分布拟合电价结束--------------------------")
        return result


if __name__ == '__main__':
    declaration = pd.read_excel(r"D:\ToGeek\work\14 山东\山东申报.xlsx", sheet_name='标准申报')
    load = pd.read_excel(r"D:\ToGeek\work\14 山东\山东申报.xlsx", sheet_name='竞价日负荷')
    all_capa = pd.read_excel(r"D:\ToGeek\work\14 山东\山东申报.xlsx", sheet_name='全省运行')
    price = pd.read_excel(r"D:\ToGeek\work\14 山东\山东申报.xlsx", sheet_name='电价')
    print(all_capa.head())

    pf = StandardDeclarationFitting(all_capa, declaration, load, price, '2022-09-16')
    data = pf.price_fitting()
    print(data)