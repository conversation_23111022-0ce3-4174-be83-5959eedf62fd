import pandas as pd
import numpy as np
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier




class PriceRealD234ETR():
    '''
    价格预测的调用，执行加载数据、训练、预测
    '''

    def __init__(self,
                 df_train,
                 df_test,
                 use_meta=True,
                 ):

        self.df_train = df_train
        self.df_test = df_test
        self.use_meta = use_meta

        # 加载grid数据、日期特征数据
        self.df_train = self.df_train.set_index('dateTime')
        self.df_test = self.df_test.set_index('dateTime')
        self.df_train.index = pd.to_datetime(self.df_train.index)
        self.df_test.index = pd.to_datetime(self.df_test.index)
        self.df_train = self.df_train.sort_index()
        self.df_test = self.df_test.sort_index()

        # 此部分特征需要自己预测D日之后的数值
        self.features_grid_part1 = [
            '系统实际发电负荷',
            '非市场化电源实际出力',
            '非市场化电源预测出力',

            '光伏实际负荷',
            '风电实际负荷',
            '水电含抽蓄实际负荷',

            # '外送电预测负荷',#无法预测
        ]

        # 此部分特征，交易中心发布了D日之后的数值, train集中特征名称
        self.features_grid_part2 = [
            '系统实际负荷',

            # '光伏实际负荷',
            # '风电实际负荷',
            # '水电含抽蓄实际负荷',
        ]

        # 交易中心发布的D日之后的预测值，test集中特征名称,与features_grid_part2一一对应
        self.features_grid_fcst_meta = [
            '系统预测负荷',
        ]

        # 需要根据已有特征进行构造
        self.features_grid_formula = [
            '实时竞价空间',
            '实时竞价空间_低于阈值',
            '实时竞价空间_高于阈值',
            # '火电竞价空间实际',
        ]

        # 时间类特征
        self.features_time = [
            'holiday',
            'hour',
            'hour_cos',
            'dayofweek',
            'dayofweek_cos',
            'dayofweek_sin',
            'month',
            'dayofyear',
        ]

        # 天气类特征
        self.features_weather = [
            't_80m', 'q_80m', 'pres_80m', 'u_80m', 'v_80m',
            't_surface', 'tp_surface', 'dswrf_surface',
            'air_density', 'total_wind_speed',

            # 'u_10m', 'v_10m', 'SUNSD_surface',
            # 'vis_surface',    'wspd_surface',
            # 'csnow_surface',    'cicep_surface',    'cfrzr_surface',    'crain_surface',
            # 'uflx_surface',    'vflx_surface',    'uswrf_surface',   'dlwrf_surface',  'ulwrf_surface',
            # 'albedo_surface',
        ]

        # 自己预测电网边界，不使用交易中心发布值
        if not self.use_meta:
            # 删除df_test中可能含有的features_grid_fcst_meta特征
            for col in self.features_grid_fcst_meta:
                if col in self.df_test.columns:
                    self.df_test = self.df_test.drop(columns=[col])
            self.features_grid_part1 = self.features_grid_part1 + self.features_grid_part2
            self.features_grid_part2 = []
            self.features_grid_fcst_meta = []

        if self.use_meta:
            # 使用交易中心发布市场边界值，检查是否有空列
            missing_columns = []
            for col in self.features_grid_fcst_meta:
                if col not in self.df_test.columns:
                    missing_columns.append(col)
                elif self.df_test[col].isna().all() or self.df_test[col].empty:
                    missing_columns.append(col)

            if missing_columns:
                raise ValueError(f"以下市场边界特征列在“未来数据”中缺失或为空: {missing_columns}")





    def pred(self,) -> pd.DataFrame:
        """
        :return:
        """


        #增加时间特征、空气密度、风速特征, 分别应用于train、test
        self.df_train = self.generate_time_and_weather_features(self.df_train)
        self.df_test = self.generate_time_and_weather_features(self.df_test)

        # 提取天气+时间特征
        df_weather_train = self.df_train[self.features_weather + self.features_time]
        df_weather_test = self.df_test[self.features_weather + self.features_time]

        # 提取市场边界特征part1
        valid_columns = list(set(self.df_train.columns) & set(self.features_grid_part1))
        df_grid_train_part1 = self.df_train[valid_columns]

        #----------------对D日之后缺失的市场边界特征进行预测-------------------
        # 使用df_weather_train，df_grid_train_part1训练，使用df_weather_test预测
        # 初始化模型
        grid_model = ExtraTreesRegressor(
            n_estimators=300,
            max_depth=8,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1,
            verbose=0,
        )

        # 拟合模型
        grid_model.fit(df_weather_train.values, df_grid_train_part1.values)

        # 预测未来值
        y_pred = grid_model.predict(df_weather_test.values)

        # 构造预测结果 DataFrame
        df_grid_test_part1 = pd.DataFrame(
            y_pred,
            columns=df_grid_train_part1.columns,
            index=df_weather_test.index
        )

        if '光伏实际负荷' in self.features_grid_part1:
            df_grid_test_part1['光伏实际负荷'] = df_grid_test_part1['光伏实际负荷'].apply(lambda x: 0 if x < 300 else x)

        #------------对D日之后grid预测数据进行整合------------
        # 处理电网发布的边界数据，构造列名映射字典,
        column_mapping = dict(zip(self.features_grid_fcst_meta, self.features_grid_part2))
        # 重命名 df 中的列
        self.df_test = self.df_test.rename(columns=column_mapping)
        #添加自己预测的grid
        self.df_test = pd.concat([self.df_test, df_grid_test_part1], axis=1)

        #------------增添构造的grid特征--------------
        self.df_train = self.generate_grid_formula_features(self.df_train)
        self.df_test = self.generate_grid_formula_features(self.df_test)

        #--------------提取训练集、测试集-------------
        X_train = self.df_train[self.features_grid_part1+
                                self.features_grid_part2+
                                self.features_grid_formula+
                                self.features_weather+
                                self.features_time]

        y_train = self.df_train['实时节点出清电价']

        X_test = self.df_test[self.features_grid_part1+
                                self.features_grid_part2+
                                self.features_grid_formula+
                                self.features_weather+
                                self.features_time]

        X_train.to_csv('X_train.csv', encoding='utf-8-sig')
        X_test.to_csv('X_test.csv', encoding='utf-8-sig')



        # -----------预测电价（DAP & RTP）三分类+回归--------------

        # 获取高电价阈值分位数%
        # high_price_threshold_dap = np.percentile(y_train_dap, 100)
        # high_price_threshold_rtp = np.percentile(y_train_rtp, 93)

        # 构建三分类标签
        y_clf = self.create_three_class_labels(y_train,
                                                zero_threshold=1,
                                                high_threshold=700)

        clf_model = ExtraTreesClassifier(
            n_estimators=300,
            max_depth=8,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1,
            verbose=0
        )

        # 训练三分类模型
        clf_model.fit(X_train.values, y_clf.values)

        # 构建正常电价训练数据
        normal_mask = (y_clf == 1)

        X_train_normal = X_train.loc[normal_mask]
        y_train_normal = y_train.loc[normal_mask]

        # 回归模型：仅预测正常电价
        reg_model = ExtraTreesRegressor(
            n_estimators=300,
            max_depth=8,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1,
            verbose=0
        )

        if not X_train_normal.empty:
            reg_model.fit(X_train_normal.values, y_train_normal.values)
        else:
            print("没有正常电价数据，回归模型无法训练")

        # 分类预测
        y_clf_pred = clf_model.predict(X_test.values)

        # 回归预测（只对类别为1的样本）
        y_reg_pred = reg_model.predict(X_test.values)

        # 组合最终结果
        # y_dap_pred = np.where(y_clf_dap_pred == 0, 0, y_reg_dap_pred)#日前价格不判断上限
        # y_dap_pred = [x if x >= 150 else 0 for x in y_dap_pred]  # 低于150设为0
        y_pred = np.where(y_clf_pred == 0, 0,
                              np.where(y_clf_pred == 2, 1000, y_reg_pred))
        y_pred = [x if x >= 200 else 0 for x in y_pred]  # 低于150设为0

        result_df = pd.DataFrame({
            'value': y_pred,
        }, index=X_test.index)

        # # 筛选后3天的数据（D+2 到 D+4）
        # result_df.index = pd.to_datetime(result_df.index)
        # D = pd.to_datetime(D)
        # result_df = result_df[result_df.index >= D + pd.Timedelta(days=2)]

        return result_df

    # 构建三分类标签函数
    def create_three_class_labels(self, prices, zero_threshold=0, high_threshold=100):
        labels = pd.Series(1, index=prices.index)
        labels[prices == zero_threshold] = 0
        labels[prices > high_threshold] = 2
        return labels

    def  generate_time_and_weather_features(self, df: pd.DataFrame,) -> pd.DataFrame:
        """
        :param df: 输入的DataFrame
        :return: 处理后的DataFrame
        """

        if 'hour' in self.features_time:
            df['hour'] = pd.to_datetime(df.index).hour
        if 'hour_sin' in self.features_time:
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        if 'hour_cos' in self.features_time:
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        if 'dayofweek' in self.features_time:
            df['dayofweek'] = pd.to_datetime(df.index).dayofweek
        if 'dayofweek_sin' in self.features_time:
            df['dayofweek_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        if 'dayofweek_cos' in self.features_time:
            df['dayofweek_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        if 'month' in self.features_time:
            df["month"] = pd.to_datetime(df.index).month
        if 'dayofyear' in self.features_time:
            df['dayofyear'] = pd.to_datetime(df.index).dayofyear

        if 'air_density' in self.features_weather:
            # 增加空气密度特征
            df['air_density'] = self.calculate_air_density(temp_c=df['t_80m'],
                                                           pressure_pa=df['pres_80m'],
                                                           relative_humidity=df['q_80m'])
        if 'total_wind_speed' in self.features_weather:
            # 增加总风速特征
            df['total_wind_speed'] = np.sqrt(df['u_80m'] ** 2 + df['v_80m'] ** 2)

        return df

    def generate_grid_formula_features(self, df: pd.DataFrame,) -> pd.DataFrame:
        # 对features_grid_formula特征进行构造
        if '火电竞价空间实际' in self.features_grid_formula and '外送电预测负荷' in df.columns:
            df['火电竞价空间实际'] = df['系统实际负荷'] - df['风电实际负荷'] - df[
                '光伏实际负荷'] - df['非市场化电源实际出力'] - df['水电含抽蓄实际负荷'] + df['外送电预测负荷']

        if '实时竞价空间' in self.features_grid_formula:
            df['实时竞价空间'] = df['系统实际负荷'] - df['风电实际负荷'] - df[
                '光伏实际负荷'] - df['非市场化电源实际出力'] - df['水电含抽蓄实际负荷']
            # 1. 低阈值判断：是否小于
            threshold_low = 7000
            df['实时竞价空间_低于阈值'] = (df['实时竞价空间'] < threshold_low).astype(int)
            # 2. 高阈值判断：是否大于
            threshold_high = 22500
            df['实时竞价空间_高于阈值'] = (df['实时竞价空间'] > threshold_high).astype(int)

        if '日前竞价空间' in self.features_grid_formula:
            df['日前竞价空间'] = df['系统预测负荷'] - df['风电预测负荷'] - df[
                '光伏预测负荷'] - df['非市场化电源预测出力'] - df['水电含抽蓄预测负荷']
            # 低阈值判断：是否小于
            threshold_low = 7000
            df['日前竞价空间_低于阈值'] = (df['日前竞价空间'] < threshold_low).astype(int)

        return df


    #计算空气密度
    def calculate_air_density(self, temp_c, pressure_pa, relative_humidity):
        # 转换到正确单位
        temp_k = temp_c + 273.15  # 温度转为开尔文
        rh = relative_humidity #/ 100  # 相对湿度转为小数
        # 计算饱和水汽压力
        es = 6.112 * np.exp((17.67 * (temp_c)) / (temp_c + 243.5))
        # 计算实际水汽压力
        e = rh * es
        # 干燥空气分压
        pd = pressure_pa - e
        # 虚拟温度
        tv = temp_k * (1 + 0.61 * e / pressure_pa)
        # 空气密度
        Rd = 287.058  # 干空气气体常数 J/(kg·K)
        air_density = pd / (Rd * tv)
        return air_density







if __name__ == '__main__':

    df_train = pd.read_csv('df_train.csv')
    df_test = pd.read_csv('df_test.csv')
    # df_test = pd.read_csv('df_test_usemetafalse.csv')


    # D = "2025-07-21"
    result_df = PriceRealD234ETR(df_train=df_train, df_test=df_test, use_meta=True).pred()
    print(result_df)

    import winsound
    winsound.Beep(1000, 1000)


#------------计算准确率--------------
    # 交易所需的准确率定义
    def accuracy_for_trade(y_true, y_pred, epsilon=0.01):
        # 定义业务意义上的“接近零”的阈值epsilon,当预测值、真实值都≤epsilon时，准确率为1，避免分母为0
        y_true = np.asarray(y_true)
        y_pred = np.asarray(y_pred)

        # 初始化准确率数组
        accuracies = np.zeros_like(y_true, dtype=float)

        # 判断哪些样本是“真实值和预测值都小于等于 0.01” → 视为准确预测
        near_zero_mask = (np.abs(y_true) <= epsilon) & (np.abs(y_pred) <= epsilon)
        accuracies[near_zero_mask] = 1.0

        # 处理剩下的样本（非 near-zero），计算 MAPE
        non_zero_mask = ~near_zero_mask
        if np.any(non_zero_mask):
            y_true_nonzero = y_true[non_zero_mask]
            y_pred_nonzero = y_pred[non_zero_mask]

            # 计算 MAPE：这里只加一个极小量防止除零，不干扰业务逻辑
            mape_values = np.abs((y_pred_nonzero - y_true_nonzero) / np.clip(y_true_nonzero, a_min=1e-8, a_max=None))
            accuracies[non_zero_mask] = 1 - mape_values

            # 限制最小准确率为 0（MAPE > 1 的情况）
            accuracies[non_zero_mask] = np.clip(accuracies[non_zero_mask], 0, 1)

        # 返回平均准确率
        return np.mean(accuracies)

    # 1. 读取历史真实数据
    true_df = pd.read_csv('./df_grid_test_raw.csv')

    # 2. 转换为 datetime 类型，并设置为索引
    true_df['dateTime'] = pd.to_datetime(true_df['dateTime'])
    true_df.set_index('dateTime', inplace=True)

    # 3. 在真实数据中筛选出与预测时间一致的部分
    matched_true = true_df.loc[result_df.index]

    # 4. 合并预测值与真实值
    comparison_df = pd.DataFrame({
        'Pred_RTP': result_df['value'],  # 预测实时电价
        'True_RTP': matched_true['实时节点出清电价']  # 真实实时电价
    }).dropna()  # 去掉缺失值

    # 从 comparison_df 中提取这三天的预测值和真实值
    y_true = comparison_df['True_RTP'].values
    y_pred = comparison_df['Pred_RTP'].values

    # 计算这三天的整体准确率
    acc = accuracy_for_trade(y_true, y_pred)

    print(f"D+2 ~ D+4 三天整体准确率：{acc:.4f}")


    # --------------画图-------------------
    import matplotlib.pyplot as plt
    # 设置中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建一个图
    plt.figure(figsize=(12, 6))
    plt.plot(comparison_df.index, comparison_df['Pred_RTP'], label='预测实时电价', color='orange', linestyle='--')
    plt.plot(comparison_df.index, comparison_df['True_RTP'], label='真实实时电价', color='blue')

    plt.title('实时节点出清电价预测 vs 真实值')
    plt.ylabel('价格')
    plt.xlabel('时间')
    plt.legend()
    plt.grid(True)

    # 自动优化日期格式显示
    plt.gcf().autofmt_xdate()
    plt.tight_layout()
    plt.show()


