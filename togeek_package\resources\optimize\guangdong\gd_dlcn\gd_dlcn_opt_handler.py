#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2025/3/24 14:12 
@Info    ：

'''
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.guangdong.gd_dlcn import DlcnOptimization

class DlcnOptimizationHandlerTS(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        input_price_data = params.pop('ahead_price')
        time_of_use = params.pop('time_of_use')
        profit = params.pop('profit')
        init_soc = params.pop('init_soc')
        capacity = params.pop('capacity')
        power = params.pop('power')
        min_soc = params.pop('min_soc', 0.05)
        max_soc = params.pop('max_soc', 0.95)
        model = DlcnOptimization(input_price_data=input_price_data, time_of_use=time_of_use, profit=profit, init_soc=init_soc, capacity=capacity,
                                                 power=power, min_soc=min_soc, max_soc=max_soc)
        result = model.get_dlcn_results()
        self.write(result)
