"""
Author: Laney
Datetime: 2022/12/5/005 18:03
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.nationwide.price_declaration_pre import DeclarationCorrect


class DeclarationCorrectValueHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        date_list = params.pop('date_list')
        is_sim = params.pop('is_sim', 0)
        sim_method = params.pop('sim_method', 1)
        sim_cal_col = params.pop('sim_cal_col', '竞价空间')
        sim_type = params.pop('sim_type', 1)
        sim_period = params.pop('sim_period', 7)
        correct_col = params.pop('correct_col', '负荷率')
        correct_period = params.pop('correct_period', 7)
        correct_quantile = params.pop('correct_quantile', (0.75, 0.75))
        min_price = params.get('min_price', 0)
        max_price = params.get('max_price', 1500)
        pred = DeclarationCorrect(source_data=source_data, date_list=date_list, is_sim=is_sim, sim_method=sim_method,
                                  sim_cal_col=sim_cal_col, sim_type=sim_type, sim_period=sim_period,
                                  correct_col=correct_col, correct_period=correct_period, correct_quantile=correct_quantile,
                                  min_price=min_price, max_price=max_price)
        self.write(pred.pred_price(to_json=True))
