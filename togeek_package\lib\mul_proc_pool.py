'''
Author: your name
Date: 2021-11-01 14:11:24
LastEditTime: 2021-11-01 16:31:48
LastEditors: your name
Description: In User Settings Edit
FilePath: \togeek\togeek_package\togeek_package\lib\mul_proc_pool.py
'''
# -*- coding: utf-8 -*-

from multiprocessing import cpu_count
from multiprocessing import Pool
from multiprocessing.dummy import Pool as DPool
from tglibs.singleton import Singleton
from togeek_package.lib.api_config import APIConfig


class ProcessPool(metaclass=Singleton):
    def __init__(self):
        cfg = APIConfig()
        n = cfg.pool_number or cpu_count()
        self.pool = Pool(n) if cfg.process_pool else DPool(n)

    def start(self, method, args):
        self.pool.apply_async(method, args=(args,))

    def close(self):
        self.pool.terminate()


__all__ = ['ProcessPool']
