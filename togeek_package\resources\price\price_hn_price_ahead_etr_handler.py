#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2025/6/9
@Info    ：

'''

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.hunan.price_ahead_etr import PriceAheadETR


class PriceAheadETRHandlerHN(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get('data')
        D = params.get('D')
        price_type = params.get('price_type')
        pred = PriceAheadETR(D=D,data=data,price_type=price_type)
        result = pred._pred()
        self.write(result)
