
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.impute import KNNImputer
from togeek_package.price.hunan.lmp_pred_mixin import DataFetcherMixin, DEF_FEATURES, AHEAD_FEATS_LIST, REAL_FEATS_LIST
import logging

logger = logging.getLogger()
# logger.addHandler(logging.StreamHandler())
# logger.setLevel(logging.DEBUG)  

# 必要数据项
REQUIRED_DATA_LIST = [
    "dateTime",
    "日前节点出清电价", 
    "实时节点出清电价",
]

class PriceAheadETR(DataFetcherMixin):
    def __init__(self, D, data, price_type="日前"):
        logger.info(f"----------开始运行：湖南节点{price_type}价格预测-----------")
        self.data=data
        self.D=D
        self.scalar = StandardScaler()
        self.model = self._default_model()
        self.price_type = price_type

    def _standalone_train(self) -> bool:
        return False

    def _default_model(self):
        return ExtraTreesRegressor(n_estimators=100, max_depth=10, random_state=80)

    def _train(self):
        raise NotImplementedError

    def _pred(self,tojson=True) -> pd.DataFrame:
        """
        :param D: 交易日
        :return:
        """
        if self.price_type not in ["日前", "实时"]:
            logger.error(
                f"[ERROR] 节点类型错误，只能为'日前'或'实时'，当前为{self.price_type}")
            raise ValueError(
                f"[ERROR] 价格类型错误，只能为'日前'或'实时'，当前为{self.price_type}")
        if self.price_type == "实时":
            feats_list = REAL_FEATS_LIST
        elif self.price_type == "日前":
            feats_list = AHEAD_FEATS_LIST
        else:
            feats_list = DEF_FEATURES
        train, test = self._data(self.D, self.data)
        # 处理缺失值
        mask = ~train.isnull().any(axis=1)
        train = train[mask]
        # 检查必要数据项是否存在
        missing_data_items = [item for item in REQUIRED_DATA_LIST if item not in train.columns]
        if missing_data_items:
            logger.error(
                f"[ERROR] 缺少以下必要数据项: {missing_data_items}")
            return ValueError(f"[ERROR] 缺少以下必要数据项: {missing_data_items}")

        if train.empty:
            logger.debug(
                f"---------------注意！交易日{D} train 清洗后没有数据，跳过预测---------------")
            return ValueError(f"[ERROR] 交易日{D} train 清洗后没有数据，跳过预测")

        if test.empty:
            logger.debug(
                f"---------------注意！交易日{D} test 没有数据，跳过预测---------------")
            return ValueError(f"[ERROR] 交易日{D} test 没有数据，跳过预测")

        # 检查特征是否存在
        valid_features = [feat for feat in feats_list if feat in train.columns]
        missing_features = set(feats_list) - set(valid_features)

        if missing_features:
            logger.warning(f"[WARNING] 缺失以下特征列，已自动剔除: {missing_features}")
       

        X_train = train[valid_features]
        logger.info(f"[INFO] 训练数据特征列:{X_train.columns.tolist()}")
        y_train = train[f"{self.price_type}节点出清电价"]
        X_test = test[valid_features]
        # 使用KNN填充缺失值
        knn_imputer = KNNImputer(n_neighbors=5)
        X_train = knn_imputer.fit_transform(X_train)
        X_test = knn_imputer.transform(X_test)

        X_train_scaled = self.scalar.fit_transform(X_train)
        self.model.fit(X_train_scaled, y_train)

        X_test_scaled = self.scalar.transform(X_test)
        y_pred = self.model.predict(X_test_scaled)
        
        df_pred = pd.DataFrame()
        df_pred["dateTime"]=test["dateTime"] # str
        df_pred["dateTime"]=df_pred["dateTime"].astype(str)
        df_pred["value"]=y_pred
        df_pred.reset_index(inplace=True, drop=True)
        if tojson:
            return df_pred.to_dict(orient='list')
        return df_pred




if __name__=='__main__':
    D = "2024-11-02"
    file_path=r"D:\fwx\gitlab\algorithms\lab\fanwenxuan\hunan\excel\df\11月.xlsx"
    data=pd.read_excel(file_path)
    pred_df=PriceAheadETR(D,data)._pred()
    print(pred_df)
