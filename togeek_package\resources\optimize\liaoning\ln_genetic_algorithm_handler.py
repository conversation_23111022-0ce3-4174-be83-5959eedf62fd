#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/4/10 17:04
# <AUTHOR> Darlene

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.liaoning.ln_subsetction_declaration_profit_common import LNSubsectionDeclarationProfit

class LNGAProfitHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        default_subsection = params.pop('default_subsection', 10)
        POP_SIZE = params.pop('POP_SIZE', 2000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 30)
        price_decimal = params.pop('price_decimal', 3)
        is_start_zero = params.pop("is_start_zero", 0)
        func_mode = params.pop('func_mode', 'eprofit')
        model = LNSubsectionDeclarationProfit(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                            lower_price=lower_price, upper_price=upper_price,
                                            min_profit=min_profit, POP_SIZE=POP_SIZE, N_GENERATIONS=N_GENERATIONS,
                                            default_subsection=default_subsection, price_decimal=price_decimal,
                                            is_start_zero=is_start_zero, func_mode=func_mode)
        result = model.predict()
        self.write(result)