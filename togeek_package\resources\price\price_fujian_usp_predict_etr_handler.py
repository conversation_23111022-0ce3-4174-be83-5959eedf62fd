from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.fujian import PriceD234ETR

class PriceD234ETRHandler(RequestHandlerBase):
    def put(self):

        params = j2o(self.request.body.decode())

        use_meta = params.pop('是否使用交易中心发布的预测值(True/False)')
        train_dict = params.pop('历史数据')
        test_dict = params.pop('未来数据')

        result_dict = PriceD234ETR(train_dict=train_dict, test_dict=test_dict, use_meta=use_meta).pred()

        self.write(result_dict)
