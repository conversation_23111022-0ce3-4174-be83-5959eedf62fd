# -*- coding: utf-8 -*-
# @Time    : 2022/5/6 16:16
# <AUTHOR> darlene
# @FileName: optimize_generator_handler.py
# @Software: PyCharm
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_generator_coal.generator_coal import GeneratorCoal
import pandas as pd
import logging
logger = logging.getLogger()

class LoadGeneratorHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generator_data = pd.DataFrame(params.get('generator_data'))
        loadrate_data = pd.DataFrame(params.get('loadrate_data'))
        gen = params.get('gen', '')
        logger.info("----------------机组负荷分配最优--------------------------")
        logger.info("-----------------------------")
        logger.info(
            "generator_data:{}, loadrate_data:{}, gen:{}".format(generator_data, loadrate_data, gen))
        generator_num = len(list(generator_data['max_power']))
        find_all = lambda data, s: [r for r in range(len(data)) if data[r] == s]
        result = {}
        if gen != '':
            r_list = find_all(gen, '1')
            z_list = find_all(gen, '0')
            gen_data = generator_data.iloc[r_list]
            z_data = generator_data.iloc[z_list]
            z_data = z_data.reset_index()
            zero_list = z_data['index'].to_list()
            loa_data = loadrate_data.iloc[:, [0] + list(map(lambda x: x + 1, r_list))]
            op = GeneratorCoal(gen_data, loa_data, zero_list)
            result[gen] = op.result
        else:
            # 循环机组组合，机组数为n，则由2的n次方-1种组合
            for c in range(1, 2 ** generator_num):
                c_str = bin(c)[2:].rjust(generator_num, '0')
                logger.info("--generator_coal--机组序列:{}".format(c_str))
                r_list = find_all(c_str, '1')
                z_list = find_all(gen, '0')
                gen_data = generator_data.iloc[r_list]
                z_data = generator_data.iloc[z_list]
                z_data = z_data.reset_index()
                zero_list = z_data['index'].to_list()
                loa_data = loadrate_data.iloc[:, [0] + list(map(lambda x: x + 1, r_list))]
                op = GeneratorCoal(gen_data, loa_data, zero_list)
                result[c_str] = op.result
        logger.info("------------end----------------")
        self.write(result)

