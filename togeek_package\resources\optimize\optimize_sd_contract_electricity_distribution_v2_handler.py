# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-12-26 10:11:36
# Description: 山东中长期合约电量分配
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_sd_contract_electricity_distribution import ContractElectricityDistribution_v2

class ContractElectricityDistribution_v2_Handler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        year = params.pop("year")
        month = params.pop("month")
        package = params.pop("package")
        package_selected_buyer = params.pop("package_selected_buyer")
        elec_demand_buyer = params.pop("elec_demand_buyer")
        generator_params = params.pop("generator_params")
        generator_overhaul = params.pop("generator_overhaul")
        choice = params.pop("choice")
        upper_threshold = params.pop("upper_threshold")
        threshold_limit = params.pop("threshold_limit")
        total_load_rate = params.pop("total_load_rate")

        model = ContractElectricityDistribution_v2(year=year,
                                                month=month,
                                                package=package,
                                                package_selected_buyer=package_selected_buyer,
                                                elec_demand_buyer=elec_demand_buyer,
                                                generator_params=generator_params,
                                                generator_overhaul=generator_overhaul,
                                                choice = choice,
                                                upper_threshold = upper_threshold,
                                                threshold_limit = threshold_limit,
                                                total_load_rate = total_load_rate)
        result = model.get_data_result()

        self.write(dict(result))
