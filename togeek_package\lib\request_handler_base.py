# -*- coding: utf-8 -*-

from tornado import web


class Request<PERSON><PERSON>lerBase(web.RequestHandler):
    def set_default_headers(self):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Headers', 'Authentication,Origin,x-requested-with,Content-Type,Accept')
        self.set_header('Access-Control-Allow-Methods', 'POST,GET,OPTIONS,PUT,DELETE')

    def options(self, *args, **kwargs):
        self.set_status(200)
        self.finish()
