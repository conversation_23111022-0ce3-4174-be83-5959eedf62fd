from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.hebei.xiongantower_pv_storage_optimization import XionganTowerPVStorageOptimization

class XionganTowerPVStorageOptimizationHandlerTS(RequestHandlerBase):
    def put(self):

        params = j2o(self.request.body.decode())
        target_date = params.pop('target_date')
        soc_initial = params.pop('soc_initial')
        load_data = params.pop('load_data')
        solar_data = params.pop('solar_data')
        weather_data = params.pop('weather_data')
        price_data = params.pop('price_data')

        dispatch = XionganTowerPVStorageOptimization(
                load_data=load_data,
                solar_data=solar_data,
                weather_data=weather_data,
                price_data=price_data,
                target_date=target_date,
                soc_initial=soc_initial
        )

        result_dict = dispatch.run()

        self.write(result_dict)
