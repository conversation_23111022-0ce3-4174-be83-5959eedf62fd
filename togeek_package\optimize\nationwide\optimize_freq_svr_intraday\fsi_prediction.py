# -*- coding: utf-8 -*-
# valid license

import numpy as np
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.kernel_ridge import <PERSON><PERSON><PERSON><PERSON>
from sklearn.metrics import r2_score, explained_variance_score


class Prediction:
    def __init__(self, x_dict, y):
        """
        构造函数
        """
        x = self._prepare_x(x_dict)
        N = x.shape[0] // 24
        y = np.asarray(y, dtype=float).ravel()
        self.feature_process = Pipeline([('stdscl', StandardScaler()),
                                         ('poly', PolynomialFeatures())])
        x = self.feature_process.fit_transform(x)
        x = np.c_[x, np.tile(np.eye(24), [N, 1])]
        self.model = KernelRidge()
        self.model.fit(x, y)
        pred_y = self.model.predict(x)
        self.r2_score = r2_score(y, pred_y)
        self.explained_variance_score = explained_variance_score(y, pred_y)

    def _prepare_x(self, x_dict):
        return np.c_[np.asarray(x_dict['pd_capacity_requirement'], dtype=float).ravel(),
                     np.asarray(x_dict['pd_forecast_node_price'], dtype=float).ravel(),
                     np.asarray(x_dict['pd_forecast_society_load'], dtype=float).ravel(),
                     np.asarray(x_dict['hs_freq_price'], dtype=float).ravel(),
                     np.asarray(x_dict['hs_freq_amount'], dtype=float).ravel()]

    def predict(self, x_dict, to_json=False):
        x = self._prepare_x(x_dict)
        N = x.shape[0] // 24
        x = self.feature_process.transform(x)
        x = np.c_[x, np.tile(np.eye(24), [N, 1])]
        y = self.model.predict(x).reshape([-1, 24])
        if to_json:
            y = {'y': y.tolist()}
        return y
