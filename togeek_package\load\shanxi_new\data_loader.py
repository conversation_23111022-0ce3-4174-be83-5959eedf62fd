import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Optional
import chinese_calendar as cc


class DataPreprocessor:
    """
    数据预处理类，自动同步负荷数据和天气数据的频率
    """
    def __init__(
        self,
        start_date: str,
        end_date: str,
        excel_file_path: str = '山西零售用户负荷.xlsx',
        weather_csv_path: str = "weather_山西.csv",
    ):
        """
        初始化数据预处理类

        参数:
            start_date: 统一的开始日期 (yyyy-MM-dd)
            end_date: 统一的结束日期 (yyyy-MM-dd)
            excel_file_path: 负荷数据Excel文件路径
            weather_csv_path: 天气数据CSV文件路径
        """
        self.start_date = start_date
        self.end_date = end_date
        self.excel_file_path = excel_file_path
        self.weather_csv_path = weather_csv_path
        self.load_freq: Optional[str] = None 

        
        self.X_train = None  # 训练集特征
        self.y_train = None  # 训练集标签
        self.X_test = None  # 测试集特征
        self.y_test = None  # 测试集标签  
        
        # 验证日期格式
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("日期格式错误，需使用'yyyy-MM-dd'格式")



    # ----------------------- 加载负荷（仅用于aggregate_all_users_load） ----------------------- #
    def load_user_load(self, customer_code: str) -> pd.DataFrame:
        """从Excel文件加载单个客户的负荷数据（简化版，仅用于汇总）"""
        # 读取Excel文件
        try:
            xls = pd.ExcelFile(self.excel_file_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"负荷数据文件不存在：{self.excel_file_path}")

        # 检查客户sheet是否存在
        if customer_code not in xls.sheet_names:
            raise ValueError(f"客户代码 '{customer_code}' 不存在于Excel文件中")

        df = xls.parse(customer_code)

        # 解析日期和时间（行列转换）
        time_cols = df.columns[1:]  # 时间点列（从第二列开始）
        timestamps, loads = [], []

        for _, date_row in df.iterrows():
            date_str = date_row.iloc[0]  # 日期值（第一列）
            for time_col in time_cols:
                time_str = time_col
                load_value = date_row[time_col]
                try:
                    # 组合日期和时间为时间戳
                    timestamp = pd.to_datetime(f"{date_str} {time_str}")
                    timestamps.append(timestamp)
                    loads.append(load_value)
                except:
                    continue  # 跳过解析失败的单元格

        # 构造负荷数据DataFrame
        load_df = pd.DataFrame({'dateTime': timestamps, 'load': loads})

        # 使用初始化的日期范围筛选
        load_df = load_df[
            (load_df['dateTime'] >= pd.to_datetime(self.start_date)) &
            (load_df['dateTime'] < pd.to_datetime(self.end_date))
        ]

        # 排序并重置索引
        load_df = load_df.sort_values('dateTime').reset_index(drop=True)

        # 设置频率（简化版，不自动识别）
        if not hasattr(self, 'load_freq'):
            self.load_freq = '15T'  # 默认15分钟

        return load_df

    # ----------------------- 加载天气 ----------------------- #
    def load_weather_data(self) -> pd.DataFrame:
        """
        从CSV文件加载天气数据（使用负荷数据的频率）
        注意：此方法**不执行特征工程**，仅处理频率对齐和插值
        """
        # 确保已经加载过负荷数据并识别了频率
        if self.load_freq is None:
            raise ValueError("请先调用load_user_load方法识别负荷数据频率")
        
        # 读取天气数据
        try:
            df = pd.read_csv(self.weather_csv_path)
            # 选择核心天气特征（移除不重要的v_10m和rh_2m）
            weather_cols = ['t_datetime_cst', 't_2m', 'tmax_2m', 'tmin_2m',
                           'u_10m', 'sp_surface', 'tp_surface', 'dswrf_surface']
            df = df[weather_cols]
            print(f"选择了 {len(weather_cols)-1} 个核心天气特征（已移除不重要特征）")
        except FileNotFoundError:
            raise FileNotFoundError(f"天气数据文件不存在：{self.weather_csv_path}")
        
        # 转换时间列并过滤无效值
        df['t_datetime_cst'] = pd.to_datetime(df['t_datetime_cst'], errors='coerce')
        df = df.dropna(subset=['t_datetime_cst']).copy()
        
        # 使用初始化的日期范围筛选
        start_date_obj = datetime.strptime(self.start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(self.end_date, '%Y-%m-%d')
        df = df[
            (df['t_datetime_cst'] >= start_date_obj) & 
            (df['t_datetime_cst'] < end_date_obj)
        ]
        
        print(f"天气数据筛选完成，共 {len(df)} 条原始记录")
        
        # 构造完整时间序列（使用负荷数据的频率）
        full_range = pd.date_range(
            start=start_date_obj,
            end=end_date_obj - timedelta(seconds=1),
            freq=self.load_freq
        )
        time_df = pd.DataFrame({"t_datetime_cst": full_range})
        
        # 合并数据并插值填补缺失值
        weather_df = pd.merge(time_df, df, on="t_datetime_cst", how="left")
        numeric_cols = weather_df.select_dtypes(include=np.number).columns.tolist()
        
        # 时间插值（确保频率一致）
        weather_df = weather_df.set_index('t_datetime_cst')
        weather_df[numeric_cols] = weather_df[numeric_cols].interpolate(method='time').bfill()
        weather_df = weather_df.reset_index()
        
        print(f"天气数据处理完成，按负荷频率 '{self.load_freq}' 生成 {len(weather_df)} 条记录")
        
        # 返回未做特征工程的原始天气数据
        return weather_df



    def _timedelta_to_freq(self, delta: pd.Timedelta) -> str:
        """将时间差转换为pandas频率字符串"""
        seconds = delta.total_seconds()
        if seconds % 86400 == 0:
            return f"{int(seconds // 86400)}D"  # 天
        elif seconds % 3600 == 0:
            return f"{int(seconds // 3600)}H"  # 小时
        elif seconds % 60 == 0:
            return f"{int(seconds // 60)}T"  # 分钟（pandas中T代表分钟）
        else:
            return f"{seconds}S"  # 秒

    # ----------------------- 特征工程 ----------------------- #
    def process_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        对天气数据进行特征工程处理（新增时间、节假日等特征）
        此方法可在外部手动调用，按需执行特征工程
        """
        feature_start_time = time.time()
        print(f"\n🔧 开始天气数据特征工程...")
        
        # 重命名时间列为统一的'dateTime'
        df = df.rename(columns={'t_datetime_cst': 'dateTime'})
        
        # 1. 添加日期列（用于筛选和分组）
        df['date'] = df['dateTime'].dt.date.astype(str)
        
        # 2. 移除dayofweek特征（重要性分析显示不相关）
        # df['dayofweek'] = df['dateTime'].dt.dayofweek  # 已移除

        # 3. 添加统一的日期类型特征（使用中国法定节假日库）
        from datetime import datetime
        def get_day_type(date_str):
            """
            获取日期类型：1=工作日, 2=周末, 3=法定节假日
            """
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            holiday_detail = cc.get_holiday_detail(date_obj)

            if holiday_detail[0] and holiday_detail[1] is not None:  # 有具体节假日名称
                return 3  # 法定节假日（如五一、端午等）
            elif not cc.is_workday(date_obj):  # 非工作日但没有具体节假日名称，即普通周末
                return 2  # 普通周末
            else:
                return 1  # 工作日

        df['day_type'] = df['date'].apply(get_day_type)
        print(f"✅ 使用chinese_calendar库添加日期类型特征 (1=工作日, 2=周末, 3=法定节假日)")

        # 时间特征（适配15分钟数据）
        # 4. 添加小时特征
        df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）

        # 5. 添加分钟特征
        df['minute'] = df['dateTime'].dt.minute  # 分钟（0-59）

        # 6. 添加一天中的分钟数特征
        df['time_of_day'] = df['hour'] * 60 + df['minute']  # 一天中的分钟数（0-1439）

        # 7. 月内周特征
        df['week_of_month'] = self.week_of_month(df['dateTime'])

        # 8. 天数索引（增强临近日期的关联性）
        df['day_index'] = (df.groupby(df['dateTime'].dt.date).ngroup() + 1)

        # 9. 添加月份特征
        df['month'] = df['dateTime'].dt.month  # 月份（1-12）

        # 10. 添加季度特征
        df['quarter'] = df['dateTime'].dt.quarter  # 季度（1-4）

        # 11. 添加年内天数特征
        df['day_of_year'] = df['dateTime'].dt.dayofyear  # 年内天数（1-365/366）

        # 12. 添加月内天数特征
        df['day_of_month'] = df['dateTime'].dt.day  # 月内天数（1-31）

        # 13. 添加是否为月初/月末特征
        df['is_month_start'] = (df['day_of_month'] <= 3).astype(int)  # 月初3天
        df['is_month_end'] = (df['day_of_month'] >= 28).astype(int)   # 月末几天

        # 14. 添加是否为周末特征（使用dateTime直接计算，因为移除了dayofweek）
        df['is_weekend'] = (df['dateTime'].dt.dayofweek >= 5).astype(int)  # 周六日为1，其他为0

        # 15. 添加季节性周期特征（正弦余弦编码）
        # 小时周期特征
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)

        # 星期周期特征（使用dateTime直接计算）
        dayofweek = df['dateTime'].dt.dayofweek
        df['dayofweek_sin'] = np.sin(2 * np.pi * dayofweek / 7)
        df['dayofweek_cos'] = np.cos(2 * np.pi * dayofweek / 7)

        # 月份周期特征
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)

        # 移除季节性余弦特征（重要性分析显示season_cos不相关）
        df['season_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        # df['season_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)  # 已移除

        # 16. 添加时段特征
        df['time_period'] = pd.cut(df['hour'],
                                 bins=[0, 6, 12, 18, 24],
                                 labels=[0, 1, 2, 3],  # 0:夜间, 1:上午, 2:下午, 3:晚上
                                 include_lowest=True).astype(int)

        # 计算特征工程耗时
        feature_time = time.time() - feature_start_time
        # 计算新增特征数量（排除原有的时间和日期相关列）
        original_cols = ['dateTime', 'date']
        new_feature_cols = [col for col in df.columns if col not in original_cols and not col.startswith('t_')]
        print(f"   ✅ 天气特征工程完成，新增 {len(new_feature_cols)} 个特征")
        print(f"   ⏱️  特征工程耗时: {feature_time:.2f} 秒")
        
        return df


    # ----------------------- 月内周 ----------------------- #
    def week_of_month(self, date_series: pd.Series) -> pd.Series:
        """计算日期在当月的第几周（从1开始）（修正缩进，作为类方法）"""
        # 确保输入为datetime类型
        if not pd.api.types.is_datetime64_any_dtype(date_series):
            date_series = pd.to_datetime(date_series)
        
        # 当月第一天
        first_day = date_series.dt.to_period('M').dt.start_time
        # 当月第一周的周一
        first_week_monday = first_day - pd.to_timedelta(first_day.dt.dayofweek, unit='d')
        # 计算当前日期所在周与第一周的差值（以周为单位）
        week_num = ((date_series - first_week_monday) // pd.Timedelta(weeks=1)).astype(int) + 1
        return week_num

    # ----------------------- 划分测试集 ----------------------- #
    def split_train_test_by_ratio(self, merged_df: pd.DataFrame, test_ratio: float = 0.2) -> tuple:
        """按比例划分训练集和测试集（时间序列专用，保留时间顺序）"""
        # 校验必要列
        if 'dateTime' not in merged_df.columns or 'load' not in merged_df.columns:
            raise ValueError("数据集必须包含'dateTime'和'load'列")

        # 按时间排序
        merged_df = merged_df.sort_values('dateTime').reset_index(drop=True)
        total_samples = len(merged_df)

        # 计算测试集大小（向上取整，确保至少1条）
        test_size = max(1, round(total_samples * test_ratio))
        train_size = total_samples - test_size

        # 划分（后test_ratio比例作为测试集）
        train_data = merged_df.iloc[:train_size]
        test_data = merged_df.iloc[train_size:]

        # 分离特征和标签
        X_train = train_data.drop(columns=['load', 'dateTime'])
        y_train = train_data['load']
        X_test = test_data.drop(columns=['load', 'dateTime'])
        y_test = test_data['load']

        # 打印划分结果
        actual_ratio = len(test_data) / total_samples * 100
        print(f"按{test_ratio*100}%比例划分完成：")
        print(f"训练集：{len(train_data)}条，测试集：{len(test_data)}条，实际测试集占比：{actual_ratio:.1f}%")

        self.X_train = X_train  # 保存训练集特征
        self.y_train = y_train  # 保存训练集标签
        self.X_test = X_test    # 保存测试集特征
        self.y_test = y_test    # 保存测试集标签

        return X_train, X_test, y_train, y_test

    def split_train_test_by_days(self, merged_df: pd.DataFrame, test_days: int = 15) -> tuple:
        """按天数划分训练集和测试集（确保测试集为96倍数，即完整天数）"""
        # 校验必要列
        if 'dateTime' not in merged_df.columns or 'load' not in merged_df.columns:
            raise ValueError("数据集必须包含'dateTime'和'load'列")

        # 按时间排序
        merged_df = merged_df.sort_values('dateTime').reset_index(drop=True)
        total_samples = len(merged_df)

        # 计算测试集大小（96个时间点 = 1天）
        test_size = test_days * 96

        # 确保测试集不超过总样本数
        if test_size >= total_samples:
            # 如果指定天数太多，自动调整为最大可能的完整天数
            max_test_days = (total_samples - 96) // 96  # 至少保留1天作为训练集
            test_days = max(1, max_test_days)
            test_size = test_days * 96
            print(f"⚠️ 指定测试天数过多，自动调整为 {test_days} 天")

        train_size = total_samples - test_size

        # 划分（后test_days天作为测试集）
        train_data = merged_df.iloc[:train_size]
        test_data = merged_df.iloc[train_size:]

        # 分离特征和标签
        X_train = train_data.drop(columns=['load', 'dateTime'])
        y_train = train_data['load']
        X_test = test_data.drop(columns=['load', 'dateTime'])
        y_test = test_data['load']

        # 打印划分结果
        actual_ratio = len(test_data) / total_samples * 100
        actual_test_days = len(test_data) / 96
        print(f"按{test_days}天划分完成：")
        print(f"训练集：{len(train_data)}条，测试集：{len(test_data)}条 ({actual_test_days:.1f}天)")
        print(f"实际测试集占比：{actual_ratio:.1f}%")

        # 验证测试集是否为96的倍数
        if len(test_data) % 96 == 0:
            print(f"✅ 测试集为完整的 {len(test_data)//96} 天数据")
        else:
            print(f"⚠️ 测试集不是完整天数，剩余 {len(test_data) % 96} 个时间点")

        self.X_train = X_train  # 保存训练集特征
        self.y_train = y_train  # 保存训练集标签
        self.X_test = X_test    # 保存测试集特征
        self.y_test = y_test    # 保存测试集标签

        return X_train, X_test, y_train, y_test


    # ----------------------- 普通时间序列数据处理 ----------------------- #
    def create_simple_time_series_data(self, df: pd.DataFrame) -> tuple:
        """
        创建普通时间序列数据（不使用滑动窗口）
        适用于LSTM等时间序列模型

        返回:
            (X_df, y_series, dates): 特征DataFrame、目标Series、日期列表
        """
        # 确保数据已按时间排序
        if not df['dateTime'].is_monotonic_increasing:
            df = df.sort_values('dateTime')

        # 提取特征列和目标列
        feature_columns = [col for col in df.columns if col not in ['dateTime', 'load', 'date']]
        target_column = "load"

        print(f"使用普通时间序列数据处理")
        print(f"特征列: {len(feature_columns)} 个 ({', '.join(feature_columns[:5])}...等)")

        # 统一所有特征列为float64类型
        try:
            df[feature_columns] = df[feature_columns].apply(
                pd.to_numeric, errors='coerce'
            ).fillna(0.0).astype('float64')
            print(f"特征列已统一转换为float64类型，共 {len(feature_columns)} 列")
        except Exception as e:
            print(f"特征列类型转换警告: {e}，将尝试继续处理")

        # 分离特征和目标
        X_df = df[feature_columns].copy()
        y_series = df[target_column].copy()
        dates = df['dateTime'].tolist()

        print(f"普通时间序列数据处理完成，共 {len(X_df)} 个样本")
        print(f"每个样本包含 {X_df.shape[1]} 个特征")

        return X_df, y_series, dates

    # ------------------------ 数据增强 ------------------------- #
    def create_enhanced_time_series_data(self, df: pd.DataFrame) -> tuple:
        """
        创建增强的时间序列数据（添加更多特征工程）
        """
        # 基础处理
        X_df, y_series, dates = self.create_simple_time_series_data(df)

        # 添加滞后特征（基于数据频率，假设15分钟频率）
        lags = [1, 2, 4, 8, 24, 48, 96]  # 15分钟到1天的滞后
        for lag in lags:
            if lag < len(y_series):
                X_df[f'load_lag_{lag}'] = y_series.shift(lag)

        # 添加滚动统计特征
        windows = [4, 8, 24, 96]  # 1小时到1天的窗口
        for window in windows:
            if window < len(y_series):
                X_df[f'load_rolling_mean_{window}'] = y_series.rolling(window).mean()
                X_df[f'load_rolling_std_{window}'] = y_series.rolling(window).std()
                X_df[f'load_rolling_max_{window}'] = y_series.rolling(window).max()
                X_df[f'load_rolling_min_{window}'] = y_series.rolling(window).min()

        # 添加差分特征
        X_df['load_diff_1'] = y_series.diff(1)
        X_df['load_diff_24'] = y_series.diff(24)  # 6小时差分（15分钟频率下24=6小时）
        X_df['load_diff_96'] = y_series.diff(96)  # 1天差分
        X_df['load_diff_96'] = y_series.diff(672)  # 7天差分

        # === 添加峰值标识特征（推荐方案：滚动窗口峰值比例） ===
        # 移除峰值比例特征（分析显示重要性为0）
        print("   🔍 跳过峰值比例特征（重要性分析显示不相关）...")

        # 添加温度相关的交互特征（使用实际温度列名t_2m）
        if 't_2m' in X_df.columns:
            X_df['temp_load_interaction'] = X_df['t_2m'] * y_series.shift(1)  # 温度与滞后1期负荷的交互
            X_df['temp_squared'] = X_df['t_2m'] **2  # 温度平方项（捕捉非线性关系）
            X_df['temp_rolling_mean_24'] = X_df['t_2m'].rolling(24).mean()  # 温度滚动均值

        # 添加最近数据权重特征（更依赖于最近的数据）
        print(f"   🔍 添加最近数据权重特征...")

        # 1. 指数加权移动平均（EWMA）- 对最近数据给予更高权重
        ewm_spans = [4, 12, 24, 48]  # 不同的衰减速度
        for span in ewm_spans:
            X_df[f'load_ewm_{span}'] = y_series.ewm(span=span).mean()

        # 2. 最近趋势特征 - 捕捉最近的变化趋势
        recent_windows = [2, 4, 8, 12]  # 最近2-12个时间点的趋势
        for window in recent_windows:
            if window < len(y_series):
                # 最近窗口的线性趋势斜率
                X_df[f'recent_trend_{window}'] = y_series.rolling(window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else 0,
                    raw=False
                )

                # 最近窗口的变化率
                X_df[f'recent_change_rate_{window}'] = (
                    y_series - y_series.shift(window)
                ) / y_series.shift(window)

        # 3. 保留关键的自适应权重特征（只保留最重要的）
        key_stability_windows = [6, 12, 24]  # 保留这些，因为adaptive_weighted_mean_6/12在重要性分析中表现较好
        for window in key_stability_windows:
            if window < len(y_series):
                # 简化的自适应加权平均（移除稳定性计算以减少特征）
                X_df[f'adaptive_weighted_mean_{window}'] = (
                    y_series.rolling(window).apply(
                        lambda x: np.average(x, weights=np.linspace(0.5, 1.0, len(x))) if len(x) == window else x.mean(),
                        raw=False
                    )
                )

        # 4. 最近峰值记忆特征 - 记住最近的峰值水平
        recent_peak_windows = [12, 24, 48, 96]
        for window in recent_peak_windows:
            if window < len(y_series):
                # 最近窗口内的最高值
                X_df[f'recent_peak_{window}'] = y_series.rolling(window).max()

                # 当前值与最近峰值的比例
                X_df[f'peak_ratio_{window}'] = y_series / X_df[f'recent_peak_{window}']

                # 距离最近峰值的时间
                def time_since_peak(series):
                    if len(series) == 0:
                        return 0
                    max_idx = series.argmax()
                    return len(series) - 1 - max_idx

                X_df[f'time_since_peak_{window}'] = y_series.rolling(window).apply(
                    time_since_peak, raw=False
                )

        # 5. 最近模式匹配特征 - 与最近相似时间点的负荷对比
        # 与昨天同时刻的比较（96个时间点前）
        if len(y_series) > 96:
            X_df['vs_yesterday_same_time'] = y_series / y_series.shift(96)
            X_df['diff_yesterday_same_time'] = y_series - y_series.shift(96)

        # 与上周同时刻的比较（7*96个时间点前）
        if len(y_series) > 7*96:
            X_df['vs_last_week_same_time'] = y_series / y_series.shift(7*96)
            X_df['diff_last_week_same_time'] = y_series - y_series.shift(7*96)

        print(f"   ✅ 添加了最近数据权重特征")

        # 填充缺失值（前向填充+后向填充+0填充）
        X_df = X_df.fillna(method='ffill').fillna(method='bfill').fillna(0)

        print(f"增强特征工程完成，特征数从 {len(X_df.columns) - len(lags) - 4*len(windows) - 3 - (3 if 't_2m' in X_df.columns else 0)} 增加到 {X_df.shape[1]}")

        return X_df, y_series, dates

# 获取excel中所有sheet名称
def get_all_sheet_names(excel_file_path="山西零售用户负荷.xlsx"):
    """获取Excel文件中所有的sheet名称"""
    try:
        xls = pd.ExcelFile(excel_file_path)
        return xls.sheet_names
    except FileNotFoundError:
        raise FileNotFoundError(f"Excel文件不存在：{excel_file_path}")
    except Exception as e:
        raise Exception(f"读取Excel文件失败：{e}")


def aggregate_all_users_load(start_date, end_date, excel_file_path="山西零售用户负荷.xlsx"):
    """
    提取Excel表中每个sheet的负荷信息，并按照对应时间相加，得到全部用户负荷总和

    参数:
        start_date: 开始日期 (yyyy-MM-dd)
        end_date: 结束日期 (yyyy-MM-dd)
        excel_file_path: Excel文件路径

    返回:
        pd.DataFrame: 包含dateTime和total_load列的汇总数据
        dict: 包含统计信息的字典
    """
    print("🚀 开始汇总所有用户的负荷数据...")
    print(f"📅 时间范围: {start_date} 到 {end_date}")
    print(f"📁 Excel文件: {excel_file_path}")

    try:
        # 获取所有sheet名称（用户代码）
        all_users = get_all_sheet_names(excel_file_path)
        print(f"📊 发现 {len(all_users)} 个用户: {all_users[:5]}..." if len(all_users) > 5 else f"📊 发现 {len(all_users)} 个用户")

        # 创建数据预处理器
        preprocessor = DataPreprocessor(start_date, end_date, excel_file_path)

        all_load_data = []
        successful_count = 0
        failed_count = 0

        # 逐个处理每个用户
        for i, user_code in enumerate(all_users, 1):
            try:
                print(f"  🔄 处理 {i}/{len(all_users)}: {user_code}")

                # 加载该用户的负荷数据
                df_load = preprocessor.load_user_load(str(user_code))

                # 筛选时间范围
                df_load = df_load[
                    (df_load['dateTime'] >= start_date) &
                    (df_load['dateTime'] <= end_date)
                ].copy()

                if len(df_load) > 0:
                    df_load['user_code'] = user_code
                    all_load_data.append(df_load[['dateTime', 'load', 'user_code']])
                    successful_count += 1
                    print(f"    ✅ 成功加载 {len(df_load)} 条记录")
                else:
                    print(f"    ⚠️ 时间范围内无数据")
                    failed_count += 1

            except Exception as e:
                print(f"    ❌ 加载失败: {str(e)}")
                failed_count += 1
                continue

        if not all_load_data:
            raise ValueError("没有成功加载任何用户的数据")

        # 合并所有数据
        combined_df = pd.concat(all_load_data, ignore_index=True)
        print(f"✅ 成功加载 {successful_count} 个用户的数据，失败 {failed_count} 个")
        print(f"📊 总计 {len(combined_df)} 条记录")

        # 按时间汇总负荷
        print("🔄 按时间汇总负荷...")
        total_load_df = combined_df.groupby('dateTime')['load'].sum().reset_index()
        total_load_df.rename(columns={'load': 'total_load'}, inplace=True)
        total_load_df = total_load_df.sort_values('dateTime').reset_index(drop=True)

        # 计算统计信息
        stats = {
            'total_users': len(all_users),
            'successful_users': successful_count,
            'failed_users': failed_count,
            'time_points': len(total_load_df),
            'total_load_min': total_load_df['total_load'].min(),
            'total_load_max': total_load_df['total_load'].max(),
            'total_load_mean': total_load_df['total_load'].mean(),
            'time_range': f"{total_load_df['dateTime'].min()} 到 {total_load_df['dateTime'].max()}"
        }

        print(f"📈 汇总完成：{stats['time_points']} 个时间点")
        print(f"   负荷总和范围: {stats['total_load_min']:.2f} - {stats['total_load_max']:.2f} kW")
        print(f"   负荷总和平均值: {stats['total_load_mean']:.2f} kW")
        print(f"   时间范围: {stats['time_range']}")

        # 保存汇总结果到CSV文件
        output_file = f"全部用户负荷总和_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        total_load_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"💾 汇总结果已保存至: {output_file}")

        return total_load_df, stats

    except Exception as e:
        print(f"❌ 汇总失败: {str(e)}")
        raise