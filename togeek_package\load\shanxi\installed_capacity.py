import pandas as pd
from datetime import datetime, timedelta
from sklearn.ensemble import ExtraTreesRegressor
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


class InstalledCapacityPred:
    def __init__(self, load_data, capacity, pred_dates):
        logger.info("----------------------随机森林_全网装机容量预测开始--------------------------")
        load_data = self._pre_load_data(load_data)
        capacity = self._pre_other_data(capacity)
        self.data = pd.merge(load_data, capacity, on='date', how='left')
        self.pred_dates = pred_dates
        self.features = ['负荷预测', '日前新能源负荷预测', '日前联络线计划', '日前必开机组', '日前必停机组', '检修总容量', '负荷总加', '竞价空间']

    def data_verify(self, df, feature_list, threshold_null=0.35):
        """
        json格式转为pd.DataFrame格式，并进行指定数据缺失值填充
        :param data_json: json, 模型传入数据
        :param feature_list: list, 待校验字段列表
        :param threshold_null: float, 缺失值比例阈值。低于阈值向后填充补齐；高于阈值补0
        :return: df, message
        """
        # 2. 遍历字段，缺失值填充
        num_should = len(df)  # 每个字段的理论数据量
        message = f"传入数据长度应为：{num_should} 条; \n"

        for feature in feature_list:
            # 1. 判断指定校验字段是否在传入字段中
            if feature not in df.columns:
                message += f"待检测字段: '{feature}' --> 不在传入数据中, 跳过检查该字段; \n"
                continue

            # 2. 统计当前字段缺失值数量，计算缺失值比例
            num_null = df[feature].isnull().sum()  # 当前字段的缺失值数量
            rate_null = num_null / num_should  # 当前字段缺失值比例

            # 3. 缺失值填充
            if rate_null == 0:
                message += f"待检测字段: '{feature}' --> 不存在缺失值; \n"
                continue
            elif rate_null < threshold_null:
                # 若缺失值比例低于指定阈值，向后填充缺失值
                df[feature] = df[feature].fillna(method='ffill').fillna(method='bfill')
                message += f"待检测字段: '{feature}' --> 缺失值数量{num_null}, 缺失值占比{rate_null:.2f}, 低于阈值{threshold_null:.2f}, 采用'向后向前法'填充; \n"
            else:
                # 若缺失值比例高于指定阈值，采用0值填充
                df[feature] = df[feature].fillna(0)
                message += f"待检测字段: '{feature}' --> 缺失值数量{num_null}, 缺失值占比{rate_null:.2f}, 高于阈值{threshold_null:.2f}, 0值填充; \n"

        message += f"检测处理完成后，df缺失值总数量：{df.isnull().sum().sum()}"

        return df, message

    def _pre_load_data(self, data):
        df = pd.DataFrame(data)
        data, message = self.data_verify(df, feature_list=['日前必开机组', '日前必停机组', '检修总容量', '日前联络线计划'],
                                         threshold_null=0.35)
        logger.info(message)    # 写入日志
        data.sort_values('date_time', inplace=True)  # 升序排列
        data['date'] = data['date_time'].astype(str).map(lambda x: x.split(' ')[0])
        data['负荷总加'] = data['负荷预测'] - data['日前联络线计划']
        data['竞价空间'] = data['负荷预测'] - data['日前联络线计划'] - data['日前新能源负荷预测']
        data = data.groupby('date').mean().reset_index()
        return data

    def _pre_other_data(self, df):
        data = pd.DataFrame(df)
        data['date'] = data['date'].astype(str).map(lambda x: x[:10])
        data.sort_values('date', inplace=True)    # 升序排列
        return data

    def run(self, to_json=True, n_estimators=100, max_depth=6, days=30):
        res = pd.DataFrame()
        dt1 = sorted(self.pred_dates)[0]
        dt0 = str(datetime.strptime(dt1, '%Y-%m-%d') + timedelta(-days)).split(" ")[0]  # 历史开始日期
        # 准备测试集
        train = self.data[(self.data['date'] >= dt0) & (self.data['date'] < dt1)]  # 获取历史数据
        for dt in self.pred_dates:
            # 准备预测集
            test = self.data[self.data['date'] == dt]                                 # 获取未来数据
            model = ExtraTreesRegressor(n_estimators=n_estimators, max_depth=max_depth, random_state=42)
            train_x = train[self.features].values
            train_y = train['装机容量'].values
            test_x = test[self.features].values

            model.fit(train_x, train_y)
            test_y = model.predict(test_x)
            tmp = pd.DataFrame({"date": test['date'], "pred": list(map(round, test_y))})
            res = pd.concat([res, tmp], axis=0)
        logger.info("----------------------随机森林_全网装机容量预测完成--------------------------")

        if to_json:
            res = res.to_dict('list')
        return res


if __name__ == '__main__':
    load_data = pd.read_excel(r"D:\Togeek\work\model_data\load\installed_capacity\load_data.xlsx")
    # reserve_load = pd.read_excel(r"D:\Togeek\work\model_data\load\installed_capacity\reserve_load.xlsx")
    capacity = pd.read_excel(r"D:\Togeek\work\model_data\load\installed_capacity\capacity.xlsx")

    res = pd.DataFrame()
    for i in range(17):
        dt1 = str(datetime(2023, 4, 1) + timedelta(i)).split(" ")[0]  # 预测日
        # try:
        m = InstalledCapacityPred(load_data, capacity, [dt1])
        tmp = m.run(to_json=True)
        print(tmp)
        # res = pd.concat([res, tmp], axis=0)
        # except:
        #     print(dt1)
        break

    # print(res.shape)
    # print(res)
