# 模型更新记录

---
## V2.1.0
**新增模型1**: 山西45天统一价格预测模型        
**模型说明**: 该模型适用于山西省的统一结算点和节点电价预测           
**接口**:
- http://139.159.204.155:8099/model_api/service/price/shanxi/common_price_total_45_v1.0

**输入数据**: 

- `data`: 历史及未来数据
- `run_date`: 运行日期

**输出数据**:     
- `etr_pred`: 随机森林模型
- `bsf_pred`: 竞价空间拟合模型
- `message`: 运行日志

**API文档**:
https://apifox.com/apidoc/shared-6c42d68a-732a-4fc0-97fc-1ad189cec7c8


---
## V2.0.4
**新增模型1**: 河北建投负荷预测模型        
**模型说明**: 该模型适用于河北现货试结算阶段，实际用电以及分时用电数据都比较少且不连续时售电公司的负荷预测           
**接口**:
- http://139.159.204.155:8099/model_api/load/hebei/load_pred_v1.0

**输入数据**: 

- `hist_weather`: 历史天气数据
- `hist_elec`: 历史实际用电数据
- `hist_meter_elec`: 历史抄表数据
- `hist_elec_hourly`: 历史分时用电数据
- `future_weather`: 预测日期的天气数据
- `future_meter_elec`: 最新抄表数据
- `city_wgt`: 天气权重，默认为{"石家庄": 0.4, "保定": 0.05, "沧州": 0.05, "衡水": 0.05, "邯郸": 0.05, "邢台": 0.4}，总权重为1

**输出数据**:     
- `elec_daily`: 日总用电量
- `elec_hourly`: 分时用电量

**API文档**:
https://apifox.com/apidoc/shared-7479662c-2499-4e42-a3f7-0bf3a349d5fe

**新增模型2**: 国能山西理想机组分段报价模型        
**模型说明**: 该模型适用于理想机组的分段报价策略           
**接口**:
- http://139.159.204.155:8099/model_api/optimize/shanxi/subsection_declaration/genetic_algorithm/guoneng_ideal_v1.0

**输入数据**: 

- `engs`: 机组数据
- `param`: 煤耗数据
- `price`: 价格数据
- `func_mode`: 优化目标，默认为”profit"-收益最大，“power”-与理想机组的出力偏差最小
- `min_price`: 报价下限，默认为0
- `max_price`: 报价上限，默认为1500
- `size_pop`: 种群数量，默认为1000
- `iter`: 迭代次数，默认为20

**输出数据**:     
- `declaration`: 分段报价方案
- `profit`: 收益

**API文档**:
https://apifox.com/apidoc/shared-a5aa86a8-19e9-4610-af77-91c8e14381af

---
## V2.0.3
**新增模型1**: 山西价差方向预测模型        
**模型说明**:该模型用于山西省的价差方向预测           
**接口**:
- http://139.159.204.155:8099/model_api/price/shanxi/price_diff_v1.0

**输入数据**: 

- `data`: date_time、日前电价、实时电价、日前统调负荷、日前联络线受计划总加、日前风电负荷、日前光伏负荷、日前检修总容量、日前新能源负荷
- `pred_lst`: 预测日期列表
- `periods`: 历史数据周期，默认为30天
- `model`: 预测采用的模型，默认为“cbc"，可选择["gbc", "xgbc", "lgbmc", "cbc", "rfc"]

**输出数据**:     
- `date_time`: 时间
- `pred`: 价差方向，其中1-日前>实时；0-日前<实时
- `prob`: 价差方向的概率
- `declaration`: 申报系数

**API文档**:
https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-116037092

**新增模型2**: 广东价差方向预测模型        
**模型说明**:该模型用于广东省的价差方向预测           
**接口**:
- http://139.159.204.155:8099/model_api/price/guangdong/price_diff_v1.0

**输入数据**: 

- `data`: date_time、日前电价、实时电价、日前统调负荷、日前省内A类电源、日前地方电源出力、日前西电东送电力、日前粤港联络线、日前必开机组总容量、日前必停机组总容量、日前检修总容量
- `pred_lst`: 预测日期列表
- `periods`: 历史数据周期，默认为30天
- `model`: 预测采用的模型，默认为“cbc"，可选择["gbc", "xgbc", "lgbmc", "cbc", "rfc"]

**输出数据**:     
- `date_time`: 时间
- `pred`: 价差方向，其中1-日前>实时；0-日前<实时
- `prob`: 价差方向的概率
- `declaration`: 申报系数

**API文档**:
https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-116037107

---
## V1.9.9
**新增模型1**: 山东价差方向预测模型        
**模型说明**:该模型用于山东省的价差方向预测           
**接口**:
- http://139.159.204.155:8099/model_api/price/shandong/price_diff_v1.0

**输入数据**: 

- `data`: date_time、日前电价、实时电价、日前直调负荷、日前联络线受电负荷、日前风电总加、日前光伏总加、日前核电总加、日前自备机组总加、日前地方电厂发电总加、日前联络线华北来电、日前联络线银东直流、日前联络线鲁固直流、日前联络线昭沂直流
- `pred_lst`: 预测日期列表
- `periods`: 历史数据周期，默认为30天
- `model`: 预测采用的模型，默认为“cbc"，可选择["gbc", "xgbc", "lgbmc", "cbc", "rfc"]

**输出数据**:     
- `date_time`: 时间
- `pred`: 价差方向，其中1-日前>实时；0-日前<实时
- `prob`: 价差方向的概率

**API文档**:
https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-105060769
---

## V1.9.8
**新增模型1**:蒙西新能源场站联合储能策略套利模型
**模型说明**:该模型用于蒙西新能源场站联合储能策略，利用储能充放电行为，基于价格预测数据进行峰谷套利，返回储能系统的充放电功率、电量。
**接口**:
- http://139.159.204.155:8099/model_api/optimize/mx/energy_incomes_v1.0

**输入数据**: 

- `price_data`: 节点价格预测值，元/MWH
- `power_data`: 新能源功率预测，MW
- `soc0`: 储能初始soc状态，%
- `energy_q`: 储能总容量，MWH
- `cap_feng`: 电场总容量，MWH
- `p_ch`:最大充电速率, kw/h
- `p_f`:最大放电速率,kw/h
- `energy_loss`:充放电损失，%
- `min_power`:储能最小剩余比例，%
- `max_power`:储能最大剩余比例，%
- `points`:点数，默认96
- `low_income`:度电收益筛选，默认为1

**输出数据**:
- `price`: 价格，元/MWH
- `soc`:每时刻soc，%
- `incomes`:收益，元
- `operate_power`:储能充放电量，充是-，放是+，kw
- `charge_power`:储能充放功率，充是-，放是+，kwh
- `declare_power`:申报功率，输入风功率预测才会返回

**API文档**:
https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-95126722

**新增模型2**:蒙西中长期置换模型-新能源
**模型说明**:该模型用于蒙西新能源场站中长期合约置换模型，计算场站间合约电量置换，使目标场站的收益和最大。
**接口**:
- http://139.159.204.155:8099/model_api/optimize/mx/replacement_xny_v1.0

**输入数据**: 

- `longterm_data`: 场站中长期数据，中长期持仓电量，中长期持仓均价，申报电量，可交易电量
- `node_price`: 节点电价，元/MWH
- `general_price`: 统一结算点价格，元/MWH
- `num_n`: 输入场站列表
- `num_m`: 目标场站列表

**输出数据**:
- `longterm_result`: 置换后的场站中长期数据，中长期持仓电量，中长期持仓均价，申报电量，结算价格，可交易电量
- `transfer_result`: 置换路径

**API文档**:
https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-99608382


## V1.9.7

**新增模型**：储能系统收益优化

**模型说明**：该模型用于<u>古瑞瓦特智慧园区项目</u>，考虑**需量调节**和**峰谷套利**，以收益最大化为目标，返回指定日期储能系统充放电功率调度计划。

**接口**:          

- http://139.159.204.155:8099/model_api/optimize/guangdong/optimize_energy_storage_v1.0

**输入数据**: 

- `date_pred`：目标日期
- `data_load`：全园区历史实际负荷(历史7天数据)，单位为：MW
- `data_price`：历史实际电价(历史7天数据)，单位为：元/MWh
- `is_selected_demand`：目标日期是否选择容量电费模式。0表示未选择，1表示已选择
- `max_demand_month`：目标需量. 若 is_selected_demand为1，则该值必须提供
- `price_max_demand`：需量月度单价，元/兆瓦时·月
- `time_point_load`：历史实际负荷的点数
- `time_point_price`：历史实际电价的点数
- `time_point_out`：输出的储能指令点数
- `elec_total`：储能系统总容量，MWh
- `dod`：储能放电深度（depth of discharge），0-1之间的浮点数
- `soc_init`：目标日初始时刻(00:00:00)的 soc，0-1之间的浮点数
- `max_power_charge`：最大充电功率，MW，正值
- `max_power_discharge`：最大放电功率，MW，正值
- `coef_charge`：储能系统充放电效率，0-1之间的浮点数，一般为0.85-0.9之间
- `capacity_transformer`：变压器计费容量，KVA
- `price_capacity`：容量月度单价（即：基本电费单价），元/千伏安·月

**输出数据**:      

- `load_pred`：目标日负荷预测值
- `price_pred`：目标日价格预测值
- `power_of_storage`：目标日各时刻储能系统的最优功率
- `soc_of_storage`：目标日各时刻储能系统的最优soc
- `max_income`：目标日最优收益
- `message`：数据校验信息。

**API文档**

- https://apifox.com/apidoc/shared-49dd939d-9019-4ad1-9376-1eb556506593



---

## V1.9.6

**新增模型**：山东D1~D8价格预测数据接口

**模型说明**：提供指定时段的：D+1的日前和实时价格预测值；D+2~D+8的日前价格预测值;  价格预测准确率

**接口**:          

- http://139.159.204.155:8099/model_api/service/price/shandong/common_price_etr_8_v1.0

**输入数据**:      

- `date_bidding_start`：竞价日起始日期
- `date_bidding_end`：竞价日终止日期
- `method_acc`：准确率计算方法类型。默认为1. 可选：[1, 2]， 1表示常用价格预测准确率，2表示国华计算方法

**输出数据**:      

- `data_pred`：实际价格&预测价格
- `method_acc`：准确率计算方法类型。
- `data_acc`：准确率

**API文档**

- https://apifox.com/apidoc/shared-b691978d-e1dc-43a4-978a-30de1893c1d5

---
## V1.9.5

**新增模型**：分时刻中标出力_线性报价        
**模型说明**：山东省发电侧的分段报价模拟出清         

**接口**:          
- http://127.0.0.1:8099/model_api/optimize/shandong/bidded_power_v1.0

**输入数据**:      
- `generator`：机组信息
- `price`：节点电价
- `sub_decl`：sub_decl
- `type`：是否为线性报价，默认为1
- `decimal`：保留小数位数，默认为3

**输出数据**:         
- `result`：中标出力数据
- `message`：模型运行日志

**API文档**            
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-95328026
---

## V1.9.4

**新增模型1**：山东D2D3日前价格

**模型说明**：用于山东D+2, D+3日的日前价格预测

**接口**:          

- http://139.159.204.155:8099/model_api/price/shandong/ahead_price_d2d3_v1.0

**输入数据**:

- `date_bidding`：str, 竞价日，如："2023-06-13"
- `date_run`：str, 运行日，如："2023-06-16"
- `time_point`：int, 每天时间点数量，默认为：24
- `min_price`：int, 价格下限，默认为：-80
- `max_price`：int, 价格上限，默认为：1300
- `data`：历史实际日前价格

**输出数据**:        

- `ahead_price_pred`： 运行日的预测价格，**若数据校验失败，则为空**
- `message_error`：报错信息，正常为空，**若数据校验失败，则返回报错信息**


**新增模型2**：通用深度调峰模型        
**模型说明**：适用于多台机组多档位的深度调峰优化模型          

**接口**:          
- http://127.0.0.1:8099/model_api/optimize/nationwide/optimize_deep_adjust_common_v1.0

**输入数据**:      
- `generator_number`：机组台数
- `deep_shift`：深调档位
- `generator_step`：发电负荷步长，默认为1MW
- `eboiler_step`：电锅炉步长，默认为1MW
- `param`：深调参数，详情参考API文档

**输出数据**:         
- `zj1`：自主调峰1档
- `lh1`：联合调峰1档
- `zj2`：自主调峰2档
- `lh2`：联合调峰2档
- ......
- ......
- `zjn`：自主调峰n档
- `lhn`：联合调峰n档

**API文档**            
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-89808683


---

## V1.9.3

**新增模型1**：山东次月月典型价格预测   

**模型说明**：本模型适用于山东次月月典型电价预测

**接口**:  

- http://139.159.204.155:8099/model_api/price/shandong/typical_price_next_month_v1.0

**输入数据**:

- `year_pred`：str, 待预测年份
- `month_pred`：str, 待预测月份
- `time_points`：int, 每天时间点数量，默认为：96
- `weight_m_1`：float, 待预测月前一月权重，默认为：0.8
- `min_price`：int, 价格下限，默认为：-80
- `max_price`：int, 价格上限，默认为：1300
- `data`：历史实际价格

**输出数据**:        

- `year_pred`：待预测年份
- `month_pred`：待预测月份
- `price_pred`： 预测价格，**若数据校验失败，则为空**
- `message_error`：报错信息，正常为空，**若数据校验失败，则返回报错信息**

**API文档**       

- https://apifox.com/apidoc/shared-308958f2-b76c-4837-944c-a6ec81da87a4


**新增模型2**：蒙西基于边际成本曲线的分时刻中标出力计算 

**模型说明**：本模型适用于蒙西发电侧的分段报价模拟出清

**接口**:          

- http://139.159.204.155:8099/model_api/optimize/mx/bided_longtime_power_v1.0

**输入数据**:

- `generators`：包含机组id、额定容量、机组最小出力、最大出力、爬坡速率、下坡速率、起始出力、停机日期
- `prices`：包含节点电价数据，传入多久的电价数据，模型返回多久的预测中标出力
- `costs`：边际成本数据

**输出数据**:        

- `genertor`: 机组id
- `date_time`： 预测时刻
- `real_power`： 预测中标出力

**API文档**       

- https://apifox.com/apidoc/shared-c48e2e3f-f660-4d2b-89d1-89fae19d91d7/api-84137257

**新增模型3**：京能山西最大收益分段报价模型         
**模型说明**：   
本模型适用于山西京能发电厂的分段报价申报策略

**接口**:          
- http://139.159.204.155:8099/model_api/optimize/shanxi/subsection_declaration/genetic_algorithm/jingneng_profit_v1.0

**输入数据**:    
- 机组参数、机组约束条件、电价、机组成本、报价上下限、最小收益、默认分段数量、种群大小、迭代次数、价格保留位数、起始出力是否从0开始，详情参考API文档

**输出数据**:        
- 收益、分段报价方案、中标出力

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-85607074


-----
## V1.9.2
**新增模型1**：蒙西分时刻中标出力模型模型   
**模型说明**：   
本模型适用于蒙西分时刻中标出力计算

**接口**:          
- http://139.159.204.155:8099/model_api/optimize/mx/bided_power_v1.0

**输入数据**:
- 1. 机组信息：机组ID、额定容量、机组最小出力、机组最大出力、机组爬坡速率、机组下坡速率、起始出力、最小出力是否为最小技术出力（默认为1，1-最小中标出力等于最小技术出力；0-最小中标出力等于0）
- 2. 机组对应的节点价格：机组ID、时间、实时节点电价
- 3. 机组分段报价信息：机组ID、分段、出力、报价，**注意：出力需要传入每段出力区间的终止出力**

**输出数据**:        
- 机组id、时间、出力

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-79854631

**新增模型2**：蒙西-中长期竞价空间拟合预测模型 
**模型说明**：   
本模型适用于蒙西售电侧的中长期现货价格预测（实时统一结算点电价）

**接口**:          
- http://139.159.204.155:8099/model_api/price/mengxi/longtime/bsgv1.0

**输入数据**:
- 1. 边界条件数据：时间、负荷预测、新能源负荷预测、东送计划值
- 2. 历史价格数据：时间、实时价格
- 3. 预测日期：预测起始日期、预测天数
- 4. 模型参数：需要一个特定位置保存并每次接收返回值时更新模型相关参数，目的是减少模型计算响应时间。在有参数情况下前3天的历史数据。**详情见API**

**输出数据**:        
- 1. 价格预测数据： 返回从起始日期开始共days天的价格预测数据
- 2. 模型参数：返回模型参数，解析并更新存储即可，下一次调用模型时调用此参数。

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-81652194

-----
## V1.9.1

**新增模型1**：山西_三段拟合法价格预测模型         
**模型说明**：   
本模型适用于山西未来5天的价格预测

**接口**:          
- http://139.159.204.155:8099/model_api/price/shanxi/declaration_fitting_price_5d_v1.0

**输入数据**:    
- 负荷预测，日前新能源负荷预测、日前联络线计划、日前价格，预测日期列表等，详情参考API文档

**输出数据**:        
- 预测时点、预测电价、拟合类型

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-76852203

**新增模型2**：山西_随机森林价格预测模型         
**模型说明**：   
本模型适用于山西未来5天的价格预测

**接口**:          
- http://139.159.204.155:8099/model_api/price/shanxi/etr_pred_price_5d_v1.0

**输入数据**:    
- 负荷预测、日前新能源负荷预测、日前联络线计划、日前必开机组、日前必停机组、检修总容量、是否为节假日、日前价格、实时价格，预测日期列表等，详情参考API文档

**输出数据**:        
- 预测时点、日前价格预测值、实时价格预测值

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-76852238

**新增模型3**：冀南_三段拟合法价格预测   
**模型说明**：   
本模型适用于河北冀南的电价预测

**接口**:          
- http://139.159.204.155:8099/model_api/price/hebei/declaration_fitting_price_v1.0

**输入数据**:
- 统调负荷预测、新能源预测、联络线计划、电价，预测日期列表等，详情参考API文档

**输出数据**:        
- 电价预测值、拟合类型

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-79167907

-----
## V1.9.0

**新增模型1**：国能山西最大收益分段报价模型         
**模型说明**：   
本模型适用于国能山西分段报价申报策略

**接口**:          
- http://139.159.204.155:8099/model_api/optimize/shanxi/subsection_declaration/genetic_algorithm/guoneng_profit_v1.0

**输入数据**:    
- 机组参数、机组约束条件、电价、机组成本、报价上下限、最小收益、默认分段数量、种群大小、迭代次数、价格保留位数、起始出力是否从0开始，详情参考API文档

**输出数据**:        
- 收益、分段报价方案、中标出力

**API文档**       
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74016753


**新增模型2：通用价格预测_提升树模型**      
**模型说明**：         
本模型用于各省日前/实时的统一结算点/节点价格预测

**接口**：      
- http://192.168.0.146:8099/model_api/common/price/xgb_v1.0

**输入数据**：     
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-01”     

**输出数据**：        
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：      
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74012716


**新增模型3~7**：山东省分段报价模型         
**模型说明**：   
本模型适用于山东省的分段报价申报策略

**接口**:          
- http://127.0.0.1:8099/model_api/optimize/shandong/subsection_declaration/genetic_algorithm/income_v1.0 
- http://127.0.0.1:8099/model_api/optimize/shandong/subsection_declaration/genetic_algorithm/profit_v1.0
- http://127.0.0.1:8099/model_api/optimize/shandong/subsection_declaration/genetic_algorithm/matrix_profit_v1.0
- http://127.0.0.1:8099/model_api/optimize/shandong/subsection_declaration/marginal_cost/adapt_v1.0
- http://127.0.0.1:8099/model_api/optimize/shandong/subsection_declaration/marginal_cost/dist_v1.0

**输入数据**:    
- 机组参数、机组约束条件、电价、机组成本、报价上下限、最小收益、默认分段数量、种群大小、迭代次数、价格保留位数、起始出力是否从0开始，详情参考API文档

**输出数据**:        
- 收益、分段报价方案、中标出力

**API文档**  
        
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74039120
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74039228
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74195253
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74039249
- https://apifox.com/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-74039375


-----
## V1.8.9

**新增模型**：蒙西分段报价模型

**模型说明**：

本模型适用于蒙西收益最大分段报价、边际成本_自适应分段、边际成本_节点电价分布

**接口**:`http://192.168.0.146:8099/model_api/optimize/mx_subsection_declaration/genetic_algorithm/profit`

**接口**:`http://192.168.0.146:8099/model_api/optimize/mx_subsection_declaration/marginal_cost/adapt`

**接口**:`http://192.168.0.146:8099/model_api/optimize/mx_subsection_declaration/marginal_cost/dist`

**输入数据**:

1. `generators`：机组信息
2. `prices`：节点电价数据
3. `costs`：单位变动成本
4. `constraints`：机组约束信息
5. `lower_price`：报价下限
6. `upper_price`：报价上限
7. `min_profit`：最小收益
8. `POP_SIZE`：种群大小
9. `N_GENERATIONS`：迭代次数
10. `default_subsection`：默认分段数量
11. `price_decimal`：价格小数位数
12. `is_start_zero`：第一段的起始出力是否从0开始
13. `n_sampling`：采样量
14. `min_split_scale`：最小分段比例
15. `dists`：节点电价的分布

**输出数据**:

1. `subsection_declaration`：分段报价方案
2. `bidded_power`：中标出力
3. `target`：方案其它参数
4. `profit`：收益

**API文档**

- https://www.apifox.cn/apidoc/shared-c48e2e3f-f660-4d2b-89d1-89fae19d91d7

-----
## V1.8.8

**新增模型**：蒙东深度调峰模型——固定发电机负荷下的最佳电锅炉负荷

**模型说明**：

本模型适用于蒙东供热机组参与火电深度调峰市场优化

**接口**:`http://192.168.0.146:8099/model_api/optimize/optimize_deep_adjust_v1.1`

**输入数据**:

1. `generator_params`：机组参数
2. `coal_consum_func`：煤耗函数的参数
3. `generator_constraints`：机组约束
4. `plant_elec_rate`：厂用电率函数的参数
5. `deep_adjust_params`：深调条件
6. `market_params`：市场条件
7. `eboiler_step`：电锅炉步长
7. `gener_type`：机组运行模式

**输出数据**:

1. `deep_adjust_eboiler`：最佳电锅炉负荷

**API文档**

- https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-68773571
- https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-68780478

-----
## V1.8.7

**新增模型**：山东中长期合约电量分配_v2

**模型说明**：

本模型用于山东省中长期双边合约电量分配策略

**接口**:`http://192.168.0.146:8099/model_api/optimize/shandong/contract_electricity_distribution_v2.0`

**输入数据**:

1. `year`：目标年份
2. `month`：目标月份
3. `package`：套餐数据
4. `package_selected_buyer`：售电公司套餐选择
5. `elec_demand_buyer`：售电公司电量需求
6. `generator_params`：机组参数
7. `generator_overhaul`：机组检修数据
8. `choice`：判断”大电量机组优先" or "低成本机组优先

**输出数据**:

1. `result_data_distribution`：输出数据1：发售两侧目标月份匹配总电量
2. `result_data_buyer`：输出数据2：售电侧数据
3. `result_data_seller`：输出数据3：发电测数据

**API文档**

- https://www.apifox.cn/apidoc/shared-777ff213-315e-4a0a-ac77-cc3095dce7e1

---

## V1.8.6

**新增模型1：山西3天通用价格预测_三段拟合模型**

**模型说明**：

本模型用于山西省日前/实时的统一结算点/节点价格预测

**接口**：  http://192.168.0.146:8099/model_api/service/price/shanxi/common_price_bsf_3_v1.0  

**输入数据**：   
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-01”     

**输出数据**：       
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：   
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-63665189


**新增模型2：山西3天通用价格预测_随机森林模型**

**模型说明**：

本模型用于山西省日前/实时的统一结算点/节点价格预测

**接口**：    http://192.168.0.146:8099/model_api/service/price/shanxi/common_price_etr_3_v1.0

**输入数据**：   
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-01”     

**输出数据**：       
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：     
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-63579560

---

## V1.8.5

**新增模型1：山西15天通用价格预测_三段拟合模型**

**模型说明**：

本模型用于山西省日前/实时的统一结算点/节点价格预测

**接口**：  http://192.168.0.146:8099/model_api/service/price/shanxi/common_price_bsf_15_v1.0  

**输入数据**：   
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-02”     

**输出数据**：       
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：   
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-59843897


**新增模型2：山西15天通用价格预测_随机森林模型**

**模型说明**：

本模型用于山西省日前/实时的统一结算点/节点价格预测

**接口**：    http://192.168.0.146:8099/model_api/service/price/shanxi/common_price_etr_15_v1.0

**输入数据**：   
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-02”     

**输出数据**：       
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：     
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-59843975

**新增模型3：山西45天通用价格预测_相似日模型**

**模型说明**：

本模型用于山西省日前/实时的统一结算点/节点价格预测

**接口**：    http://192.168.0.146:8099/model_api/service/price/shanxi/common_price_sim_45_v1.0

**输入数据**：   
1. `price`:历史30天的价格数据
2. `run_date`:运行日期，形如：“2023-02-02”     

**输出数据**：       
1. `date_time`：时刻
2. `pred_price`：预测价格
3. `message`：运行日志

**API文档**：    
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-59843569


---

## V1.8.4

**新增模型1**：**通用随机森林模型**

**模型说明**：

1. 本模型可用于**统一结算点日前/实时价格预测**，**节点日前/实时价格预测**。
2. 该模型区别于分省份随机森林模型的特点在于：
    - 不能同时预测日前/实时价格，如需要，可调用两次；
    - 必须传入字段：**火电竞价空间**；
    - 不指定传入字段的数量和名称，可按需新增或去除某些字段（各省传入字段不同，火电竞价空间计算方式存在差异）
    - 不能传入空值

**接口**：

- http://192.168.0.146:8099/model_api/common/price/etr

**输入数据**

1. `all_data`：传入数据
    - `label`：预测字段数据
    - `bidding_space`：火电竞价空间
    - `字段名`：用于模型训练的其他字段

2. `date_list_pred`：预测日期列表

3. `days_train`：训练数据天数

**输出数据**

1. `label_pred`：预测结果

**API文档**：

- https://www.apifox.cn/apidoc/shared-01db59e3-12c1-43d9-801e-d9dee4e1eb4c



**新增模型2**：**通用三段拟合模型**

**模型说明**：

1. 本模型可用于**统一结算点日前/实时价格预测**，**节点日前/实时价格预测**。
2. 该模型区别于分省份竞价空间拟合模型的特点在于：
    - 必须传入**火电竞价空间**；
    - 不能传入空值

**接口**：

- http://192.168.0.146:8099/model_api/common/price/bsf

**输入数据**

1. `all_data`：传入数据，需涵盖历史日期数据以及待预测日期数据
    - `label`：预测字段数据
    - `bidding_space`：火电竞价空间
2. `date_list_pred`：预测日期列表

**输出数据**

1. `label_pred`：预测数据
2. `type`：类型

**API文档**：

- https://www.apifox.cn/apidoc/shared-a48c984a-8410-4d4d-b154-b1efb3e2d006




-----
## V1.8.3
**新增模型**：山东日前统一结算点电价预测_申报分布拟合

**接口**：
- http://192.168.0.146:8099/model_api/price/shandong/declaration_fitting_pricev1.0

**输入数据**
1. `running_generator`：全网运行机组数据
2. `standard_declaration`：标准报价方案
3. `load`：负荷预测数据
4. `price`：现货价格数据
5. `pred_date`：预测日期
6. `sim_dates`：选取相似日的历史周期，默认为14天
7. `points`：每天的时刻数，默认为24，仅支持【24，48，96】个时点的数据
8. `min_price`：最低价格，默认为0
9. `max_price`：最高价格，默认为1500

**输出数据**
1. `date_time`：日期
2. `price`：预测电价
3. `message`：模型运行的记录

**API文档**：
- https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-58708286
-----
## V1.8.2
**新增模型1**：山西15天日前电价预测_竞价空间拟合

**接口**：
- http://192.168.0.146:8099/model_api/service/price/shanxi/ahead_price_etr_15_v1.0

**输入数据**:
1. `run_date`：运行日期，形如“2022-12-28”

**输出数据**:
1. `date_time`：输出数据1：时点
2. `预测日前价格`：输出数据2：日前电价预测值
3. `type`：输出数据3：拟合类型

**API文档**：
- https://www.apifox.cn/apidoc/shared-02054221-3350-44b4-a9ef-1f10fda166c2

**新增模型2**：山西15天日前电价预测_极端随机树模型

**接口**：
- http://192.168.0.146:8099/model_api/service/price/shanxi/ahead_price_etr_15_v1.0

**输入数据**:
1. `run_date`：运行日期，形如“2022-12-28”

**输出数据**:
1. `date_time`：输出数据1：时点
2. `日前价格`：输出数据2：日前电价实际值
3. `日前价格预测值`：输出数据3：日前电价预测值

**API文档**：
- https://www.apifox.cn/apidoc/shared-02054221-3350-44b4-a9ef-1f10fda166c2

**新增模型3**：输出模型参数的时间序列模型

**接口**：
- http://127.0.0.1:8099/model_api/load/nationwide/customer_load_pre_faster_v1.0

**输入数据**:
1. `load`：单用户负荷数据
2. `holiday`：节假日数据
3. `hist_model`：历史模型参数

**输出数据**:
1. `load`：单用户负荷数据
2. `model`：本次模型参数

**API文档**：
- https://www.apifox.cn/apidoc/shared-a1874e17-fe5a-4eae-be2e-507a81dc949e

**新增模型4**

**接口**：
- http://127.0.0.1:8099/model_api/price/dongbei/boot_capacityv1.0

**输入数据**：
1. `all_data`：竞价空间相关的历史数据和预测日数据，包含字段：["系统负荷预测", "地方燃煤出力总加预测", "日前新能源负荷预测", "水电出力总加预测", "核电出力总加预测", "日前联络线计划"]
2. `date_list_yuce`：预测目标日期的列表
3. `boot_data`：运行容量历史数据
4. `coe`：修正系数，默认3.9

**输出数据**：
1. 字典形式，key表示预测日期，value表示预测运行容量

**API文档**：
https://www.apifox.cn/apidoc/shared-f722a001-6f81-4123-98f8-f19f600061e6/api-57097462



-----
## V1.8.1

**新增模型**：山东中长期合约电量分配

**url**:`http://192.168.0.146:8099/model_api/optimize/shandong/contract_electricity_distribution_v1.0`

**输入数据**:

1. `year`：目标年份
2. `month`：目标月份
3. `package`：套餐数据
4. `package_selected_buyer`：售电公司套餐选择
5. `elec_demand_buyer`：售电公司电量需求
6. `generator_params`：机组参数
7. `generator_overhaul`：机组检修数据

**输出数据**:

1. `result_data_distribution`：输出数据1：发售两侧目标月份匹配总电量
2. `result_data_buyer`：输出数据2：售电侧数据
3. `result_data_seller`：输出数据3：发电测数据

**API文档**

- https://www.apifox.cn/apidoc/shared-71acfcb6-d323-4ec6-ba78-e27a696f184a



-----
## V1.8.0
新增模型：
- 东北随机森林价格预测模型
    从原有随机森林模型中分离出来，V1.0
- 东北三段拟合价格预测模型
    从原有三段模型中分离出来，V1.0
- 通用模型：多项式拟合模型
    输入x和y，以及多项式阶数，返回对应的系数

-----
## V1.7.9
新增模型：
- 通用模型：煤耗曲线拟合
    根据发电机负荷、供热量及总煤耗量拟合出关于发电机和供热量的三次煤耗曲线
    
- 价格预测模型：三段法竞价空间拟合加负荷率修正模型
    在原来三段法拟合模型的基础上，增加使用负荷率修正0值部分
-----
## V1.7.8
新增模型：蒙西价格预测
   - 从原随机森林价格预测模型分离出
   - pre_list 默认是['统一结算点价格', '呼包东价格', '呼包西价格']

-----
## V1.7.7
新增模型：跨省输配电价格增长计算
   - areas 大区信息
   - node_area 省间信息
   - line_data 线路信息
      分三种方法：线损率最低，落地价最低，输电线路最短
      1 线损率最低：先招有权图的最小权，再计算费用
      2 落地价最低：从起始点开始，依次计算到相邻点的费用
      3 输电线路最短：从起始点开始，依次计算步数和费用
-----

## V1.7.6

新增模型：供热机组参与火电机组深度调峰优化     
   - 单机组运行模式, gener_type=1    
   - 双机组运行模式, gener_type=2

-----

## V1.7.5

1 随机森林价格预测模型：
   - 增加特殊日模式，增加字段`special_sign`，默认为0(非特殊日模式);若为1(特殊日模式)
   - 增加特殊日的星期模式，增加字段`special_days`，列表行，[1,2,3]，表现特殊日是周一、周二、周三，三天，默认空列表
2 自相关时间序列预测模型：
   - 增加特殊日模式，增加字段`special_sign`，默认为0(非特殊日模式);若为1(特殊日模式)
   - 增加特殊日的星期模式，增加字段`special_days`，列表行，[1,2,3]，表现特殊日是周一、周二、周三，三天，默认空列表

-----
## V1.7.4

竞价空间拟合价格预测模型:
   - 增加省间现货模式，增加字段`sj_sign`，默认为0(非省间现货模式);若为1(省间现货模式)
   - 增加省间现货的星期模式，增加字段`sj_date`，列表行，[1,2,3]，表现省间现货是周一、周二、周三，三天，默认空列表

-----

## V1.7.3

新增模型：通用距离计算

---

## V1.7.2

1 中长期价格预测模型：   
   - 月度、旬价格预测模型修改    
   - 原理：按照价格在上下限之间的比例进行预测

2 三段拟合法价格预测模型：
   - 增加预测价格上下限制，限制在0~1500

------


## V1.7.1

优化模型运行方式：顺序执行 --> 并行

------

## V1.7.0

新增蒙西地区新能源预测模型：`load/newpower_mx`
    - 根据历史同时刻相似风速，取相似三个时刻的风负荷求均值，再用线性回归方法进行修正
1.输入数据
    - `speed_data`:历史观测风速、预测日及未来十天的风速数据
    - `power_feng`: 历史风负荷数据
    - `power_guang`: 历史光负荷数据
    - `yuce_list`: 预测日期列表
    - `weight_dic`： 蒙西全地区风速计算公式

-----


## V1.6.9
新增发电量约束遗传算法模型
    - 多机组总发电量单天分段报价
    - 多机组多发电量多天分段报价
    - 多机组总发电量多天分段报价

新增分段报价通用模型
    - 遗传算法
        - 收益最大
        - 收入最大
    - 边际成本
        - 自适应分段
        - 按照节点电价分布预测

---


## V1.6.8
新增新能源申报系数预测模型：`optimize/xny_lambda_avg`  
    - 依据历史该节点日前价格与实时价格的差的均值计算lambda，以均值在最大最小范围中的位置为依据，同比例置于lambda的约束范围中  
1. 输入数据   
    - `price`：时间、日前价格、实时价格  
    - `lambda_min`：lambda系数的最小值  
    - `lambda_max`：lambda系数的最大值  
2. 输出数据
    - `建议的lambda值`：时间、lambda值
---

## V1.6.7
价格预测随机森林模型:`price/eval_value`
1. 新增`province`的`通用`配置，这样就不用多次配置省份了

---


## V1.6.6

新增电厂负荷分配模型：`optimize/generator_handler`
1. 输入数据
    - `generator_data`: 机组容量、机组最小出力
    - `loadrate_data`: 每台机组的负荷率对应煤耗
    - `gen`: 限定开停机组，如"0101010"，0为必停，1为必开，若不传入，则返回输入机组的所有分配结果
2. 模型方法
    - 当输入`gen`时，根据必开机组的煤耗曲线，次序决定机组的出力，计算每条运行路径到这台机组为止，出力总数，通过计算每个阶段每个总出力，到达此点，最优机组组合，以平均煤耗最小为优
    - 当不输入`gen`时，计算所有（机组数量n^2-1种）情况下的最优分配


---


## V1.6.5

1. 新增火电机组运行容量预测模型
    - 模型方法为: 要预测D+1日的运行容量，D日机组容量 + (D+1日竞价空间-D日竞价空间) / 3.9

---


## V1.6.2

1. 修改原分段报价模型小数点输出问题
2. 原模型有4种方法，分别为
    - `sassign`：指定分段比例`segs`，分段与边际成本围成的面积最小
    - `adapt`：自动匹配报价分段的比例，并保证每段比例不小于指定值`min_split_scale`，同时满足报价曲线与边际成本曲线围成面积最小。
    - `dist_adapt`：该方法利用节点价格的概率预测结果，指导报价分段比例，使报价曲线在价格出现的高概率区域分段较密。**混合概率分布上采样，返回一个分段函数结果**
    - `dist_adapt/split`：与`dist_adapt`相似，**分别在指定的n个分布上采样，返回n个分段函数结果**

---

## V1.6.1

1. 修改分段报价_遗传算法模型：`optimize_subsection_declaration`

    - 将出力取整，并合并出力相同的分段

    - 在原有输出的基础上，新增字段·`'power_start', 'power_end'`以匹配生产库中分段数据的结构。

2. 修改通用时间序列`common_autocorr_prophet`的上下限限制



------

## V1.6.0

新增**售电公司日前市场负荷申报推荐方案**，根据历史日前和实时的预测和实际负荷，返回**激进型**和**平衡型**两种负荷申报推荐方案。



**输入数据示例**：

1. `history`：历史数据

   ![](README/history.jpg)

2. `predictions`: 申报日预测数据

   ![](README/predictions.jpg)

3. `limits`: 负荷上下限限制



**输出数据示例**：

![](README/result.jpg)



---

## V1.5.2

1. 新增**广东台山节点电价**极端随机树模型配置，主要传入数据如下

   ![](README/广东台山节点价格传入数据.jpg)

2. 修正分时刻中标出力为0的问题

3. 升级**自然断点包**`jenkspy==0.2.0`，更新基础镜像`togeek_data:V1.2`

---

## V1.5.1

2. 修改三段拟合模型，使其输出值不带序号



---

## V1.5.0

1. 新增**分时刻中标出力计算**：optimize_bided_power

2. 输入

   - **generators**

     | 字段名称           | 类型  | 说明                                   |
     | :----------------- | :---: | :------------------------------------- |
     | **generator**      |  str  | 机组ID（不能为空）                     |
     | **rated_capacity** | float | 额定容量（不能为空）                   |
     | **min_capacity**   | float | 最小出力（不能为空）                   |
     | **max_capacity**   | float | 最大出力（不能为空）                   |
     | **upward**         | float | 爬坡速率（不能为空）                   |
     | **downward**       | float | 下坡速率（为空时用同机组上坡速率代替） |

   - **prices**

     | 字段名称        | 类型  | 说明                     |
     | :-------------- | :---: | :----------------------- |
     | **generator**   |  str  | 机组ID（不能为空）       |
     | **time**        |  str  | 时间点（不能为空）       |
     | **ahead_price** | float | 日前节点价格（不能为空） |
     | **real_price**  | float | 实时节点价格（不能为空） |

   - **subsection_declaration**

     | 字段名称       | 类型  | 说明                 |
     | :------------- | :---: | :------------------- |
     | **generator**  |  str  | 机组ID（不能为空）   |
     | **subsection** |  int  | 分段标号（不能为空） |
     | **power**      | float | 出力（不能为空）     |
     | **price**      | float | 报价（不能为空）     |

3. 输出

   - 分时点中标出力



---

## V1.4.0

1. 新增**分段报价模型**：optimize_subsection_declaration

2. 方法：遗传算法

3. 输入

   - **generators**

     | 字段名称           | 类型  | 说明                                   |
     | :----------------- | :---: | :------------------------------------- |
     | **generator**      |  str  | 机组ID（不能为空）                     |
     | **rated_capacity** | float | 额定容量（不能为空）                   |
     | **min_capacity**   | float | 最小出力（不能为空）                   |
     | **max_capacity**   | float | 最大出力（不能为空）                   |
     | **upward**         | float | 爬坡速率（不能为空）                   |
     | **downward**       | float | 下坡速率（为空时用同机组上坡速率代替） |
     | **subsection**     |  int  | 分段数量（为空时，默认分6段）          |

   - **prices**

     | 字段名称        | 类型  | 说明                     |
     | :-------------- | :---: | :----------------------- |
     | **generator**   |  str  | 机组ID（不能为空）       |
     | **time**        |  str  | 时间点（不能为空）       |
     | **ahead_price** | float | 日前节点价格（不能为空） |
     | **real_price**  | float | 实时节点价格（不能为空） |

4. 输出

   - 总收益：`income`

   - 最优分段报价：`subsection_declaration`

   - 分时点中标出力：`bided_power`

     ![](README/subsection_declaration.jpg)



---

## V1.3.4

### 极端随机树模型

#### 修改内容

1. 重写极端随机树模型，添加数据验证
2. 去掉字段"日前必停机组"  (项目反映，没有必停数据)
3. 模型内部自动筛选字段数据完整的历史数据进行预测
4. 从筛选出的完整数据中选择最后30天进行训练

#### 数据要求

- 预测结果最佳：历史连续30天数据
- 最低要求：至少1天历史数据（日前和实时价格数据都要有）
- 历史数据不能为空
- 日期可间断

#### 数据示例

- 输入

  ![](README/极端随机树输入数据示例.png)

- 输出

  ![](README/极端随机树输出示例.png)

### 相似日模型

#### 修改内容

1. 内部做筛选，只使用所有字段齐全的历史日期做训练

#### 数据要求

- 预测结果最佳：历史连续30天数据
- 最低要求：至少1天历史数据
- 历史数据允许含nan值
- 日期可间断

#### 数据示例

- 输入

  ![](README/相似日模型输入数据示例.png)

- 输出（仅传入2天历史数据的输出）

  ![](README/相似日模型输出示例.png)

### 火电机组装机容量

#### 修改内容

1. 使用传入数据的最后30天做预测，而不是预测日前30天的数据
2. 使用logistic + floor + cap的方式，修正最值

#### 数据要求

- 预测结果最佳：历史连续30天数据
- 最低要求：至少2天历史数据
