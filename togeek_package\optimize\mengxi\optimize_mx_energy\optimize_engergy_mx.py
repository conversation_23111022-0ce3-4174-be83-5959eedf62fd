#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/10 10:56
# <AUTHOR> Darlene
import numpy as np
import pandas as pd
import datetime
import warnings
import logging


warnings.filterwarnings("ignore")
logger = logging.getLogger()


'''
1、处理价格数据
2、处理功率数据
3、开始计算
'''
class MxEnergy:
    def __init__(self, price_data, power_data, soc0, energy_q, cap_feng, p_ch, p_f, energy_loss, min_power, max_power,
                 points, low_income, unit_con):
        logger.info("------------------蒙西储能策略模型开始计算------------------------")
        self.star_time = datetime.datetime.now()
        self.price_data = self._pre_data(price_data)   # 价格预测
        self.power_data = self._pre_power(power_data)   # 风功率预测
        self.low_income = low_income    # 度电成本筛选比例
        self.points = points
        self.unit_con = int(unit_con)   # 单位换算，如果是kw，就是1000，是mw就是1

        # 电场参数
        self.cap_feng = cap_feng  # 风电场总装机

        # 机组性能参数
        self.soc0 = int(soc0 * 100)  # 初始soc值
        self.energy_q = energy_q  # 储能容量, MW
        self.p_ch = int(p_ch / self.unit_con / (self.points / 24) / self.energy_q * 100)     # 最大充电功率, 换算成15分钟的soc
        self.p_f = int(p_f / self.unit_con / (self.points / 24) / self.energy_q * 100)    # 最大放电功率,换算成15分钟的soc
        self.energy_loss = energy_loss  # 充放电损失，只算一次, %
        self.min_power = int(min_power * 100)    # 最小剩余电量, %
        self.max_power = int(max_power * 100)     # 最大剩余电量, %

    def _pre_data(self, data):
        '''
        处理价格数据
        '''
        # data = data.rename(columns={"实际": 'price'})
        data = pd.DataFrame(data)
        data['日期'] = data['date_time'].map(lambda s: str(s).split(' ')[0])
        data['_时间'] = data['date_time'].map(lambda s: str(s).split(' ')[1])
        # data['时间'] = data.reset_index(drop=True).index
        # print(data.reset_index(drop=True))
        data['时间'] = data['_时间'].map(
            lambda s: int(int(str(s).split(":")[0]) * 4 + (int(str(s).split(":")[1]) / 15) + 1))
        return data
    def _pre_power(self, data):
        '''
        处理功率曲线数据
        '''
        if data == {}:
            data = pd.DataFrame()
        else:
            data = pd.DataFrame(data)
            data['日期'] = data['date_time'].map(lambda s: str(s).split(' ')[0])
            data['_时间'] = data['date_time'].map(lambda s: str(s).split(' ')[1])
            data['时间'] = data.reset_index(drop=True).index
        return data

    def incomes(self, prices, point):
        max_change = min(self.p_ch, self.p_f)
        soc_point = 101
        delta_soc0 = self.soc0
        min_p = min(self.soc0, self.min_power)
        max_p = max(self.soc0, self.max_power)
        enable_state = np.zeros((soc_point, point + 1))
        for i in range(point + 1):
            enable_state[max(min(delta_soc0, max(delta_soc0 - i * max_change, 0)), min_p): min(max(delta_soc0 + 1,
                                                                                   min(delta_soc0 + i * max_change + 1,
                                                                                       soc_point + 1)), max_p + 1), i] = 1
        step_income = np.zeros((soc_point, point + 1))
        step_income_path = np.zeros((soc_point, point + 1))
        # 度电成本
        step_cost = np.zeros((soc_point, point + 1))
        for row in prices.iterrows():
            t = int(row[1]['时间'])
            p = row[1]['price']
            tmp_p = prices[prices['时间'].astype(int) > t]['price'].values
            # 需要记录充到soc0时产生的成本
            for soc in np.where(enable_state[:, t] == 1)[0]:
                ahead_section = list(
                    range(max(0, soc - max_change), min(max_p + 1, soc + max_change + 1)))
                ahead_exist = np.where(enable_state[:, t - 1] == 1)[0]
                ahead_index = list(set(ahead_section).intersection(set(ahead_exist)))
                # 计算各个路径到达当前状态时候的收益
                tmp_incomes = []
                tmp_incomes1 = []
                tmp_costs = []
                if ahead_index:
                    for i in ahead_index:
                        if i <= soc:
                            tmp_fee = (soc - i) * 0.01 * self.energy_q * p
                            tmp_income = step_income[i, t - 1] - tmp_fee
                            if soc == 0:
                                tmp_cost = step_cost[i, t - 1]
                                tmp_incomes.append(step_income[i, t - 1])
                                tmp_incomes1.append(step_income[i, t - 1])
                            else:
                                if soc == i:
                                    tmp_cost = step_cost[i, t - 1]
                                else:
                                    # 如果上一时刻的成本为0 且soc0>min_power
                                    if step_cost[i, t - 1] == 0 and self.soc0 > self.min_power:
                                        tmp_cost = p
                                    else:
                                        # 算度电成本
                                        tmp_cost = (step_cost[i, t - 1] * i * 0.01 * self.energy_q + tmp_fee) / (
                                                    soc * 0.01 * self.energy_q)
                                # # tmp_cost = tmp_fee / ((soc - i) * 0.01 * self.energy_q)
                                # # 在后面找大于度电成本的价格点数
                                tmp_pr = (self.low_income + tmp_cost) / self.energy_loss
                                back_data = np.where(tmp_p > tmp_pr)[0].shape[0]
                                # time_n = soc / max_change
                                # if back_data >= time_n:
                                if back_data >= 1:
                                    # 如果充电，不允许充到max以上
                                    if soc > self.max_power:
                                        tmp_incomes1.append(-9999999999)
                                    else:
                                        tmp_incomes1.append(tmp_income)
                                else:
                                    if soc == i:
                                        tmp_incomes1.append(step_income[i, t - 1])
                                    else:
                                        tmp_incomes1.append(-9999999999)
                                tmp_incomes.append(tmp_income)

                            tmp_costs.append(tmp_cost)
                        else:
                            # 放电
                            tmp_fee = (i - soc) * 0.01 * self.energy_q * p * self.energy_loss
                            tmp_income = step_income[i, t - 1] + tmp_fee
                            # 放电不算成本
                            tmp_cost = step_cost[i, t - 1]
                            if soc < self.min_power:
                                tmp_incomes.append(-9999999999)
                                tmp_incomes1.append(-9999999999)
                            else:
                                # 当价格损耗后大于成本+度电收益筛选，才允许放电，否则不允许
                                if p >= (tmp_cost + self.low_income) / self.energy_loss:
                                    tmp_incomes.append(tmp_income)
                                    tmp_incomes1.append(tmp_income)
                                else:
                                    tmp_incomes.append(-9999999999)
                                    tmp_incomes1.append(-9999999999)
                            tmp_costs.append(tmp_cost)
                    if tmp_incomes1 and list(set(tmp_incomes1))[0] == -9999999999 and len(set(tmp_incomes1)) == 1:
                        # 所有充电的路都不给充
                        enable_state[soc, t] = 0
                    incomes = np.column_stack((ahead_index, tmp_incomes1))
                    max_index = np.argmax(tmp_incomes1)
                    step_income[soc, t] = tmp_incomes[max_index]
                    step_cost[soc, t] = tmp_costs[max_index]
                    step_income_path[soc, t] = incomes[max_index][0]
                else:
                    # 没有交集，表示前一步没有路可以走到soc，要把soc，t置为0
                    enable_state[soc, t] = 0
        max_id = np.argmax(step_income[:, point])
        max_income = step_income[max_id, point]
        soc_path = [max_id]
        income_path = [max_income]
        cost_path = [step_cost[max_id, point]]
        a = int(step_income_path[max_id, point])
        for t in range(point, 0, -1):
            soc_path.append(a)
            income_path.append(step_income[a, t - 1])
            cost_path.append(step_cost[a, t - 1])
            a = int(step_income_path[a, t - 1])

        return max_income, soc_path[::-1], income_path[::-1], cost_path[::-1]

    def get_result(self):
        '''
        看输入了几天，对应输出几天
        '''
        result = pd.DataFrame()
        prices = self.price_data[['date_time', 'price']].sort_values('date_time')
        points = prices.shape[0]
        # points = 96
        prices['时间'] = prices.reset_index(drop=True).index + 1
        max_income, soc_path, income_path, cost_path = self.incomes(prices, points)
        # result['time'] = prices['_时间'].values
        result['date_time'] = prices['date_time']
        result['price'] = prices['price'].values
        # data['soc'] = pd.Series(soc_path[1:]) + self.min_power# .map(lambda x: x + 20 if x > 70 else x   )
        # result['soc'] = np.sum([soc_path[1:], [self.min_power for i in range(len(soc_path[1:]))]], axis=0).tolist()
        if len(set(soc_path[1:])) == 1 and list(set(soc_path[1:]))[0] == 0:
            result['soc'] = self.soc0
        else:
            result['soc'] = soc_path[1:]
        result['incomes'] = income_path[1:]
        result['costs'] = cost_path[1:]
        # data['cost'] = cost_path[:-1]
        # 找到从20充到soc0的时刻点，从这往后，减去20冲到soc0的成本
        # 给出储能充放电功率和建议申报功率，先不给soc
        # 用此刻的soc-上一刻soc_1
        soc_1 = [self.soc0] + result['soc'][:-1].to_list()
        delta_soc = np.array(soc_1) - np.array(result['soc'])
        result['operate_power'] = delta_soc * 0.01
        result['operate_power'] = result['operate_power'].map(lambda x: 1 * self.unit_con * self.energy_q * x)
        result['soc'] = soc_1
        # data['operate_power'] = data['operate_power'].to_list()
        # if result.empty:
        #     result = result.copy(deep=True)
        # else:
        #     result = pd.concat([result, result])
        result['charge_power'] = result['operate_power'] * 4
        if not self.power_data.empty:
            powers = self.power_data[['date_time', '_时间', 'power']].sort_values('date_time')
            result['declare_power'] = powers['power'] * self.unit_con + result['charge_power']

        result = result.set_index('date_time')
        logger.info("------------------蒙西储能策略模型计算完成------------------------")
        return result.to_dict()

if __name__ == '__main__':

    start_time = datetime.datetime.now()
    print(f"开始时间{datetime.datetime.now()}")
    price_data = pd.read_excel(r"D:\02file\000code\2023\0817dataresearch\data_research\多伦风场_优化\20230614_zy\data_input/储能模型充放测算.xlsx", sheet_name='价格')[['time', '价格']]
    price_data['date_time'] = '2023-08-24 ' + price_data['time']
    del price_data['time']
    price_data['价格'] = price_data['价格'].fillna(method='ffill')
    price_data = price_data.rename(columns={'价格': 'price'})  #
    price_data['date_time'] = pd.to_datetime(price_data['date_time']).astype(str)
    power_data = {}
    pre_all = pd.DataFrame()
    # n_list = [0, 100, 150, 200, 250, 300, 400, 500]
    n_list = [0]
    for n in n_list:
        print(f"-------------------{n}----------------")
        # n=500
        tmp_result = pd.DataFrame()
        op = MxEnergy(price_data, power_data, soc0=0.9, energy_q=3, cap_feng=20, p_ch=1, p_f=9, energy_loss=0.88,
                      min_power=0.2, max_power=0.98, points=96, low_income=n, unit_con=1)
        result = pd.DataFrame(op.get_result())
        result['n'] = n
        if pre_all.empty:
            pre_all = result.copy(deep=True)
        else:
            pre_all = pd.concat([pre_all, result])
    # print(pd.DataFrame(result))
    # print(pre_all)
    # pre_all.to_excel(r'./data_output\多伦测算3.xlsx')
    print(f"结束时间{datetime.datetime.now()}")
    print(f"用时{datetime.datetime.now() - start_time}")
    # print(power_data.to_dict('list'))











