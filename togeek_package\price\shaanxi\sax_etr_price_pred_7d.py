#!/usr/bin/env python
# coding: utf-8
import numpy as np
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
import datetime
import warnings
import logging
import json
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class EtrPredict7d:
    def __init__(self, data, pred_list, threshold_value=None, quantile_night=0.5, quantile_day=0.75):
        logger.info("----------------------极端随机树ETR价格预测--------------------------")
        self.province = "陕西"
        self.pred_list = pred_list
        self.threshold_value = threshold_value
        self.quantile_night = quantile_night
        self.quantile_day = quantile_day
        data, flag, msg = self.pre_source(data)
        # logger.info(f"数据校验结果flag：{flag, msg}")
        self.data, self.features = self._validate_data(data)

    def pre_source(self, source_data):
        """
        数据字段校验 + 数据整理
        :param source_data: json格式, 市场边界条件
        :return:
        """
        msg = ""
        flag = True

        # 1. json --> pd.DataFrame
        data = pd.DataFrame.from_dict(source_data, orient='index').T
        data['date'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[0])
        # data['week'] = pd.to_datetime(data['date']).dt.weekday.map(lambda s: int(s) + 1)
        date_lst = data['date'].tolist()
        columns = data.columns.tolist()

        # 1. 预测日期列表校验
        # 判断传入的数据中包含要预测日期的数据
        if set(self.pred_list) < set(date_lst):
            msg += "--’source_data‘中包括预测日的数据，检查通过--\n"
        elif set(self.pred_list) == set(date_lst):
            msg += "--’source_data‘中缺少历史日期的数据，请检查！\n"
            flag *= False
        else:
            msg += "--’source_data‘中预测日的数据不完整，请检查！\n"
            flag *= False

        # 2. 新能源数据校验
        for col in ['日前新能源负荷预测', '日前风电负荷预测', '日前光伏负荷预测']:
            num_null = data[col].isnull().sum()
            if num_null == 0:
                msg += f"----'{col}'数据不存在缺失值，检查通过；"
            else:
                msg += f"----'{col}'数据存在缺失值，请检查！\n"
                flag *= False

        # 3. 统调负荷、日前联络线计划, 日前发电总出力, 日前水电出力, 日前非市场化机组出力
        for col in ['负荷预测', '日前联络线计划', '日前发电总出力', '日前水电出力', '日前非市场化机组出力']:
            if col in columns:
                num = data[col].isnull().sum()

                if num > 0:
                    if num % 96 == 0:
                        n = num // 96
                        lst = data[col].dropna().tolist()
                        data[col] = lst + lst[-96:] * n  # 用最后一天的数据填充缺失值
                        msg += f"'{col}'缺少{n}天的数据，已经使用最近一天的数据补充完整；"
                    else:
                        msg += f"----'{col}'的历史数据有缺失值，请检查！\n"
                        flag *= False
        if flag:
            # 2. 计算竞价空间
            data['火电竞价空间'] = data['负荷预测'] - data['日前新能源负荷预测'] - data['日前联络线计划'] - data['日前水电出力']
            # data['date_time'] = data['date_time'].astype(str)
            data.set_index('date_time', inplace=True)
            data.sort_index(inplace=True)  # 升序排列
            data = data.drop('date', axis=1)
            return data, flag, msg
        else:
            return data, flag, msg

    def _validate_data(self, data):
        yuce_all_keys = list(data.columns)
        yuce_all_keys.remove("日前价格")
        yuce_all_keys.remove("实时价格")

        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        # logger.info(f"数据字段：{data.tail(10)}")
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        # data['星期'] = pd.to_datetime(data['日期']).dt.weekday

        # 特征字段，不包含价格
        features = yuce_all_keys + ['小时']
        return data, features

    def predict_day_price(self, days=30, n_est=100, max_depth=8, min_price=40, max_price=1000):
        all_data = self.data
        features_base = self.features
        pred_list = self.pred_list
        pred_list.sort()            # 将预测日期升序排
        time_points = len(all_data['时间'].unique())  # 传入数据时点

        # 阈值参数
        none_threshold_value = True if self.threshold_value is None else False

        # 创建空DataFrame，用来预测结果存储
        pre_data = pd.DataFrame()
        hist_data = all_data[all_data['日期'] < pred_list[0]]

        for dt in pred_list:
            # print(f'-------------------预测{dt}的数据-----------------')
            # 创建空Da-taFrame，用来预测结果存储
            tmp = pd.DataFrame()

            # 先进行"日前价格"，再进行"实时价格"
            for pred_type in ['日前价格', '实时价格']:
                # 实时价格预测时，将日前价格作为特征字段; 并且训练数据相较于日前价格，少一天
                if pred_type == '实时价格':
                    data_input = hist_data.dropna(subset=['日前价格', '实时价格'])
                    features = features_base + ['日前价格']
                    end_date_train = str(pd.to_datetime(dt) - datetime.timedelta(1)).split(' ')[0]
                else:
                    data_input = hist_data.dropna(subset=['日前价格'])
                    features = features_base
                    end_date_train = dt

                train_all = data_input[data_input['日期'] < end_date_train]  # 所有日前价格/实时价格非空的训练数据
                # 如下判断目的：减少日前价格训练数据的损失
                if pred_type == '日前价格':
                    del train_all['实时价格']
                    train_all = train_all.dropna()  # 筛选非null样本作为训练数据
                else:
                    train_all = train_all.dropna()  # 筛选非null样本作为训练数据
                train = train_all[-days * time_points:]  # 取非空传入数据的最后30天数据作为训练数据

                pred = all_data[all_data['日期'] == dt]  # 预测数据

                # 划分数据集
                X_train = train[features]
                if X_train.shape[0] == 0:
                    raise ValueError('删除null值后，训练数据为空，请检查传入数据')

                X_pred = pred[features]
                if pred_type == '实时价格':
                    X_pred['日前价格'] = tmp['日前价格预测值']
                # 判断预测日训练数据是否含NaN值
                if not X_pred.isnull().sum().sum() == 0:
                    raise ValueError('预测日数据含NaN值，请检查!')
                if X_pred.shape[0] == 0:
                    raise ValueError('预测日无数据，请检查!')

                y_train = train[[pred_type]]

                logger.info(f'---------------------------{pred_type}------------------------')
                # logger.info(f'X_train：\n{X_train}')
                # logger.info(f'训练数据字段：{list(X_train.columns)}')
                # logger.info(f'训练数据NaN值数量：{X_train.isnull().sum().sum()}')

                # 模型训练、预测
                etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=8099)
                etr.fit(X=X_train, y=y_train)
                res = etr.predict(X=X_pred)  # np.array类型
                # 预测结果处理
                result = pred[[pred_type]]
                result[pred_type + '预测值'] = res

                # 对预测值进行最大最小值修正
                result[pred_type + '预测值'] = result[pred_type + '预测值'].map(
                    lambda s: min_price if s < min_price else max_price if s > max_price else s)

                train2 = train_all[-7 * time_points:]
                min_price2 = train2[train2[pred_type] <= 50][pred_type].quantile(0.5)

                # 经分析在时间段[0-18]之间，易出现价格下限值（陕西40），将这个区间分为两段[0-7]和[7-18], 通过竞价空间来修正预测结果
                # (18, 24]之间，通过竞价空间，修正价格上限值（陕1000）
                if none_threshold_value:
                    train_zero = train2[train2[pred_type] <= min_price2]
                    train_zero_01 = train_zero[(train_zero['小时'] >= 0) & (train_zero['小时'] <= 7)]
                    train_zero_02 = train_zero[(train_zero['小时'] > 7) & (train_zero['小时'] <= 18)]

                    threshold_value_01 = train_zero_01['火电竞价空间'].quantile([self.quantile_night]).values[0]
                    threshold_value_02 = train_zero_02['火电竞价空间'].quantile([self.quantile_day]).values[0]

                    result[['小时', '火电竞价空间']] = pred[['小时', '火电竞价空间']]

                    # 最大值最小值修正
                    if threshold_value_01 != np.NaN:
                        result.loc[((result['小时'] <= 7) & (result['火电竞价空间'] <= threshold_value_01)),
                                   pred_type + '预测值'] = min_price2
                    if threshold_value_02 != np.NaN:
                        result.loc[((result['小时'] > 7) & (result['小时'] <= 18) &
                                    (result['火电竞价空间'] <= threshold_value_02)), pred_type + '预测值'] = min_price2

                result = result[[pred_type + '预测值']]
                tmp = pd.concat([tmp, result], axis=1)
            pre_data = pd.concat([pre_data, tmp])
        pre_data = pre_data.reset_index()
        pre_data.columns = ['date_time', 'ahead_pred_price', 'real_pred_price']
        logger.info("-------------------------End of ETR ---------------------------------")
        logger.info(f"result: \n {pre_data}")
        return pre_data


if __name__ == '__main__':
    from datetime import timedelta, date
    all_data = pd.read_excel(r"E:\python\tmpdata\陕煤陕西价格预测01.xlsx")
    all_data['date_time'] = all_data['date_time'].astype(str)
    res = pd.DataFrame()
    for i in range(1):
        
        pred_list = [str(date(2025, 3, 25) + timedelta(i+k)) for k in range(7)]
        try:
            etr = EtrPredict7d(data=all_data.to_dict('list'),
                               pred_list=pred_list)
            tmp_res = etr.predict_day_price(days=7)
            tmp_res['run_date'] = (pd.to_datetime(pred_list[0])-timedelta(1)).strftime('%Y-%m-%d')
            res = pd.concat([res, tmp_res], axis=0)
        except Exception as e:
            print(pred_list, e)
    res.to_excel(r"E:\python\tmpdata\p陕煤陕西价格预测02.xlsx")

