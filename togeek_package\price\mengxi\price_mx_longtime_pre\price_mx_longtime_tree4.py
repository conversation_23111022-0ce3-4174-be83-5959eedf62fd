#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/8 13:41
# <AUTHOR> <PERSON><PERSON>
'''
蒙西中长期价格预测，4~8天，先预测竞价空间，再预测价格
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/9/25 16:47
# <AUTHOR> Dar<PERSON>
import numpy as np
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
from datetime import datetime, timedelta
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class ElPricePreLongtime48:
    def __init__(self, history_data, pred_data, province, date_list_yuce, min_value, max_value, jiedian_type='统一结算点'):
        logger.info("----------------------蒙西4~8天极端随机树ETR价格预测--------------------------")
        self.province = province
        self.date_list_yuce = date_list_yuce
        self.jiedian_type = jiedian_type
        self.min_value = min_value
        self.max_value = max_value
        self.pred_data = self._validate_data(self._pre_data(pred_data))
        self.history_data = self._validate_pre_data(self._pre_data(history_data))

    def _pre_data(self, data):
        d = pd.DataFrame(data)  # index为日期时间，str, 形如：'2020-08-08 00:15'
        if 'date_time' in d.columns:
            d.set_index('date_time', inplace=True)
        d.sort_index(inplace=True)  # 升序排列

        # 传入数据中若含有列"时间"和"日期"，将其删掉
        if '时间' in d.columns:
            d = d.drop('时间', axis=1)
        if '日期' in d.columns:
            d = d.drop('日期', axis=1)
        d = d.applymap(lambda x: float(x))  # 所有数据转为float类型
        return d

    def _validate_data(self, data):

        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        data['星期'] = pd.to_datetime(data['日期']).dt.weekday
        # 给东送取负值
        data['日前东送计划'] = data['日前东送计划'].map(lambda x: 0 - abs(x))
        data['预测火电竞价空间'] = data['统调负荷预测'] - data['日前新能源负荷预测'] + data['日前东送计划']
        return data

    def _validate_pre_data(self, data):
        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        data['星期'] = pd.to_datetime(data['日期']).dt.weekday
        # 给东送取负值
        data['实际东送计划'] = data['实际东送计划'].map(lambda x: abs(x))
        data['实际火电竞价空间'] = data['实际统调负荷'] - data['实际新能源负荷'] + data['实际东送计划']
        return data

    def pre_jingjia(self, data, target, date_pred_list, yer=1):
        '''
        极端随机树预测
        Args:
            target: 目标字段
            days_train:用于训练的天数

        Returns:

        '''
        # 1. 传入数据整理
        if "date_time" in data.columns:
            data.set_index("date_time", inplace=True)
        if 'date' not in data.columns:
            data['date'] = data.index.map(lambda x: str(x).split(' ')[0])
        data = data.sort_index()
        features = [col for col in data.columns if (col != target) & (col != 'date')]
        # 3. 遍历预测日，滚动预测
        data_pred_list = list()
        for idx, date_pred in enumerate(date_pred_list, 1):
            # 确定数据起止日期
            date_start_train = str(pd.to_datetime(date_pred) - timedelta(50)).split(" ")[0]
            date_end_train = str(pd.to_datetime(date_pred) - timedelta(int(yer))).split(" ")[0]
            print(
                f"[{idx:2}/{len(date_pred_list)}], date_pred: {date_pred}, train_data: {date_start_train} ~ {date_end_train}")

            # 筛选数据，划分数据集
            data_train = data[
                (data['date'] >= date_start_train) & (data['date'] <= date_end_train)].dropna()
            data_test = data[data['date'] == date_pred]
            del data_train['date']
            del data_test['date']
            train_X, train_y = data_train[features], data_train[[target]]
            test_X = data_test[features]
            df_pred = pd.DataFrame(index=data_test.index)

            # 数据标准化
            # scaler_X = StandardScaler().fit(train_X)
            # scaler_y = StandardScaler().fit(train_y)
            # train_X_std = scaler_X.transform(train_X)
            # train_y_std = scaler_y.transform(train_y)
            # test_X_std = scaler_X.transform(test_X)

            etr = ExtraTreesRegressor(max_depth=10, n_estimators=50, random_state=24)
            etr.fit(train_X, train_y)
            pred_std = etr.predict(test_X).reshape(-1, 1)
            # pred = scaler_y.inverse_transform(pred_std)  # 反向标准化
            # 当前模型预测结果保存
            df_pred[f'pre_{target}'] = pred_std

            data_pred_list.append(df_pred)

        # 4. 回测结果整理
        data_pred = pd.concat(data_pred_list)

        return data_pred


    def predict_day_price(self, days=50, n_est=100, max_depth=6):
        '''
        1、先预测竞价空间，再预测价格
        Parameters
        ----------
        days
        n_est
        max_depth
        min_value
        max_value

        Returns
        -------

        '''
        print('---------------------开始预测竞价空间----------------------')
        if 'date_time' not in self.pred_data.columns and 'date_time' not in self.history_data.columns:
            input_data = self.pred_data[['预测火电竞价空间']].merge(self.history_data[['实际火电竞价空间']], how='left', left_index=True, right_index=True)
        else:
            input_data = self.pred_data[['date_time', '预测火电竞价空间']].merge(self.history_data[['date_time', '实际火电竞价空间']], how='left', on='date_time')
            input_data = input_data.set_index('date_time')
        test_data = self.pre_jingjia(input_data, '实际火电竞价空间', self.date_list_yuce)
        test_data = test_data.rename(columns={'pre_实际火电竞价空间': '实际火电竞价空间'})
        jingjia_data = pd.concat([self.history_data[['实际火电竞价空间']], test_data[['实际火电竞价空间']]])
        jingjia_data = jingjia_data.merge(self.history_data[['价格']], how='left', left_index=True, right_index=True)
        # 得到预测竞价空间，预测价格
        features_base = ['实际火电竞价空间']
        date_list_yuce = self.date_list_yuce
        date_list_yuce.sort()  # 将预测日期升序排
        time_points = len(self.history_data['时间'].unique())  # 传入数据时点
        # 创建空DataFrame，用来预测结果存储
        pre_data = pd.DataFrame()
        for yuce_d1 in date_list_yuce:
            print('-------------------预测{}的数据-----------------'.format(yuce_d1))
            # 创建空DataFrame，用来预测结果存储
            pre_data1 = pd.DataFrame()
            data_input = jingjia_data.dropna(subset=['价格'])
            last_date = str(datetime.strptime(yuce_d1, "%Y-%m-%d") + timedelta(-1)).split(" ")[0]
            train_all = data_input[data_input.index < last_date]#.dropna()  # 所有实时价格非空的训练数据
            train = train_all[-days * time_points:]  # 取非空传入数据的最后30天数据作为训练数据
            pred = jingjia_data[jingjia_data.index.map(lambda x:str(x).split(' ')[0]) == yuce_d1]  # 预测数据
            # 划分数据集
            X_train = train[features_base]
            if X_train.shape[0] == 0:
                raise ValueError('删除null值后，训练数据为空，请检查传入数据')
            X_pred = pred[features_base]
            # 判断预测日训练数据是否含NaN值
            if not X_pred.isnull().sum().sum() == 0:
                raise ValueError('预测日数据含NaN值，请检查!')
            if X_pred.shape[0] == 0:
                raise ValueError('预测日无数据，请检查!')
            y_train = train[['价格']]
            logger.info(f'---------------------------价格------------------------')
            logger.info(f'X_train：\n{X_train}')
            logger.info(f'训练数据字段：{list(X_train.columns)}')
            logger.info(f'训练数据NaN值数量：{X_train.isnull().sum().sum()}')

            # 模型训练、预测
            etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)
            etr.fit(X=X_train, y=y_train)
            res = etr.predict(X=X_pred)  # np.array类型
            # 预测结果处理
            result = pd.DataFrame(index=pred.index)
            result['价格预测值'] = res

            # 对预测值进行最大最小值修正
            result['价格预测值'] = result['价格预测值'].map(
                lambda s: self.min_value if s < self.min_value else self.max_value if s > self.max_value else s)
            result = result[['价格预测值']]
            pre_data = pd.concat([pre_data, result])
        logger.info("-------------------------End of ETR ---------------------------------")
        logger.info(f"result: \n {pre_data}")
        return pre_data


if __name__ == '__main__':
    rq_data = pd.read_csv(rf"D:\02file\2023\05蒙西数据/data/0-2023-04-30预测边界数据48.csv")
    ss_data = pd.read_csv(rf"D:\02file\2023\05蒙西数据/data/0-2023-04-30实时边界数据48.csv")
    print(rq_data.head())
    print(ss_data.head())
    # print(ss_data)
    rq_data = rq_data.set_index('date_time')
    ss_data = ss_data.set_index('date_time')
    # print(rq_data.dropna().to_dict())
    print(ss_data.dropna().to_dict())
    # bid_date_list = pd.date_range('2023-09-20', '2023-09-24').map(lambda s: str(s).split(" ")[0]).values
    # # print(bid_date_list)
    # elprice = ElPricePreLongtime48(rq_data, ss_data, province='蒙西', date_list_yuce=bid_date_list, min_value=0, max_value=5180)
    # print(elprice.history_data.columns)
    # print(elprice.history_data.columns)
    # result = elprice.predict_day_price()
    # print(result.to_dict())

