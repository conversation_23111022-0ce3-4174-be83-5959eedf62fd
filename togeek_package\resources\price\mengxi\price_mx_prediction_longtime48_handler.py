#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/8 14:17
# <AUTHOR> <PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_longtime_pre import ElPricePreLongtime48
from tornado import gen, concurrent
from tglibs.easy_json import j2o


class PricePredictionLongtimeHandlerMX48(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())
        province = data.get('province', '蒙西')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        history_data = data['history_data']
        pred_data = data['pred_data']
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 5180)
        r = yield self.predict(history_data, pred_data, province, date_list_yuce, min_value, max_value, jiedian_type)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, history_data, pred_data, province, date_list_yuce, min_value, max_value, jiedian_type):
        elpd_val = ElPricePreLongtime48(history_data, pred_data, province, date_list_yuce, min_value, max_value, jiedian_type)
        result = elpd_val.predict_day_price()
        return result.to_dict()