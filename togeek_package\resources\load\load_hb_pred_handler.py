#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/10/26 13:28 
@Info    ：

'''

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.hebei.hb_load_daily import LoadPredHB


class LoadPredHanderHB(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        hist_weather = params.pop('hist_weather')
        hist_elec = params.pop('hist_elec')
        hist_meter_elec = params.pop('hist_meter_elec')
        hist_elec_hourly = params.pop('hist_elec_hourly')
        future_weather = params.pop('future_weather')
        future_meter_elec = params.pop('future_meter_elec')
        city_wgt = params.pop('city_wgt', {"石家庄": 0.4, "保定": 0.05, "沧州": 0.05, "衡水": 0.05, "邯郸": 0.05, "邢台": 0.4})
        m = LoadPredHB(hist_weather=hist_weather, hist_elec=hist_elec, hist_meter_elec=hist_meter_elec,
                       hist_elec_hourly=hist_elec_hourly, future_weather=future_weather,
                       future_meter_elec=future_meter_elec, city_wgt=city_wgt)
        result = m.run()
        self.write(result)
