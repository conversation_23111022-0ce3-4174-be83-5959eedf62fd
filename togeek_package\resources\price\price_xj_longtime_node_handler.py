#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/25 15:18
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.xinjiang.price_predict_node_weather import PredPriceWeatherXj

class PredPriceWeatherHandlerXj(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        weather_data = params.get('weather_data')
        price_data = params.get('price_data')
        holidays = params.get('holidays')
        pre_date = params.get('pre_date')
        pre_days = params.get('pre_days')
        target_cols = params.get('target_cols')
        pre_type = params.get('pre_type')
        # weather_data, price_data, holidays, pre_dates, pre_days, target_cols
        pred = PredPriceWeatherXj(weather_data=weather_data, price_data=price_data, holidays=holidays, pre_date=pre_date,
                                  pre_days=pre_days, target_cols=target_cols, pre_type=pre_type)
        result = pred.pre_price(json=True)
        self.write(result)
