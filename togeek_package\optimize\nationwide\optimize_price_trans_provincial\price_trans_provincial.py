# -*- coding: utf-8 -*-
# @Time    : 2022/11/16 13:56
# <AUTHOR> darlene
# @FileName: price_trans_provincial.py
# @Software: PyCharm
# price of trans-provincial
# 跨省输配电价格增长计算

import numpy as np
import pandas as pd
import networkx as nx
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class OptimizeProvincial:
    def __init__(self, areas, node_area, line_data, start, price, way):
        '''
        1、初始化参数
        2、初始化输入数据
        3、构造图
        4、计算
        '''
        logger.info("-------------------跨省输配电价格增长计算-----------------------------")
        self.areas = pd.DataFrame(areas).dropna()
        self.node_area = pd.DataFrame(node_area).dropna()
        self.line_data = self.pre_data(pd.DataFrame(line_data))
        self.start = start
        self.price = price
        self.way = way
        # 创建一个图，这个图仅仅是为了找邻居，取边的属性用
        self.G = self.make_graph()
        self.result = self.get_result()

    def pre_data(self, data):
        data1 = data.groupby(['起点省', '终点省'])['输电价格'].min().reset_index()
        data1 = data1.merge(data, how='left', on=['起点省', '终点省', '输电价格'])
        return data1



    def make_graph(self):
        '''
        构造一个图，并添加节点和边，以及边的权重
        '''
        # 添加大区
        G = nx.Graph()
        try:
            G.add_nodes_from(list(np.array(self.areas['大区名称'])))
            # 添加所有省，及省一级节点
            G.add_nodes_from(list(np.array(self.node_area['节点名称'])))
            # 添加大区内省与省之间的线路，应用大区的权
            for area in np.array(self.areas['大区名称']):
                # print(area)
                area_info = self.areas[self.areas['大区名称'] == area]
                area_point_node = self.node_area[self.node_area['节点所属区域'] == area]['节点名称']
                # 取电量电价、线损率、分享价格空间，区内用自己
                area_price, area_loss, area_share_price = area_info[['电量电价', '线损率', '分享价格空间']].values[0]
                # 大区内的点，两两要连起来，权以大区的权为准
                for point_node in area_point_node:
                    # 添加大区与省的线路，这个线路用以解决线路落到大区，本区内所有省都要支付区内跨省费用
                    G.add_edge(area, point_node, weight=0, price=area_price, loss=area_loss, share_price=area_share_price)
                    left_point = point_node
                    for right_point in area_point_node:
                        if left_point != right_point:
                            # 连接本区内的所有点
                            G.add_edge(left_point, right_point, weight=0, price=area_price, loss=area_loss,
                                       share_price=area_share_price)
            # 主干线路
            for edge in zip(self.line_data['起点省'], self.line_data['终点省'], self.line_data['输电价格'], self.line_data['线损率'], self.line_data['分享价格空间']):
                G.add_edge(edge[0], edge[1], weight=1, price=edge[2], loss=edge[3], share_price=edge[4])
        except Exception as err:
            logger.error('构造图出现问题:{}'.format(err))
        return G

    def get_name(self, data):
        # 获取data的所有行
        len_type = data.shape[0]
        # 存储所有的线路名称
        all_name = []
        # 依次遍历每一行的线路，找到其中有线路名称的，存到line_name里，没有就存空字符串
        for i in range(len_type):
            line_name = []
            line = data.loc[i]['传输路线'].split(',')
            for j in range(len(line) - 1):
                left_point = line[j]
                right_point = line[j + 1]
                if not self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)].empty:
                    line_name = line_name + list(
                        self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)]['线路名称'].values)
                elif not self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)].empty:
                    line_name = line_name + list(
                        self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)]['线路名称'].values)
                else:
                    line_name = line_name + ['']
            all_name.append(line_name)
        data['线路名称'] = all_name
        data['线路名称'] = data['线路名称'].map(lambda x: ','.join(x))
        return data

    # 最小费用
    def low_fee(self):
        # S:    已经找到最优值
        # T:    与S连接的，从这些值里找最小的，这个最小的就被认为找到了最优值
        # 初始化S，使用三个数组来保存
        S_point = [self.start]
        S_father = [[self.start]]
        S_weight = [self.price]

        # 初始化T，也要使用三个数组来保存
        T_point = []
        T_father = []
        T_weight = []
        s_ne = self.G[S_point[0]]  # 取了初始点的邻接点
        for s_p in s_ne:
            # 邻接点加入T，算权重
            T_point.append(s_p)
            T_father.append(S_father[0])
            # 父节点是初始点，依次算权重
            T_weight.append(
                S_weight[0] + S_weight[0] * s_ne[s_p]['loss'] / 100 / (1 - s_ne[s_p]['loss'] / 100) + s_ne[s_p][
                    'price'] + s_ne[s_p]['share_price'] * 2)

        # 每次操作T中最小的点
        while len(T_point) > 0:
            # 找最小权重
            min_index = T_weight.index(min(T_weight))
            min_point = T_point[min_index]
            min_father = T_father[min_index]
            min_weight = T_weight[min_index]

            # 移动到S
            S_point.append(T_point[min_index])
            S_father.append(T_father[min_index])
            S_weight.append(T_weight[min_index])
            T_point.pop(min_index)
            T_father.pop(min_index)
            T_weight.pop(min_index)

            # 找出这个最小直接连接的点，把这些点的值更新一下，然后添加到T中
            ne = self.G[min_point]
            for p in ne:
                if p in S_point:
                    continue
                if p in T_point:
                    p_index = T_point.index(p)
                    tmp_weight = min_weight + min_weight * ne[p]['loss'] / 100 / (1 - ne[p]['loss'] / 100) + ne[p][
                        'price'] + ne[p]['share_price'] * 2
                    if tmp_weight < T_weight[p_index]:
                        T_father[p_index] = min_father + [min_point]
                        T_weight[p_index] = tmp_weight
                else:
                    T_point.append(p)
                    tmp_father = min_father + [min_point]
                    T_father.append(tmp_father)
                    T_weight.append(
                        min_weight + min_weight * ne[p]['loss'] / 100 / (1 - ne[p]['loss'] / 100) + ne[p]['price'] +
                        ne[p]['share_price'] * 2)
                    # print(p, min_point, min_weight + min_weight*ne[p]['loss']/100/(1-ne[p]['loss']/100) + ne[p]['price'] + ne[p]['share_price']*2 , min_weight, ne[p]['loss'], ne[p]['price'], ne[p]['share_price'])

                # 合并一下
        df_low_fee = pd.DataFrame(np.array([S_point, S_father, S_weight]).T, columns=['省', '来源', '落地价格'])
        df_low_fee['来源'] = df_low_fee['来源'].map(lambda x: ','.join(x))
        df_low_fee['传输路线'] = df_low_fee['来源'] + ',' + df_low_fee['省']
        del df_low_fee['来源']
        del df_low_fee['省']
        df_low_fee = self.get_name(df_low_fee)
        return df_low_fee.to_dict('record')

    def get_stepname(self, S_point, S_father, S_weight, S_step):
        # 获取data的所有行
        len_type = len(S_point)
        data = pd.DataFrame()
        # 存储所有的线路名称
        all_name = []
        # 依次遍历每一行的线路，找到其中有线路名称的，存到line_name里，没有就存空字符串
        for i in range(len_type):
            line_list = []
            end_node = S_point[i]
            lines = S_father[i]
            lines = list(map(lambda x: x + [end_node], lines))
            for j in range(len(lines)):
                line_name = []
                for p in range(len(lines[j]) - 1):
                    left_point = lines[j][p]
                    right_point = lines[j][p + 1]
                    if not self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)].empty:
                        line_name = line_name + list(
                            self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)]['线路名称'].values)
                    elif not self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)].empty:
                        line_name = line_name + list(
                            self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)]['线路名称'].values)
                    else:
                        line_name = line_name + ['']
                line_list.append(line_name)
            S_father[i] = lines
            all_name.append(line_list)
        # data['省'] = S_point
        data['传输路线'] = S_father
        data['落地价格'] = S_weight
        data['步数'] = S_step
        data['线路名称'] = all_name
        # data['线路名称'] = data['线路名称'].map(lambda x: ','.join(x))
        return data

    # 最小步数
    def low_step(self):
        # S:    已经找到最优值
        # S_point = ['辽宁', '北京', '湖北', '湖南']
        # S_father = [[['辽宁']],
        #             [['辽宁']],
        #             [['辽宁']],
        #             [['辽宁', '北京']
        #              ['辽宁', '湖北']]]
        # S_weight = [[100],
        #             [100+20],
        #             [100+50],
        #             [100+20+10,100+50+10]]
        # S_step = [0,1,1,2]
        # T:    与S连接的，从这些值里找最小的，这个最小的就被认为找到了最优值
        # 初始化S，使用三个数组来保存
        S_point = [self.start]
        S_father = [[[self.start]]]
        S_weight = [[self.price]]
        S_step = [0]

        # 初始化T，也要使用三个数组来保存
        T_point = []
        T_father = []
        T_weight = []
        T_step = []
        s_ne = self.G[S_point[0]]  # 取了初始点的邻接点
        for s_p in s_ne:
            T_point.append(s_p)
            T_father.append(S_father[0])
            T_weight.append([S_weight[0][0] + S_weight[0][0] * s_ne[s_p]['loss'] / 100 / (
                        1 - s_ne[s_p]['loss'] / 100) + s_ne[s_p]['price'] + s_ne[s_p]['share_price']])
            T_step.append(S_step[0] + 1)

        # 每次操作T中最小的点
        # i = 0
        while len(T_point) > 0:
            # i = i+1
            # print(i ,"_000", S_point)
            # print(i ,"_000", T_point)
            # print(i ,"_000", S_father)
            # print(i ,"_000", T_father)
            # print(i ,"_000", S_weight)
            # print(i ,"_000", T_weight)
            # print(i ,"_000", S_step)
            # print(i ,"_000", T_step)
            # 找最小
            min_index = T_step.index(min(T_step))
            min_point = T_point[min_index]
            min_father = T_father[min_index]
            min_weight = T_weight[min_index]
            min_step = T_step[min_index]

            # 移动到S
            S_point.append(T_point[min_index])
            S_father.append(T_father[min_index])
            S_weight.append(T_weight[min_index])
            S_step.append(T_step[min_index])
            T_point.pop(min_index)
            T_father.pop(min_index)
            T_weight.pop(min_index)
            T_step.pop(min_index)

            # 找出这个最小直接连接的点，把这些点的值更新一下，然后添加到T中
            ne = self.G[min_point]
            for p in ne:
                if p in S_point: continue
                if p in T_point:
                    p_index = T_point.index(p)
                    tmp_step = min_step + 1
                    if tmp_step == T_step[p_index]:
                        # 对于相同的最小值，只能更新内容，不能新建一级下标
                        # 从父节点来的weight，无论有几个，都只能附加在二级下标
                        for mw in min_weight:
                            T_weight[p_index].append(
                                mw + mw * ne[p]['loss'] / 100 / (1 - ne[p]['loss'] / 100) + ne[p]['price'] + ne[p][
                                    'share_price'])
                        # 从父节点继承father路径，在二级下标中附加
                        for mf in min_father:
                            t_mf = mf + [min_point]
                            T_father[p_index].append(t_mf)
                        T_step[p_index] = tmp_step
                else:
                    T_point.append(p)
                    # 对于新增的点，从父节点开始计算。父节点如果有多个内容，则计算多遍。
                    # 计算完成后新增一个一级下标
                    tmp_weight = []
                    for mw in min_weight:
                        tmp_weight.append(
                            mw + mw * ne[p]['loss'] / 100 / (1 - ne[p]['loss'] / 100) + ne[p]['price'] + ne[p][
                                'share_price'])
                    T_weight.append(tmp_weight)
                    tmp_father = []
                    for mf in min_father:
                        t_mf = mf + [min_point]
                        tmp_father.append(t_mf)
                    T_father.append(tmp_father)
                    T_step.append(min_step + 1)
                # 合并一下
        # df_low_step = pd.DataFrame()

        df_low_step = self.get_stepname(S_point, S_father, S_weight, S_step)

        return df_low_step.to_dict('records')

    # 最小线损率
    def low_loss(self):
        node_list = list(self.G.nodes)
        result_low_loss = []
        for node in node_list:
            out_put = {}
            try:
                line = nx.dijkstra_path(self.G, source=self.start, target=node, weight='loss')
                min_loss = nx.dijkstra_path_length(self.G, source=self.start, target=node, weight='loss')
                p = self.price
                line_name = []
                for i in range(0, len(line) - 1):
                    left_point = line[i]
                    right_point = line[i + 1]
                    weight = self.G[left_point][right_point]
                    p = p + p * weight['loss'] / 100 / (1 - weight['loss'] / 100) + weight['price'] + weight[
                        'share_price']
                    if not self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)].empty:
                        line_name = line_name + list(
                            self.line_data[(self.line_data['起点省'] == left_point) & (self.line_data['终点省'] == right_point)]['线路名称'].values)
                    elif not self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)].empty:
                        line_name = line_name + list(
                            self.line_data[(self.line_data['起点省'] == right_point) & (self.line_data['终点省'] == left_point)]['线路名称'].values)
                    else:
                        line_name = line_name + ['']
                # print(line, min_loss, p, line_name)
                out_put['传输路线'] = line
                out_put['线损率'] = min_loss
                out_put['落地价格'] = p
                out_put['线路名称'] = line_name
                result_low_loss.append(out_put)
            except Exception as err:
                # logger.error('计算最小折损率报错:', err)
                continue
        return result_low_loss

    def get_result(self):
        result = {}
        try:
            if 1 in self.way:
                result['线损率最低'] = self.low_loss()
            if 2 in self.way:
                result['落地价最低'] = self.low_fee()
            if 3 in self.way:
                result['输电线路最短'] = self.low_step()
        except Exception as err:
            logger.error('计算过程出现错误,', err)
        return result


if __name__ == "__main__":
    # areas, node_area, line_data, start, price, way=[1, 2, 3]
    # areas = pd.read_excel(r'D:\02file\000code\2022\0928dataresearch-省间\data_research\optimize_省间输电计算\赋权图信息.xlsx', sheet_name='大区信息')
    # node_area = pd.read_excel(r'D:\02file\000code\2022\0928dataresearch-省间\data_research\optimize_省间输电计算\赋权图信息.xlsx', sheet_name='节点信息')
    # line_data = pd.read_excel(r'D:\02file\000code\2022\0928dataresearch-省间\data_research\optimize_省间输电计算\赋权图信息.xlsx', sheet_name='边信息')
    # start = '湖北'
    # price = 470
    node_area = []
    price = 44
    start = "安徽"
    areas = []
    way = [
        1,
        2,
        3
    ]
    line_data = [
        {
            "分享价格空间": 0.0,
            "线损率": 7.0,
            "线路名称": "锦苏直流",
            "终点区域": "华东",
            "终点地市": "宿州",
            "终点省": "江苏",
            "起点区域": "华中",
            "起点地市": "西昌",
            "起点省": "四川",
            "输电价格": 51.1
        },
        {
            "分享价格空间": 4.53,
            "线损率": 7.2,
            "线路名称": "天中直流",
            "终点区域": "华中",
            "终点地市": "郑州",
            "终点省": "河南",
            "起点区域": "西北",
            "起点地市": "哈密",
            "起点省": "新疆",
            "输电价格": 61.3
        },
        {
            "分享价格空间": 4.06,
            "线损率": 6.5,
            "线路名称": "宾金直流",
            "终点区域": "华东",
            "终点地市": "金华",
            "终点省": "浙江",
            "起点区域": "华中",
            "起点地市": "宜宾",
            "起点省": "四川",
            "输电价格": 45.4
        },
        {
            "分享价格空间": 5.55,
            "线损率": 4.26,
            "线路名称": "灵绍直流",
            "终点区域": "华东",
            "终点地市": "绍兴",
            "终点省": "浙江",
            "起点区域": "西北",
            "起点地市": "灵武",
            "起点省": "宁夏",
            "输电价格": 65.9
        },
        {
            "分享价格空间": 9.86,
            "线损率": 4.14,
            "线路名称": "祁韶直流",
            "终点区域": "华中",
            "终点地市": "湘潭",
            "终点省": "湖南",
            "起点区域": "西北",
            "起点地市": "酒泉",
            "起点省": "甘肃",
            "输电价格": 60.2
        },
        {
            "分享价格空间": 0.0,
            "线损率": 7.0,
            "线路名称": "鲁固直流",
            "终点区域": "华北",
            "终点地市": "青州",
            "终点省": "山东",
            "起点区域": "东北",
            "起点地市": "扎鲁特旗",
            "起点省": "内蒙古",
            "输电价格": 21.78
        },
        {
            "分享价格空间": 0.0,
            "线损率": 3.32,
            "线路名称": "锡泰直流",
            "终点区域": "华东",
            "终点地市": "泰州",
            "终点省": "江苏",
            "起点区域": "华北",
            "起点地市": "锡盟",
            "起点省": "内蒙古",
            "输电价格": 84.16
        },
        {
            "分享价格空间": 0.0,
            "线损率": 7.0,
            "线路名称": "雁淮直流",
            "终点区域": "华东",
            "终点地市": "淮安",
            "终点省": "江苏",
            "起点区域": "华北",
            "起点地市": "忻州",
            "起点省": "山西",
            "输电价格": 74.8
        },
        {
            "分享价格空间": 0.0,
            "线损率": 7.0,
            "线路名称": "吉泉直流",
            "终点区域": "华东",
            "终点地市": "宣城",
            "终点省": "安徽",
            "起点区域": "西北",
            "起点地市": "昌吉",
            "起点省": "新疆",
            "输电价格": 82.9
        },
        {
            "分享价格空间": 0.0,
            "线损率": 6.5,
            "线路名称": "昭沂直流",
            "终点区域": "华北",
            "终点地市": "临沂",
            "终点省": "山东",
            "起点区域": "西北",
            "起点地市": "鄂尔多斯",
            "起点省": "内蒙古",
            "输电价格": 58.95
        },
        {
            "分享价格空间": 0.0,
            "线损率": 5.83,
            "线路名称": "青豫直流",
            "终点区域": "华中",
            "终点地市": "驻马店市",
            "终点省": "河南",
            "起点区域": "西北",
            "起点地市": "海南藏族自治州",
            "起点省": "青海",
            "输电价格": 64.89
        },
        {
            "分享价格空间": 0.0,
            "线损率": 6.0,
            "线路名称": "雅湖直流",
            "终点区域": "华中",
            "终点地市": "抚州市",
            "终点省": "江西",
            "起点区域": "华中",
            "起点地市": "甘孜藏族自治州",
            "起点省": "四川",
            "输电价格": 76.2
        },
        {
            "分享价格空间": 0.0,
            "线损率": 5.0,
            "线路名称": "陕武直流",
            "终点区域": "华中",
            "终点地市": "武汉",
            "终点省": "湖北",
            "起点区域": "西北",
            "起点地市": "榆林",
            "起点省": "陕西",
            "输电价格": 57.0
        },
        {
            "分享价格空间": 0.0,
            "线损率": 0.0,
            "线路名称": "建苏直流",
            "终点区域": "华东",
            "终点地市": "苏州",
            "终点省": "江苏",
            "起点区域": "华中",
            "起点地市": "凉山",
            "起点省": "四川",
            "输电价格": 0.0
        },
        {
            "分享价格空间": 0.0,
            "线损率": 0.0,
            "线路名称": "复奉直流",
            "终点区域": "华东",
            "终点地市": "上海",
            "终点省": "上海",
            "起点区域": "华中",
            "起点地市": "宜宾",
            "起点省": "四川",
            "输电价格": 0.0
        },
        {
            "分享价格空间": 2.25,
            "线损率": 1.0,
            "线路名称": "灵宝直流",
            "终点区域": "西北",
            "终点地市": "宝鸡",
            "终点省": "陕西",
            "起点区域": "华中",
            "起点地市": "灵宝",
            "起点省": "河南",
            "输电价格": 40.3
        },
        {
            "分享价格空间": 2.23,
            "线损率": 3.0,
            "线路名称": "德宝直流",
            "终点区域": "西北",
            "终点地市": "宝鸡",
            "终点省": "陕西",
            "起点区域": "华中",
            "起点地市": "德阳",
            "起点省": "四川",
            "输电价格": 33.6
        },
        {
            "分享价格空间": 1.53,
            "线损率": 1.7,
            "线路名称": "高岭直流",
            "终点区域": "华北",
            "终点地市": "京津唐",
            "终点省": "京津唐",
            "起点区域": "东北",
            "起点地市": "葫芦岛",
            "起点省": "辽宁",
            "输电价格": 23.5
        },
        {
            "分享价格空间": 6.5,
            "线损率": 7.5,
            "线路名称": "龙政直流",
            "终点区域": "华东",
            "终点地市": "常州",
            "终点省": "江苏",
            "起点区域": "华中",
            "起点地市": "宜昌",
            "起点省": "湖北",
            "输电价格": 67.5
        },
        {
            "分享价格空间": 4.2,
            "线损率": 7.5,
            "线路名称": "葛南直流",
            "终点区域": "华东",
            "终点地市": "上海",
            "终点省": "上海",
            "起点区域": "华中",
            "起点地市": "宜昌",
            "起点省": "湖北",
            "输电价格": 55.8
        },
        {
            "分享价格空间": 3.2,
            "线损率": 7.5,
            "线路名称": "林枫直流",
            "终点区域": "华东",
            "终点地市": "上海",
            "终点省": "上海",
            "起点区域": "华中",
            "起点地市": "宜昌",
            "起点省": "湖北",
            "输电价格": 43.9
        },
        {
            "分享价格空间": 5.5,
            "线损率": 7.5,
            "线路名称": "宜华直流",
            "终点区域": "华东",
            "终点地市": "上海",
            "终点省": "上海",
            "起点区域": "华中",
            "起点地市": "宜昌",
            "起点省": "湖北",
            "输电价格": 68.5
        },
        {
            "分享价格空间": 3.2,
            "线损率": 7.65,
            "线路名称": "江城直流",
            "终点区域": "#N/A",
            "终点地市": "",
            "终点省": "南网",
            "起点区域": "华中",
            "起点地市": "宜昌",
            "起点省": "湖北",
            "输电价格": 38.5
        },
        {
            "分享价格空间": 2.69,
            "线损率": 7.0,
            "线路名称": "宁东直流（银东直流）",
            "终点区域": "华北",
            "终点地市": "青岛",
            "终点省": "山东",
            "起点区域": "西北",
            "起点地市": "银川",
            "起点省": "宁夏",
            "输电价格": 50.8
        },
        {
            "分享价格空间": 0.0,
            "线损率": 0.0,
            "线路名称": "宜昌背靠背直流",
            "终点区域": "华中",
            "终点地市": "宜昌",
            "终点省": "湖北",
            "起点区域": "华中",
            "起点地市": "重庆",
            "起点省": "重庆",
            "输电价格": 0.0
        },
        {
            "分享价格空间": 0.0,
            "线损率": 0.0,
            "线路名称": "施州背靠背直流",
            "终点区域": "华中",
            "终点地市": "恩施",
            "终点省": "湖北",
            "起点区域": "华中",
            "起点地市": "重庆",
            "起点省": "重庆",
            "输电价格": 0.0
        },
        {
            "分享价格空间": 0.0,
            "线损率": 0.0,
            "线路名称": "长南Ⅰ线 1000kV",
            "终点区域": "华中",
            "终点地市": "南阳",
            "终点省": "河南",
            "起点区域": "华北",
            "起点地市": "长治",
            "起点省": "山西",
            "输电价格": 0.0
        }
    ]

    ptp = OptimizeProvincial(areas, node_area, line_data, start, price, way=[1, 2, 3])
    print(ptp.result)
