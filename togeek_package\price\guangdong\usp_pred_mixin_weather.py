"""
<AUTHOR>
@Date    ：2025/7/24
@Info    ：广东现货价格预测
        用于预测日前 价格/负荷 增加 D15 的天气预报数据
        数据处理：1. 所有日前边界 2. 气象数据
        数据聚合：1. 按小时聚合 2. 按天聚合
"""
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

import logging
logger = logging.getLogger()

REAL_IDX_NAME_LIST = [
    # real
    "火电竞价空间实际",  # 等于省内B类机组实际出力
    "系统实际负荷",
    "外送电实际负荷(粤港联络线)",
    "外来电实际负荷",
    "地方电源实际出力",
    "省内A类机组实际出力",
    "省内B类机组实际出力",
    "负备用实际负荷",
    "正备用实际负荷",
    # 部分日期缺少数据："新能源实际出力","水电实际负荷",
    # 全零数据："实际一次调频备用负荷","必停机组实际总容量","必开机组实际总容量",
    # 无数据："非市场化机组出力实际值","风电实际负荷","机组检修实际总容量",
]

DEF_IDX_NAME_LIST = [
    # ahead
    "火电竞价空间预测",  # 等于省内B类机组预测出力
    "必开机组预测总容量",
    "必停机组预测总容量",
    "机组检修预测总容量",
    "系统预测负荷",
    "外送电预测负荷(粤港联络线)",
    # "外来电预测负荷",
    "地方电源预测出力",
    "省内A类机组预测出力",
    "省内B类机组预测出力",
    "负备用预测负荷",
    "正备用预测负荷",
    "预测一次调频备用负荷",
    # price
    "日前统一结算点电价",
    "实时统一结算点电价",  # 15min
    "日前节点出清电价",
    "实时节点出清电价",  # 1h
    # 缺少指标：新能源预测负荷(xny_power_d)、xny_power_d1；抽蓄电站出力计划 pump_storage_power
    # 部分日期缺少数据："预测一次调频备用负荷",
] + REAL_IDX_NAME_LIST
DIFF_FEATURES = [
    "火电竞价空间预测",
    "必开机组预测总容量",
    # "必停机组预测总容量",
    "机组检修预测总容量",
    "系统预测负荷",
    # "外送电预测负荷(粤港联络线)",
    # "外来电预测负荷",
    "地方电源预测出力",
    # "省内A类机组预测出力",
    # "省内B类机组预测出力",
    "负备用预测负荷",
    # "正备用预测负荷",
    "预测一次调频备用负荷",
    # 用7日均值替代缺失值
    "火电竞价空间实际",
    "系统实际负荷",
    # "外送电实际负荷(粤港联络线)",
    # "外来电实际负荷",
    "地方电源实际出力",
    # "省内A类机组实际出力",
    # "省内B类机组实际出力",
    "负备用实际负荷",
    "正备用实际负荷",
    "地方电源实际出力",
    "日前出力",
    # "日前负荷",
    # "日前火电竞价空间",
    # "日前备用比例",
    "日前火电调节裕度",
    # "日前火电紧张",
    "实时出力",
    # "实时负荷",
    # "实时火电竞价空间",
    # "实时备用比例",
    "实时火电调节裕度",
    "火电竞价空间差值",
    "系统负荷差值",
    "负荷差值",
    "外来电差值",
    "正备用差值",
    # "负备用差值",
    # "A类机组差值",
    "地方电源差值",
    # "实时火电紧张",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    "holiday",
    "day_type",
    # "ahead_price_D1",
    # "ahead_price_D1_mean",
    # "ahead_price_D1_std",
    # "ahead_price_D1_median",
    # "ahead_weekly_mean",
    # "ahead_weekly_std",
    # "ahead_weekly_median",
    # "real_weekly_mean",
    # "real_weekly_std",
    # "real_weekly_median",
    "diff_price_D1",
    "diff_price_D1_mean",
    "diff_price_D1_std",
    "diff_price_D1_median",
    "diff_weekly_mean",
    "diff_weekly_std",
    "diff_weekly_median",
    "u_10m",
    "v_10m",
    # "t_80m",
    "q_80m",
    "pres_80m",
    # "u_80m",
    # "v_80m",
    # "tp_surface",
    # "vis_surface",
    "wspd_surface",
    "t_surface",
    # "csnow_surface",
    # "cicep_surface",
    # "cfrzr_surface",
    "crain_surface",
    "SUNSD_surface",
    # "uflx_surface",
    # "vflx_surface",
    "dswrf_surface",
    # "dlwrf_surface",
    # "uswrf_surface",
    # "ulwrf_surface",
    # "albedo_surface",
    "温度敏感区间",
    "合成风速_80m",
    "有效辐射强度",
    # "合成风速",
    # "风速区间",
    # "风速方向",
    # "平均风速_3",
    # "有效风速",
]
DEF_FEATURES = [
    "火电竞价空间预测",
    "必开机组预测总容量",
    "必停机组预测总容量",
    "机组检修预测总容量",
    "系统预测负荷",
    "外送电预测负荷(粤港联络线)",
    # "外来电预测负荷",
    "地方电源预测出力",
    "省内A类机组预测出力",
    # "省内B类机组预测出力",
    "负备用预测负荷",
    "正备用预测负荷",
    "预测一次调频备用负荷",
    # "地方电源实际出力_real_D2",
    # "省内A类机组实际出力_real_D2",
    # "省内B类机组实际出力_real_D2",
    # "系统实际负荷_real_D2",
    # "外来电实际负荷_real_D2",
    # "外送电实际负荷(粤港联络线)_real_D2",
    # 用7日均值替代缺失值
    "火电竞价空间实际",
    "系统实际负荷",
    "外送电实际负荷(粤港联络线)",
    "外来电实际负荷",
    "地方电源实际出力",
    "省内A类机组实际出力",
    # "省内B类机组实际出力",
    "负备用实际负荷",
    "正备用实际负荷",
    "地方电源实际出力",
    "日前出力",
    # "日前负荷",
    "日前备用比例",
    "日前火电调节裕度",
    "日前火电紧张",
    "实时出力",
    # "实时负荷",
    "实时备用比例",
    "实时火电调节裕度",
    "实时火电紧张",
    "火电竞价空间差值",
    "系统负荷差值",
    "负荷差值",
    "外来电差值",
    "正备用差值",
    "负备用差值",
    "A类机组差值",
    "地方电源差值",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    "holiday",
    "day_type",
    "ahead_price_D1",
    "ahead_price_D1_mean",
    "ahead_price_D1_std",
    "ahead_price_D1_median",
    "ahead_weekly_mean",
    "ahead_weekly_std",
    "ahead_weekly_median",
    # "real_weekly_mean",
    # "real_weekly_std",
    # "real_weekly_median",
    "diff_price_D1",
    "diff_price_D1_mean",
    "diff_price_D1_std",
    "diff_price_D1_median",
    "diff_weekly_mean",
    "diff_weekly_std",
    "diff_weekly_median",
    "u_10m",
    "v_10m",
    "t_80m",
    "q_80m",
    "pres_80m",
    "u_80m",
    "v_80m",
    "tp_surface",
    # "vis_surface",
    "wspd_surface",
    "t_surface",
    # "csnow_surface",
    # "cicep_surface",
    # "cfrzr_surface",
    "crain_surface",
    "SUNSD_surface",
    # "uflx_surface",
    # "vflx_surface",
    "dswrf_surface",
    # "dlwrf_surface",
    # "uswrf_surface",
    # "ulwrf_surface",
    # "albedo_surface",
    "温度敏感区间",
    "合成风速_80m",
    "有效辐射强度",
    "合成风速",
    # "风速区间",
    # "风速方向",
    # "平均风速_3",
    # "有效风速",
]

AHEAD_FEATS_LIST = [
    "火电竞价空间预测",
    "必开机组预测总容量",
    "必停机组预测总容量",
    "机组检修预测总容量",
    "系统预测负荷",
    "外送电预测负荷(粤港联络线)",
    # "外来电预测负荷",
    "地方电源预测出力",
    "省内A类机组预测出力",
    # "省内B类机组预测出力",
    "负备用预测负荷",
    "正备用预测负荷",
    "预测一次调频备用负荷",
    # 用7日均值替代缺失值
    "火电竞价空间实际",
    "系统实际负荷",
    # "日前出力",
    # "日前负荷",
    "日前备用比例",
    "日前火电调节裕度",
    "日前火电紧张",
    # "实时出力",
    # "实时负荷",
    # "实时备用比例",
    # "实时火电调节裕度",
    # "实时火电紧张",
    # "火电竞价空间差值",
    # "系统负荷差值",
    # "负荷差值",
    # "外来电差值",
    # "正备用差值",
    # "负备用差值",
    # "A类机组差值",
    # "地方电源差值",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    "holiday",
    "day_type",
    "ahead_price_D1",
    "ahead_price_D1_mean",
    "ahead_price_D1_std",
    "ahead_price_D1_median",
    "ahead_weekly_mean",
    "ahead_weekly_std",
    # "ahead_weekly_median",
    # "real_price_D2",
    # "real_price_D2_D1_mean",
    # "real_price_D2_D1_std",
    # "real_price_D2_D1_median",
    # "real_weekly_mean",
    # "real_weekly_std",
    # "real_weekly_median",
    # "diff_price_D1",
    # "diff_price_D1_mean",
    # "diff_price_D1_std",
    # "diff_price_D1_median",
    # "diff_weekly_mean",
    # "diff_weekly_std",
    # "diff_weekly_median",
    "u_10m",
    "v_10m",
    # "t_80m",
    # "q_80m",
    # "pres_80m",
    "u_80m",
    "v_80m",
    # "tp_surface",
    # "vis_surface",
    # "wspd_surface",
    # "t_surface",
    # "csnow_surface",
    # "cicep_surface",
    # "cfrzr_surface",
    "crain_surface",
    # "SUNSD_surface",
    # "uflx_surface",
    # "vflx_surface",
    # "dswrf_surface",
    # "dlwrf_surface",
    # "uswrf_surface",
    # "ulwrf_surface",
    # "albedo_surface",
    "温度敏感区间",
    "合成风速_80m",
    "有效辐射强度",
    "合成风速",
    # "风速区间",
    # "风速方向",
    # "平均风速_3",
    # "有效风速",
]

REAL_FEATS_LIST = [
    "火电竞价空间预测",
    "系统预测负荷",
    "正备用预测负荷",
    # "地方电源实际出力_real_D2",
    # "省内A类机组实际出力_real_D2",
    # "省内B类机组实际出力_real_D2",
    # "系统实际负荷_real_D2",
    # "外来电实际负荷_real_D2",
    # "外送电实际负荷(粤港联络线)_real_D2",
    # 用7日均值替代缺失值
    "火电竞价空间实际",
    "系统实际负荷",
    "外送电实际负荷(粤港联络线)",
    "外来电实际负荷",
    "地方电源实际出力",
    "省内A类机组实际出力",
    # "省内B类机组实际出力",
    "负备用实际负荷",
    "正备用实际负荷",
    "地方电源实际出力",
    # "日前出力",
    # "日前负荷",
    "日前备用比例",
    "实时出力",
    # "实时负荷",
    "实时备用比例",
    "实时火电调节裕度",
    "实时火电紧张",
    "火电竞价空间差值",
    "系统负荷差值",
    "负荷差值",
    # "外来电差值",
    # "正备用差值",
    # "负备用差值",
    # "A类机组差值",
    "地方电源差值",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    "holiday",
    "day_type",
    "ahead_price_D1_std",
    "ahead_weekly_std",
    # "real_price_D2",
    "real_price_D2_D1_mean",
    # "real_price_D2_D1_std",
    # "real_price_D2_D1_median",
    "real_weekly_mean",
    "real_weekly_std",
    "real_weekly_median",
    "u_10m",
    "v_10m",
    "t_80m",
    # "q_80m",
    # "pres_80m",
    "u_80m",
    "v_80m",
    # "tp_surface",
    # "vis_surface",
    "wspd_surface",
    "t_surface",
    # "csnow_surface",
    # "cicep_surface",
    # "cfrzr_surface",
    "crain_surface",
    # "SUNSD_surface",
    # "uflx_surface",
    # "vflx_surface",
    "dswrf_surface",
    # "dlwrf_surface",
    # "uswrf_surface",
    # "ulwrf_surface",
    # "albedo_surface",
    "温度敏感区间",
    "合成风速_80m",
    "有效辐射强度",
    "合成风速",
    # "风速区间",
    # "风速方向",
    # "平均风速_3",
    # "有效风速",
]



class DataFetcherMixin:
    def _data(self, D: str,df=None,df_holiday=None,df_gfs=None,freq="1h"):
        """
        获取基础数据
        :param D: 交易日
        :return: X、y，都是pd.DataFrame
        """
        df = pd.DataFrame.from_dict(df)
        df_holiday = pd.DataFrame.from_dict(df_holiday)
        df_gfs = pd.DataFrame.from_dict(df_gfs)
        df['dateTime']=pd.to_datetime(df['dateTime'])
        df_gfs['dateTime']=pd.to_datetime(df_gfs['dateTime'])

        # # D-5之前的统一结算点电价（15min）用节点出清电价(1h)替代
        # D_5 = datetime.strptime(D, "%Y-%m-%d") - timedelta(days=5)
        # D_5_str = datetime.strftime(D_5, "%Y-%m-%d")
        # df.loc[df["dateTime"] < D_5_str, "日前统一结算点电价"] = df["日前节点出清电价"]
        # df.loc[df["dateTime"] < D_5_str, "实时统一结算点电价"] = df["实时节点出清电价"]

        df = self._with_features(df)
        df = self._with_holiday_features(df,df_holiday=df_holiday)
        
        # 取D+1的天气数据
        future_d=15
        df_gfs_feats = self._weather_features(df_gfs, future_d=future_d,freq=freq)
        df_all = pd.merge(df, df_gfs_feats, on="dateTime", how="outer")
        df_all['date']=df_all['dateTime'].dt.date.astype(str)
        df_all['hour']=df_all['dateTime'].dt.hour

        # 运行日
        run_day = datetime.strptime(D, "%Y-%m-%d") + timedelta(days=1)
        run_day_str = datetime.strftime(run_day, "%Y-%m-%d")
        start = datetime.strptime(D, "%Y-%m-%d") - timedelta(days=30)
        start_str = datetime.strftime(start, "%Y-%m-%d")
        mask = (df_all["date"] >= start_str) & (df_all["date"] < run_day_str)
        train0 = df_all[mask]
        test = df_all[df_all['date'] == run_day_str]

        # 筛选出缺失值比例大于等于 30% 的列
        missing_ratio = train0.isna().mean()
        selected_columns = missing_ratio[missing_ratio < 0.30].index
        # 根据筛选结果保留相应的列
        train = train0[selected_columns]
        # 输出缺失值大于30%的列名
        if not missing_ratio[missing_ratio >= 0.30].empty:
            logger.warning("训练数据中缺失值大于30%的列：" + str(missing_ratio[missing_ratio >= 0.30].index))
        if "实时统一结算点电价" not in train.columns:
            logger.warning("--------------- 注意！实时统一结算点电价缺失数据较多，实时价格预测结果会受影响，注意检查数据 ---------------")
            train["实时统一结算点电价"]=train0["实时统一结算点电价"]
        train = train.dropna(axis=1, how="all")

        # 筛选值全部相同的列
        unique_values = train.nunique()
        constant_columns = unique_values[unique_values == 1].index.tolist()
        if constant_columns:
            logger.warning("训练数据中值全部相同的列有：" + str(constant_columns))
            # 删除值全部相同的列
            train = train.drop(columns=constant_columns)

        # 补全训练集缺失值
        for col in train.columns:
            train[col] = train.apply(
                lambda row: self.fill_missing_values(col, row, train) if pd.isna(row[col]) else row[col], axis=1
            )
        test = test[train.columns]

        if test.empty:
            raise ValueError("no test data")

        return train, test

    def _to_freq(self, df, freq):
        p_date_start = df["dateTime"].min().strftime("%Y-%m-%d")
        p_date_end = df["dateTime"].max().strftime("%Y-%m-%d")

        full_range = pd.date_range(
            start=p_date_start + " 00:00:00",
            end=p_date_end + " 23:59:59",
            freq=freq
        )

        hourly_df = pd.DataFrame({"dateTime": full_range})
        return pd.merge(hourly_df, df, on="dateTime", how="left")

    def _weather_features(self, df: pd.DataFrame, future_d, freq="15min") -> pd.DataFrame:
        """
        气象特征
        15天与35天数据不同，特征不同
        """
       
        # 构造完整小时级时间序列
        df = self._to_freq(df, freq)
        # 动态字段插值（温度、风速等）
        numeric_cols = df.select_dtypes(include=np.number).columns.tolist()

        df = df.set_index('dateTime')
        df[numeric_cols] = df[numeric_cols].interpolate(method='time').bfill()
        df = df.reset_index()
        
        if future_d>15:
            # 负荷预测：电力负荷通常与温度、湿度、时间周期强相关
            # 1. 温度特征
            # 1.1 温度敏感区间：将 t_2m 转换为分段特征（如 <5°C 低温供暖、5-27°C 舒适区、>27°C 高温制冷）。
            df["温度敏感区间"] = df["t_2m"].apply(lambda x: 0 if x < 5 else (1 if 5 <= x <= 27 else 2))
            # 1.2 日温差
            df["日温差"] = df["tmax_2m"] - df["tmin_2m"]
            # 1.3 体感温度
            # df["体感温度"] = 0.8 * df["t_2m"] + (df["rh_2m"] / 100) * (df["t_2m"] - 14.4) + 46.4

            # 2. 湿度特征
            # 高温高湿组合 当 t_2m > 28°C 且 rh_2m > 70% 时标记为 1，其他为0
            df["高温高湿"] = np.where((df["t_2m"] > 28) & (df["rh_2m"] > 70), 1, 0)

            # 光能出力预测：光伏发电依赖辐射强度、云量、温度等
            # 4. 云量衰减后辐射
            df["云量衰减后辐射"] = df["dswrf_surface"] * (100 - df["tcc_atmo"]) / 100
        else:
            df["温度敏感区间"] = df["t_surface"].apply(lambda x: 0 if x < 5 else (1 if 5 <= x <= 27 else 2))
            df["合成风速_80m"] = np.sqrt(df["u_80m"] ** 2 + df["v_80m"] ** 2)
            df["有效辐射强度"] = df["dswrf_surface"] * (100 - df["albedo_surface"]) / 100

        # 风能出力预测：风能出力与风速、风向、气压强相关
        # 3. 风速特征
        # 3.1 合成风速 风能与风速^3呈正比
        df["合成风速"] = np.sqrt(df["u_10m"] ** 2 + df["v_10m"] ** 2)
        # 3.2 风速区间 离散化为 0-3m/s（停转）、3-12m/s（工作区）、>12m/s（切出）【待确认】
        df["风速区间"] = df["合成风速"].apply(lambda x: 0 if x < 3 else (1 if 3 <= x < 12 else 2))
        # 3.3 风速方向
        df["风速方向"] = np.arctan2(df["v_10m"], df["u_10m"])
        # 3.4 风速统计特征 过去3小时平均风速（反应惯性）
        df["平均风速_3"] = df["合成风速"].rolling(3).mean()
        # 3.5 有效风速：风能与风速^3呈正比
        df["有效风速"] = df["合成风速"] ** 3

        return df

    def fill_missing_values(self, col, row, df):
        current_hour = row['hour']
        current_date = row['dateTime']
        # 计算 7 天前的日期
        seven_days_ago = current_date - pd.Timedelta(days=7)
        # 筛选出过去 7 天同一小时的数据
        historical_data = df[(df['dateTime'] >= seven_days_ago) & (
            df['dateTime'] < current_date) & (df['hour'] == current_hour)][col]

        # 若历史数据存在，则取平均值填充，否则保留缺失值
        if not historical_data.empty:
            return historical_data.mean()

        return row[col]

    def _to_hourly(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.resample("h").mean().reset_index()


    def _with_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特征工程
        """

        # 价差、价差方向特征
        df["日前价格>实时价格"] = np.where(df["日前统一结算点电价"] > df["实时统一结算点电价"], 1, 0)
        df["日前价格-实时价格"] = df["日前统一结算点电价"] - df["实时统一结算点电价"]
        df["大价差方向"] = np.where(df["日前价格-实时价格"] >= 100, 1, np.where(df["日前价格-实时价格"] <= -100, 2, 0))
        df["大价差"] = np.where(df["大价差方向"] >= 1, 1, 0)
        
        # 实时特征：用前七天均值替代还没有的值
        # 在 D-2 < df_real[df['date']] < run_day_str的数据，用ffill填充

        # 日前边界特征 - 供需特征
        # df["日前出力"] = df["省内A类机组预测出力"] + df["地方电源预测出力"] + df["外来电预测负荷"]
        df["日前负荷"] = df["系统预测负荷"] - df["外送电预测负荷(粤港联络线)"]
        # df["日前火电竞价空间"] = df["日前负荷"] - df["日前出力"]
        df["日前备用比例"] = (df["正备用预测负荷"] + df["负备用预测负荷"]) / df["系统预测负荷"]
        df["日前火电调节裕度"] = df["火电竞价空间预测"] / df["系统预测负荷"]
        df["日前火电紧张"] = (df["日前火电调节裕度"] < 0.5).astype(int)

        # 实时边界特征
        df["实时出力"] = df["省内A类机组实际出力"] + df["地方电源实际出力"] + df["外来电实际负荷"]
        df["实时负荷"] = df["系统实际负荷"] - df["外送电实际负荷(粤港联络线)"]
        # df["实时火电竞价空间"] = df["实时负荷"] - df["实时出力"]
        df["实时备用比例"] = (df["正备用实际负荷"] + df["负备用实际负荷"]) / df["系统实际负荷"]
        df["实时火电调节裕度"] = df["火电竞价空间实际"] / df["系统实际负荷"]
        df["实时火电紧张"] = (df["实时火电调节裕度"] < 0.5).astype(int)

        # 日前-实时特征
        df["火电竞价空间差值"] = df["火电竞价空间预测"] - df["火电竞价空间实际"]
        df["系统负荷差值"] = df["系统预测负荷"] - df["系统实际负荷"]
        df["负荷差值"] = df["日前负荷"] - df["实时负荷"]
        # df["外来电差值"] = df["外来电预测负荷"] - df["外来电实际负荷"]
        df["正备用差值"] = df["正备用预测负荷"] - df["正备用实际负荷"]
        df["负备用差值"] = df["负备用预测负荷"] - df["负备用实际负荷"]
        df["A类机组差值"] = df["省内A类机组预测出力"] - df["省内A类机组实际出力"]
        df["地方电源差值"] = df["地方电源预测出力"] - df["地方电源实际出力"]

        # 时间特征
        df['hour'] = df['dateTime'].dt.hour
        df['dayofweek'] = df['dateTime'].dt.dayofweek
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['dayofweek_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dayofweek_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        # df['dayofyear']=df['dateTime'].dt.dayofyear

        # 峰谷特征：尖峰、峰段、平段、谷段、深谷  高峰时段为10-12点、14-19点：3；低谷时段为0-8点：1；其余时段为平段：2
        df['period'] = df['hour'].apply(
            lambda x: 3 if 10 <= x < 12 or 14 <= x < 19 else (1 if 0 <= x < 8 else 2))

        # 时序统计特征
        df["ahead_price_D1"] = df["日前统一结算点电价"].shift(24)  # 前一天同时刻
        df['ahead_price_D1_mean'] = df['ahead_price_D1'].rolling(
            24).mean()  # 前一天统计特征
        df['ahead_price_D1_std'] = df['ahead_price_D1'].rolling(24).std()
        df['ahead_price_D1_median'] = df['ahead_price_D1'].rolling(24).median()

        shifts_ahead = [24 * i for i in range(1, 8)]  # [24, 48, ..., 168]
        df["ahead_weekly_mean"] = pd.concat([df["日前统一结算点电价"].shift(s) for s in shifts_ahead], axis=1).mean(
            axis=1, skipna=True
        )
        df["ahead_weekly_std"] = pd.concat([df["日前统一结算点电价"].shift(s) for s in shifts_ahead], axis=1).std(
            axis=1, skipna=True
        )
        df["ahead_weekly_median"] = pd.concat([df["日前统一结算点电价"].shift(s) for s in shifts_ahead], axis=1).median(
            axis=1, skipna=True
        )

        df["real_price_D2"] = df["实时统一结算点电价"].shift(
            72
        )  # 预测D+1时只能获得D-2的实时电价数据，相当于预测日的三天前
        df['real_price_D2_D1_mean'] = df['real_price_D2'].rolling(
            24).mean()  # D-2
        df['real_price_D2_D1_std'] = df['real_price_D2'].rolling(24).std()
        df['real_price_D2_D1_median'] = df['real_price_D2'].rolling(
            24).median()

        shifts_real = [24 * i for i in range(3, 10)]
        df["real_weekly_mean"] = pd.concat([df["实时统一结算点电价"].shift(s) for s in shifts_real], axis=1).mean(
            axis=1, skipna=True
        )
        df["real_weekly_std"] = pd.concat([df["实时统一结算点电价"].shift(s) for s in shifts_real], axis=1).std(
            axis=1, skipna=True
        )
        df["real_weekly_median"] = pd.concat([df["实时统一结算点电价"].shift(s) for s in shifts_real], axis=1).median(
            axis=1, skipna=True
        )

        df["diff_price_D1"] = df["日前价格-实时价格"].shift(24)
        df["diff_price_D1_mean"] = df["diff_price_D1"].rolling(24).mean()
        df["diff_price_D1_std"] = df["diff_price_D1"].rolling(24).std()
        df["diff_price_D1_median"] = df["diff_price_D1"].rolling(24).median()
        df["diff_weekly_mean"] = pd.concat([df["日前价格-实时价格"].shift(s) for s in shifts_ahead], axis=1).mean(
            axis=1, skipna=True
        )
        df["diff_weekly_std"] = pd.concat([df["日前价格-实时价格"].shift(s) for s in shifts_ahead], axis=1).std(
            axis=1, skipna=True
        )
        df["diff_weekly_median"] = pd.concat([df["日前价格-实时价格"].shift(s) for s in shifts_ahead], axis=1).median(
            axis=1, skipna=True
        )

        # 开机台数特征
        # 燃气开机台数分为low、medium、high 三个时段
        GAS_SEGMENTS = {
            "low": [1, 2, 3, 4, 5, 6],        # 01:00-07:00
            # 00:00+08:00-15:00+23:00
            "medium": [0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 23],
            "high": [16, 17, 18, 19, 20, 21, 22]  # 16:00-22:00
        }
        df['gas_unit'] = df['hour'].apply(lambda x: 0 if x in GAS_SEGMENTS['low'] else (
            1 if x in GAS_SEGMENTS['medium'] else 2))
        # 太阳能开机台数
        df['solar_unit'] = df['hour'].apply(
            lambda x: 0 if x in [6, 19] else (1 if x in range(7, 19) else 2))

        return df

    def _with_holiday_features(self, df: pd.DataFrame, df_holiday:pd.DataFrame) -> pd.DataFrame:
        if df_holiday is None or df_holiday.empty:  
            return df
        # 工作日和节假日

        date_type = df_holiday

        date_type['date'] = pd.to_datetime(date_type['day'], format='%Y%m%d')
        date_type['date'] = date_type['date'].astype(str)

        type_mapping = {'调休节假日': 0, '周一': 1, '周二': 2, '周三': 3, '周四': 4, '周五': 5, '周六': 6, '周日': 7,
                        '调休工作日': 8, '法定节假日': 9}
        date_type['day_type'] = date_type['type'].map(type_mapping)
        date_type = date_type[['date', 'day_type']]

        df['date'] = df['dateTime'].dt.date.astype(str)
        df = pd.merge(df, date_type, on='date', how='left')
        df['day_type'] = df['day_type'].astype(int)
        df["holiday"] = df["day_type"].apply(lambda x: 1 if x in [0, 6, 7, 9] else 0)  # 节假日：1，工作日：0

        return df

if __name__ == "__main__":
    fetcher = DataFetcherMixin()
    D = "2025-07-24"
    df=pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\guangdong\df.xlsx")
    df_gfs=pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\guangdong\df_gfs.xlsx")
    df_holiday=None
    train, test = fetcher._data(D=D,df=df,df_holiday=df_holiday,df_gfs=df_gfs)
    # train.to_excel(f"D:/fwx/gitlab/algorithms/lab/fanwenxuan/guangdong/excel/df/train_{D}.xlsx")
    # test.to_excel(f"D:/fwx/gitlab/algorithms/lab/fanwenxuan/guangdong/excel/df/test_{D}.xlsx")
