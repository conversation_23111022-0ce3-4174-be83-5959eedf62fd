#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/8 17:21
# <AUTHOR> Dar<PERSON>
from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_longtime_pre import ElPricePreLongtimeNode48
from tornado import gen, concurrent
from tglibs.easy_json import j2o


class PricePredictionLongtimenNodeHandlerMX48(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())
        pre_date_list = data['pre_date_list']
        history_data = data['history_data']
        pre_data = data['pre_data']
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 5180)
        r = yield self.predict(history_data, pre_data, pre_date_list, min_value, max_value)

        self.write(r)


    @concurrent.run_on_executor
    def predict(self, history_data, pre_data, pre_date_list, min_value, max_value):
        elpd_val = ElPricePreLongtimeNode48(history_data, pre_data, pre_date_list, min_value, max_value)
        result = elpd_val.predict()
        return result.to_dict()