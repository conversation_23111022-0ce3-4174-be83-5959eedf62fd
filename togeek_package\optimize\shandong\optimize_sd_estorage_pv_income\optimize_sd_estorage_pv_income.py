"""
# -*- coding: utf-8 -*-
# Author : lmm
# Time : 2024/1/31 13:44
# Description :  光储充一体化，计算收益最大的储能充放电策略，以及光伏上网功率
                 根据光伏训练模型，得到光伏在未来一天的预测功率
                 对传入的负荷数据，划分为节假日、周末和周内，对负荷数据分别处理
                 建立优化模型
                 求解模型，得到结果
"""

import os
import pandas as pd
import time
import requests
from datetime import datetime, timedelta
from chinese_calendar import is_workday, is_holiday
import pyomo.environ as pyo
import warnings
import logging
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class OptimizeEvStoragePv:

    def __init__(self, data_load, data_price, data_storage, pv_price, start_year, end_year, holidays_day, start_time):
        logger.info("---------------------- START: 山东青岛中石油光储充一体化 收益优化 --------------------------")
        self.data_load = data_load  # 负荷数据, json格式
        self.data_price = data_price  # 电价数据, json格式
        self.data_storage = data_storage  # 储能参数
        self.pv_price = pv_price   # 光伏上网电价
        self.start_year = start_year   # 传入的负荷数据的开始年份
        self.end_year = end_year  # 传入的负荷数据的结束年份
        self.holidays_day = holidays_day  # 节假日日期
        self.start_time = start_time  # 开始调用模型的时间

    def get_next_day(self):
        """ 根据开始调用模型的时间，得到未来一天的时间序列 """

        # 获取时间范围
        time_stamp = time.mktime(time.strptime(self.start_time, "%Y-%m-%d %H:%M:%S"))
        first_stamp = time_stamp + 15 * 60  # 需要预测光伏出力的开始时间
        end_stamp = time_stamp + 15 * 60 * 96  # 需要预测光伏出力的结束时间
        first_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(first_stamp))
        end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_stamp))
        date_time = pd.Series(pd.date_range(start=first_time, end=end_time, freq='15T'))

        return date_time, time_stamp, first_stamp, end_stamp, first_time, end_time

    def predict_pv(self):
        """ 获取光伏未来一天预测的出力 """

        start_time = self.start_time
        year_month = start_time[:4] + start_time[5:7]
        m = int(start_time[11])
        if m == 0:
            start_date = start_time[:10] + "_" + start_time[12:16]
        else:
            start_date = start_time[:10] + "_" + start_time[11:16]
        url = f"http://113.140.12.194:22880/project/sdqdzsy/{year_month}/{start_date}.json"  # 将此处替换为要访问的API URL
        response = requests.get(url)
        pv_pred = response.json()
        df_pred = pd.DataFrame(pv_pred)

        return df_pred

    def get_pv_time_frame(self):
        """ 光伏每个月的出力范围 """

        # 光伏每个月的出力范围
        time_series = pd.Series(pd.date_range(start="2023-01-01 00:00:00", end="2023-01-02 00:00:00", freq='15T')[:-1])
        time = time_series.map(lambda x: str(x).split(" ")[1])
        a1 = [0] * 32 + [1] * 33 + [0] * 31  # 冬季
        a2 = [0] * 28 + [1] * 41 + [0] * 27  # 春季
        a3 = [0] * 24 + [1] * 49 + [0] * 23  # 夏季
        a = []
        a.append(a1)
        a.append(a1)
        a.append(a2)
        a.append(a2)
        a.append(a2)
        a.append(a3)
        a.append(a3)
        a.append(a3)
        a.append(a2)
        a.append(a2)
        a.append(a2)
        a.append(a1)

        aa = pd.DataFrame(a)
        aa = aa.T
        pv_time_frame = pd.concat([time, aa], axis=1)
        pv_time_frame.columns = ["time", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]

        return pv_time_frame

    def predict_pv_processed(self):
        """ 根据光伏出力范围对预测的光伏出力进行处理 """

        df_pred = self.predict_pv()    # 光伏预测出力
        pv_time_frame = self.get_pv_time_frame()    # 光伏每个月的出力时间范围

        # 处理预测的光伏出力数据
        df_pred["month"] = df_pred["date_time"].map(lambda x: str(x).split(" ")[0].split("-")[1])
        df_pred["time"] = df_pred["date_time"].map(lambda x: str(x).split(" ")[1])
        months = df_pred["month"].unique()
        pv_processed = []
        for month in months:
            df_pred1 = df_pred[df_pred["month"] == month]
            pv_time_frame1 = pv_time_frame[["time", month]]
            pred_range_merge = pd.merge(df_pred1, pv_time_frame1, how="left", on="time")
            pv_processed = pv_processed + (pred_range_merge[month] * pred_range_merge["pv"]).tolist()

        df_pred["pv"] = pv_processed

        return df_pred

    def get_workdays(self, start_date, end_date):
        """ 获取某一年的所有休息日和工作日 """

        # 获取周末和工作日
        holidays = []  # 休息日，包含周末和节假日
        weekends = []  # 结果为[]
        workdays = []  # 工作日

        current_date = start_date
        while current_date <= end_date:
            if is_holiday(current_date):
                holidays.append(current_date.strftime("%Y-%m-%d %H:%M").split(" ")[0])
            elif not is_workday(current_date):
                weekends.append(current_date.strftime("%Y-%m-%d %H:%M").split(" ")[0])
            else:
                workdays.append(current_date.strftime("%Y-%m-%d %H:%M").split(" ")[0])

            current_date += timedelta(days=1)

        return holidays, workdays

    def get_load_mean(self, data_load_new):
        """ 对传入的data_load_new数据按时刻取均值"""

        user_load_new = data_load_new.pivot_table(index="time", columns="date", values="user_load")
        user_load = user_load_new.mean(axis=1).values
        ev_load_new = data_load_new.pivot_table(index="time", columns="date", values="ev_load")
        ev_load = ev_load_new.mean(axis=1).values
        df_load_average = pd.concat([pd.DataFrame(data_load_new["time"][:96]), pd.DataFrame(user_load), pd.DataFrame(ev_load)], axis=1)
        df_load_average.columns = ["time", "user_load", "ev_load"]

        return df_load_average

    def get_date_classify(self):
        """ 获取某一年或某几年的所有节假日、周末和工作日 """

        # 2023-2024年的所有节假日
        holidays_day = self.holidays_day

        # 定义日期范围
        start_date_2023 = datetime(self.start_year, 1, 1)
        end_date_2023 = datetime(self.start_year, 12, 31)
        start_date_2024 = datetime(self.end_year, 1, 1)
        end_date_2024 = datetime(self.end_year, 12, 31)
        holidays_2023, workdays_2023 = self.get_workdays(start_date_2023, end_date_2023)
        holidays_2024, workdays_2024 = self.get_workdays(start_date_2024, end_date_2024)

        weekends_day = []  # 2023-2024年的所有不包含节假日的周末
        workdays_day = []  # 2023-2024年的工作日
        weekends_day = weekends_day + list(filter(lambda x: x not in holidays_day, holidays_2023))
        weekends_day = weekends_day + list(filter(lambda x: x not in holidays_day, holidays_2024))
        workdays_day = workdays_day + workdays_2023
        workdays_day = workdays_day + workdays_2024

        return holidays_day, weekends_day, workdays_day

    def predict_load(self):
        """ 预测未来一天的充电桩负荷和负载 """

        # 获取时间范围
        date_time, time_stamp, first_stamp, end_stamp, first_time, end_time = self.get_next_day()

        # 获取2023-2024年的所有节假日、周末和工作日
        holidays_day, weekends_day, workdays_day = self.get_date_classify()

        # 历史负荷数据
        data_load = pd.DataFrame(self.data_load)
        # 0. 判断传入的负荷数据某一天有没有缺失的点，如果有缺失的，删除这一天的数据
        data_load["date"] = data_load["date_time"].map(lambda x: str(x).split(" ")[0])
        counts = data_load.groupby("date").count()
        data_time_index = [i for i in counts.index.tolist() if (counts.loc[i, "date_time"] == 96) & (counts.loc[i, "user_load"] == 96) & (counts.loc[i, "ev_load"] == 96)]
        data_load_new = data_load[data_load["date"].isin(data_time_index)].reset_index(drop=True)
        data_load_new["time"] = data_load_new["date_time"].map(lambda x: str(x).split(" ")[1])
        # 1. 判断传入的负荷数据是否小于0，如果小于0则置为0
        data_load_new["ev_load"] = data_load_new["ev_load"].apply(lambda x: max(x,0))
        data_load_new["user_load"] = data_load_new["user_load"].apply(lambda x: max(x, 0))
        # 2. 判断传入的数据是否满足60天，如果不满足，则未来一天的负荷取所有数据的平均值
        total_days = int(len(data_load_new) / 96)
        if total_days < 60:
            loads_pred = self.get_load_mean(data_load_new)
            loads_pred.columns = ["time", "user_load", "ev_load"]
            date_time1 = pd.DataFrame(date_time)
            date_time1["time"] = date_time1[0].map(lambda x:str(x).split(" ")[1])
            loads_pred = pd.merge(date_time1, loads_pred, on=["time"])
            loads_pred = loads_pred[["time", "user_load", "ev_load"]]
        # 3. 计算节假日的负荷，如果传入的数据没有节假日，这未来一天节假日的负荷取所有数据的平均值
        # 3.1 将所有的负荷数据分为节假日、周末和工作日数据
        # 3.2 将分割好的负荷数据按时刻计算均值
        else:
            data_load_holidays = data_load_new[data_load_new["date"].isin(holidays_day)].reset_index(drop=True)    # 节假日
            data_load_weekends = data_load_new[data_load_new["date"].isin(weekends_day)].reset_index(drop=True)    # 周末
            data_load_workdays = data_load_new[data_load_new["date"].isin(workdays_day)].reset_index(drop=True)    # 工作日
            if len(data_load_holidays) == 0:
                load_holidays_average = self.get_load_mean(data_load_new)
            else:
                load_holidays_average = self.get_load_mean(data_load_holidays)      # 节假日平均负荷
            load_weekends_average = self.get_load_mean(data_load_weekends)      # 周末平均负荷
            load_workdays_average = self.get_load_mean(data_load_workdays)      # 工作日平均负荷

            # 根据调用模型的时间，将计算的节假日、周末和工作日负荷按时刻拼接为一天的负荷
            load_pred_first = self.choose_load_average(first_time, holidays_day, load_holidays_average, weekends_day, load_weekends_average, load_workdays_average)
            first_index = load_pred_first[load_pred_first["time"] == (first_time.split(" ")[1])].index.tolist()[0]
            loads_pred1 = load_pred_first.loc[first_index:, :]

            # 如果开始时间和结束时间是同一天，则只选择开始时间的负荷按时刻拼接为一天的负荷
            if first_time.split(" ")[0] == end_time.split(" ")[0]:
                loads_pred = loads_pred1
            # 如果开始时间和结束时间不是同一天，则选择开始时间和结束时间的负荷按时刻拼接为一天的负荷
            else:
                load_pred_end = self.choose_load_average(end_time, holidays_day, load_holidays_average, weekends_day, load_weekends_average, load_workdays_average)
                end_index = load_pred_end[load_pred_end["time"] == (end_time.split(" ")[1])].index.tolist()[0]
                loads_pred = pd.concat([loads_pred1, load_pred_end.loc[:end_index, :]]).reset_index(drop=True)

        # 负荷数据整理
        df_load = pd.DataFrame()
        df_load = pd.concat([df_load, pd.DataFrame(date_time), loads_pred], axis=1)
        df_load.columns = ["date_time", "time", "user_load", "ev_load"]
        df_load["user_load"] = df_load["user_load"].map(lambda x: round(x, 2))    # 保留两位小数
        df_load["ev_load"] = df_load["ev_load"].map(lambda x: round(x, 2))    # 保留两位小数

        return df_load

    def choose_load_average(self, first_time, holidays_day, load_holidays_average, weekends_day, load_weekends_average, load_workdays_average):
        """ 根据传入的日期选择匹配的平均负荷 """

        if first_time.split(" ")[0] in holidays_day:
            load_pred = load_holidays_average
        elif first_time.split(" ")[0] in weekends_day:
            load_pred = load_weekends_average
        else:
            load_pred = load_workdays_average

        return load_pred

    def data_process(self):
        """ 对传入的价格和负荷数据处理 """

        data_load = self.predict_load()
        data_pv = self.predict_pv_processed()
        data_pv["date_time"] = pd.to_datetime(data_pv["date_time"])

        data_price = pd.DataFrame(self.data_price)
        data_price["date_time"] = pd.to_datetime(data_price["date_time"])

        # 1. 将负荷数据、光伏数据和价格数据整理到一起
        df = pd.merge(data_pv, data_load, how='left', on='date_time')
        data = pd.merge(df, data_price, how='left', on='date_time')

        return data

    def model_pyomo(self, data, storage):
        """ 建立模型   """

        # 1. 全局参数计算
        grid_price = tuple(data["grid_price"].values)  # 购电电价
        ev_price = tuple(data["ev_price"].values)  # 充电桩价格
        ev_service_price = tuple(data["ev_service_price"].values)  # 充电桩服务费
        user_load = tuple(data["user_load"].values)    # 负载数据
        ev_load = tuple(data["ev_load"].values)  # 充电桩负荷
        pv = tuple(data["pv"].values)  # 光伏预测出力

        # 2. 创建模型
        model = pyo.ConcreteModel()

        # 3. 添加索引集合
        model.idx = pyo.RangeSet(0, 95)    # 首尾均包含

        # 4. 添加参数
        model.grid_price = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(grid_price)})    # 购电电价
        model.ev_price = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(ev_price)})    # 充电桩价格
        model.ev_service_price = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(ev_service_price)})  # 充电桩服务费
        model.user_load = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(user_load)})  # 负载数据
        model.ev_load = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(ev_load)})    # 充电桩负荷
        model.pv = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(pv)})    # 光伏预测出力

        # 5. 添加决策变量
        # 5.1 决策变量1：储能系统充电功率列表
        model.power_charge_list = pyo.Var(model.idx, domain=pyo.Reals, bounds=(0, storage["max_power_charge"]))
        # 5.2 决策变量2：储能系统放电功率列表
        model.power_discharge_list = pyo.Var(model.idx, domain=pyo.Reals, bounds=(0, -1 * storage["max_power_discharge"]))
        # 5.3 添加决策变量3：每个时段终点储能的电量值
        model.elec_list = pyo.Var(model.idx, domain=pyo.Reals, bounds=(storage["elec_total"] * (1 - storage["dod"]), storage["elec_total"]))
        # 5.4 添加决策变量4：光伏给充电桩的功率
        model.pv_cs = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.5 添加决策变量5：光伏给负载的功率
        model.pv_ld = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.6 添加决策变量6：光伏给储充的功率
        model.pv_es = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.7 添加决策变量7：光伏上网的功率
        model.pv_grid = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.8 添加决策变量8：电网下给充电桩的功率
        model.grid_cs = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.9 添加决策变量9：储放给充电桩的功率
        model.des_cs = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.10 添加决策变量10：电网下给负载的功率
        model.grid_ld = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.11 添加决策变量11：储放给负载的功率
        model.des_ld = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.12 添加决策变量12：电网下给储充的功率
        model.grid_es = pyo.Var(model.idx, domain=pyo.NonNegativeReals)

        # 6. 添加目标函数
        def obj_rule(model):
            """ 定义目标函数
            收益：充电桩收益 + 光伏余量上网收益 + 加油站电费 — 总支出
            充电桩收益 = 充电桩负荷收益 + 充电桩服务费
            光伏余量 = 光伏预测出力 - 光伏给充电桩、负载和储能的功率和
            加油站电费 = 用户负荷 * 电网购电电价
            总支出 = 电网费用+光伏上网收益*0.1+(充电桩收益(电费+服务费)-充电桩从电网购电电费)*0.1 + 加油站电费*0.125 + 充电桩服务费 * 0.3 * 0.9
            电网费用 = (电网——>储能充电 + 电网——>充电桩 + 电网——>负载) * 购电电价
            """
            ev_income = sum([(model.ev_price[i] + model.ev_service_price[i]) * model.ev_load[i] for i in model.idx])   # 充电桩收益 + 充电桩服务费
            pv_income = sum([self.pv_price * model.pv_grid[i] for i in model.idx])     # 光伏余量上网收益
            user_income = sum([model.grid_price[i] * model.user_load[i] for i in model.idx])  # 加油站电费
            grid_cost = sum([(model.grid_es[i] + model.grid_ld[i] + model.grid_cs[i]) * model.grid_price[i] for i in model.idx])   # 从电网购电费用
            cs_cost = sum([model.grid_price[i] * model.grid_cs[i] for i in model.idx])  # 充电桩从电网购电电费
            cs_service = sum([(model.ev_service_price[i]) * model.ev_load[i] for i in model.idx])   # 充电桩服务费
            cost = grid_cost + pv_income * 0.1 + (ev_income - cs_cost) * 0.1 + user_income * 0.125 + cs_service * 0.3 * 0.9

            # 总收益
            income = (ev_income + pv_income + user_income - cost) * 1/4

            return income
        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)

        # 7. 添加约束条件
        # 7.1 光伏给充电桩、负载和储能和上网的功率约束
        def const_rule1(model, i):
            """ 定义约束条件1 """
            return model.pv_cs[i] + model.pv_ld[i] + model.pv_es[i] + model.pv_grid[i] == model.pv[i]
        model.constraint1 = pyo.Constraint(model.idx, rule=const_rule1)

        # 7.2 充电桩负荷 = 电网给充电桩 + 光伏给充电桩 + 储放给充电桩 * 放电效率
        def const_rule2(model, i):
            """ 定义约束条件2 """
            return model.grid_cs[i] + model.pv_cs[i] + model.des_cs[i] * storage["coef_charge"] == model.ev_load[i]
        model.constraint2 = pyo.Constraint(model.idx, rule=const_rule2)

        # 7.3 负载负荷 = 电网给负载 + 光伏给负载 + 储放给负载 * 放电效率
        def const_rule3(model, i):
            """ 定义约束条件3 """
            return model.grid_ld[i] + model.pv_ld[i] + model.des_ld[i] * storage["coef_charge"] == model.user_load[i]
        model.constraint3 = pyo.Constraint(model.idx, rule=const_rule3)

        # 7.4 储能充电功率 = 电网给储充 + 光伏给储充
        def const_rule4(model, i):
            """ 定义约束条件4 """
            return model.grid_es[i] + model.pv_es[i] == model.power_charge_list[i]
        model.constraint4 = pyo.Constraint(model.idx, rule=const_rule4)

        # 7.5 储能放电功率 = 储放给充电桩 + 储放给负载
        def const_rule5(model, i):
            """ 定义约束条件5 """
            return model.des_cs[i] + model.des_ld[i] == model.power_discharge_list[i]
        model.constraint5 = pyo.Constraint(model.idx, rule=const_rule5)

        # 7.6 约束条件6：每个时段终点电量 = 该时段起点电量 + (该时段充电功率 - 该时段放电功率) * 时间段
        def const_rule6(model, i):
            """ 定义约束条件6 """
            if i == 0:
                return model.elec_list[i] == storage["elec_total"] * storage["soc_init"] + (model.power_charge_list[i] - model.power_discharge_list[i]) * 1/4
            else:
                return model.elec_list[i] == model.elec_list[i - 1] + (model.power_charge_list[i] - model.power_discharge_list[i]) * 1/4
        model.constraint6 = pyo.Constraint(model.idx, rule=const_rule6)

        # 7.7 约束条件7：光伏上网和电网给储充不能同时发生
        def const_rule7(model, i):
            """ 定义约束条件7 """
            return model.pv_grid[i] * model.grid_es[i] <= 1e-10
        model.constraint7 = pyo.Constraint(model.idx, rule=const_rule7)

        # 7.8 约束条件8：光伏上网和电网给充电桩不能同时发生
        def const_rule8(model, i):
            """ 定义约束条件8 """
            return model.pv_grid[i] * model.grid_cs[i] <= 1e-10
        model.constraint8 = pyo.Constraint(model.idx, rule=const_rule8)

        # 7.9 约束条件9：光伏上网和电网给负载不能同时发生
        def const_rule9(model, i):
            """ 定义约束条件9 """
            return model.pv_grid[i] * model.grid_ld[i] <= 1e-10
        model.constraint9 = pyo.Constraint(model.idx, rule=const_rule9)

        # 7.10 约束条件10：储能不能同时充放电
        def const_rule10(model, i):
            """ 定义约束条件10 """
            return model.power_discharge_list[i] * model.power_charge_list[i] <= 1e-6
        model.constraint10 = pyo.Constraint(model.idx, rule=const_rule10)

        return model

    def optimize(self):

        # 数据处理
        data = self.data_process()
        storage = self.data_storage

        # 优化问题建模
        logger.info("start: 建模 ... ")
        model = self.model_pyomo(data=data, storage=storage)

        # 调用求解器ipopt求解
        logger.info("start: 优化求解 ... ")
        # opt = pyo.SolverFactory("coptampl")
        opt = pyo.SolverFactory("ipopt")
        solution = opt.solve(model)

        # 最优解 & 对应参数提取
        max_income = model.obj()  # 目标日期最大收入(元)
        power_charge_of_storage = [pyo.value(model.power_charge_list[i]) for i in range(len(data))]  # 储能充电功率
        power_discharge_of_storage = [pyo.value(model.power_discharge_list[i]) for i in range(len(data))]  # 储能放电功率
        elec_of_storage = [pyo.value(model.elec_list[i]) for i in range(len(data))]  # 储能电量
        pv_cs = [pyo.value(model.pv_cs[i]) for i in range(len(data))]  # 光伏给充电桩的功率
        pv_ld = [pyo.value(model.pv_ld[i]) for i in range(len(data))]  # 光伏给负载的功率
        pv_es = [pyo.value(model.pv_es[i]) for i in range(len(data))]  # 光伏给储充的功率
        pv_grid = [pyo.value(model.pv_grid[i]) for i in range(len(data))]  # 光伏上网的功率
        grid_cs = [pyo.value(model.grid_cs[i]) for i in range(len(data))]  # 电网给充电桩的功率
        des_cs = [pyo.value(model.des_cs[i]) for i in range(len(data))]  # 储放给充电桩的功率
        grid_ld = [pyo.value(model.grid_ld[i]) for i in range(len(data))]  # 电网下给负载的功率
        des_ld = [pyo.value(model.des_ld[i]) for i in range(len(data))]  # 储放给负载的功率
        grid_es = [pyo.value(model.grid_es[i]) for i in range(len(data))]  # 电网下给储充的功率

        # 数据后处理
        data_out = data[["date_time", "grid_price", "ev_price", "ev_service_price", "pv", "user_load", "ev_load"]]
        data_out["date_time"] = data_out["date_time"].map(lambda x: str(x))
        data_out["power"] = pd.Series(power_charge_of_storage).map(lambda x: round(x, 2))  # 储能充电
        power_discharge_of_storage = pd.Series(power_discharge_of_storage).map(lambda x: round(x, 2))
        index = power_discharge_of_storage[power_discharge_of_storage > 0].index.tolist()  # 找出储能放电的索引行
        data_out.loc[index, "power"] = -1 * power_discharge_of_storage[index]  # 储能充放电合并到一起
        data_out["elec_of_storage"] = pd.Series(elec_of_storage).map(lambda x: round(x, 2))
        data_out["pv_cs"] = pd.Series(pv_cs).map(lambda x: round(x, 2))  # 光伏给充电桩的功率
        data_out["pv_ld"] = pd.Series(pv_ld).map(lambda x: round(x, 2))   # 光伏给负载的功率
        data_out["pv_es"] = pd.Series(pv_es).map(lambda x: round(x, 2))    # 光伏给储充的功率
        data_out["pv_grid"] = pd.Series(pv_grid).map(lambda x: round(x, 2))  # 光伏上网的功率
        data_out["grid_cs"] = pd.Series(grid_cs).map(lambda x: round(x, 2))  # 电网给充电桩的功率
        data_out["des_cs"] = pd.Series(des_cs).map(lambda x: round(x, 2))   # 储放给充电桩的功率
        data_out["grid_ld"] = pd.Series(grid_ld).map(lambda x: round(x, 2))  # 电网下给负载的功率
        data_out["des_ld"] = pd.Series(des_ld).map(lambda x: round(x, 2))  # 储放给负载的功率
        data_out["grid_es"] = pd.Series(grid_es).map(lambda x: round(x, 2))  # 电网下给储充的功率
        data_out["pv_eu"] = data_out["pv_cs"] + data_out["pv_ld"] + data_out["pv_es"]
        data_out["grid"] = data_out["grid_cs"] + data_out["grid_ld"] + data_out["grid_es"]
        # 计算每个时间点的收益
        ev_income = (data_out["ev_price"] + data_out["ev_service_price"]) * data_out["ev_load"]
        pv_income = self.pv_price * data_out["pv_grid"]
        user_income = data_out["user_load"] * data_out["grid_price"]
        grid_cost = data_out["grid"] * data_out["grid_price"]
        cs_cost = data_out["grid_cs"] * data_out["grid_price"]
        cs_service = (data_out["ev_service_price"]) * data_out["ev_load"]
        cost = grid_cost + pv_income * 0.1 + (ev_income - cs_cost) * 0.1 + user_income * 0.125 + cs_service * 0.3 * 0.9

        # 总收益
        income = (ev_income + pv_income + user_income - cost) * 1 / 4

        data_out["income"] = pd.Series(income).map(lambda x: round(x, 2))  # 每个时间点的收益
        # 删除多余的列
        data_out.drop(columns=["pv_cs", "pv_ld", "pv_es", "des_cs", "grid_ld", "grid_es", "des_ld"], inplace=True)
        # 模型输出结果整理
        data_out_dict = {}
        data_out_dict["storage_strategy"] = data_out.to_dict(orient='records')
        # data_out_dict = data_out.to_dict(orient='records')

        logger.info("---------------------- END: 山东青岛中石油光储充一体化 收益优化 --------------------------")

        return data_out_dict


if __name__ == '__main__':
    import time
    from datetime import timedelta
    time0 = time.time()

    path = r"D:/项目/2024/山东/青岛中石油/20240118_lm/data/data_input/data_input1.json"
    # path_data_demo = r"D:/项目/2023/山东/海辰新能源项目/20230922_lm/data/data_input1.xlsx"

    import json

    with open(path, 'r', encoding='UTF-8') as f:
        data = json.loads(f.read())
    df_load = pd.DataFrame(data["data_load"])
    df_price = pd.DataFrame(data["data_price"])
    storage = data["data_storage"]
    pv_price = data["pv_price"]
    start_year = data["start_year"]
    end_year = data["end_year"]
    holidays_day = data["holidays_day"]
    start_time = data["start_time"]

    m = OptimizeEvStoragePv(data_load=df_load, data_price=df_price, data_storage=storage, pv_price=pv_price, start_year=start_year, end_year=end_year, holidays_day=holidays_day, start_time=start_time)

    data_out_dict = m.optimize()
    data_out = pd.DataFrame(data_out_dict["storage_strategy"])

    # 输出数据保存
    path_out = r"D:\项目\2024\山东\青岛中石油\20240118_lm\data\data_output/data_output.xlsx"
    # path_out = r".\data_output\data_output.xlsx"

    data_out.to_excel(path_out, index=False)
    print(f"优化结果已保存，请查看：{os.path.abspath(path_out)}")

    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1 - time0))}")






