# -*- coding: utf-8 -*
# valid license

import pandas as pd
from prophet import Prophet
from json import loads
import logging

logger = logging.getLogger()

class CustomerLoadFasterPre:
    def __init__(self, load, holiday, pre_days, point_day=24, cap=None, floor=None, include_history=True,
                 flexibility=5, hist_model=None):
        assert 1 <= flexibility <=10
        logger.info("------------------------------------------")
        logger.info("----------时间序列预测用户负荷-------------------")
        logger.info("load:{}, holiday:{}, pre_days:{}, point_day={}, cap={}, floor={}, include_history={},flexibility={}".format(
            load, holiday, pre_days, point_day, cap, floor, include_history, flexibility))
        self.params = {"seasonality_mode": 'multiplicative', "changepoint_prior_scale": flexibility / 100}
        self.load = self._prepare_load(load, cap, floor, self.params, point_day)
        self._prepare_holiday(holiday, self.params)
        self.pre_days = pre_days * point_day
        self.inc_his = include_history
        self.point_day = point_day
        self.hist_model = hist_model  # 训练好的模型的参数，字典
        self.freq = {24: 'H', 96: '15min', 48: '30min'}[point_day]

    def _prepare_load(self, load, cap, floor, params, point_day):
        if isinstance(load, str):
            load = loads(load)
        if isinstance(load, dict):
            load['ds'] = pd.to_datetime(load['ds'])
            load = pd.DataFrame(load)
        if cap is not None:
            load['cap'] = cap
            if floor is None:
                load['floor'] = floor
        if 'cap' in load:
            params['growth'] = 'logistic'

        load.set_index("ds", inplace=True)
        load.index = pd.DatetimeIndex(load.index)
        load.sort_index(inplace=True)
        min_period = {24: '60T', 96: '15T', 48: '30T'}[point_day]
        load = load.resample(min_period).first()
        load.reset_index(inplace=True)

        return load

    def _prepare_holiday(self, holiday, params):
        if holiday is None:
            return
        if isinstance(holiday, str):
            holiday = loads(holiday)
        if isinstance(holiday, dict):
            holiday["ds"] = pd.to_datetime(holiday["ds"])
            holiday = pd.DataFrame(holiday)
        params["holidays"] = holiday

    def stan_init(self, m):
        """
        从训练好的模型中检索参数，以格式从训练过的模型中检索参数用于初始化一个新的stan模型
        :param m: 训练好的模型
        :return: 包含模型m参数的字典
        """
        res = {}
        for pname in ['k', 'm', 'sigma_obs']:
            res[pname] = m.params[pname][0][0]
        for pname in ['delta', 'beta']:
            res[pname] = m.params[pname][0].tolist()
        return res

    def predict(self, to_json=False):
        model = Prophet(**self.params)
        if self.hist_model is None:
            model.fit(self.load)
        else:
            model.fit(self.load, init=self.hist_model)
        # print(self.stan_init(model))
        future = model.make_future_dataframe(periods=self.pre_days, include_history=self.inc_his, freq=self.freq)
        result = model.predict(future)[['ds', 'yhat']]
        result.loc[result[result["yhat"] < 0].index, "yhat"] = 0

        if to_json:
            result = {'ds': result.ds.astype(str).tolist(),
                      'yhat': result.yhat.tolist(),
                      'model': self.stan_init(model)
                      }
        logger.info("------end of customer-------")
        logger.info("result:{}".format(result))
        return result


if __name__ == "__main__":
    import numpy as np
    import datetime
    holiday = pd.read_pickle(r"D:\ToGeek\work\model_data\load\sigle_user_load_prophet\holiday.pkl")
    load = pd.read_pickle(r"D:\ToGeek\work\model_data\load\sigle_user_load_prophet\customer.pkl")
    t1 = datetime.datetime.now()
    hist_model = {'k': -0.16996807120329377,
                  'm': 0.06955124936299413,
                  'sigma_obs': 0.03279271178708411,
                  'delta': np.array([-5.47356299e-08,  1.09656249e-07,  6.24163103e-02,  2.35706989e-05,
       -2.37611584e-09, -2.69040106e-08, -1.35805231e-08, -1.47092415e-07,
        9.58462907e-08,  1.22699160e-05,  1.40296813e-01,  5.75761507e-02,
        2.37965034e-07,  2.17940467e-08, -4.95859495e-04, -5.57867407e-04,
       -5.96547217e-03, -5.96726354e-02, -9.01227744e-03, -8.97866517e-08,
       -7.75072341e-08,  1.14718960e-07, -6.07560369e-08, -7.04601387e-08,
       -1.74571416e-07]),
                  'beta': np.array([-1.04119950e-02,  2.39162620e-02,  1.90308946e-02,  2.95776694e-02,
       -3.86063302e-03, -9.46812802e-03, -6.10466199e-02, -5.64748700e-02,
       -1.58424958e-02,  4.60967938e-02, -3.29074772e-02,  5.71732792e-06,
       -1.87843504e-02, -2.39820222e-02,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00, -4.98482414e-01,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00, -9.89276416e-02, -6.44849313e-02, -9.45863377e-02,
       -1.00639492e-01, -8.83411176e-02, -4.69631940e-02, -7.82487678e-02,
       -5.32877727e-02, -1.43305264e-01, -1.11967570e-01, -1.33546778e-01,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
        0.00000000e+00,  0.00000000e+00,  0.00000000e+00])}

    p = CustomerLoadFasterPre(load, holiday, 1, include_history=False, hist_model=hist_model)
    pre = p.predict(False)
    t2 = datetime.datetime.now()
    print(t2-t1)
    print(pre)
