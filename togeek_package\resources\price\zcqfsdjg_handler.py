# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
# from zcqfsdjgyc.lib.request_handler_base import RequestHandlerBase
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shanxi.price_zcqfsdjg.zcqfsdjg_yue import *
from togeek_package.price.shanxi.price_zcqfsdjg.zcqfsdjg_xun import *
from togeek_package.price.shanxi.price_zcqfsdjg.zcqfsdjg_ri import *


class ZcqfsdYueHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data_yue = params.pop('data_yue')
        xianjia = params.pop('xianjia')
        p = Yue(data_yue=data_yue, xianjia=xianjia)
        pred = p.predict_yue(to_json=True)
        self.write(pred)


class ZcqfsdXunHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data_xun = params.pop('data_xun')
        xianjia = params.pop('xianjia')
        p = Xun(data_xun=data_xun, xianjia=xianjia)
        pred = p.predict_xun(to_json=True)
        self.write(pred)


class ZcqfsdRiHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        rgd = params.pop('rgd')
        xianjia = params.pop('xianjia')
        p = Ri(rgd=rgd, xianjia=xianjia)
        pred = p.predict_ri(to_json=True)
        self.write(pred)
