import pandas as pd
import numpy as np
import json
import logging
import requests
import pandas as pd
import pymysql
from sqlalchemy import create_engine
from sklearn.ensemble import ExtraTreesRegressor
from datetime import datetime,timedelta
from requests.adapters import HTTPAdapter
import time
from requests.packages.urllib3.util.retry import Retry
import random
logger = logging.getLogger()

class SunPowerPred:
    
    def __init__(self,t,start_datetime,his_power,lon_lat,point_day,point_movement,cap):
        
        logger.info("------------------------------------------")
        logger.info("----------光伏电站出力预测-------------------")
        logger.info("t:{}, start_datetime:{}, his_power:{}, lon_lat={}, point_day={}, point_movement={}, cap={}".format(
            t, start_datetime, his_power, lon_lat, point_day, point_movement, cap))
        
        self.t = t
        self.start_datetime = start_datetime     # 预测的起始时间 "start_datetime":"2019-04-01 01:00:00"
        self.his_power = his_power               # 历史的光伏出力数据 "his_power":{"powe"r:[],"ds":[]}
        self.lon_lat = lon_lat                   # 光伏电站的经纬度 "lon_lat": "lon,lat"  
        self.point_day = point_day               # 颗粒度:"point_day":24,48,96，与历史出力的颗粒度对齐
        self.point_movement = point_movement     # 气象数据向前挪动的点数 
        self.cap = cap                           # 光伏额定容量   

    def data_prepare(self):
        """校验历史数据"""
        logger.info("----------校验历史数据-------------------")
        if isinstance(self.his_power, dict):
            if len(self.his_power['ds']) != len(self.his_power['power']):
                logging.info(f"ds与power字段长度不一致，len(ds)={len(self.his_power['ds'])},len(power)={len(self.his_power['power'])}")
            self.his_power = pd.DataFrame(self.his_power)
            self.his_power['date'] = self.his_power['ds'].apply(lambda s : str(s)[:10])
            self.his_power.sort_values(by="ds",inplace=True)

        logging.info(f"his_power类型应为dict，而不是{type(self.his_power)}")
        return self.his_power
    
    def add_points(self,):
        """根据挪点数确定时点特征字典"""
        if self.point_day == 24:
            time_range = pd.date_range("00:00:00", "23:00:00", freq="60min").astype(str).map(lambda s: s.split(' ')[1])
            time_points = np.arange(1, 25)
        elif self.point_day == 48:
            time_range = pd.date_range("00:00:00", "23:30:00", freq="30min").astype(str).map(lambda s: s.split(' ')[1])
            time_points = np.arange(1, 49)
        elif self.point_day == 96:
            time_range = pd.date_range("00:00:00", "23:45:00", freq="15min").astype(str).map(lambda s: s.split(' ')[1])
            time_points = np.arange(1, 97)
        # 根据point_movement移动时刻顺序
        time_points = np.roll(time_points, -self.point_movement)

        time_dict = pd.DataFrame({"time": time_range, "points": time_points})
        return time_dict
    
    def get_data_his_obs(self,table_name="e_terraqt_era5_weather"):
        """从e_terraqt_era5_weather中取出当前光伏电站经纬度的历史气象数据"""
        logger.info("----------从e_terraqt_era5_weather中取出当前光伏电站经纬度的历史气象数据-------------------")
        # 确定历史数据的日期范围
        date_uniques = self.his_power['date'].unique()
        if self.point_movement > 0:
            # 向后移动，最小日期加一天
            min_date = date_uniques.min()
            new_min_date = (pd.to_datetime(min_date) + pd.Timedelta(days=-1)).strftime('%Y-%m-%d')
            date_list = [new_min_date] + date_uniques.tolist()
        elif self.point_movement == 0:
            # 不移动
            date_list = date_uniques.tolist()
        elif self.point_movement < 0:
            # 向前移动，最大日期加一天
            max_date = date_uniques.max()
            new_max_date = (pd.to_datetime(max_date) + pd.Timedelta(days=1)).strftime('%Y-%m-%d')
            date_list = date_uniques.tolist() + [new_max_date]
        try:
            start_date = min(date_list)
            end_date = max(date_list)
        except ValueError:
            logging.info("日期列表为空，无法确定起始和结束日期。")
            start_date = None
            end_date = None
        
        # 数据库连接信息
        db_url = "mysql+pymysql://extana:Grafana2019@124.71.13.142:3306/extana"
        # 创建数据库引擎
        engine = create_engine(db_url)
        # 创建数据库引擎
        query = f"""
        SELECT *
        FROM {table_name}
        WHERE DATE(t_datetime_cst) BETWEEN '{start_date}' AND '{end_date}'
        AND lon = {self.lon_lat[0]}
        AND lat = {self.lon_lat[1]}
        """
        print(query)
        
        try:
            df = pd.read_sql(query, con=engine)
            print(df.columns)
            return df
        except Exception as e:
            logging.info(f"数据查询失败: {e}")
            return pd.DataFrame()  # 返回空 DataFrame 以防出错

    def weather_point_fill(self,weather):
        """气象数据时点填充"""
        logger.info("----------气象数据时点填充-------------------")
        logging.info("---------------气象数据时点填充---------------")
        # 根据当前数据的间隔分钟数，对气象数据进行填充
        if self.point_day == 24:
            min_period = '1h'
            time_end = " 23:00:00"
        elif self.point_day == 48:
            min_period = '30T'
            time_end = " 23:30:00"
        elif self.point_day == 96:
            min_period = '15T'
            time_end = " 23:45:00"
        # 构造完整时间序列
        t_date_list = pd.to_datetime(weather['t_datetime_cst']).dt.date.astype(str).unique().tolist()
        print("\nt_date_list")
        print(t_date_list)
        t_date_time_start = str(pd.to_datetime(min(t_date_list))).split(" ")[0] + " 00:00:00"
        print(t_date_time_start)
        t_date_time_end = str(pd.to_datetime(max(t_date_list))).split(" ")[0] + time_end
        print(t_date_time_start)
        weather_tmp = pd.DataFrame({"t_datetime_cst": pd.date_range(t_date_time_start, t_date_time_end,
                                                                    freq=min_period).astype(str)
                                        })
        # 设置参数
        cols_ffill = ['lat', 'lon']
        cols_interpolate = [
             'u_10m', 'v_10m', 'wspd_10m', 'u_100m',
            'v_100m', 'wspd_100m', 't2m', 
            # 'ssrd-acc', 'ssr-acc', 
            'ssr', 
            'ssrd',
            'tp', 'sp', 'rh'
        ]
        # 组合完整时间
        weather['t_datetime_cst'] = weather['t_datetime_cst'].astype(str)
        weather_merge = pd.merge(weather_tmp, weather, how="left")
        # 向后填充
        weather_merge[cols_ffill] = weather_merge[cols_ffill].ffill()
        # 线性插值
        weather_merge[cols_interpolate] = weather_merge[cols_interpolate].interpolate()
        weather_merge['t_date_cst'] = weather_merge['t_datetime_cst'].map(lambda s: s.split(" ")[0])
        return weather_merge,cols_interpolate,min_period
    
    def point_to_time(self,points):
        """
        将时间点转换为时间格式（每个点代表 30 分钟）。
        :param points: 时间点（1 到 48）
        :return: 时间字符串，格式为 HH:MM:00
        """
        if self.point_day==48:
            hours = (points - 1) // 2  # 每 2 个点代表 1 小时
            minutes = ((points - 1) % 2) * 30  # 余数乘以 30 分钟
        elif self.point_day==96:
            hours = (points - 1) // 4
            minutes = ((points - 1) % 4) * 15
        elif self.point_day==24:
            hours = points - 1  # 每个点代表 1 小时
            minutes = 0  # 分钟固定为 0
        return f"{hours:02d}:{minutes:02d}:00"
    
    def process_train_data(self,weather):
        """对气象数据进行挪点，与历史气象数据进行时点匹配,构造训练数据"""
        logger.info("----------训练数据处理-------------------")
        # 添加时点特征
        time_dict = self.add_points()
        weather['time'] = weather['t_datetime_cst'].str[11:]
        weather = pd.merge(weather,time_dict,on='time')
        weather = weather.sort_values(by='t_datetime_cst')
        # 选出对应的气象数据、替换t_datetime_cst字段
        if self.point_movement<=0:
            weather = weather[(-self.point_movement):len(self.his_power)+(-self.point_movement)]
            # print(weather)
        elif self.point_movement>0:
            weather = weather[(self.point_day-self.point_movement):len(self.his_power)+(self.point_day-self.point_movement)]
            
        weather = weather.reset_index()
        weather['ds'] = self.his_power['ds']
        train_data = pd.merge(self.his_power,weather,on="ds")
        return train_data
    
    def generate_time_series_result(self,min_period):
        """
        生成预测结果的时间序列的函数
        
        参数:
        start_datetime (str): 起始时间，格式为 "YYYY-MM-DD HH:MM:SS"
        min_period (str): 时间间隔，例如 "30T"（30分钟）、"15T"（15分钟）、"1H"（1小时）
        point_movement (int): 起始时间的偏移量，正数表示往前推，负数表示往后推
        t (int): 生成的时间序列的天数

        返回:
        pd.DatetimeIndex: 生成的时间序列
        """
        # 将起始时间转换为datetime对象
        start_datetime = pd.to_datetime(self.start_datetime)

        # 计算时间间隔的分钟数
        if "T" in min_period:
            period_minutes = int(min_period[:-1])  # 提取分钟数
        elif "h" in min_period:
            period_minutes = int(min_period[:-1]) * 60  # 提取小时数并转换为分钟
        else:
            raise ValueError("min_period 格式不正确，应为 'XT' 或 'XH'，例如 '30T' 或 '1H'")

        # 计算起始时间的偏移量
        start_time = start_datetime
        # 计算总的时间序列长度
        if "T" in min_period:
            periods_per_day = 24 * 60 // period_minutes  # 每天的间隔数
        elif "h" in min_period:
            periods_per_day = 24 // (period_minutes // 60)  # 每天的间隔数

        total_periods = self.t * periods_per_day  # 总的时间序列长度

        # 生成时间序列
        time_series = pd.date_range(start=start_time, periods=total_periods, freq=min_period)
        return time_series
    
    def train_pred(self,train_data,cols,min_period):
        """历史数据训练模型、并预测"""
        logger.info("----------历史数据训练模型、并预测-------------------")
        cols_train = cols + ['points']
        wea_train = train_data[cols_train].values
        df_train = train_data['power'].values
        # 训练
        m = ExtraTreesRegressor(random_state=1).fit(wea_train, df_train)
        pred_weather = self.get_data_pred_obs(min_period=min_period)
        weather_pred = pred_weather[cols_train].values
        # pred_weather.to_csv('pred_weather.csv',index=False)
        # 预测
        pred = m.predict(weather_pred)
        pred_weather_datetime = self.generate_time_series_result(min_period=min_period)
        # 将预测结果和时间整理为 DataFrame
        result_df = pd.DataFrame({
            'date_time': pred_weather_datetime,  # 时间字段
            'power': pred  # 预测结果字段
        }) 
        # 0-5 21-0 结果置为0
        result_df['time'] = result_df['date_time'].dt.strftime('%H:%M:%S')
        result = self.set_pred_power_to_zero(df=result_df,min_period=min_period,time_column='time')
        result = result.drop(columns="time", axis=1)
        result['date_time'] = pd.to_datetime(result['date_time']).dt.strftime('%Y-%m-%d %H:%M:%S')
        # result.to_csv("result.csv",index=False)
        # # 将预测结果和时间整理为字典
        result_dict = {
            'date_time': result['date_time'].values.tolist(),  # 时间列表
            'power': result['power'].values.tolist()  # 预测结果列表
        }
        print(result_dict)
        logger.info("----------光伏电站出力预测完成-------------------")
        return result_dict
            
    def generate_time_series(self,min_period):
        """
        生成时间序列的函数，生成时间序列的函数，从获取到的预测气象数据中截取需要的

        参数:
        start_datetime (str): 起始时间，格式为 "YYYY-MM-DD HH:MM:SS"
        min_period (str): 时间间隔，例如 "30T"（30分钟）、"15T"（15分钟）、"1H"（1小时）
        point_movement (int): 起始时间的偏移量，正数表示往前推，负数表示往后推
        t (int): 生成的时间序列的天数

        返回:
        pd.DatetimeIndex: 生成的时间序列
        """
        # 将起始时间转换为datetime对象
        start_datetime = pd.to_datetime(self.start_datetime)

        # 计算时间间隔的分钟数
        if "T" in min_period:
            period_minutes = int(min_period[:-1])  # 提取分钟数
            
        elif "h" in min_period:
            period_minutes = int(min_period[:-1]) * 60  # 提取小时数并转换为分钟
            
        else:
            raise ValueError("min_period 格式不正确，应为 'XT' 或 'Xh'，例如 '30T' 或 '1h'")
        # 计算起始时间的偏移量
        if self.point_movement>=0:
            start_time = start_datetime + timedelta(minutes=period_minutes * -self.point_movement)
        else:
            start_time = start_datetime + timedelta(minutes=period_minutes * -self.point_movement)
        # 计算总的时间序列长度
        if "T" in min_period:
            periods_per_day = 24 * 60 // period_minutes  # 每天的间隔数
        elif "h" in min_period:
            periods_per_day = 24 // (period_minutes // 60)  # 每天的间隔数
        total_periods = self.t * periods_per_day  # 总的时间序列长度
        # 生成时间序列
        time_series = pd.date_range(start=start_time, periods=total_periods, freq=min_period)
        time_series_str_list = [dt.strftime("%Y-%m-%d %H:%M:%S") for dt in time_series]
        return time_series_str_list

    def get_pred_weather(self,time_value):
        """获取预测日气象数据"""
        variable_names = [
                        't2m@C',  # 地面2米气温
                        'u10m',   # 地面10米风U分量
                        'v10m',  # 地面10米风V分量
                        'ws10m',  # [预处理]地面10米风速
                        'u100m',  # 地面100米风U分量
                        'v100m',  # 地面100米风V分量
                        'ws100m',  # [预处理]地面100米风速
                        'ssrd-acc', # [原始累积值] 地表下行短波(太阳)辐射
                        'ssr-acc', # [原始累积值] 地表净短波(太阳)辐射
                        'ssr',  # [预处理] 地表净短波（太阳）辐射<br>地表接收的短波(太阳)辐射 - 地表反射回大气的短波辐射<br>地球年平均地表净短波辐射约为168W/m^2
                        'ssrd',  # [预处理] 地表下行短波（太阳）辐射<br>从大气向地表辐射的短波(太阳)辐射通量，短波指可见光和近红外<br>是光伏发电的主要能源来源
                
                        # 'ssr-acc','ssrd-acc',
                        # 'tp-acc',  # [原始累积值] 总降水
                        'tp',  # [预处理] 一小时内总降水
                        # 'asn',  # 雪反照率<br>新鲜的雪大约在0.95左右，随着杂质累积会降低到0.4左右
                        'sp',  # 地表气压
                        # 'msl', # 平均海平面气压
                        'rh', # [预处理] 相对湿度
                        ]
        
        retry_strategy = Retry(
            total=5,  # 增加重试次数
            backoff_factor=1,  # 退避因子，间隔时间将逐渐增加
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["POST"],
        )
        adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100, max_retries=retry_strategy)

        # API 请求 URL
        url = f"https://api-pro-openet.terraqt.com/v1/ifs_surface/multi/point"
        # url = "https://api-pro-openet.terraqt.com/v1/era5_surface/point"
        headers = {
            'Content-Type': 'application/json',
            'token': "kBDMwMDNlZzY2ADMwQWZwIjYhZTOyUGZ",
        }

        # 使用上下文管理器自动管理 Session 的生命周期
        with requests.Session() as session:
            # 挂载适配器
            session.mount("https://", adapter)
            session.mount("http://", adapter)

            all_data = pd.DataFrame(columns=['lat','lon','u_10m','v_10m','wspd_10m','u_100m','v_100m','wspd_100m','t_datetime_cst'])  # 用于存储当前 time 值的所有坐标的预测数据
            for coord_list in [self.lon_lat]:
                print(f"正在处理坐标列表: {coord_list}")

                # 更新 request_data 中的 time 参数和 points 参数
                request_data = {
                    'points': [coord_list],
                    'avg': False,
                    'mete_vars': variable_names,
                    'time': time_value,  # 使用当前遍历的 time 值
                }

                try:
                    # 发送请求
                    response = session.post(url, headers=headers, json=request_data, timeout=300)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            # print(data)
                            data_p = self.process_data_to_dataframe(data)
                            # all_data.append(data)
                            all_data = pd.concat([all_data,data_p])
                            print(f"API 请求成功")
                        except ValueError as e:
                            print(f"JSON 解析失败): {e}")
                    
                    else:
                        print(f"API 请求失败 ，状态码: {response.status_code}，错误信息: {response.text}")
                except requests.exceptions.RequestException as e:
                    print(f"请求发生错误 ( 部分坐标): {e}")
                    # 生成随机的睡眠时间（例如 0.5 到 2 秒之间）
                sleep_time = random.uniform(0.5, 2)  # 随机生成 0.5 到 2 秒的睡眠时间
                print(f"随机睡眠时间: {sleep_time:.2f} 秒")
                time.sleep(sleep_time)  # 暂停随机时间
            # 返回当前 time 值的所有坐标预测数据
            print(f"预测数据已获取")
            print(all_data.shape)
            return all_data
          
    def process_data_to_dataframe(self,data):
        """预测气象数据整理，整理获取的json数据为dataframe格式,并重命名字段名称"""
        # 假设 `data` 是 response.json()['data']
        data_df = data['data']
        # 提取变量名
        variable_names = data_df.get('mete_var', [])
        # 初始化一个空的 DataFrame
        df_list = []
        # 遍历每个 location 和 values 对
        for item in data_df['data']:
            location = item['location']  # 获取 location
            values = item['values']  # 获取数据值
            # 将 values 转换为 DataFrame
            temp_df = pd.DataFrame(values, columns=variable_names)
            # 添加位置信息和其他元数据
            temp_df['longitude'] = location[0]
            temp_df['latitude'] = location[1]
            temp_df['time_fcst'] = data_df['time_fcst']
            temp_df['timestamp'] = data_df['timestamp']
            # 将临时 DataFrame 添加到列表中
            df_list.append(temp_df)
        # 合并所有 DataFrame
        df = pd.concat(df_list, ignore_index=True)
        
        # 重命名字段名称
        df.rename(columns={
            'time_fcst': 'p_date_utc',
            'latitude':'lat',
            'longitude':'lon',
            'u10m':'u_10m',
            'v10m':'v_10m',
            'ws10m':'wspd_10m',
            'u100m':'u_100m',
            'v100m':'v_100m',
            'ws100m':'wspd_100m',
            'timestamp':'t_datetime_cst'
            },inplace=True)
        return df

    def set_ssrd_ssr_to_zero(self,df, min_period, time_column='time'):
        """
        根据 min_period 参数，在指定时间范围内将 ssrd 和 ssr 字段置为 0
        :param df: 输入的 DataFrame
        :param min_period: 时间间隔，可以是 "1h"、"30T" 或 "15T"
        :param time_column: 时间列的名称，默认为 'time'
        :return: 修改后的 DataFrame
        """
        # 确保时间列是 datetime 类型
        df[time_column] = pd.to_datetime(df[time_column])
        start_time_1 = '00:00:00'
        end_time_1 = '05:00:00'
        start_time_2 = '21:00:00'
        # 根据 min_period 设置时间范围
        if min_period == "1h":
            # 1h 间隔
            end_time_2 = '23:00:00'
        elif min_period == "30T":
            # 30T 间隔
            end_time_2 = '23:30:00'
        elif min_period == "15T":
            # 15T 间隔
            end_time_2 = '23:45:00'
        else:
            raise ValueError("min_period 参数无效，仅支持 '1h'、'30T' 或 '15T'")
        
        # 将时间列转换为字符串以便筛选
        df['time_str'] = df[time_column].dt.time.astype(str)
        
        # 筛选时间范围并将 ssrd 和 ssr 置为 0
        df.loc[
            ((df['time_str'] >= start_time_1) & (df['time_str'] <= end_time_1)) |
            ((df['time_str'] >= start_time_2) & (df['time_str'] <= end_time_2)),
            ['ssrd', 'ssr']
        ] = 0
        
        # 删除临时列
        df = df.drop(columns=['time_str'])
        df[time_column] = df[time_column].astype(str)
        return df
    
    def set_pred_power_to_zero(self,df, min_period, time_column='time'):
        """
        根据 min_period 参数，在指定时间范围内将 ssrd 和 ssr 字段置为 0
        :param df: 输入的 DataFrame
        :param min_period: 时间间隔，可以是 "1h"、"30T" 或 "15T"
        :param time_column: 时间列的名称，默认为 'time'
        :return: 修改后的 DataFrame
        """
        # 确保时间列是 datetime 类型
        df[time_column] = pd.to_datetime(df[time_column])
        start_time_1 = '00:00:00'
        end_time_1 = '05:00:00'
        start_time_2 = '21:00:00'
        # 根据 min_period 设置时间范围
        if min_period == "1h":
            # 1h 间隔
            end_time_2 = '23:00:00'
        elif min_period == "30T":
            # 30T 间隔
            end_time_2 = '23:30:00'
        elif min_period == "15T":
            # 15T 间隔
            end_time_2 = '23:45:00'
        else:
            raise ValueError("min_period 参数无效，仅支持 '1h'、'30T' 或 '15T'")
        
        # 将时间列转换为字符串以便筛选
        df['time_str'] = df[time_column].dt.time.astype(str)
        
        # 筛选时间范围并将 ssrd 和 ssr 置为 0
        df.loc[
            ((df['time_str'] >= start_time_1) & (df['time_str'] <= end_time_1)) |
            ((df['time_str'] >= start_time_2) & (df['time_str'] <= end_time_2)),
            ['power']
        ] = 0
        
        # 删除临时列
        df = df.drop(columns=['time_str'])
        df[time_column] = df[time_column].astype(str)
        return df
    
    def get_data_pred_obs(self,min_period):
        """获取预测的气象数据"""
        
        if self.point_movement<0: 
            # 生成时间序列
            time_series = pd.date_range(start=self.start_datetime, periods = (self.t+1) * self.point_day, freq=min_period)
        elif self.point_movement==0: 
            time_series = pd.date_range(start=self.start_datetime, periods = self.t * self.point_day, freq=min_period)
        elif self.point_movement>0: 
            # 因为永远取的是昨天的天气，所以即使是向后挪动，长度也够，因此起始时间不用减掉1天
            # start_time = pd.to_datetime(self.start_datetime) - timedelta(1)
            time_series = pd.date_range(start=self.start_datetime, periods = self.t * self.point_day, freq=min_period)
        
        # 根据时间序列确定预测特征长度、要获取的气象数据日期
        unique_dates = pd.Series(time_series.date).astype(str).unique()

        
        # 判断获取哪一天发布的气象信息
        current_time = datetime.now()
        print(current_time)
        # 格式化输出
        formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        print("当前时间:", formatted_time)
        

        
        time_value = str(pd.to_datetime(min(unique_dates)) - timedelta(1))
        print(time_value)
        pred_weather = self.get_pred_weather(time_value = time_value)
        pred_weather.to_csv('pred_weather_ori.csv',index=False)
        # 对气象数据进行处理
        pred_weather_p,cols,min_period = self.weather_point_fill(weather = pred_weather)
        # ---------------------------------特殊处理，------------------------------------

        time_dict = self.add_points()
        pred_weather_p['time'] = pred_weather_p['t_datetime_cst'].str[11:]
        pred_weather_p = pd.merge(pred_weather_p,time_dict,on='time')
        pred_weather_p = pred_weather_p.sort_values(by='t_datetime_cst')
        pred_weather_p = self.set_ssrd_ssr_to_zero(df=pred_weather_p, min_period=min_period, time_column='time')

        pred_weather_p['time'] = pd.to_datetime(pred_weather_p['time']).dt.strftime('%H:%M:%S')

        # 给start_time 加上挪动的点数 确定预测日期的开始时间 ，结束的时间根据t和point_day一起确定
        pred_weather_datetime = self.generate_time_series(min_period=min_period)

        # pred_weather = pred_weather_p[pred_weather_p['t_datetime_cst'].isin(pred_weather_datetime)]
        pred_weather = pred_weather_p[
            (pred_weather_p['t_datetime_cst']>=min(pred_weather_datetime))&
            (pred_weather_p['t_datetime_cst']<=max(pred_weather_datetime))
            ]

        return pred_weather
        
if __name__ == "__main__":
    # 1. 读取历史数据
    input_file = "input.json"  # 替换为你的 JSON 文件路径
    # 读取 JSON 文件
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)  # 将 JSON 文件内容加载为 Python 字典
    # 打印读取的数据
    # print("读取的 JSON 数据：")
    # print(json.dumps(data, indent=4, ensure_ascii=False))
    
    # 2. 校验历史数据
    sp_instance = SunPowerPred( 
                                t=data['t'],
                                start_datetime=data['start_datetime'],
                                his_power=data['his_power'],
                                lon_lat=data['lon_lat'],
                                point_day=data['point_day'],
                                point_movement=data['point_movement'],
                                cap=data['cap']
                                )
    
    his_power = sp_instance.data_prepare()
    # print(his_power)
    # 3. 获取历史气象数据、补充点数
    his_weather = sp_instance.get_data_his_obs()
    his_weather_p,cols,min_period = sp_instance.weather_point_fill(weather = his_weather)
    print(min_period)
    
    # print(his_weather_p.columns)
    # print(his_weather_p[30:])
    # 4. 气象数据挪点、添加时点特征、准备历史训练数据
    train_data = sp_instance.process_train_data(weather = his_weather_p)
    # print(train_data)
    # train_data.to_csv('train_data.csv',index=False)
    result = sp_instance.train_pred(train_data = train_data,cols = cols,min_period = min_period)
