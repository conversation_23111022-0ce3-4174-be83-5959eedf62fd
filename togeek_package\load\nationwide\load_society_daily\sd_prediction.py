# -*- coding: utf-8 -*-
# valid license

import pandas as pd
from json import loads
from prophet import Prophet


class Prediction:
    def __init__(self, elec, holiday, pred_days, cap=None, floor=None, include_history=True, flexibility=5):
        assert 1 <= flexibility <= 10
        self.params = {'seasonality_mode': 'multiplicative', 'changepoint_prior_scale': flexibility / 100}
        self.elec = self._prepare_elec(elec, cap, floor, self.params)
        self._prepare_holiday(holiday, self.params)
        self.pred_days = pred_days
        self.inc_his = include_history

    def _prepare_elec(self, elec, cap, floor, params):
        if isinstance(elec, str):
            elec = loads(elec)
        if isinstance(elec, dict):
            elec['ds'] = pd.to_datetime(elec['ds'])
            elec = pd.DataFrame(elec)
        if cap is not None:
            elec['cap'] = cap
            if floor is None:
                elec['floor'] = floor
        if 'cap' in elec:
            params['growth'] = 'logistic'
        return elec

    def _prepare_holiday(self, holiday, params):
        if holiday is None:
            return
        if isinstance(holiday, str):
            holiday = loads(holiday)
        if isinstance(holiday, dict):
            holiday['ds'] = pd.to_datetime(holiday['ds'])
            holiday = pd.DataFrame(holiday)
        params['holidays'] = holiday

    def predict(self, to_json=False):
        model = Prophet(**self.params)
        model.fit(self.elec)
        future = model.make_future_dataframe(periods=self.pred_days, include_history=self.inc_his)
        result = model.predict(future)[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
        if to_json:
            result = {'ds': result.ds.astype(str).tolist(),
                      'yhat': result.yhat.tolist(),
                      'yhat_lower': result.yhat_lower.tolist(),
                      'yhat_upper': result.yhat_upper.tolist()}
        return result


if __name__ == '__main__':
    print("803")
    elec = pd.read_pickle('electricity.pkl')
    print("804")
    holiday = pd.read_pickle('holiday.pkl')
    p = Prediction(elec, holiday, 30)
    print("805")
    print(p.predict(True))
