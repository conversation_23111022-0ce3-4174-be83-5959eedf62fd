# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.nationwide.price_declaration_pre import Declaration


class DeclarationValueEvalHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        date_list = params.pop('date_list')
        sj_sign = params.get('sj_sign', 0)
        sj_date = params.get('sj_date', [])
        min_value = params.get("min_value", 0)
        max_value = params.get("max_value", 1500)
        pred = Declaration(source_data=source_data, date_list=date_list, sj_sign=sj_sign, sj_date=sj_date,
                           min_value=min_value, max_value=max_value)
        self.write(pred.pre_price(to_json=True))
