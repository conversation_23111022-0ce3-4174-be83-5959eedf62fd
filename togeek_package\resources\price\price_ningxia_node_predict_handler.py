from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.ningxia import ETRNingxiaNodePricePredict

class ETRNingxiaNodePricePredictHandler(RequestHandlerBase):
    def put(self):

        params = j2o(self.request.body.decode())
        D = params.pop('D')
        test_days = params.pop('test_days')
        price_dict = params.pop('price_data')
        weather_dict = params.pop('weather_data')
        grid_dict = params.pop('grid_data')


        predictor = ETRNingxiaNodePricePredict(price_dict=price_dict,
                                               weather_dict=weather_dict,
                                               grid_dict=grid_dict)
        result_dict = predictor.run(D=D, test_days=test_days)

        self.write(result_dict)
