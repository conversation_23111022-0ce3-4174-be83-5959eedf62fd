[DEFAULT]

tongyijiesuan_keys_山西 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
tongyijiesuan_all_keys_山西 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "日前必开机组", "日前必停机组", "是否为节假日", "检修总容量"]
tongyijiesuan_beijianshu_山西 = ["负荷预测"]
tongyijiesuan_jianshu_山西 = ["日前联络线计划", "日前新能源负荷预测"]

jiedian_keys_山西 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
jiedian_all_keys_山西 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "日前必开机组", "日前必停机组", "是否为节假日", "检修总容量"]
jiedian_beijianshu_山西 = ["负荷预测"]
jiedian_jianshu_山西 = ["日前联络线计划", "日前新能源负荷预测"]


tongyijiesuan_keys_山西华电 = ["统调公用市场化煤机出力空间", "市场化煤机富裕容量（发电侧）", "负荷总需求", "直调用电负荷（平台）"]
tongyijiesuan_all_keys_山西华电 = ["统调公用市场化煤机出力空间", "市场化煤机富裕容量（发电侧）", "负荷总需求", "直调用电负荷（平台）"]
tongyijiesuan_beijianshu_山西华电 = ["煤机出力空间"]
tongyijiesuan_jianshu_山西华电 = ["非市场化煤机占用"]
tongyijiesuan_jianshu_02_山西华电 = ["非市场化煤机占用"]


jiedian_keys_山西华电 = ["统调公用市场化煤机出力空间", "市场化煤机富裕容量（发电侧）", "负荷总需求", "直调用电负荷（平台）"]
jiedian_all_keys_山西华电 = ["统调公用市场化煤机出力空间", "市场化煤机富裕容量（发电侧）", "负荷总需求", "直调用电负荷（平台）"]
jiedian_beijianshu_山西华电 = ["煤机出力空间"]
jiedian_jianshu_山西华电 = ["非市场化煤机占用"]
jiedian_jianshu_02_山西华电 = ["非市场化煤机占用"]

tongyijiesuan_keys_广东 = ["统调负荷预测", "省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测", "检修总容量预测", "日前必开机组"]
tongyijiesuan_all_keys_广东 = ["统调负荷预测", "省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测", "检修总容量预测", "日前必开机组"]
tongyijiesuan_beijianshu_广东 = ["统调负荷预测"]
tongyijiesuan_jianshu_广东 = ["省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测"]

jiedian_keys_广东 = ["统调负荷预测", "省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测", "检修总容量预测", "日前必开机组"]
jiedian_all_keys_广东 = ["统调负荷预测", "省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测", "检修总容量预测", "日前必开机组"]
jiedian_beijianshu_广东 = ["统调负荷预测"]
jiedian_jianshu_广东 = ["省内A类电源预测", "地方电源出力预测", "西电东送电力预测", "粤港联络线预测"]

tongyijiesuan_keys_通用 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
tongyijiesuan_all_keys_通用 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "日前必开机组", "日前必停机组", "是否为节假日", "检修总容量"]
tongyijiesuan_beijianshu_通用 = ["负荷预测"]
tongyijiesuan_jianshu_通用 = ["日前联络线计划", "日前新能源负荷预测"]

jiedian_keys_通用 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
jiedian_all_keys_通用 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "日前必开机组", "日前必停机组", "是否为节假日", "检修总容量"]
jiedian_beijianshu_通用 = ["负荷预测"]
jiedian_jianshu_通用 = ["日前联络线计划", "日前新能源负荷预测"]

tongyijiesuan_keys_山东 = ["直调负荷", "联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加", "地方电厂发电总加"]
tongyijiesuan_all_keys_山东 = ["直调负荷", "联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加", "地方电厂发电总加"]
tongyijiesuan_beijianshu_山东 = ["直调负荷"]
tongyijiesuan_jianshu_山东 = ["联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加"]

jiedian_keys_山东 = ["直调负荷", "联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加", "地方电厂发电总加"]
jiedian_all_keys_山东 = ["直调负荷", "联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加", "地方电厂发电总加"]
jiedian_beijianshu_山东 = ["直调负荷"]
jiedian_jianshu_山东 = ["联络线受电负荷", "风电负荷", "光伏负荷", "核电总加", "自备机组总加"]

tongyijiesuan_keys_蒙西 = ["现货竞价空间", "日前东送计划", "日前新能源负荷预测"]
tongyijiesuan_all_keys_蒙西 = ["负荷预测", "日前新能源负荷预测", "日前东送计划", "检修总容量"]
tongyijiesuan_beijianshu_蒙西 = ["负荷预测"]
tongyijiesuan_jianshu_蒙西 = ["日前东送计划", "日前新能源负荷预测"]

jiedian_keys_蒙西 = ["现货竞价空间", "日前东送计划", "日前新能源负荷预测"]
jiedian_all_keys_蒙西 = ["负荷预测", "日前新能源负荷预测", "日前东送计划", "检修总容量", "正备用"]
jiedian_beijianshu_蒙西 = ["负荷预测"]
jiedian_jianshu_蒙西 = ["日前东送计划", "日前新能源负荷预测", "检修总容量"]


tongyijiesuan_keys_福建 = ["现货竞价空间", "联络线计划预测", "可再生能源总出力预测"]
tongyijiesuan_all_keys_福建 = ["统调负荷预测", "可再生能源总出力预测", "联络线计划预测", "检修总容量", "非市场化出力"]
tongyijiesuan_beijianshu_福建 = ["统调负荷预测"]
tongyijiesuan_jianshu_福建 = ["联络线计划预测", "可再生能源总出力预测", "非市场化出力"]

jiedian_keys_福建 = ["现货竞价空间", "联络线计划预测", "可再生能源总出力预测"]
jiedian_all_keys_福建 = ["统调负荷预测", "可再生能源总出力预测", "联络线计划预测", "检修总容量", "非市场化出力"]
jiedian_beijianshu_福建 = ["统调负荷预测"]
jiedian_jianshu_福建 = ["联络线计划预测", "可再生能源总出力预测", "非市场化出力"]

tongyijiesuan_keys_新疆 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
tongyijiesuan_all_keys_新疆 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "检修总容量", "调频需求"]
tongyijiesuan_beijianshu_新疆 = ["负荷预测"]
tongyijiesuan_jianshu_新疆 = ["日前联络线计划", "日前新能源负荷预测"]

jiedian_keys_新疆 = ["现货竞价空间", "日前联络线计划", "日前新能源负荷预测"]
jiedian_all_keys_新疆 = ["负荷预测", "日前新能源负荷预测", "日前联络线计划", "检修总容量", "调频需求"]
jiedian_beijianshu_新疆 = ["负荷预测"]
jiedian_jianshu_新疆 = ["日前联络线计划", "日前新能源负荷预测"]