#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/7/12 16:20
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_energy import MxEnergy
class MxEnergyHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price_data = params.pop('price_data')
        power_data = params.pop('power_data',  {})
        soc0 = params.pop('soc0')
        energy_q = params.pop('energy_q')
        cap_feng = params.pop('cap_feng', 0)
        p_ch = params.pop('p_ch')
        p_f = params.pop('p_f')
        energy_loss = params.pop('energy_loss')
        min_power = params.pop('min_power')
        max_power = params.pop('max_power')
        points = params.pop('points', 96)
        low_income = params.pop('low_income', 1)
        unit_con = params.pop('unit_con', 1000)
        model = MxEnergy(price_data=price_data, power_data=power_data, soc0=soc0, energy_q=energy_q,
                         cap_feng=cap_feng, p_ch=p_ch, p_f=p_f, energy_loss=energy_loss, min_power=min_power,
                         max_power=max_power, points=points, low_income=low_income, unit_con=unit_con)
        result = model.get_result()
        self.write(result)