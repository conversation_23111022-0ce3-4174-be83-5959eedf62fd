# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON>
Datetime: 2023/1/13/013 18:26
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shandong.standard_declaration_fitting import StandardDeclarationFitting

class StandardDeclarationFittingHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        running_generator = params.get('running_generator')
        standard_declaration = params.get('standard_declaration')
        load = params.get('load')
        price = params.get('price')
        pred_date = params.get('pred_date')
        sim_dates = params.get('sim_dates', 14)
        points = params.get('points', 24)
        min_price = params.get('min_price', 0)
        max_price = params.get('max_price', 1500)
        pred = StandardDeclarationFitting(running_generator=running_generator,
                                          standard_declaration=standard_declaration,
                                          load=load,
                                          price=price,
                                          pred_date=pred_date,
                                          sim_dates=sim_dates,
                                          points=points,
                                          min_price=min_price,
                                          max_price=max_price)
        result = pred.price_fitting(to_json=True)
        self.write(result)