"""
Author: Laney
Datetime: 2022/9/27/027 13:24
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_deep_adjust import OptimizeDeepAdjustSingle, OptimizeDeepAdjustDouble, \
    OptimizeDeepAdjustCommon


class OptimizeDeepAdjustHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generator_params = params.pop('generator_params')
        generator_constraints = params.pop('generator_constraints')
        plant_elec_rate = params.pop('plant_elec_rate')
        deep_adjust_params = params.pop('deep_adjust_params')
        market_params = params.pop('market_params')
        generator_step = params.pop('generator_step', 1)
        eboiler_step = params.pop('eboiler_step', 1)
        gener_type = params.pop('gener_type', 1)   # 默认为1，其中：1-单机组运行；2-双机组运行
        if gener_type == 1:
            coal_consum_func = params.pop('coal_consum_func',
                                          [-4.74563558e-08, 1.45483857e-08, 2.13840137e-09, 4.58335480e-11,
                                           5.54337885e-05, -8.23418551e-06, -6.15648273e-07, 2.59435718e-01,
                                           1.70057089e-02, 1.40767179e+01])
            model = OptimizeDeepAdjustSingle(generator_params=generator_params,
                                             coal_consum_func=coal_consum_func,
                                             generator_constraints=generator_constraints,
                                             plant_elec_rate=plant_elec_rate,
                                             deep_adjust_params=deep_adjust_params,
                                             market_params=market_params)
        elif gener_type == 2:
            coal_consum_func = params.pop('coal_consum_func',
                                          [[-4.74563558e-08, 1.45483857e-08, 2.13840137e-09, 4.58335480e-11, 5.54337885e-05,
                                            -8.23418551e-06, -6.15648273e-07, 2.59435718e-01, 1.70057089e-02, 1.40767179e+01],
                                           [-4.74563558e-08, 1.45483857e-08, 2.13840137e-09, 4.58335480e-11, 5.54337885e-05,
                                            -8.23418551e-06, -6.15648273e-07, 2.59435718e-01, 1.70057089e-02, 1.40767179e+01]]
                                          )
            model = OptimizeDeepAdjustDouble(generator_params=generator_params,
                                             coal_consum_func=coal_consum_func,
                                             generator_constraints=generator_constraints,
                                             plant_elec_rate=plant_elec_rate,
                                             deep_adjust_params=deep_adjust_params,
                                             market_params=market_params)
        else:
            raise Exception(f'不支持[gener_type = {gener_type}]的运行模式')
        model.fit(generator_step, eboiler_step)
        result = model.get_result()
        self.write(dict(result))


class OptimizeDeepAdjustCommonHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        param = params.pop('param')
        generator_number = params.pop('generator_number')
        deep_shift = params.pop('deep_shift')
        generator_step = params.pop('generator_step', 1)
        eboiler_step = params.pop('eboiler_step', 1)
        model = OptimizeDeepAdjustCommon(param=param, generator_number=generator_number, deep_shift=deep_shift)
        res = model.deal_result(generator_step, eboiler_step)
        self.write(res)

