"""
Author: <PERSON><PERSON>
Datetime: 2022/12/29/029 11:21
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shanxi.price_total import PricePredTotal


class PricePredTotalHandlerServiceCommonSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.pop('data')
        pred_date = params.pop('pred_date', None)
        min_price = params.pop('min_price', 0)
        max_price = params.pop('max_price', 1500)
        model = PricePredTotal(data, pred_date=pred_date, min_price=min_price, max_price=max_price)
        res = model.run(to_json=True)
        self.write(res)

