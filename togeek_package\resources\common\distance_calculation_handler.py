# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-07-25 15:20:27
# Description: 通用距离计算的 handler
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.common.common_distance_calculation import DistanceCalculation


class DistanceCalculationHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data_history = params.pop('data_history')
        data_object = params.pop('data_object')

        model_distance = DistanceCalculation(data_history=data_history, data_object=data_object)
        self.write(model_distance.calc_distance())
