'''
Author: san
Date: 2022-04-13 10:42:54
LastEditTime: 2022-05-06 15:23:28
给定机组容量、煤耗参数，求解给定机组组合及总出力，最优化煤耗目标，分解各机组出力
1、输入：机组额定容量、最小出力、机组特定负荷率下的煤耗；
2、输出：机组各种组合，机组数为n，则由2的n次方-1种组合
#### 目标函数 ####
min平均煤耗 = sum(各机组出力*各机组当前负荷率下的煤耗)/总出力
'''
import numpy as np
import pandas as pd
import logging
logger = logging.getLogger()


class GeneratorCoal:
    def __init__(self, generator_data, loadrate_data, zero_list):
        # 机组容量、最小技术出力，机组容量
        self.generator_data = generator_data
        # 机组煤耗
        # 煤耗曲线，按行存储每台机组的煤耗曲线，二次曲线，p2,p1,b
        self.loadrate_data = loadrate_data
        # 补全数据
        self.zero_list = zero_list

        self.result = self.dp()

    def dp(self):
        result = {}
        try:
            # 容量、煤耗数据，表达的机组要一样多
            if len(list(self.loadrate_data.columns)[1:]) != len(list(self.generator_data['max_power'])):
                logger.info("容量表和煤耗表机组数量不一致，请检查！！！")
                return result
            self.generator_data['max_power'] = self.generator_data['max_power'].map(lambda x: round(x + 0.1))
            self.generator_data['min_power'] = self.generator_data['min_power'].map(lambda x: round(x + 0.1))
            # 检查最小出力是否小于机组容量
            power_diff = self.generator_data['max_power'] - self.generator_data['min_power']
            if power_diff.min() <= 0:
                logger.info("最小出力不能大于等于机组容量!!!")
                return result
            generator_max_power = list(self.generator_data['max_power'])  # [630, 600, 630, 630, 630, 1000, 1000]
            generator_min_power = list(self.generator_data['min_power'])  # [180, 150, 180, 180, 180, 300, 300]
            power_max = sum(generator_max_power)  # 机组总容量
            generator_num = len(generator_max_power)

            coal_lines = []
            generators_name = list(self.loadrate_data.columns)[1:]
            for generator_name in generators_name:
                rate = list(self.loadrate_data['负荷率'])
                coal = list(self.loadrate_data[generator_name])
                parameter2 = np.polyfit(rate, coal, 2)
                coal_lines.append(list(parameter2))

            enable_power = np.zeros((power_max + 1, generator_num + 1))
            for i in range(1, generator_num + 1):
                enable_power[sum(generator_min_power[:i]):min(sum(generator_max_power[:i]) + 1, power_max + 1), i] = 1
            enable_power[0, 0] = 1

            step_coal = np.zeros((power_max + 1, generator_num + 1))
            step_coal_path = np.zeros((power_max + 1, generator_num + 1))
            for s in range(1, generator_num + 1):
                fun_coal = np.poly1d(coal_lines[s - 1])
                for cur_power in np.where(enable_power[:, s] == 1)[0]:
                    ahead_section = list(range(max(0, cur_power - generator_max_power[s - 1]),
                                               max(1, cur_power - generator_min_power[s - 1] + 1)))
                    ahead_exist = np.where(enable_power[:, s - 1] == 1)[0]
                    ahead_index = list(set(ahead_section).intersection(set(ahead_exist)))
                    cur_coals = []
                    cur_ahead_index = []
                    for i in ahead_index:
                        diff_power = cur_power - i
                        if diff_power > 0 and diff_power < generator_min_power[s - 1]:
                            continue
                        diff_coal = fun_coal(diff_power / generator_max_power[s - 1]) * diff_power
                        cur_coal = step_coal[i, s - 1] + diff_coal
                        cur_coals.append(cur_coal)
                        cur_ahead_index.append(i)
                    coals = np.column_stack((cur_ahead_index, cur_coals))
                    min_index = np.argmin(cur_coals)
                    step_coal[cur_power, s] = cur_coals[min_index]
                    step_coal_path[cur_power, s] = coals[min_index][0]
            # 准备输出文件，第一列到第n列表示机组出力，第n+1列为机组总出力，第n+2列为机组总煤耗，第n+3列为机组总平均煤耗
            output = np.zeros((power_max + 1, generator_num + 3))
            for cur_power in range(sum(generator_min_power), power_max + 1):
                output[cur_power, generator_num] = cur_power
                output[cur_power, generator_num + 1] = step_coal[cur_power, generator_num]
                output[cur_power, generator_num + 2] = step_coal[cur_power, generator_num] / cur_power
                step_sum_power = cur_power
                for t in range(generator_num, 0, -1):
                    tmp_last = step_sum_power
                    step_sum_power = int(step_coal_path[step_sum_power, t])
                    output[cur_power, t - 1] = tmp_last - step_sum_power

            col_name = generators_name
            col_name.append('总出力')
            col_name.append('总煤耗')
            col_name.append('平均煤耗')
            output_dataframe = pd.DataFrame(output, index=None, columns=col_name)
            for z in self.zero_list:
                output_dataframe[z] = 0
            return output_dataframe.to_dict(orient='records')
        except Exception as err:
            logger.error("出现错误：{}!!!".format(err))
            return result


if __name__ == "__main__":
    # 机组容量、最小技术出力，机组容量
    generator_data = pd.read_excel(r'input.xlsx', sheet_name='机组容量', index_col=0)
    # 机组煤耗
    # 煤耗曲线，按行存储每台机组的煤耗曲线，二次曲线，p2,p1,b
    loadrate_data = pd.read_excel('input.xlsx', sheet_name='负荷率', index_col=None)

    generator_num = len(list(generator_data['max_power']))
    print(generator_num)
    # 循环机组组合，机组数为n，则由2的n次方-1种组合
    for c in range(1, 2 ** generator_num):
        c_str = bin(c)[2:].rjust(generator_num, '0')
        print(c_str)
        find_all = lambda data, s: [r for r in range(len(data)) if data[r] == s]
        r_list = find_all(c_str, '1')
        gen_data = generator_data.iloc[r_list]
        z_list = find_all(c_str, '0')
        print(z_list)
        z_data = generator_data.iloc[z_list]
        z_data = z_data.reset_index()
        zero_list = z_data['index'].to_list()
        print(zero_list, r_list)
        gen_data = generator_data.iloc[r_list]
        z_data = generator_data.iloc[z_list]
        loa_data = loadrate_data.iloc[:, [0] + list(map(lambda x:x+1, r_list))]
        print(gen_data)
        print(loa_data)
        op = GeneratorCoal(gen_data, loa_data, zero_list)
        print(pd.DataFrame(op.result))


