#!/usr/bin/env python
# coding: utf-8
import numpy as np
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
import datetime
from togeek_package.price.nationwide.price_sim_el_price_prediction.model_config import ModelConfig
import warnings
import logging
from togeek_package.lib.data_verification import data_verify

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class ElPriceValueDataset:
    def __init__(self, data, province, date_list_yuce, special_sign=0, special_days=[], jiedian_type='统一结算点', threshold_value=None, quantile_night=0.5,
                 quantile_day=0.75):
        logger.info("----------------------极端随机树ETR价格预测--------------------------")
        self.province = province
        self.date_list_yuce = date_list_yuce
        self.jiedian_type = jiedian_type
        self.threshold_value = threshold_value
        self.quantile_night = quantile_night
        self.quantile_day = quantile_day
        self.special_sign = special_sign
        self.special_days = special_days
        self.data, self.features = self._validate_data(self._pre_data(data))

    def _pre_data(self, data):
        # json --> pd.DataFrame，空值填充

        d, message = data_verify(data_json=data,
                                 feature_list=['日前必开机组', '日前必停机组', '检修总容量', '日前联络线计划'],
                                 threshold_null=0.35)
        logger.info(message)    # 写入日志

        # d的index为日期时间，str, 形如：'2020-08-08 00:15'
        if 'date_time' in d.columns:
            d.set_index('date_time', inplace=True)
        d.sort_index(inplace=True)  # 升序排列

        # 传入数据中若含有列"时间"和"日期"，将其删掉
        if '时间' in d.columns:
            d = d.drop('时间', axis=1)
        if '日期' in d.columns:
            d = d.drop('日期', axis=1)
        d = d.applymap(lambda x: float(x))  # 所有数据转为float类型

        return d

    def _validate_data(self, data):
        config = ModelConfig()

        if self.jiedian_type == '统一结算点':
            yuce_all_keys = config.get_tongyijiesuan_all_keys(self.province)
            beijianshu_keys = config.get_tongyijiesuan_beijianshu(self.province)
            jianshu_keys = config.get_tongyijiesuan_jianshu(self.province)
        elif self.jiedian_type == '节点':
            yuce_all_keys = config.get_jiedian_all_keys(self.province)
            beijianshu_keys = config.get_jiedian_beijianshu(self.province)
            jianshu_keys = config.get_jiedian_jianshu(self.province)
        else:
            print(self.jiedian_type, ' 该结算点类型暂不支持')
            raise Exception(self.jiedian_type + '该节点类型暂不支持')

        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        data['星期'] = pd.to_datetime(data['日期']).dt.weekday
        data['火电竞价空间'] = data[beijianshu_keys].sum(axis=1) - data[jianshu_keys].sum(axis=1)

        # 判断：当"火电竞价空间" < "日前必开机组"时, "火电竞价空间" = "日前必开机组"
        def cal(df):
            return max(df['火电竞价空间'], df['日前必开机组'])
        if '日前必开机组' in data.columns:
            data['火电竞价空间'] = data.apply(cal, axis=1)

        # 特征字段，不包含价格
        features = yuce_all_keys + ['小时', '火电竞价空间']
        return data, features

    def predict_day_price(self, days=30, n_est=130, max_depth=11, min_value=0, max_value=1500):
        all_data = self.data
        features_base = self.features
        date_list_yuce = self.date_list_yuce
        date_list_yuce.sort()  # 将预测日期升序排
        # yuce_d1 = date_list_yuce[0]  # 预测日第一天
        time_points = len(all_data['时间'].unique())  # 传入数据时点
        # 阈值参数
        none_threshold_value = True if self.threshold_value is None else False
        quantile_night = self.quantile_night
        quantile_day = self.quantile_day
        # 创建空DataFrame，用来预测结果存储
        pre_data = pd.DataFrame()
        for yuce_d1 in date_list_yuce:
            print('-------------------预测{}的数据-----------------'.format(yuce_d1))
            # 创建空DataFrame，用来预测结果存储
            pre_data1 = pd.DataFrame()
            if self.special_sign and self.special_days:
                week = datetime.datetime.strptime(yuce_d1, '%Y-%m-%d').weekday()
                if week in self.special_days:  # 非省间现货日
                    input_data = all_data[all_data['星期'].isin(self.special_days)]
                elif week not in self.special_days:
                    input_data = all_data[~all_data['星期'].isin(self.special_days)]
            else:
                input_data = all_data[all_data['日期'] < date_list_yuce[0]]
            # 先进行"日前价格"，再进行"实时价格"
            for yuce_type in ['日前价格', '实时价格']:
                # 实时价格预测时，将日前价格作为特征字段; 并且训练数据相较于日前价格，少一天
                if yuce_type == '实时价格':
                    data_input = input_data.dropna(subset=['日前价格', '实时价格'])
                    features = features_base + ['日前价格']
                    end_date_train = str(pd.to_datetime(yuce_d1) - datetime.timedelta(1)).split(' ')[0]
                else:
                    data_input = input_data.dropna(subset=['日前价格'])
                    features = features_base
                    end_date_train = yuce_d1

                train_all = data_input[data_input['日期'] < end_date_train]  # 所有日前价格/实时价格非空的训练数据
                # 如下判断目的：减少日前价格训练数据的损失
                if yuce_type == '日前价格':
                    del train_all['实时价格']
                    train_all = train_all.dropna()  # 筛选非null样本作为训练数据
                else:
                    train_all = train_all.dropna()  # 筛选非null样本作为训练数据
                train = train_all[-days * time_points:]  # 取非空传入数据的最后30天数据作为训练数据

                pred = all_data[all_data['日期'] == yuce_d1]  # 预测数据

                # 划分数据集
                X_train = train[features]
                if X_train.shape[0] == 0:
                    raise ValueError('删除null值后，训练数据为空，请检查传入数据')

                X_pred = pred[features]
                if yuce_type == '实时价格':
                    X_pred['日前价格'] = pre_data1['日前价格预测值']
                # 判断预测日训练数据是否含NaN值
                if not X_pred.isnull().sum().sum() == 0:
                    raise ValueError('预测日数据含NaN值，请检查!')
                if X_pred.shape[0] == 0:
                    raise ValueError('预测日无数据，请检查!')

                y_train = train[[yuce_type]]

                logger.info(f'---------------------------{yuce_type}------------------------')
                logger.info(f'X_train：\n{X_train}')
                logger.info(f'训练数据字段：{list(X_train.columns)}')
                logger.info(f'训练数据NaN值数量：{X_train.isnull().sum().sum()}')

                # 模型训练、预测
                etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=80)
                etr.fit(X=X_train, y=y_train)
                res = etr.predict(X=X_pred)  # np.array类型
                # 预测结果处理
                result = pred[[yuce_type]]
                result[yuce_type + '预测值'] = res

                # 对预测值进行最大最小值修正
                result[yuce_type + '预测值'] = result[yuce_type + '预测值'].map(
                    lambda s: min_value if s < min_value else max_value if s > max_value else s)

                # 经分析在时间段[0-18]之间，易出现价格下限值（山西0），将这个区间分为两段[0-7]和[7-18], 通过竞价空间来修正预测结果
                # (18, 24]之间，通过竞价空间，修正价格上限值（山西1500）
                if none_threshold_value:
                    train_zero = train[train[yuce_type] == min_value]
                    train_zero_01 = train_zero[(train_zero['小时'] >= 0) & (train_zero['小时'] <= 7)]
                    train_zero_02 = train_zero[(train_zero['小时'] > 7) & (train_zero['小时'] <= 18)]
                    # train_1500 = train[train[yuce_type] == max_value]
                    # train_1500_03 = train_1500[train_1500['小时'] > 18]
                    threshold_value_01 = train_zero_01['火电竞价空间'].quantile([quantile_night]).values[0]
                    threshold_value_02 = train_zero_02['火电竞价空间'].quantile([quantile_day]).values[0]
                    # threshold_value_03 = train_1500_03['火电竞价空间'].quantile([0.8]).values[0]
                    result[['小时', '火电竞价空间']] = pred[['小时', '火电竞价空间']]

                    # 最大值最小值修正
                    if threshold_value_01 != np.NaN:
                        result.loc[((result['小时'] <= 7) & (result['火电竞价空间'] <= threshold_value_01)),
                                   yuce_type + '预测值'] = min_value
                    if threshold_value_02 != np.NaN:
                        result.loc[((result['小时'] > 7) & (result['小时'] <= 18) &
                                    (result['火电竞价空间'] <= threshold_value_02)), yuce_type + '预测值'] = min_value
                    # result[yuce_type + '预测值'] = result[yuce_type + '预测值'] * result['weight']  # 最小值修正
                    # result.loc[(result['小时'] > 18) & (result['火电竞价空间'] > threshold_value_03), yuce_type + '预测值'] = max_value  # 最高价格修正
                result = result[[yuce_type, yuce_type + '预测值']]
                pre_data1 = pd.concat([pre_data1, result], axis=1)
            pre_data = pd.concat([pre_data, pre_data1])
        logger.info("-------------------------End of ETR ---------------------------------")
        logger.info(f"result: \n {pre_data}")
        return pre_data


if __name__ == '__main__':
    all_data_test = pd.read_pickle(r"D:\02file\0000文档\03模型类资料\模型API测试\data\price\sim_price\data_sim_day.pkl")
    date_list_yuce_test = ['2020-08-26', '2020-08-27']
    province_test = '山西'
    jiedian_type_test = '统一结算点'  # default = '统一结算点'
    # days1 = 30                  # default = 30, 表示用作训练的历史天数
    # n_est1 = 80                 # default = 80, 子树数量
    # min_value = 0               # default = 0, 价格下限
    # max_value = 1500            # default = 1500, 价格上限
    # threshold_value = None      # default = none, 竞价空间的阈值，低于该值，将价格设为0
    # quantile_night = 0          # default = 0.0, 当threshold_value为None时， 才根据此值来确定夜间(0, 7]竞价空间的阈值
    # quantile_day = 0            # default = 0.0, 当threshold_value为None时， 才根据此值来确定白天(7, 18]竞价空间的阈值

    etr_test = ElPriceValueDataset(data=all_data_test,
                                   date_list_yuce=date_list_yuce_test,
                                   province=province_test,
                                   jiedian_type=jiedian_type_test, special_sign=1, special_days=[1, 2, 3])
    pre_data_test = etr_test.predict_day_price()

    print(f'result: \n {pre_data_test}')
