# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : lmm
# Date       : 2024-02-22 14:48:36
# Description: 山东青岛中石油光储充一体化
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.shandong.optimize_sd_estorage_pv_income import OptimizeEvStoragePv

class OptimizeEvStoragePvHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data_load = params.pop("data_load")
        data_price = params.pop("data_price")
        data_storage = params.pop("data_storage")
        pv_price = params.pop("pv_price")
        start_year = params.pop("start_year")
        end_year = params.pop("end_year")
        holidays_day = params.pop("holidays_day")
        start_time = params.pop("start_time")

        model = OptimizeEvStoragePv(data_load=data_load,
                                                data_price=data_price,
                                                data_storage=data_storage,
                                                pv_price=pv_price,
                                                start_year=start_year,
                                                end_year=end_year,
                                                holidays_day=holidays_day,
                                                start_time=start_time)
        result = model.optimize()

        self.write(dict(result))
