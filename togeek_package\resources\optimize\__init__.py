# -*- coding:utf-8 -*-
from .freq_svr_intraday_handler import FreqSvrIntradayHandler
from .biding_space_handler import Bidding<PERSON>paceHandler
from .optimal_bid_handler import OptimalBidingHandler
from .operation_capacity_handler import OperationCapacityHandler
from .subsection_declaration_handler import SubsectionDeclarationHandler
from .bided_power_handler import BidedPowerHandler, BiddedPowerHandlerSD
from .load_declaration_handler import LoadD<PERSON>larationHandler
from .xny_lambda_avg_handler import XnyLambdaAvgHandler
from .optimize_deep_adjust_handler import OptimizeDeepAdjustHandler, OptimizeDeepAdjustCommonHandler
from .optimize_deep_adjust_eboiler_handler import OptimizeDeepAdjustEboilerHandler
# from .genetic_algorithm_elec.daily_total_elec import DailyTotalElecHandler
# from .genetic_algorithm_elec.days_total_elec import DaysTotalElecHandler
# from .genetic_algorithm_elec.days_daily_elec import DaysDailyElecHandler
# from .subsection_declaration.genetic_algorithm_handler import <PERSON><PERSON><PERSON>fit<PERSON>and<PERSON>, GAIncomeHandler
# from .subsection_declaration.marginal_cost_handler import AdaptMCSCommonHandler, AssignMCSCommonHandler, \
#     DistAdaptMCSCommonHandler
from .shandong_subsection_declaration import GAIncomeHandlerSD, GAProfitHandlerSD, MatrixGAProfitHandlerSD, \
    AssignMCSHandlerSD, AdaptMCSHandlerSD, DistAdaptMCSHandlerSD
from .shanxi_subsection_declaration import GAProfitHandlerSXGN, GAProfitHandlerSXJN, GAIdealProfitHandler
# from .optimize_energy_storage_handler import OptimizeEnergyStorageHandler, OptimizeEnergyStorageHandlerV2
# from .optimize_energy_storage_handler import OptimizeEnergyStorageHandlerV3
from .sd_spot_declaration_lambda_handler import Spot_Declaration_lambda_SD_Handler
from .liaoning.ln_genetic_algorithm_handler import LNGAProfitHandler
from .guangdong import GDGAProfitHandler, GDGAProfitHandlerTS, GDGACustomProfitHandlerTS
from .guangdong.gd_dlcn.gd_dlcn_opt_handler import DlcnOptimizationHandlerTS
from .nanwang import GAProfitHandlerNFDW
from .mengxi.mxxny_replacement_handler import MxReplacementLyHandler
from .hebei.xiongantower_pv_storage_optimization.xiongan_tower_pv_storage_optimization_main_handler import XionganTowerPVStorageOptimizationHandlerTS
from .mengxi.optimize_mx_monthfile_handler import MXMonthlyFileHandler
from .guangdong.gd_marginal_cost_handler import GAProfitHandlerGD