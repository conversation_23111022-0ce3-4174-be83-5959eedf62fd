import os
import base64
from Crypto.Cipher import AES
from datetime import date
from tglibs.date_time_parser import DateTimeParser


class VL:
    _code = 'default'
    _x = 7 ** 2 + 2 ** 4

    def __init__(self, license_file, code=None):
        from logging import Logger
        self.log = Logger('root')
        if code:
            self._code = code
        self._s = self.get_serial()
        try:
            with open(license_file) as f:
                license_str = f.read()
        except:
            self.show_serial('license文件不存在或无法读取license文件')
        succ, msg = self.valid_license(license_str)
        if succ:
            self.log.info(msg)
        else:
            self.show_serial(msg)

    def get_serial(self):
        raise NotImplementedError()

    def _cmd(self, l):
        return ''.join(chr(i + self._x) for i in l)

    def _uncmd(self, s):
        return [ord(c) - self._x for c in s]

    def _code_encode(self, s):
        t = '~6!z@3#c$0%7^2&f*g(x)0-k_o=n+z[b]5{6}8\\x|w;3:3\'k"e,5<m.3>b/o?t`e0e122t3p4w5l6u7f8v9ta4b8cgdme' \
            'rfegahainjkkwl1mhndoxpxqhrvsatvuvvewdxwytzhAiBqC2D0EyF0G8H5InJyKfL6MrN2OjPmQvRoStTwUtVwWlXbYfZs'
        d = {k: v for k, v in zip(t[::2], t[1::2])}
        return ''.join(d[c] for c in s)

    def valid_license(self, s):
        # return True, '授权码验证通过'
        try:
            d = dict([s.split(':') for s in self._parse_license(s).split(';')])
            if d['serial'] != self._s:
                return False, '无效授权码'
            if DateTimeParser(d['validity_period']).datetime.date() < date.today():
                return False, '授权过期'
            return True, '授权码验证通过'
        except:
            return False, '无法解析授权码'

    def _key(self):
        v = b'\xe9\xa1\x16\xdcq\x9a\xe7\xe2\x8b\xc2\xe4^\xef\xc0.\xfb\xf5\x1d+\x96\xb6\x1f0VW\xc8d\x1f\xaa]\x9d\xd6'
        l = len(v)
        v1 = v[:l // 2]
        v2 = v[l // 2:]
        return v2[::-1] + v1

    def _parse_license(self, s):
        s = base64.b64decode(s.encode('ascii'))
        ciphertext, tag, nonce = s[:-32], s[-32:-16], s[-16:]
        cipher = AES.new(self._key(), AES.MODE_EAX, nonce)
        return cipher.decrypt_and_verify(ciphertext, tag).decode('ascii')

    def show_serial(self, msg=None):
        if msg:
            self.log.error(msg)
        self.log.error(f'请利用以下序列码从合法渠道获取授权文件\n{self._s}')
        exit(1)


class VLA(VL):
    def get_serial(self):
        c = self._cmd([43, 50, 39, 54, -33, -20, 34, -33, 35, 40, 50, 42])
        try:
            with os.popen(c) as output:
                lines = output.readlines()
            c = self._cmd([50, 40, 38, 45, 32, 51, 52, 49, 36, -4])
            lines = [l.strip() for l in lines if c in l]
            lines = ' '.join(lines)
            value = lambda x: x.split(c[-1])[-1] if c in x else ''
            lines = [value(l) for l in lines.split()]
            if not lines:
                raise RuntimeError()
            return self._code_encode(f'{"".join(lines)}{self._code}')
        except RuntimeError:
            self.log.error('请在管理员用户下执行')
            exit(1)


class VLB(VL):
    def get_serial(self):
        c = self._cmd([18, 4, 17, 8, 0, 11])
        over_value = os.environ.get(c, '')
        return self._code_encode(f'{over_value}{self._code}')
