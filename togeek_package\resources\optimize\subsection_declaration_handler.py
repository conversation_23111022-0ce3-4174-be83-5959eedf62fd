# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_subsection_declaration import SubsectionDeclaration


class SubsectionDeclarationHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        default_subsection = params.pop('default_subsection', 6)
        POP_SIZE = params.pop('POP_SIZE', 2500)
        N_GENERATIONS = params.pop('N_GENERATIONS', 4)
        model = SubsectionDeclaration(generators=generators, prices=prices,
                                      default_subsection=default_subsection, POP_SIZE=POP_SIZE,
                                      N_GENERATIONS=N_GENERATIONS)
        result = model.predict()
        self.write(result)
