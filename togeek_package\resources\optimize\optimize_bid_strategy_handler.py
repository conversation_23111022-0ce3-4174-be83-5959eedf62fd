# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : lmm
# Date       : 2024-02-22 14:48:36
# Description: 山东青岛中石油光储充一体化
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.hebei.optimize_bid_strategy import OptimizeCost

class OptimizeCostHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        trading_price = params.pop("trading_price")
        data = params.pop("data")
        limit_ratio = params.pop("limit_ratio")
        upper_ratio = params.pop("upper_ratio")
        month = params.pop("month")
        basic_cost = params.pop("basic_cost")

        model = OptimizeCost(data=data,
                             trading_price=trading_price,
                             limit_ratio=limit_ratio,
                             upper_ratio=upper_ratio,
                             month=month,
                             basic_cost=basic_cost)
        result = model.optimize()

        self.write(dict(result))
