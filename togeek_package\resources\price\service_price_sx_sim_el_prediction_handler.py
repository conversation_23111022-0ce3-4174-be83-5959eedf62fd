"""
Author: <PERSON><PERSON>
Datetime: 2022/12/29/029 11:21
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shanxi.price_sim_el_price_prediction import EtrPredictPrice


class PricePredictionValueEvalHandlerServiceSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        run_date = params.pop('run_date', None)
        bsf = EtrPredictPrice(run_date=run_date)
        data = bsf.pred_price(to_json=True)
        self.write(data)

