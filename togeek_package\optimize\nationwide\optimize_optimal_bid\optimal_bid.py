#!/usr/bin/env python
# coding: utf-8

import numpy as np
import pandas as pd





class Biding:
    def __init__(self,source_data):
        self.source_data = self.pre_source(pd.DataFrame(source_data))
        self.data_x, self.data_p = self.get_points()
        # self.result = self.optimal_price()

    def pre_source(self, data):

        data['日期'] = data['date_time'].map(lambda s: str(s).split(' ')[0])
        data['时间'] = data['date_time'].map(lambda s: str(s).split(' ')[1])
        return data


    def get_points(self):
        x1 = np.arange(0, 1, 0.1)
        x2 = np.arange(0, 1, 0.1)
        x3 = np.arange(0, 1, 0.1)
        p1 = np.arange(0, 1, 0.1)
        p2 = np.arange(0, 1, 0.1)
        p3 = np.arange(0, 1, 0.1)
        p4 = np.arange(0, 1, 0.1)
        X1, X2, X3 = np.meshgrid(x1, x2, x3)
        frame_X1 = pd.DataFrame(X1.reshape(1000), columns=['x1'])
        frame_X2 = pd.DataFrame(X2.reshape(1000), columns=['x2'])
        frame_X3 = pd.DataFrame(X3.reshape(1000), columns=['x3'])
        data_x = pd.concat([frame_X1, frame_X2, frame_X3], axis=1)
        P1, P2, P3, P4 = np.meshgrid(p1, p2, p3, p4)
        frame_P1 = pd.DataFrame(P1.reshape(10000), columns=['p1'])
        frame_P2 = pd.DataFrame(P2.reshape(10000), columns=['p2'])
        frame_P3 = pd.DataFrame(P3.reshape(10000), columns=['p3'])
        frame_P4 = pd.DataFrame(P4.reshape(10000), columns=['p4'])
        data_p = pd.concat([frame_P1, frame_P2, frame_P3, frame_P4], axis=1)
        return data_x, data_p

    def income(self, xx, pp, price):
        income = 0
        point_income = 0
        for arp in price.itertuples():
            xa = 0
            xr = 0
            if getattr(arp, '日前价格') > getattr(pp, 'p4'):
                xa = 1
            elif getattr(arp, '日前价格') > getattr(pp, 'p3'):
                xa = getattr(xx, 'x3')
            elif getattr(arp, '日前价格') > getattr(pp, 'p2'):
                xa = getattr(xx, 'x2')
            elif getattr(arp, '日前价格') > getattr(pp, 'p1'):
                xa = getattr(xx, 'x1')
            else:
                xa = 0
            if getattr(arp, '实时价格') > getattr(pp, 'p4'):
                xr = 1
            elif getattr(arp, '实时价格') > getattr(pp, 'p3'):
                xa = getattr(xx, 'x3')
            elif getattr(arp, '实时价格') > getattr(pp, 'p2'):
                xr = getattr(xx, 'x2')
            elif getattr(arp, '实时价格') > getattr(pp, 'p1'):
                xr = getattr(xx, 'x1')
            else:
                xr = 0
            point_income = getattr(arp, '日前价格') * xa + getattr(arp, '实时价格') * (xr - xa)
            income = income + point_income
        # print(income)
        return income

    def get_incomes(self, price):
        incomes = pd.DataFrame([[0, 0, 0, 0, 0, 0, 0, 0]], columns=('income', 'x1', 'x2', 'x3', 'p1', 'p2', 'p3', 'p4'))
        i = 0
        for xx in self.data_x.itertuples():
            # print(xx)
            if getattr(xx, 'x1') < getattr(xx, 'x2') and getattr(xx, 'x2') < getattr(xx, 'x3'):
                for pp in self.data_p.itertuples():
                    if getattr(pp, 'p4') > getattr(pp, 'p3') and getattr(pp, 'p3') > getattr(pp, 'p2') and getattr(pp, 'p2') > getattr(pp, 'p1'):
                        i = i + 1
                        # print(i,end='')
                        cur_income = self.income(xx, pp, price)
                        append_income = pd.DataFrame(
                            {'income': cur_income, 'x1': getattr(xx, 'x1'), 'x2': getattr(xx, 'x2'), 'x3': getattr(xx, 'x3'), 'p1': getattr(pp, 'p1'), 'p2': getattr(pp, 'p2'),
                             'p3': getattr(pp, 'p3'), 'p4': getattr(pp, 'p4')}, index=[0])
                        # print(append_income)
                        incomes = incomes.append(append_income, ignore_index=True)
            # print(incomes)
        sorted_incomes = incomes.sort_values(by='income', ascending=False)
        sorted_incomes = list(sorted_incomes.reset_index(drop=True).loc[0].T)
        return sorted_incomes

    def optimal_price(self, to_json=False):
        date_list = list(self.source_data['日期'].drop_duplicates())
        result_data = pd.DataFrame(columns=['income', 'x1', 'x2', 'x3', 'p1', 'p2', 'p3', 'p4'], index=date_list)
        for _date in date_list:
            price_data = self.source_data[self.source_data['日期'] == _date]

            price_data['实时价格'] = price_data['实时价格'].map(lambda s: float(s) / 1500)
            price_data['日前价格'] = price_data['日前价格'].map(lambda s: float(s) / 1500)
            result_data.loc[_date] = self.get_incomes(price_data)
        result_data['income'] = result_data['income'] * 1500
        result_data['p1'] = result_data['p1'] * 1500
        result_data['p2'] = result_data['p2'] * 1500
        result_data['p3'] = result_data['p3'] * 1500
        result_data['p4'] = result_data['p4'] * 1500
        if to_json:
            result_data = result_data.to_dict()
        return result_data

if __name__ == '__main__':
    import time
    print(time.ctime())

    source_data = pd.read_excel('train_data/ar_price.xlsx', index_col=0)
    b = Biding(source_data)
    pre = b.optimal_price(to_json=False)
    print(pd.DataFrame(pre))
    print(time.ctime())



