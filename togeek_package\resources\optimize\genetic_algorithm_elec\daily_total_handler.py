# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_subsection_declaration_elec.SubsectionGA import GeneratorInfoDaily, GADaily


class DailyTotalElecHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        costs = params.pop('costs')
        prices = params.pop('prices')
        elecs = params.pop('elecs')
        constraints = params.pop('constraints')
        func_mode = params.pop('func_mode', 'profit')
        min_pirce = params.pop('min_price', 0)
        max_pirce = params.pop('max_price', 1500)
        subsection = params.pop('subsection', 10)
        size_pop = params.pop('size_pop', 2000)
        max_iter = params.pop('max_iter', 30)
        prob_mut = params.pop('prob_mut', 0.005)
        fit_mode = params.pop('fit_mode', 2)
        precision = params.pop('precision', 0.1)
        r = params.pop('r', 0.1)
        generator = GeneratorInfoDaily(generators=generators, costs=costs, prices=prices, elecs=elecs,
                                       constraints=constraints, min_price=min_pirce, max_price=max_pirce,
                                       func_mode=func_mode, subsection=subsection, max_iter=max_iter,
                                       precision=precision)

        lb = generator.lb
        ub = generator.ub
        n_dim = generator.n_dim

        # 约束条件：最小发电量约束
        # def constraint_min_power(elec):
        #     return generator.min_elec - sum(elec)

        # 约束条件：最大发电量约束
        # def constraint_max_power(elec):
        #     return sum(elec) - generator.max_elec

        model = GADaily(func=generator.func, n_dim=n_dim, generator=generator, min_price=min_pirce, max_price=max_pirce,
                        size_pop=size_pop, max_iter=max_iter, prob_mut=prob_mut, fit_mode=fit_mode, lb=lb, ub=ub,
                        constraint_ueq=(generator.constraint_max_power, generator.constraint_min_power),
                        precision=precision)

        result = model.run(r)
        self.write(result)
