# -*- coding: utf-8 -*-
# valid license

import pandas as pd
from togeek_package.price.nationwide.price_sim_el_price_prediction.model_config import ModelConfig
import logging
logger = logging.getLogger()


class ElPriceDataset:
    def __init__(self, data, province, date_list_yuce, jiedian_type='统一结算点', jiage_name = '日前价格'):
        logger.info("--------------------相似日算法预测电价------------------------------")
        self.jiedian_type = jiedian_type
        self.date_list_yuce = date_list_yuce
        self.province = province
        self.jiage = jiage_name
        self.data, self.yuce_keys = self._validate_data(self._pre_data(data))
        self.hm_list = self.data.groupby(['时间']).mean().index.values

    def _pre_data(self, data):
        d = pd.DataFrame(data)
        d.sort_index(inplace=True)
        d = d.applymap(lambda x: float(x))

        return d

    def _validate_data(self, data):
        config = ModelConfig()
        province = self.province
        jiedian_type = self.jiedian_type

        if jiedian_type == '统一结算点':
            yuce_keys = config.get_tongyijiesuan_keys(province)
            beijianshu_keys = config.get_tongyijiesuan_beijianshu(province)
            jianshu_keys = config.get_tongyijiesuan_jianshu(province)
        elif jiedian_type == '节点':
            yuce_keys = config.get_jiedian_keys(province)
            beijianshu_keys = config.get_jiedian_beijianshu(province)
            jianshu_keys = config.get_jiedian_jianshu(province)
        else:
            print(jiedian_type, ' 该结算点类型暂不支持')
            raise Exception(jiedian_type + '该节点类型暂不支持')

        all_columns = data.columns.values
        count_cols = ['现货竞价空间']
        all_need_cols = list(set(yuce_keys).union(set(beijianshu_keys)).union(set(jianshu_keys)))
        all_need_cols = list(set(all_need_cols).difference(set(count_cols)))

        lost_cols = list(set(all_need_cols).difference(set(all_columns)))  # all_need_cols中有而all_columns中没有的

        if len(lost_cols) > 0:
            print('缺失影响因素', lost_cols)
            raise Exception('缺失影响因素:' + str(lost_cols))



        if self.jiage not in all_columns:
            print('缺失历史价格数据', self.jiage)
            raise Exception('缺失历史价格数据:' + self.jiage)

        data['日期'] = data.index
        data['日期'] = data['日期'].apply(lambda x: x.split(' ')[0])

        data['时间'] = data.index
        data['时间'] = data['时间'].apply(lambda x: x.split(' ')[1])

        for count_col in count_cols:
            data[count_col] = data[beijianshu_keys].sum(axis=1) - data[jianshu_keys].sum(axis=1)

        data.dropna()

        return data, yuce_keys

    # 将数据处理为分时数据
    def hour_stack(self, hour_data01, key_name):
        hm_list = self.hm_list
        hour_data01.index = hour_data01['日期']
        hour_data02 = pd.DataFrame()

        for i in hm_list:
            data_temp = hour_data01[hour_data01['时间'] == i].stack().unstack(0)
            hour_data02 = pd.concat([hour_data02, data_temp.loc[key_name]], join='outer', axis=1)

        hour_data02.columns = hm_list

        return hour_data02

    def sim_day_prediction(self):
        data = self.data
        yuce_keys = self.yuce_keys
        hm_list = self.hm_list
        date_list_yuce = self.date_list_yuce

        result = {}
        compare_list = yuce_keys
        key_list = ['日期', '时间']

        for _date in date_list_yuce:
            history_data = data[data['日期'] < _date]
            the_date_data = data[data['日期'] == _date]
            mul_yinsu_juzhen = pd.DataFrame()

            for key in compare_list:
                this_key_list = key_list.copy()
                this_key_list.append(key)
                sigle_yinsu = history_data[this_key_list]
                sigle_yinsu_juzhen = pd.DataFrame()

                yuce_key = key
                yuce_key_data = the_date_data[[yuce_key, '日期', '时间']]

                for hm_time in hm_list:
                    data_temp = sigle_yinsu[[key, '日期', '时间']]
                    data_temp = data_temp[data_temp['时间'] == hm_time]
                    data_temp.index = data_temp['日期']

                    this_day_vallue = yuce_key_data[yuce_key_data['时间'] == hm_time]
                    this_day_vallue.columns = data_temp.columns
                    this_day_vallue.index = [_date]
                    data_temp = pd.concat([data_temp, this_day_vallue], join='outer', axis=0)

                    sigle_yinsu_juzhen = pd.concat([sigle_yinsu_juzhen, data_temp[key]], join='outer', axis=1)

                sigle_yinsu_juzhen.columns = hm_list
                sigle_yinsu_juzhen = sigle_yinsu_juzhen.rename(columns=lambda x: key + '_' + str(x))

                mul_yinsu_juzhen = pd.concat([mul_yinsu_juzhen, sigle_yinsu_juzhen], join='outer', axis=1)

            print(f'mul_yinsu_juzhen:\n {mul_yinsu_juzhen}')
            mul_yinsu_juzhen_bzh = (mul_yinsu_juzhen - mul_yinsu_juzhen.mean()) / mul_yinsu_juzhen.std()

            mul_yinsu_juzhen_corr = mul_yinsu_juzhen_bzh.T.corr()
            _date_corr = mul_yinsu_juzhen_corr[_date]
            _date_corr = _date_corr.sort_values(ascending=False)
            result[_date] = _date_corr[1: ].to_dict()

        return result

    def sim_minute_prediction(self):
        data = self.data
        yuce_keys = self.yuce_keys
        hm_list = self.hm_list
        date_list_yuce = self.date_list_yuce

        result = {}

        for _date in date_list_yuce:
            history_data = data[data['日期'] <= _date]
            yinsu_all_data = pd.DataFrame()
            res_temp = {}

            for key_name in yuce_keys:
                hour_xhjjkj = history_data[[key_name, '日期', '时间']]
                hour_xhjjkj01 = self.hour_stack(hour_xhjjkj, key_name).T
                hour_xhjjkj01['时间'] = hour_xhjjkj01.index
                hour_xhjjkj01 = hour_xhjjkj01.rename(index=lambda x: str(x) + '_' + key_name)
                yinsu_all_data = pd.concat([yinsu_all_data, hour_xhjjkj01], join='outer', axis=0)

            for hour in hm_list:
                hour_data = yinsu_all_data[yinsu_all_data['时间'] == hour]
                hour_data = hour_data.drop(['时间'], axis=1)
                hour_data = hour_data.applymap(lambda x: float(x))

                # hour_data_bzh = ((hour_data.T - hour_data.T.mean()) / hour_data.T.std()).T
                hour_data_bzh = ((hour_data.T - hour_data.T.min()) / hour_data.T.max()).T
                hour_data_bzh_corr = hour_data_bzh.corr()
                hour_temp_single = hour_data_bzh_corr[_date]
                hour_temp_single = hour_temp_single.sort_index()[:-1]
                hour_temp_single = hour_temp_single.sort_values(ascending=False)
                hour_temp_single.index = hour_temp_single.index + ' ' + hour

                res_temp[_date + ' ' + hour] = hour_temp_single.to_dict()

            result[_date] = res_temp
        logger.info("------end of sim-------")
        logger.info("result:{}".format(result))
        return result


if __name__ == '__main__':
    import time
    t = time.time()

    pre_fun_type = '相似日'
    province = '山西'
    jiedian_type = '统一结算点'
    date_list_yuce = ['2020-08-03', '2020-08-27']
    # all_data = pd.read_excel('山西数据.xlsx', index_col=0)
    all_data = pd.read_pickle('data.pkl')

    elpd = ElPriceDataset(all_data, province, date_list_yuce, jiedian_type)

    if pre_fun_type == '相似日':
        res = elpd.sim_day_prediction()
    elif pre_fun_type == '相似时刻':
        res = elpd.sim_minute_prediction()
    else:
        print('pre_fun_type 暂不支持该计算类型：', pre_fun_type)

    print(res)
    print('time----------------->', time.time() - t)
