#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/min_ops.h>

namespace at {


// aten::min.dim(Tensor self, int dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> min(const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::min_dim::call(self, dim, keepdim);
}

// aten::min.dim_min(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) min, Tensor(b!) min_indices) -> (Tensor(a!) values, Tensor(b!) indices)
inline ::std::tuple<at::Tensor &,at::Tensor &> min_out(at::Tensor & min, at::Tensor & min_indices, const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::min_dim_min::call(self, dim, keepdim, min, min_indices);
}
// aten::min.dim_min(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) min, Tensor(b!) min_indices) -> (Tensor(a!) values, Tensor(b!) indices)
inline ::std::tuple<at::Tensor &,at::Tensor &> min_outf(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & min, at::Tensor & min_indices) {
    return at::_ops::min_dim_min::call(self, dim, keepdim, min, min_indices);
}

// aten::min.names_dim(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> min(const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::min_names_dim::call(self, dim, keepdim);
}

// aten::min.names_dim_min(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) min, Tensor(b!) min_indices) -> (Tensor(a!) values, Tensor(b!) indices)
inline ::std::tuple<at::Tensor &,at::Tensor &> min_out(at::Tensor & min, at::Tensor & min_indices, const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::min_names_dim_min::call(self, dim, keepdim, min, min_indices);
}
// aten::min.names_dim_min(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) min, Tensor(b!) min_indices) -> (Tensor(a!) values, Tensor(b!) indices)
inline ::std::tuple<at::Tensor &,at::Tensor &> min_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & min, at::Tensor & min_indices) {
    return at::_ops::min_names_dim_min::call(self, dim, keepdim, min, min_indices);
}

// aten::min(Tensor self) -> Tensor
inline at::Tensor min(const at::Tensor & self) {
    return at::_ops::min::call(self);
}

// aten::min.unary_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & min_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::min_unary_out::call(self, out);
}
// aten::min.unary_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & min_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::min_unary_out::call(self, out);
}

// aten::min.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & min_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::min_out::call(self, other, out);
}
// aten::min.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & min_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::min_out::call(self, other, out);
}

// aten::min.other(Tensor self, Tensor other) -> Tensor
inline at::Tensor min(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::min_other::call(self, other);
}

}
