# -*- coding: utf-8 -*-
# @Time    : 2022/12/15 17:57
# <AUTHOR> darlene
# @FileName: loadrate_fitting.py
# @Software: PyCharm
import numpy as np
import pandas as pd


class LoadRate:
    def __init__(self, fit_x, fit_y, n):
        '''
        fit_x   拟合x
        fit_y   拟合y
        n  几次拟合
        '''
        self.fit_x = np.array(fit_x)
        self.fit_y = np.array(fit_y)
        self.n = int(n)

    def fitting(self):
        result = {}
        parametern = np.polyfit(self.fit_x, self.fit_y, self.n)
        result['多项式系数'] = list(parametern)
        return result

if __name__ == '__main__':
    data = pd.read_excel(r'D:\02file\2022\18little\05半月报\负荷率拟合.xlsx')
    x = data[data['date'] == '2022-10-17']['负荷率']
    y = data[data['date'] == '2022-10-17']['日前价格实际值']
    n = 3
    print(list(x))
    print(list(y))
    # load = LoadRate(x, y, n)
    # pn = load.fitting()
    # print(pn)
