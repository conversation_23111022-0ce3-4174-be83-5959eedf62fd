#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2025/3/24 14:12 
@Info    ：

'''
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.guangdong import GDCustomSubsectionDeclarProfitTS


class GDGACustomProfitHandlerTS(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        contracts = params.pop('contracts')
        costs = params.pop('costs')
        constraints = params.pop('constraints')
        powers = params.pop('powers')
        lower_price = params.pop('lower_price', 0)
        upper_price = params.pop('upper_price', 1500)
        min_profit = params.pop('min_profit', 0)
        POP_SIZE = params.pop('POP_SIZE', 3000)
        N_GENERATIONS = params.pop('N_GENERATIONS', 20)
        price_decimal = params.pop('price_decimal', 3)
        price_gap = params.pop('price_gap', 1)
        func_mode = params.pop('func_mode', 'eprofit')
        model = GDCustomSubsectionDeclarProfitTS(generators=generators, prices=prices, contracts=contracts, costs=costs,
                                                 constraints=constraints, powers=powers, lower_price=lower_price,
                                                 upper_price=upper_price, min_profit=min_profit, POP_SIZE=POP_SIZE,
                                                 N_GENERATIONS=N_GENERATIONS, price_decimal=price_decimal,
                                                 func_mode=func_mode, price_gap=price_gap)
        result = model.predict()
        self.write(result)
