"""
Author: <PERSON><PERSON>
Datetime: 2022/12/29/029 11:20
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shanxi.price_similar_date_pre import SimPredPrice


class SimPredPriceHandlerServiceCommonSX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        price = params.pop('price')
        run_date = params.pop('run_date', None)
        m = SimPredPrice(price, run_date=run_date)
        data = m.pred_price(to_json=True)
        self.write(data)
