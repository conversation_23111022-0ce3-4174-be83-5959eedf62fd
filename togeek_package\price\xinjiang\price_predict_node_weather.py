#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/23 13:51
# <AUTHOR> Darlene
'''
    根据气象数据预测未来价格
'''

import pandas as pd
import datetime
import os
import xgboost as xgb
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
warnings.filterwarnings('ignore')


class PredPriceWeatherXj:
    def __init__(self, weather_data, price_data, holidays, pre_date, pre_days, target_cols, pre_type):
        print(f"========================开始预测新疆节点电价========================")
        self.pre_type = pre_type  # 节点/统一结算点
        self.weather_data = self.pre_weather(weather_data)
        self.price_data = self.create_lag_features(price_data, target_cols)
        self.holidays = pd.DataFrame(holidays)
        self.pre_date = pre_date   # 待预测的日期
        self.pre_days = pre_days    # 预测未来的t列表
        self.target_cols = target_cols   # 待预测字段列表



    def pre_weather(self, df):
        '''
        处理气象数据：1、增加特征；2、滑动窗口
        '''
        df = pd.DataFrame(df)
        # 取date_time
        df = df.rename_axis('date_time').reset_index() if 'date_time' not in df.columns else df
        # 如果是统一结算点，按城市求均值
        if self.pre_type == '统一结算点':
            df = df.groupby('date_time').mean().reset_index()
        df['date'] = df['date_time'].map(lambda x: str(x).split(' ')[0])
        df['date_time'] = pd.to_datetime(df['date_time'])
        df['hour'] = df['date_time'].map(lambda x: x.hour)
        df['day_of_week'] = df['date_time'].map(lambda x: x.dayofweek)
        df['day_of_month'] = df['date_time'].map(lambda x: x.day)
        df['month'] = df['date_time'].map(lambda x: x.month)
        df['is_weekend'] = df['date_time'].isin([5, 6]).astype(int)
        df['date_time'] = df['date_time'].astype(str)
        df = self.create_weather_features(df)
        return df

    def create_weather_features(seld, df):
        # 气象组合特征
        if all(col in df.columns for col in ['wspd_10m', 'dswrf_surface']):
            df['wind_solar_interaction'] = df['wspd_10m'] * df['dswrf_surface']

        # 气象滚动特征
        weather_cols = ['wspd_10m', 'dswrf_surface', 't_surface', 'tp_surface']
        for col in weather_cols:
            if col in df.columns:
                for window in [24, 24 * 3]:
                    df[f'{col}_rolling_mean_{window}'] = df[col].rolling(window=window).mean()

        return df

    def create_lag_features(self, df, col_list):
        df = pd.DataFrame(df)
        # 取date_time
        df = df.rename_axis('date_time').reset_index() if 'date_time' not in df.columns else df
        for target_col in col_list:
            # 创建滞后特征
            for lag in range(1, 7 + 1):
                df[f'{target_col}_lag_{lag}'] = df[target_col].shift(lag)

            # 创建滚动统计特征
            windows = [24, 24 * 3, 24 * 7]  # 1天、3天、1周窗口
            for window in windows:
                df[f'{target_col}_rolling_mean_{window}'] = df[target_col].rolling(window=window).mean()
                df[f'{target_col}_rolling_std_{window}'] = df[target_col].rolling(window=window).std()
        return df

    def pre_price(self, json=True):
        '''
            1、
        '''
        results = pd.DataFrame()
        a_data = self.weather_data.merge(self.price_data, how='outer', on='date_time')  # [['date_time', '日前电价']]
        a_data['date'] = a_data['date_time'].map(lambda x: x.split(' ')[0])
        a_data = a_data.merge(self.holidays, how='left', on='date')
        pre_date = datetime.datetime.strptime(self.pre_date, '%Y-%m-%d')
        pre_list = [(pre_date + datetime.timedelta(days=d)).strftime('%Y-%m-%d') for d in self.pre_days]
        print(f"----数据处理完成，开始预测价格----")
        for col in self.target_cols:
            print(f"预测{col}")
            train_data = a_data[a_data['date'] < self.pre_date]
            test_data = a_data[a_data['date'].isin(pre_list)]
            drop_cols = ['date_time', 'date'] + self.target_cols
            X_train = train_data.drop(columns=drop_cols)
            y_train = train_data[col]
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            model = xgb.XGBRegressor(
                objective='reg:squarederror',
                n_estimators=200,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42
            )
            model.fit(X_train_scaled, y_train)
            # 取目标列
            X_test_fe = test_data.drop(columns=drop_cols)
            # 标准化
            X_test_scaled = scaler.transform(X_test_fe)
            y_pred = model.predict(X_test_scaled)
            y_test = test_data[['date_time']]
            y_test[f"pre_{col}"] = y_pred
            if results.empty:
                results = y_test.copy(deep=True)
            else:
                results = results.merge(y_test, how='outer', on=['date_time'])
        print(f"预测完成，准备输出结果")
        if json == True:
            results = results.to_dict('list')
        return results

if __name__ == '__main__':
    path = r'D:\02file\2025\05新疆电价预测\output'
    weather = pd.read_excel(os.path.join(path, 'weather.xlsx'))
    # weather = weather[weather['t'].isin([1, 5])]
    price = pd.read_excel(os.path.join(path, 'price.xlsx'))
    holiday = pd.read_excel(os.path.join(path, 'holiday.xlsx')).iloc[:, 1:]
    t_list = list(range(5, 16))
    pre_cols = ['日前电价', '日内电价']
    pre = PredPriceWeatherXj(weather, price, holiday, pre_date='2025-06-20', pre_days=t_list, target_cols=pre_cols, pre_type='节点')
    result = pre.pre_price(json=False)
    print(result)
    # print(date_list)
