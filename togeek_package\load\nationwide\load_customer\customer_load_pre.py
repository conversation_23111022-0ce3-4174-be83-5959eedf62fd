# -*- coding: utf-8 -*
# valid license

import pandas as pd
from prophet import Prophet
from json import loads
import logging

logger = logging.getLogger()

class Prediction:
    def __init__(self, load, holiday, pre_days, point_day=24, cap=None, floor=None, include_history=True,
                 flexibility=5):
        assert 1 <= flexibility <=10
        logger.info("------------------------------------------")
        logger.info("----------时间序列预测用户负荷-------------------")
        logger.info("load:{}, holiday:{}, pre_days:{}, point_day={}, cap={}, floor={}, include_history={},flexibility={}".format(
            load, holiday, pre_days, point_day, cap, floor, include_history, flexibility))
        self.params = {"seasonality_mode":'multiplicative', "changepoint_prior_scale":flexibility / 100}
        self.load = self._prepare_load(load, cap, floor, self.params, point_day)
        self._prepare_holiday(holiday, self.params)
        self.pre_days = pre_days * point_day
        self.inc_his = include_history
        self.point_day = point_day
        # self.freq = freq
        self.freq = {24: 'H', 96: '15min', 48: '30min'}[point_day]

    def _prepare_load(self, load, cap, floor, params, point_day):
        if isinstance(load, str):
            load = loads(load)
        if isinstance(load, dict):
            load['ds'] = pd.to_datetime(load['ds'])
            load = pd.DataFrame(load)
        if cap is not None:
            load['cap'] = cap
            if floor is None:
                load['floor'] = floor
        if 'cap' in load:
            params['growth'] = 'logistic'

        load.set_index("ds", inplace=True)
        load.index = pd.DatetimeIndex(load.index)
        load.sort_index(inplace=True)
        min_period = {24: '60T', 96: '15T', 48: '30T'}[point_day]
        load = load.resample(min_period).first()
        load.reset_index(inplace=True)

        return load

    def _prepare_holiday(self, holiday, params):
        if holiday is None:
            return
        if isinstance(holiday, str):
            holiday = loads(holiday)
        if isinstance(holiday, dict):
            holiday["ds"] = pd.to_datetime(holiday["ds"])
            holiday = pd.DataFrame(holiday)
        params["holidays"] = holiday
    def predict(self, to_json=False):
        # model = Prophet(**self.params)
        model = Prophet(**self.params)
        model.fit(self.load)
        future = model.make_future_dataframe(periods=self.pre_days, include_history=self.inc_his, freq=self.freq)
        result = model.predict(future)[['ds', 'yhat']]
        result.loc[result[result["yhat"]<0].index,"yhat"]=0

        if to_json:
            result = {'ds': result.ds.astype(str).tolist(),
                      'yhat': result.yhat.tolist()
                      }
        logger.info("------end of customer-------")
        logger.info("result:{}".format(result))
        return result

if __name__ == "__main__":
    holiday = pd.read_pickle("train_data/holiday.pkl")
    load = pd.read_pickle("train_data/customer.pkl")
    # print(load)
    p = Prediction(load, holiday, 1, include_history=False)
    pre = p.predict(False)
    print(pre)
