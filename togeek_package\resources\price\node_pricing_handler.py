# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.nationwide.price_node_price_intraday import Prediction


class NodePricingHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        train = params['train']
        pred = params['pred']
        p = Prediction(train, pred)
        self.write({'pred': p.predict().tolist()})
