#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/8/11 11:33
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_replacement import ReplacementMxHD


class MxReplacementHDHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generals = params.pop('generals')
        num_m = params.pop('num_m', [])
        longterm_data = params.pop('longterm_data')
        node_price = params.pop('node_price')
        general_price = params.pop('general_price')
        high_line = params.pop('high_line', 1.1)
        low_line = params.pop('low_line', 0.9)
        points = params.pop('points', 96)
        op_time = params.pop('op_time', [])
        model = ReplacementMxHD(generals=generals, longterm_data=longterm_data, node_price=node_price, general_price=general_price,
                           points=points, high_line=high_line, low_line=low_line, op_time=op_time, num_m=num_m)
        result = model.get_result(json=True)
        self.write(result)