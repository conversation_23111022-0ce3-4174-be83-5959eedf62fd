#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2024/4/12 10:20
# <AUTHOR> Darlene
# -*- coding: utf-8 -*-
# valid license

import sys
from tglibs.easy_dir import join

sys.path.append(join(__file__, ''))

import numpy as np
import itertools
from sklearn.tree import DecisionTreeRegressor
from multiprocessing import Pool
from functools import partial
import pandas as pd
import operator
import logging
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger()

__all__ = ['AdaptMCS', 'DistAdaptMCS', 'CanNotFit']


class CanNotFit(Exception):
    pass


class MarginalCostSegmentationBase:
    def __init__(self, generators, prices, costs, constraints,
                 lower_price=0, upper_price=1500,
                 default_subsection=10, price_decimal=3,
                 n_sampling=1e5, min_split_scale=0.1, is_start_zero=0, min_price=1, dists=None):
        # # is_start_zero=1, 代表起始出力从0开始；is_start_zero=0，代表起始出力从最小出力开始
        # 初始化输入信息
        self.generators = pd.DataFrame(generators)       # 机组信息
        self.prices = pd.DataFrame(prices)               # 机组节点电价
        self.costs = pd.DataFrame(costs)                 # 机组变动成本
        self.constraints = pd.DataFrame(constraints)     # 机组分段约束条件-爬坡速率、出力上下限
        self.default_subsection = default_subsection     # 默认申报分段数量
        self.subsection = default_subsection             # 分段数量
        self.min_split_scale = min_split_scale
        self.n_sampling = int(n_sampling)
        self.price_decimal = int(price_decimal)          # 每个分段出力的最小间隔
        self.is_start_zero = is_start_zero               # 起始出力是否从0开始， 如果不是从0开始，则分段数量需要 + 1
        self.dists = dists                               # 节点电价分布
        self.lower_price = lower_price                   # 报价下限
        self.upper_price = upper_price                   # 报价上限
        self.dist = None                                 # 单个机组的节点电价分布

        # 默认值
        self.freqs = [24, 48, 96]    # 每日的时间点的数量只能是这些数字
        self.gap = 15                # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        self.max_split = 10          # 分段数量，这里仅做初始化

        # 数据格式化
        self.installed_capacity = 0  # 装机容量
        self.min_power = 0           # 最小技术出力
        self.beginning_power = 0     # 初始出力
        self.min_step = 0            # 最小步长
        self.subsection_gap = 1      # 每个分段的最小出力间隔
        self.allowed_stop = 0        # 机组是否允许开停机

        self.min_price = min_price

        self.lower_power = None      # 出力下限
        self.upper_power = None      # 出力上限
        self.upward_speed = None     # 爬坡速率：MW每分钟
        self.raw_x = None
        self.raw_y = None
        self.price = None

        self.px = None
        self.py = None

        msg = self._prepare_load()  # 数据预处理 + 数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 判断预测日是否要停机
        if 'to_stop' not in self.generators.columns:  # 如果没有该列，则设置为默认值0，即不停机
            self.generators['to_stop'] = 0
        self.generators['to_stop'].fillna(0, inplace=True)  # 空值填充为0
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        for col in ['installed_capacity', 'min_power', 'beginning_power', 'subsection', 'allowed_stop']:
            self.generators[col] = self.generators[col].astype('int')

        for col in ['min_step', 'subsection_gap']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 prices 数据类型并排序
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        if (self.prices['ahead_price'].isnull().all()) & (self.prices['real_price'].isnull().all()):
            logger.info("ahead_price与real_price不能同时为空，至少传入一个")
            raise Exception()
        elif self.prices['ahead_price'].isnull().all():  # ahead_price这一列全部为空
            self.prices['real_price'] = self.prices['real_price'].astype('float')
        elif self.prices['real_price'].isnull().all():  # real_price这一列全部为空
            self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
        else:  # 两列都不为空
            self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
            self.prices['real_price'] = self.prices['real_price'].astype('float')
        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        # 修正 constriants 数据类型
        self.constraints['generator'] = self.constraints['generator'].astype('str')
        self.constraints['period'] = self.constraints['period'].astype('str')
        for col in ['lower_power', 'upper_power', 'upward_speed']:
            self.constraints[col] = self.constraints[col].astype('float')

        # 修正 costs 数据类型
        self.costs['generator'] = self.costs['generator'].astype('str')
        self.costs['load'] = self.costs['load'].astype('int')
        self.costs['cost'] = self.costs['cost'].astype('float')

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + \
                   hasnull['min_step'] + hasnull['subsection_gap'] + hasnull['allowed_stop']
        if num_null > 0:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop中有" + \
                   str(num_null) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop中没有空值；"

        # 2、循环generator，检查爬坡速率及出力上下限分段数量符合要求
        hasnull2 = self.constraints.isnull().sum()
        num_null2 = hasnull2['generator'] + hasnull2['period'] + hasnull2['lower_power'] + \
                    hasnull2['upper_power'] + hasnull2['upward_speed']
        if num_null2 > 0:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中有" + \
                   str(num_null2) + "个空值，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中没有空值；\n"

        # 2、循环generator，检查连续运行时长和连续停机时长，一个必须为0值
        for g in self.generators['generator']:
            try:
                running_hour = int(self.generators[self.generators['generator'] == g]['running_hour'])
                stopping_hour = int(self.generators[self.generators['generator'] == g]['stopping_hour'])
                if running_hour + stopping_hour != 0:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值不同时为0, "
                    if running_hour * stopping_hour == 0:
                        msg += "其中一个值为0，"
                    else:
                        msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                               + str(stopping_hour) + ", 不符合规定，请检查传入数据; "
                        raise Exception()
                else:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值同时为0，不符合规定，请检查传入数据; "
                    raise Exception()
            except:
                msg += "2, 机组数据有异常，请检查传入的机组数据。"
                raise Exception()
            finally:
                msg += "机组数据验证通过；\n"

        # 起始出力如果是空值则用最小出力代替；分段数量如果是空值则用默认值代替；
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['subsection'].fillna(self.default_subsection, inplace=True)

        # 3、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freqs中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中;  "
                else:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中;  "
                    logger.info(msg)
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                # gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 96):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 48):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 24):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                else:
                    msg += "2.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    logger.info(msg)
                    raise Exception()
            except:
                msg += "2, 价格数据有异常，请检查传入的价格数据。"
                logger.info(msg)
                raise Exception()

        # 4、循环generator，检查成本
        generators = self.costs['generator'].unique()
        if len(generators) != len(self.generators['generator']):
            msg += "3, costs中应有" + str(len(self.generators['generator'])) + "台机组，costs表中有" + \
                   str(len(generators)) + "台机组，机组数量不一致，请检查传入数据。"
            logger.info(msg)
            raise Exception()
        else:
            for g in generators:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null = hasnull['generator'] + hasnull['load'] + hasnull['cost']
                    if num_null > 0:
                        msg = msg + "3, 机组" + str(g) + "的generator、load、cost中有" + \
                              str(num_null) + "个空值，请检查传入数据。"
                        logger.info(msg)
                        raise Exception()
                    else:
                        msg = msg + "3, 机组" + str(g) + "的generator、load、cost中没有空值,"
                except:
                    msg += "3, 机组" + str(g) + "的输入数据有异常。"
                    logger.info(msg)
                    raise Exception()
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。"

        # 5、修正成本
        try:
            self._correct_cost_new()
            msg += '成本修正完成。\n'
        except:
            msg += '成本修正异常。'
            logger.info(msg)
            raise Exception()
        return msg

    def _correct_cost(self):
        """
        处理传入的边际成本曲线不符合单调递增的情况，末段价格增加 10*段数 元/MWh
        先将出力按照从小到大的顺序排序，再将成本下降的曲线修正为最大成本+非常小的值
        :return: None
        """
        new_cost = pd.DataFrame(columns=['generator', 'load', 'cost'])
        self.costs = self.costs[['generator', 'load', 'cost']]
        self.costs.sort_values(['generator', 'load'], inplace=True)
        for g in self.costs['generator'].unique():
            costs = self.costs[self.costs['generator'] == g]
            costs.reset_index(drop=True, inplace=True)
            subsection = self.generators.loc[self.generators['generator'] == g, 'subsection'].values[0]
            costs['diff'] = costs['cost'].diff().fillna(0)
            end_cost = costs['cost'].max()
            idx = list(costs[costs['diff'] <= 0].index)
            if (len(idx) == 1) | (sum(costs['diff']) == 0):  # 如果价格递增或者是一条直线则不进行修正
                new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
                continue
            elif int(idx[1]) == 1:
                # 取最小出力对应的价格，拿这个价格值找另一个最相近的出力点，截取从这个点开始的成本曲线
                cost0 = self.costs[self.costs['load'] == self.min_power]
                start_cost = costs['cost'][0]
                num = len(costs)
                if start_cost == end_cost:
                    end_cost += 10 * subsection
                costs['cost'] = list(np.linspace(start_cost, end_cost, num, dtype=float))
            else:
                start_cost = costs['cost'][int(idx[1]) - 1]
                num = len(costs) - int(idx[1]) + 1
                if start_cost == end_cost:
                    end_cost += 10 * subsection
                costs.loc[int(idx[1]) - 1:, 'cost'] = list(np.linspace(start_cost, end_cost, num, dtype=float))
            new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
        self.costs = new_cost

    def _correct_cost_new(self):
        """
        处理传入的边际成本曲线不符合单调递增的情况，末段价格增加 10*段数 元/MWh
        先将出力按照从小到大的顺序排序，再将成本下降的曲线修正为最大成本+非常小的值
        :return: None
        """
        new_cost = pd.DataFrame(columns=['generator', 'load', 'cost'])
        self.costs = self.costs[['generator', 'load', 'cost']]
        self.costs.sort_values(['generator', 'load'], inplace=True)
        for g in self.costs['generator'].unique():
            min_power = self.generators[self.generators['generator'] == g]['min_power'].values[0]
            costs = self.costs[(self.costs['generator'] == g) & (self.costs['load'] >= min_power)]
            costs.reset_index(drop=True, inplace=True)
            subsection = self.generators.loc[self.generators['generator'] == g, 'subsection'].values[0]
            costs['diff'] = costs['cost'].diff().fillna(0)
            end_cost = list(costs['cost'])[-1]
            idx = list(costs[costs['diff'] <= 0].index)
            if (len(idx) == 1) | (sum(costs['diff']) == 0) | (costs['diff'] >= 0).all():  # 如果价格递增或者是一条直线则不进行修正
                new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
                continue
            elif int(idx[1]) == 1:  # 先下降再上升
                start_cost = list(costs['cost'])[0]
                # start_power = costs[costs['cost'] == start_cost]['load'].values[0]
                num = len(costs)
                if start_cost >= end_cost:
                    start_cost = costs['cost'].min()
                    # start_power = costs[costs['cost'] == start_cost]['load'].values[0]
                    # end_cost = costs['cost'].max()
                # costs = costs[costs['load'] >= start_power]
                costs['cost'] = [start_cost] * len(idx) + list(np.linspace(start_cost, end_cost, num-len(idx), dtype=float))
            else:
                start_cost = costs['cost'][int(idx[1]) - 1]
                num = len(costs) - int(idx[1]) + 1
                if start_cost == end_cost:
                    end_cost += 10 * subsection
                costs.loc[int(idx[1]) - 1:, 'cost'] = list(np.linspace(start_cost, end_cost, num, dtype=float))
            new_cost = pd.concat([new_cost, costs[['generator', 'load', 'cost']]], axis=0)
        self.costs = new_cost

    def _prepare_constraints(self, g):
        """
        将出力上下限及爬坡速率设置为长度=freq的列表
        :param g: 机组编号
        :return: 出力下限、出力上限、爬坡速率，列表
        """
        constraint = self.constraints[self.constraints['generator'] == g]
        constraint = constraint[['generator', 'period', 'lower_power', 'upper_power', 'upward_speed']]
        constraint['start'] = constraint['period'].apply(lambda x: x.split('-')[0]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
        constraint['end'] = constraint['period'].apply(lambda x: x.split('-')[1]).map(
            lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))

        lower_power = []
        upper_power = []
        upward_speed = []
        for i in range(constraint.shape[0]):
            lst = constraint.iloc[i, :].tolist()
            for j in range(lst[5], lst[6] + 1):
                lower_power.append(lst[2])
                upper_power.append(lst[3])
                upward_speed.append(lst[4])

        if len(lower_power) not in self.freqs:
            logger.info("机组" + str(g) + "的时间段不在" + str(self.freqs) + "中，请检查传入数据。")
            raise Exception()
        return lower_power, upper_power, upward_speed

    def y_from_raw(self, x):
        """
        根据给定的x，从原始曲线中求出y
        :param x: 给定x
        :return: 原始曲线中的y
        """
        x = np.asarray(x, dtype=float)
        y = np.empty_like(x, dtype=float)
        y[:] = np.nan
        valid = (x >= self.min_power) & (x <= self.raw_x[-1])    # 判断x是否位于出力上下限之间
        y[valid] = np.interp(x[valid], self.raw_x, self.raw_y)  # 一维线性插值
        return y[valid]    # 若x位于上下限之外，返回y为nan

    def x_from_raw(self, y):
        """
        根据给定的y，从原始曲线中求出x（对于对应多个x时，返回均值）
        :param y: 给定y
        :return: 原始曲线中的x
        """
        y = np.asarray(y, dtype=float)
        xs = []
        for x_i, y_i, x_next, y_next in np.c_[self.raw_x[:-1], self.raw_y[:-1], self.raw_x[1:], self.raw_y[1:]]:
            x = np.empty_like(y, dtype=float)
            x[:] = np.nan
            x_i, x_next, y_i, y_next = (x_i, x_next, y_i, y_next) if y_next >= y_i else (x_next, x_i, y_next, y_i)
            valid = (y >= y_i) & (y <= y_next)
            x[valid] = (x_i + x_next) / 2 if y_i == y_next else np.interp(y[valid], [y_i, y_next], [x_i, x_next])
            xs.append(x)
        x = np.empty_like(y, dtype=float)
        x[:] = np.nan
        xs = np.vstack(xs).T
        valid = ~np.all(np.isnan(xs), axis=1)
        x[valid] = np.nanmean(xs[valid], axis=1)
        return x

    def y_from_seg(self, x):
        """
        根据给定的x，从分段曲线中求出y
        :param x: 给定x
        :return: 分段曲线中的y
        """
        assert self.px is not None
        assert self.py is not None
        x = np.asarray(x, dtype=float)
        y = np.empty_like(x, dtype=float)
        y[:] = np.nan
        px = np.r_[0, self.px]
        # px = np.r_[self.px, self.raw_x[-1]]
        for x_i, y_i, x_next in np.c_[px[:-1], self.py, px[1:]]  :
            y[(x >= x_i) & (x <= x_next)] = y_i
        y[0] = self.py[0]
        return y

    def x_from_seg(self, y, sampling_type='uniform'):
        """
        根据给定的y，从分段曲线中采样出x
        :param y: 给定y
        :sampling_type: 在x的每段区间的采样方式 uniform均匀采样 max取每段最大值 min取每段最小值 mean取每段均值
        :return: 分段曲线中的x
        """
        assert self.px is not None
        assert self.py is not None
        assert sampling_type in ['uniform', 'max', 'min', 'mean']
        if sampling_type == 'max':
            sampling = lambda l, a, b: b
        elif sampling_type == 'min':
            sampling = lambda l, a, b: a
        elif sampling_type == 'mean':
            sampling = lambda l, a, b: (a + b) / 2
        else:
            sampling = lambda l, a, b: np.random.uniform(a, b, l)
        y = np.asarray(y, dtype=float)
        x = np.empty_like(y, dtype=float)
        x[:] = np.nan
        px = np.r_[self.px, self.raw_x[-1]]
        py = np.r_[self.py, self.raw_y[-1]]
        for x_i, y_i, x_next, y_next in np.c_[px[:-1], py[:-1], px[1:], py[1:]]:
            index = (y >= y_i) & (y <= y_next)
            x[index] = sampling(np.sum(index), x_i, x_next)
        return x

    def calc_seg_ratio(self):
        """
        计算每个分段长度的比例
        :return: 每段长度占比的数组
        """
        assert self.px is not None
        dx = np.diff(self.px)
        ratio = dx / dx.min()
        ratio = np.insert(ratio, 0, 0)
        # dx = np.diff(np.r_[self.px, self.raw_x[-1]])
        return ratio

    def calc_area_ratio(self):
        """
        计算分段和原始曲线围成每块面积的比例
        :param n_sampling: 采样数量(默认为10万)
        :return: 每块面积占比的数组
        """
        x = np.linspace(self.min_power, self.raw_x[-1], int(self.n_sampling))
        yr = self.y_from_raw(x)
        ys = self.y_from_seg(x)

        begin = 0
        r = []

        for i in range(0, len(ys) - 1):
            if ys[i + 1] != ys[i]:
                r.append([begin, i, i - begin])
                begin = i

        r.append([begin, len(ys), len(ys) - begin])

        region = np.empty((len(r), 3), dtype=int)
        region[::1] = r

        result = np.empty((len(region), 2), dtype=float)
        for (xl, xr, w), i in zip(region, itertools.count()):
            result[i, 0] = x[xl]
            result[i, 1] = (ys[xl:xr] - yr[xl:xr]).sum()
        result[:, 1] = result[:, 1] / np.abs(result[:, 1]).sum()
        return result[:, 1]

    def fit(self):
        px, py = self._fit()
        s = np.diff(py)
        if np.any(s < 0):
            self.px = self.py = None
            raise CanNotFit(f'第{(np.r_[:s.shape[0]][s < 0] + 1).tolist()}段不满足单调非递减')
        self.px, self.py = px, py
        return self.px, self.py

    def _fit(self):
        raise NotImplementedError

    # 没有设置最小步长时，修正分段报价为整数，且每段间隔≥1MW
    def _cal_xy1(self):
        # 根据边际成本曲线生成初步的分段报价 x y
        self.fit()

        # 根据分段情况计算价格
        self.px = np.r_[self.px, self.installed_capacity]
        self.px = np.floor(self.px).astype(int)  # 修正出力为整数
        self.py = self.y_from_raw(self.px)

        # 修正第一段出力的报价：如果不允许停机，第一段报价为报价下限
        if self.allowed_stop == 0:
            self.py[0] = 0

        # 修正输出小数点问题
        del_index = []
        for i in range(1, len(self.px)):
            if self.px[i] - self.px[i - 1] < self.subsection_gap:
                del_index.append(i - 1)
        self.px = np.delete(self.px, del_index)
        self.py = np.delete(self.py, del_index)

        # 价格修正为保留3位小数
        self.py = [round(x, self.price_decimal) for x in self.py]

    # 根据最小步长及爬坡速率，修正分段报价x, y, x表示分段出力，y表示当前分段的报价
    def _cal_xy(self):
        # 根据边际成本曲线生成初步的分段报价 x y
        self.fit()

        # 考虑最小步长及两段报价之间出力间隔≥1MW，规整到最小步长的间隔，间隔太小就减少分段，所以最终报价段数可能小于默认分段数
        del_x = []
        last_xi = self.min_power - self.min_step
        for i in range(len(self.px)):
            if self.px[i] - last_xi < max(self.min_step, self.subsection_gap):
                del_x.append(i)
                continue
            if (self.px[i] - last_xi) % self.min_step >= self.min_step / 2:
                self.px[i] = int(self.px[i] + self.min_step - (self.px[i] - last_xi) % self.min_step)
                last_xi = self.px[i]
            else:
                if self.px[i] - last_xi >= self.min_step:
                    self.px[i] = int(self.px[i] - (self.px[i] - last_xi) % self.min_step)
                    last_xi = self.px[i]
                else:
                    self.px[i] = int(self.px[i] - (self.px[i] - last_xi) % self.min_step)
                    del_x.append(i)
        self.px = np.delete(self.px, del_x)
        self.px = np.r_[self.px, self.raw_x[-1]]
        self.py = self.y_from_raw(self.px)


        # 用报价下限来筛选报价分段，如果某一段小于1元/MWH，则去掉该段
        # del_x = []
        # del_y = []
        lenx = len(self.px)
        for i in range(1 , lenx):
            if self.py[i] - self.py[i - 1] < self.min_price:
                # del_x.append(self.px[i])
                self.py[i:] = self.py[i:] + 1

        # 判断最后一段的报价是否大于成本的最后一个值
        # min_cost = self.costs[self.costs['generator'] == g]['cost'].values[-1]
        # if self.py[-1] > min_cost:
        #     self.py[-1] = min_cost
        # 修正第一段出力的报价：如果不允许停机，第一段报价为报价下限
        if self.allowed_stop == 0:
            self.py[0] = 0

        # 修正第一段为最小出力，最后一段出力为最大出力
        self.px[0] = self.min_power
        self.px[-1] = self.installed_capacity

        # 价格修正为保留3位小数
        self.py = [round(x, self.price_decimal) for x in self.py]

    # 依据出清电价，模拟中标出力
    def _simulate_bidded_power(self, col):  # col = ['ahead_price', 'real_price']
        xs = []  # 中标出力
        # 前一天的最后一个点的出力
        last_point_power = max(self.min_power, self.beginning_power)
        # 爬坡及下坡速率修正
        for p_t, lower_power, upper_power, upward_speed in \
                zip(self.price[col], self.lower_power, self.upper_power, self.upward_speed):
            x_t = 0  # 初始化：当前时刻出力
            i = len(self.py) - 1  # - 1

            # 1 出清价格大于报价，才能中标，最小到p[0]=0, 一定中标，中了最小出力
            while i >= 0:
                if p_t >= self.py[i]:
                    x_t = self.px[i]
                    break
                else:
                    i = i - 1
            # 判断机组中标出力是否为0，如果是0，则不再进行爬坡速率及出力上下限的约束，暂时不考虑停机时长约束及允许开机
            if x_t != 0:
                # 2 爬坡速率修正，且需要修正为整数
                if x_t > last_point_power + upward_speed * self.gap:
                    last_point_power = int(last_point_power + upward_speed * self.gap)
                    x_t = last_point_power
                elif x_t < last_point_power - upward_speed * self.gap:
                    last_point_power = np.ceil(last_point_power - upward_speed * self.gap)
                    x_t = last_point_power
                else:
                    last_point_power = x_t

                # 3 每个时点出力的上下限修正
                if x_t > upper_power:
                    last_point_power = upper_power
                    x_t = last_point_power
                elif x_t < lower_power:
                    last_point_power = lower_power
                    x_t = last_point_power
            xs.append(x_t)
        return np.array(xs)

    def write_result(self, g):
        # 判断边际成本是否为一条直线，如果是一条直线，则根据分段数量直接输出结果
        if len(set(self.raw_y)) > 1:
            # 修正中标出力
            self._cal_xy()
        else:
            self.px = []
            x = int((self.installed_capacity - self.min_power) / (self.subsection - 1))  # 根据段数平分出力
            y = self.raw_y[0]  # 边际成本
            self.py = []
            for i in range(self.subsection):
                self.px.append(self.min_power + x * i)
                self.py.append(y + i * 10)                # 每段之间价格增加10元/MWh
            self.px[-1] = self.installed_capacity         # 修正最后一段为装机容量

            if self.allowed_stop == 0:
                self.py = np.insert(self.py, 0, 0)
                self.py = self.py[:-1]

            # 价格修正为保留3位小数
            self.py = [round(x, self.price_decimal) for x in self.py]

        # 分段报价方案
        subsection = pd.DataFrame(columns=['generator', 'subsection', 'power_start', 'power_end', 'power', 'price'])
        subsection['subsection'] = range(1, len(self.px) + 1)
        subsection['generator'] = g
        subsection['power'] = self.px      # 分段报价方案 - 出力
        subsection['price'] = self.py      # 分段报价方案 - 报价
        subsection['power_end'] = self.px  # 分段报价方案 - 每段的上限
        subsection['power_start'] = subsection['power_end'].shift(1)      # 每段的上限
        subsection['power_start'] = subsection['power_start'].fillna(0)
        subsection = subsection.dropna(axis=0)
        subsection['power_start'] = subsection['power_start'].astype(int)  # 修正数据类型为整型
        subsection['power_end'] = subsection['power_end'].astype(int)      # 修正数据类型为整型
        subsection['power'] = subsection['power'].astype(int)              # 修正数据类型为整型

        # 模拟中标出力
        bidded_power = pd.DataFrame(columns=['generator', 'time'])
        bidded_power['time'] = self.price['time'].copy()
        bidded_power['generator'] = g

        if self.prices['ahead_price'].isnull().all():   # 日前价格为空
            bidded_power['ahead_power'] = None
            bidded_power['real_power'] = self._simulate_bidded_power('real_price')     # 实时市场中标出力
        elif self.prices['real_price'].isnull().all():  # 实时价格为空
            bidded_power['ahead_power'] = self._simulate_bidded_power('ahead_price')   # 日前市场中标出力
            bidded_power['real_power'] = None
        else:
            bidded_power['ahead_power'] = self._simulate_bidded_power('ahead_price')  # 日前市场中标出力
            bidded_power['real_power'] = self._simulate_bidded_power('real_price')  # 实时市场中标出力

        # 计算面积占比及分段比例
        target = pd.DataFrame(columns=['generator', 'subsection', 'area_ratio', 'seg_ratio'])
        target['subsection'] = range(1, len(self.px) + 1)
        area_ratio = self.calc_area_ratio().tolist()
        while len(area_ratio) < len(self.px):
            area_ratio.append(0)
        target['area_ratio'] = area_ratio
        target['seg_ratio'] = self.calc_seg_ratio().tolist()
        target['generator'] = g

        # 输出分段报价结果
        return subsection, bidded_power, target

    # 停机方案
    def shut_down_scheme(self, g):
        logger.info("机组" + str(g) + "需要停机，给出停机方案")
        x = []
        p = []
        for i in range(self.subsection - 1):
            j = self.subsection - i - 1
            x.append(self.min_power + i)
            p.append(self.upper_price - 0.01 * j)
        x.append(self.installed_capacity)
        p.append(self.upper_price)

        # 返回分段报价方案
        subsection = pd.DataFrame(columns=['generator', 'subsection', 'power_start', 'power_end', 'power', 'price'])
        subsection['power'] = x  # 分段报价方案 - 出力
        subsection['price'] = p  # 分段报价方案 - 报价
        subsection['subsection'] = range(1, len(subsection) + 1)
        subsection['generator'] = g
        subsection['power_end'] = x  # 分段报价方案 - 每段的上限
        subsection['power_start'] = subsection['power_end'].shift(1)  # 每段的上限
        subsection['power_start'] = subsection['power_start'].fillna(0)
        subsection = subsection.dropna(axis=0)

        # 返回分时刻中标出力
        bided_power = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        bided_power['time'] = self.price['time'].copy()
        bided_power['generator'] = g
        bided_power['ahead_power'] = 0
        bided_power['real_power'] = 0

        # 计算面积占比及分段比例
        target = pd.DataFrame(columns=['generator', 'subsection', 'area_ratio', 'seg_ratio'])
        target['subsection'] = range(1, len(x) + 1)
        target['area_ratio'] = 0
        target['seg_ratio'] = 0
        target['generator'] = g

        return subsection, bided_power, target

    def predict(self):
        result = {}                     # 模型输出结果汇总
        subsection = pd.DataFrame()     # 分段报价方案
        bidded_power = pd.DataFrame()   # 分时刻中标出力
        target = pd.DataFrame()         # 目标函数结果
        # 1、循环generator；
        for i, g in enumerate(self.generators['generator']):
            # print("---第%s台机组---" % g)
            # 1.1、获取当前机组边界信息及节点电价
            self.to_stop = int(self.generators[self.generators['generator'] == g]['to_stop'])
            self.stopping_hour = int(self.generators[self.generators['generator'] == g]['stopping_hour'])
            if self.stopping_hour > 0:  # 机组如果是停机状态，则不再给出分段报价方案
                continue
            self.price = self.prices[self.prices['generator'] == g].copy()
            self.freq = self.price.shape[0]
            self.gap = 1440 / self.freq
            self.price['时间'] = self.price['time'].map(
                lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap) + 1))
            # 1.2、获取基本参数
            self.subsection = int(self.generators[self.generators['generator'] == g]['subsection'].values[0] - self.is_start_zero)
            self.min_power = self.generators[self.generators['generator'] == g]['min_power'].values[0]
            self.beginning_power = self.generators[self.generators['generator'] == g]['beginning_power'].values[0]
            self.lower_power, self.upper_power, self.upward_speed = self._prepare_constraints(g)
            self.min_step = float(self.generators[self.generators['generator'] == g]['min_step'].values[0])
            self.subsection_gap = float(self.generators[self.generators['generator'] == g]['subsection_gap'].values[0])
            self.allowed_stop = int(self.generators[self.generators['generator'] == g]['allowed_stop'].values[0])
            self.installed_capacity = int(self.generators[self.generators['generator'] == g]['installed_capacity'].values[0])

            if self.to_stop == 1:  # 预测日机组需要停机，则给出停机方案
                tmp_sub, tmp_bidded_power, tmp_target = self.shut_down_scheme(g)
            else:
                # 1.3、获取raw_x、raw_y参数
                self.cost = self.costs[self.costs['generator'] == g].copy()
                self.raw_x = list(self.cost['load'])
                self.raw_y = list(self.cost['cost'])

                # 1.4、检查边际成本曲线中最大值和最小值是否包含机组的最小出力和装机容量
                # if (self.raw_x[0] > self.min_power) | (self.raw_x[-1] < self.installed_capacity):
                    # raise Exception(f"边际成本曲线中的负荷为[{self.raw_x[0]}, {self.raw_x[-1]}]，"
                    #                 f"机组的出力区间为[{self.min_power}, {self.installed_capacity}]，不匹配")

                if self.dists:
                    self.dist = self.dists[i] or [('uniform', (np.min(self.raw_y), np.max(self.raw_y)))]
                else:
                    self.dist = [('uniform', (np.min(self.raw_y), np.max(self.raw_y)))]  # 默认为均匀分布

                # 2.1、获取并存储各机组结果
                tmp_sub, tmp_bidded_power, tmp_target = self.write_result(g)

            # 2.2、合并各机组结果
            subsection = pd.concat([subsection, tmp_sub], axis=0)
            bidded_power = pd.concat([bidded_power, tmp_bidded_power], axis=0)
            target = pd.concat([target, tmp_target], axis=0)

        # 3、输出结果汇总为result
        result['subsection_declaration'] = subsection.to_dict('list')
        result['bidded_power'] = bidded_power.to_dict('list')
        result['target'] = target.to_dict('list')

        logger.info(f'result = {result}')
        logger.info("---------------------------边际成本_分段报价模型运行结束--------------------------")
        return result




class AdaptMCS(MarginalCostSegmentationBase):
    def __init__(self, generators, prices, costs, constraints, lower_price=0, upper_price=1500, default_subsection=10,
                 price_decimal=3, n_sampling=1e5, min_split_scale=0.1, is_start_zero=1,min_price=1):
        super().__init__(generators, prices, costs, constraints, lower_price, upper_price, default_subsection,
                         price_decimal, n_sampling, min_split_scale, is_start_zero, min_price)
        logger.info("---------------------------边际成本_自适应分段报价模型--------------------------")

    def _fit(self):
        x = np.linspace(self.min_power, self.raw_x[-1], int(self.n_sampling))
        y = self.y_from_raw(x)
        regr = DecisionTreeRegressor(max_leaf_nodes=self.subsection, min_samples_leaf=self.min_split_scale)
        # max_leaf_nodes：最大叶节点数；min_samples_leaf；叶子节点最小样本数
        regr.fit(x.reshape((-1, 1)), y.reshape((-1, 1)))
        px = np.r_[self.min_power,
                   np.asarray(sorted(regr.tree_.threshold[regr.tree_.feature == 0])) +
                   (self.raw_x[-1] - self.min_power) / self.n_sampling]
        py = regr.predict(px.reshape((-1, 1))).ravel()
        return px, py


class DistAdaptMCS(MarginalCostSegmentationBase):
    def __init__(self, generators, prices, costs, constraints, lower_price=0, upper_price=1500, default_subsection=10,
                 price_decimal=3, n_sampling=1e5, min_split_scale=0.1, is_start_zero=1, min_price=1, dists=None):
        super().__init__(generators, prices, costs, constraints, lower_price, upper_price, default_subsection,
                         price_decimal, n_sampling, min_split_scale, is_start_zero, min_price, dists)
        logger.info("---------------------------边际成本_根据节点电价分布_分段报价模型--------------------------")

    def sampling(self, dist, n_sampling, scale=1):
        name, param = dist
        if name == 'uniform':
            _down, _up = param
            _mean = (_down + _up) / 2
            _half = (_up - _down) / 2
            _down -= _mean - _half * scale
            _up += _mean + _half * scale
            y = np.random.uniform(_down, _up, n_sampling)
        elif name == 'normal':
            _mean, _std = param
            _std *= scale
            y = np.random.normal(_mean, _std, n_sampling)
        else:
            raise Exception(f'不支持[{name}]分布类型')
        x = self.x_from_raw(y)
        # valid = ~np.isnan(x)
        valid = (x >= self.min_power) & (x <= self.raw_x[-1])   # 分段报价在出力上下限之间
        return x[valid], y[valid]

    def mix_sampling(self, n_sampling, scale=1):
        pool = Pool()
        samp = pool.map(partial(self.sampling, n_sampling=n_sampling, scale=scale), self.dist)
        pool.close()
        return np.concatenate([d[0] for d in samp]), np.concatenate([d[1] for d in samp])

    def _fit(self):
        for scale in range(5):
            x, y = self.mix_sampling(self.n_sampling, 2 ** scale)
            if x.shape[0] == 0:
                continue
            regr = DecisionTreeRegressor(max_leaf_nodes=self.subsection, min_samples_leaf=self.min_split_scale)
            regr.fit(x.reshape((-1, 1)), y.reshape((-1, 1)))
            px = np.r_[self.min_power,
                       np.asarray(sorted(regr.tree_.threshold[regr.tree_.feature == 0])) +
                       (self.raw_x[-1] - self.min_power) / self.n_sampling]
            py = regr.predict(px.reshape((-1, 1))).ravel()
            return px, py
        raise DistAdaptMCS.CantSampling('无法采样')

    class CantSampling(Exception):
        pass


if __name__ == '__main__':
    generator = pd.read_excel(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\input.xlsx", sheet_name="generators", index_col=None)
    cost = pd.read_excel(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\input.xlsx", sheet_name="costs_mc", index_col=None)
    price = pd.read_excel(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\input.xlsx", sheet_name="prices", index_col=None)
    constraint = pd.read_excel(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\input.xlsx", sheet_name="constraints", index_col=None)

    bs = DistAdaptMCS(generator, price, cost, constraint)
    res = bs.predict()   # n_sampling
    print(pd.DataFrame(res['subsection_declaration']))
    print(pd.DataFrame(res['bidded_power']))
    print(pd.DataFrame(res['target']))
    print('------------')
