# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_freq_svr_intraday import Prediction


class FreqSvrIntradayHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        pred = Prediction(params['training']['x'], params['training']['y'])
        result = pred.predict(params['predict']['x'], to_json=True)
        result['r2_score'] = pred.r2_score
        result['explained_variance_score'] = pred.explained_variance_score
        self.write(result)
