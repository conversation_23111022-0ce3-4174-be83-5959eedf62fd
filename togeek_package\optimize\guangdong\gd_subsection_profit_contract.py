#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/17 16:17
# <AUTHOR> Darlene
# -*- coding: utf-8 -*

import numpy as np
import pandas as pd
import operator
import warnings
import logging
import os
from multiprocessing import Pool

warnings.filterwarnings("ignore")
logger = logging.getLogger()
cpu_num = os.cpu_count()


class SubsectionDeclarProfitGD:
    def __init__(self, generators, prices, costs, constraints, plants, min_gen, lower_price=0, upper_price=1500, min_profit=0, POP_SIZE=1000,
                 N_GENERATIONS=100, default_subsection=3, price_gap=1, price_decimal=3, is_start_zero=0):
        # 初始化输入信息
        self.generators = pd.DataFrame(generators)    # 机组信息
        self.prices = pd.DataFrame(prices)            # 机组节点电价
        self.costs = pd.DataFrame(costs)              # 机组变动成本
        self.constraints = pd.DataFrame(constraints)  # 机组分段约束条件-爬坡速率、出力上下限
        self.plants = pd.DataFrame(plants)            # 场站-机组数据
        self.default_subsection = default_subsection  # 默认申报分段数量
        self.min_profit = min_profit                  # 机组停机的最小收益
        self.lower_price = lower_price                # 报价下限
        self.upper_price = upper_price                # 报价上限
        self.price_gap = price_gap                    # 两段间报价的最小差值
        self.price_decimal = price_decimal            # 价格保留位数，默认保留小数点后3位
        self.min_gen = pd.DataFrame(min_gen)          # 最小开始台数，当必须要保开机时，保证最小开机台数，最大开机台数，取收益前几台开机，其他给停机方案

        # 默认值
        self.freqs = [24, 48, 96]                     # 每日的时间点的数量只能是这些数字
        self.gap = 15                                 # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        self.subsection = 3                           # 分段数量，这里仅做初始化
        self.is_start_zero = is_start_zero            # 第一段起始出力是否从0开始

        # 遗传算法基本参数
        self.gene_size = 10                 # 基因大小，代表每个数字的精度，10个位表示1024
        self.DNA_SIZE = self.gene_size * 2 * self.subsection  # 基因组大小
        self.POP_SIZE = POP_SIZE            # 种群数量
        self.N_GENERATIONS = N_GENERATIONS  # 进化代数
        self.CROSSOVER_RATE = 0.6           # 杂交比例，按照基因杂交；将来应该可以选择按照染色体、基因、点位三种方式都可以杂交
        self.MUTATION_RATE = 0.04           # 变异可能性
        self.hybridization_rate = 0.4       # 子代从亲代获取基因的比例，0.4表示父6母4，循环是放回式采样，所以母不到4

        # 数据格式化
        self.installed_capacity = 0         # 装机容量
        self.min_power = 0                  # 最小技术出力
        self.beginning_power = 0            # 初始出力
        self.min_step = 0                   # 最小步长
        self.subsection_gap = 1             # 每段出力的最小间隔
        self.allowed_stop = 0               # 是否允许停机，默认为0-不允许停机，1-允许停机
        self.f_auxiliary_power_ratio = 0    # 事前厂用电率
        self.b_auxiliary_power_ratio = 0    # 事后厂用电率

        self.p1 = []                        # 一次项成本公式x1
        self.p2 = []                        # 二次项成本公式x2
        self.lower_power = None             # 出力下限
        self.upper_power = None             # 出力上限
        self.upward_speed = None            # 爬坡速率：MW每分钟
        self.min_boot_hour = 0              # 最小开机时长
        self.min_stop_hour = 0              # 最小停机时长
        self.running_hour = 0               # 连续开机时长
        self.stopping_hour = 0              # 连续停机时长


        # 写入log
        logger.info("------------------分段报价_收益最大_遗传算法_模型开始运行------------------------")
        msg = self._prepare_load()          # 数据预处理 + 数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        for col in ['installed_capacity', 'min_power', 'beginning_power', 'subsection', 'allowed_stop', 'min_stop_hour',
                    'min_boot_hour', 'running_hour', 'stopping_hour']:
            self.generators[col] = self.generators[col].astype('int')
        for col in ['min_step', 'subsection_gap', 'f_auxiliary_power_ratio', 'b_auxiliary_power_ratio']:
            self.generators[col] = self.generators[col].astype('float')

        # 修正 constriants 数据类型
        self.constraints['generator'] = self.constraints['generator'].astype('str')
        self.constraints['period'] = self.constraints['period'].astype('str')
        for col in ['lower_power', 'upper_power', 'upward_speed']:
            self.constraints[col] = self.constraints[col].astype('float')

        # 修正 prices 数据类型并排序
        if 'real_price' not in self.prices.columns:  # 如果没有实时价格数据，则设置实时价格=日前价格，等价于计算利润时只考虑日前市场
            self.prices['real_price'] = self.prices['ahead_price']
        if self.prices['real_price'].isnull().all():
            self.prices['real_price'] = self.prices['ahead_price']
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        for col in ['ahead_price', 'real_price', 'mid_long_term_elec', 'mid_long_term_price']:  # 修正数据类型
            self.prices[col] = self.prices[col].astype('float')

        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、installed_capacity、min_power、min_step、gap、allowed_stop、min_stop_hour、
        # min_boot_hour、running_hour、stopping_hour)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['installed_capacity'] + hasnull['min_power'] + hasnull['min_step'] + \
                   hasnull['subsection_gap'] + hasnull['allowed_stop'] + hasnull['min_stop_hour'] + \
                   hasnull['min_boot_hour'] + hasnull['running_hour'] + hasnull['stopping_hour']
        if num_null > 0:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour、cost中有" + str(num_null) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg += "1.1, generator中generator、installed_capacity、min_power、min_step、subsection_gap、allowed_stop、min_stop_hour、" \
                        "min_boot_hour、running_hour、stopping_hour、cost中没有空值；"

        # 2、循环generator，检查爬坡速率及出力上下限分段数量符合要求
        hasnull2 = self.constraints.isnull().sum()
        num_null2 = hasnull2['generator'] + hasnull2['period'] + hasnull2['lower_power'] + \
                    hasnull2['upper_power'] + hasnull2['upward_speed']
        if num_null2 > 0:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中有" + \
                   str(num_null2) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg += "1.2, constraints中generator、period、lower_power、upper_power、upward_speed中没有空值；\n"

        # 2、循环generator，检查连续运行时长和连续停机时长，一个必须为0值
        for g in self.generators['generator']:
            try:
                running_hour = int(self.generators[self.generators['generator'] == g]['running_hour'])
                stopping_hour = int(self.generators[self.generators['generator'] == g]['stopping_hour'])
                if running_hour + stopping_hour != 0:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值不同时为0, "
                    if running_hour * stopping_hour == 0:
                        msg += "其中一个值为0，"
                    else:
                        msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                               + str(stopping_hour) + ", 不符合规定，请检查传入数据; "
                        raise Exception()
                else:
                    msg += "2, 机组" + str(g) + " 的连续运行时长为" + str(running_hour) + ", 连续停机时长为" \
                           + str(stopping_hour) + ", 两个值同时为0，不符合规定，请检查传入数据; "
                    raise Exception()
            except:
                msg += "2, 机组数据有异常，请检查传入的机组数据。"
                raise Exception()
            finally:
                msg += "机组数据验证通过；\n"

        # 起始出力如果是空值则用最小出力代替；分段数量如果是空值则用默认值代替；事前/事后厂用电率如果是空值则用0代替
        self.generators['beginning_power'].fillna(self.generators['min_power'], inplace=True)
        self.generators['subsection'].fillna(self.default_subsection, inplace=True)
        self.generators['f_auxiliary_power_ratio'].fillna(0, inplace=True)
        self.generators['b_auxiliary_power_ratio'].fillna(0, inplace=True)

        # 3、循环generator，检查价格；
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freqs中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中;  "
                else:
                    msg = msg + "3.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中;  "
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                # gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 96):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 48):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 24):
                    msg = msg + "3.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过；\n"
                else:
                    msg = msg + "3.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    raise Exception()
            except:
                msg += "价格数据有异常，请检查传入的价格数据。"
                logger.info(msg)
                raise Exception()

        # 4、循环generator，检查变动成本
        self.costs['generator'] = self.costs['generator'].astype('str')
        generators = self.costs['generator'].unique()
        if len(generators) != len(self.generators['generator']):
            msg += "4, costs中应有" + str(len(self.generators['generator'])) + "台机组，costs表中有" + \
                   str(len(generators)) + "台机组，机组数量不一致，请检查传入数据。"
            raise Exception()
        else:
            for g in generators:
                try:
                    # 1、每个机组必须有对应的变动成本值，
                    cost = self.costs[self.costs['generator'] == g].copy()
                    hasnull = cost.isnull().sum()  # 计算这些列里的空值数量
                    num_null = hasnull['generator'] + hasnull['load_rate'] + hasnull['cost']
                    if num_null > 0:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中有" + \
                              str(num_null) + "个空值，请检查传入数据。"
                        raise Exception()
                    else:
                        msg = msg + "4, 机组" + str(g) + "的generator、load_rate、cost中没有空值,"
                except:
                    msg += "4, 机组" + str(g) + "的输入数据有异常。"
                    raise Exception()
                finally:
                    msg += "机组" + str(g) + " 数据验证通过。\n"
        return msg

    def _prepare_constraints(self, g):
        """
        将出力上下限及爬坡速率设置为长度=freq的列表
        :param g: 机组编号
        :return: 出力下限、出力上限、爬坡速率，列表
        """
        constraint = self.constraints[self.constraints['generator'] == g]
        constraint = constraint[['generator', 'period', 'lower_power', 'upper_power', 'upward_speed']]
        # constraint['start'] = constraint['period'].apply(lambda x: x.split('-')[0]).map(
        #     lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
        # constraint['end'] = constraint['period'].apply(lambda x: x.split('-')[1]).map(
        #     lambda s: int(int(str(s)[0:2]) * (60 / self.gap) + (int(str(s)[3:5]) / self.gap)))
        lower_power = constraint['lower_power'].to_list()
        upper_power = constraint['upper_power'].to_list()
        upward_speed = constraint['upward_speed'].to_list()
        return lower_power, upper_power, upward_speed

    # 分拆x和p，并转化到上下限内
    def _splitxy(self, xp_line):
        # 将分段转换为累加值，作为递增的分段和递增的价格
        x = np.cumsum(xp_line[:self.subsection])
        x = x * (self.installed_capacity - self.min_power) + self.min_power
        p = np.cumsum(xp_line[self.subsection:])
        p = p * (self.upper_price - self.lower_price) + self.lower_price
        return x, p

    # 根据最小步长及爬坡速率，修正分段报价x, p
    def _correct_xp(self, x, p):  # x表示分段出力，p表示当前分段的报价
        if self.is_start_zero == 1:
            x[0] = self.min_power                   # 如果出力从0开始，则修正第一段出力为出力下限
        else:
            x[0] = self.min_power + self.min_step   # 如果出力不是从0开始，则修正第一段出力为出力下限 + 1

        p_ = np.insert(p, 0, self.lower_price)      # 修正第一段报价为0价
        p_ = p_[:-1]
        p_ = [round(x, int(self.price_decimal)) for x in p_]   # 修正价格的小数点位数

        # 考虑最小步长及两段报价之间出力间隔≥1MW，规整到最小步长的间隔，间隔太小就减少分段，所以最终报价段数可能小于默认分段数
        del_x = []
        last_xi = x[0]
        for i in range(1, len(x)):
            if x[i] - last_xi < max(self.min_step, self.subsection_gap):
                del_x.append(i)
                continue
            if (x[i] - last_xi) % self.min_step >= self.min_step / 2:
                x[i] = int(x[i] + self.min_step - (x[i] - last_xi) % self.min_step)
                last_xi = x[i]
            else:
                if x[i] - last_xi >= self.min_step:
                    x[i] = int(x[i] - (x[i] - last_xi) % self.min_step)
                    last_xi = x[i]
                else:
                    x[i] = int(x[i] - (x[i] - last_xi) % self.min_step)
                    del_x.append(i)
        x_ = np.delete(x, del_x)
        x_[-1] = self.installed_capacity                       # 修正最后一段出力必须为最大出力
        p_ = np.delete(p_, del_x)

        # 判断每个出力段对应的价格是否相等，如果相等则合并相应的出力段
        del_p = []
        for j in range(1, len(p_)):
            if p_[j] - p_[j-1] < self.price_gap:
                del_p.append(j)

        x_ = np.delete(x_, del_p)
        p_ = np.delete(p_, del_p)
        return x_, p_

    # 依据出清电价，模拟中标出力
    def _simulate_bidded_power(self, x, p, col):   # col = ['ahead_price', 'real_price']
        xs = []  # 中标出力
        last_point_power = max(self.min_power, self.beginning_power)  # 前一天的最后一个点的出力

        # 爬坡及下坡速率修正
        for p_t, lower_power, upper_power, upward_speed in zip(self.price[col], self.lower_power, self.upper_power, self.upward_speed):
            x_t = 0  # 初始化：当前时刻出力
            i = len(p) - 1  # - 1

            # 1 出清价格大于报价，才能中标，最小到p[0]=0, 一定中标，中了最小出力
            while i >= 0:
                if p_t >= p[i]:
                    x_t = x[i]
                    break
                else:
                    i = i - 1
            # 判断机组中标出力是否为0，如果是0，则不再进行爬坡速率及出力上下限的约束，暂时不考虑停机时长约束及允许开机
            if x_t != 0:
                # 2 爬坡速率修正，且需要修正为整数
                if x_t > last_point_power + upward_speed * self.gap:
                    last_point_power = int(last_point_power + upward_speed * self.gap)
                    x_t = last_point_power
                elif x_t < last_point_power - upward_speed * self.gap:
                    last_point_power = np.ceil(last_point_power - upward_speed * self.gap)
                    x_t = last_point_power
                else:
                    last_point_power = x_t

                # 3 每个时点出力的上下限修正
                if x_t > upper_power:
                    last_point_power = upper_power
                    x_t = last_point_power
                elif x_t < lower_power:
                    last_point_power = lower_power
                    x_t = last_point_power
            xs.append(x_t)
        return np.array(xs)

    # 评价函数： 依据分段出力x和对应的分段报价p，计算日前和实时市场各时点的中标出力和总收益
    def _income(self, x, p):
        x_, p_ = self._correct_xp(x, p)
        xas = self._simulate_bidded_power(x_, p_, 'ahead_price')  # 日前市场中标出力
        xrs = self._simulate_bidded_power(x_, p_, 'real_price')   # 实时市场中标出力

        long_elec = np.array(self.price['mid_long_term_elec'])    # 中长期电量，单位MWh
        long_price = np.array(self.price['mid_long_term_price'])  # 中长期电价，单位元/MWh

        # 变动成本计算
        load_rate = xrs / self.installed_capacity  # 负荷率
        point_cost = self.p2[0] * load_rate * load_rate + self.p2[1] * load_rate + self.p2[2]  # 变动成本

        # 日前上网电量 = 日前出力 /4 * （1-事前厂用电率）
        # 实时上网电量 = 实时出力 / 4 * （1-事后厂用电率）
        # 单位转换，将功率转换位电能：间隔15分钟，相当于除以4；间隔30分钟，相当于除以2；间隔1小时，相当于除以1
        xas_elec = xas * (self.gap / 60) * (1 - self.f_auxiliary_power_ratio)  # 日前上网电量
        xrs_elec = xrs * (self.gap / 60) * (1 - self.b_auxiliary_power_ratio)  # 实时上网电量

        # 收入 = 中长期带能量 * 中长期电价 + （日前上网电量-中长期电量）*日前电价 + （实时上网电量-日前上网电量）*实时电价  192.168.1.215:8988
        point_revenue = long_elec * long_price + (xas_elec - long_elec) * self.price.ahead_price + (xrs_elec - xas_elec) * self.price.real_price

        # 收益 = 收入 - 成本
        profit = sum(point_revenue - point_cost * xrs * (self.gap / 60))
        return profit, x_, p_, xas, xrs

    # 计算每个染色体
    def cal_xp(self, xp):
        x, p = self._splitxy(xp)
        profit, x_, p_, xas, xrs = self._income(x, p)
        return profit, x_, p_, xas, xrs

    # 基因组表达
    def _translateDNA(self, pop):  # pop表示种群矩阵，一行表示一个二进制编码表示的DNA，矩阵的行数为种群数目
        # 表达分两步，第一步将基因序列转换为十进制数字，第二步依据同一个机组下各个基因代表的分段，依据权重分配分段
        # 先定义一个占位的
        pop_10 = np.ones([pop.shape[0], 1])
        # 按照单个基因大小取值，总共有分段数量-1然后乘以2，再乘以机组数量个基因
        # 从第一个基因开始，一个一个往后取
        for s in range(0, (self.subsection * 2)):
            # 第一步，取出当前位置的基因序列，并且转换为十进制
            b = pop[..., self.gene_size * s:self.gene_size * (s + 1)].dot(2 ** np.arange(self.gene_size)[::-1])
            c = b.reshape(pop.shape[0], 1)
            pop_10 = np.concatenate((pop_10, c), axis=1)
        # 去掉第一个占位的
        pop_10 = pop_10[..., 1:]
        # 按照机组分组，机组内计算总权重，然后分配分段，价格也分段
        pop_weight = np.ones([pop.shape[0], 1])
        # 取偶数列当做x出力分段的依据
        pop_u_x = pop_10[:, ::2]
        # 用自身分段的x除以分段和，划分权重
        # print(pop_u_x)
        u_x_sum = np.sum(pop_u_x, axis=1)
        u_x_sum = 1 / u_x_sum
        u_x_sum_tile = np.tile(u_x_sum, (self.subsection, 1)).T
        # print(u_x_sum_tile)
        pop_u_x = np.multiply(pop_u_x, u_x_sum_tile)
        # print(pop_u_x)
        pop_weight = np.concatenate((pop_weight, pop_u_x), axis=1)
        # 取奇数列当做p价格分段的依据
        pop_u_p = pop_10[:, 1::2]
        u_p_sum = np.sum(pop_u_p, axis=1)
        u_p_sum = 1 / u_p_sum
        pop_u_p_tile = np.tile(u_p_sum, (self.subsection, 1)).T
        pop_u_p = np.multiply(pop_u_p, pop_u_p_tile)
        pop_weight = np.concatenate((pop_weight, pop_u_p), axis=1)
        # print(pop_weight[...,1:])
        return pop_weight[..., 1:]

    # 单点变异
    def _mutation(self, child):
        if np.random.rand() < self.MUTATION_RATE:               # 以MUTATION_RATE的概率进行变异
            mutate_point = np.random.randint(0, self.DNA_SIZE)  # 随机产生一个实数，代表要变异基因的位置
            child[mutate_point] = child[mutate_point] ^ 1       # 将变异点的二进制为反转：按位异或，相同为0，相异为1

    # 杂交，按照基因杂交，基因排布为量价交叉 1 mother-[shape]
    def _crossover_and_mutation(self, pop):
        new_pop = []
        for father in pop:  # 遍历种群中的每一个个体，将该个体作为父亲
            child = father  # 孩子先得到父亲的全部基因
            if np.random.rand() < self.CROSSOVER_RATE:  # 产生子代时不是必然发生交叉，而是以一定的概率发生交叉
                mother = pop[np.random.randint(pop.shape[0]-1)]  # 在种群中选择另一个个体，并将该个体作为母亲
                # 子代从亲代获取基因的比例，0.4表示父6母4，循环是放回式采样，所以母不到4
                for i in range(int(2 * self.subsection * self.hybridization_rate)):
                    cross_points = np.random.randint(low=0, high=pop.shape[1])  # 随机产生杂交基因的位点
                    # 孩子得到位于交叉点所在位置的母亲的一个基因
                    child[(int(cross_points / self.gene_size)) * self.gene_size:(int(cross_points / self.gene_size)) * self.gene_size + self.gene_size] \
                    = mother[(int(cross_points / self.gene_size)) * self.gene_size:(int(cross_points / self.gene_size)) * self.gene_size + self.gene_size]
            self._mutation(child)  # 每个后代有一定的机率发生变异
            new_pop.append(child)
        return new_pop

    # 选择新种群，根据收益越大，被选中的概率越大
    def _select(self, pop, profit):  # nature selection wrt pop's fitness
        profit = profit.astype(float)
        p_min = min(profit)
        p_max = max(profit)
        if p_max == p_min:
            idx = np.arange(len(profit))
        else:
            fit_ = (profit - p_min) / (p_max - p_min)   # 标准化处理
            # 过滤掉np.nan值
            fit_ = filter(lambda x: x >= 0, fit_)
            fit = np.array(list(fit_))
            # logger.info(fit.sum(), sorted(fit))
            # 从一维数组(a)中随机抽取数字，组成指定大小(size)的数组，replace=True表示可以取相同数字，数组p与数组a对应，表示数组a中每个元素被抽到的概率
            # 在这里，profit越大, 所在样本被抽到的概率越大
            idx = np.random.choice(a=np.arange(fit.shape[0]), size=fit.shape[0], replace=True, p=fit / (fit.sum()))
        return pop[idx]

    # 计算日前市场和实时市场的中标出力、总收益，返回该种群中的最优解
    def _get_fitness(self, pop):
        pop_weight = self._translateDNA(pop)

        pool = Pool(processes=cpu_num)
        result = pool.map(self.cal_xp, pop_weight)
        pool.close()

        # 提取收益并找到最优个体
        profits = []
        best_profit = -np.inf
        best_x = []
        best_p = []
        best_xa = []
        best_xr = []

        for item in result:
            if len(item) == 5:
                profit, x_, p_, xa, xr = item
                profits.append(profit)

                # 记录当前代最优个体
                if profit > best_profit:
                    best_profit = profit
                    best_x = x_
                    best_p = p_
                    best_xa = xa
                    best_xr = xr
            else:
                profits.append(-np.inf)  # 无效结果设为负无穷

        # 保存当前代最优个体
        self.current_best_profit = best_profit
        self.current_best_x = best_x
        self.current_best_p = best_p
        self.current_best_xa = best_xa
        self.current_best_xr = best_xr

        return np.array(profits)
    # 输出结果处理：判断最终收益，如果小于设定收益，给出保开机方案或停机方案
    def deal_result(self, g, max_idx, tmp_result):  # g为机组编号， best_subsection为N代之后的最佳结果
        # 结果拆解
        profits, xas, xrs, xs, ps = tmp_result
        profit = profits[max_idx]

        # 最终收益与最小收益对比，小于最小收益的启用手动报价方案
        if profit < self.min_profit:
            # 如果最终收益小于最小收益，找最小开机台数，排
            # 判断状态
            if (self.running_hour >= self.min_boot_hour) & self.allowed_stop:  # 给出停机方案
                sign = '停机方案'
                # print("机组" + str(g) + "可以停机")
                logger.info("机组" + str(g) + "可以停机")
                x = []
                p = []
                for i in range(self.subsection - 1):
                    j = self.subsection - i - 1
                    if self.is_start_zero == 0:
                        i += 1
                    x.append(self.min_power + i)
                    p.append(self.upper_price - 0.01 * j)
                x.append(self.installed_capacity)
                p.append(self.upper_price)
            elif self.running_hour > 0:                 # 给出保开机方案
                sign = '保开机方案'
                # print("机组" + str(g) + "以最低出力运行")
                logger.info("机组" + str(g) + "以最低出力运行")
                x = []
                p = [self.lower_price]
                for i in range(self.subsection - 1):
                    j = self.subsection - i - 2
                    if self.is_start_zero == 0:
                        i += 1
                    x.append(self.min_power + i * self.min_step)
                    p.append(self.upper_price - 0.01 * j)
                x.append(self.installed_capacity)
            else:                                    # 机组处于停机状态，给出停机方案
                # print("机组" + str(g) + "处于停机状态")
                sign = '停机方案'
                logger.info("机组" + str(g) + "处于停机状态，给出停机方案")
                x = []
                p = []
                for i in range(self.subsection - 1):
                    j = self.subsection - i - 1
                    if self.is_start_zero == 0:
                        i += 1
                    x.append(self.min_power + i)
                    p.append(self.upper_price - 0.01 * j)
                x.append(self.installed_capacity)
                p.append(self.upper_price)
            xa = self._simulate_bidded_power(x, p, 'ahead_price')
            xr = self._simulate_bidded_power(x, p, 'real_price')
        else:
            sign = '正常报价方案'
            xa = xas[max_idx]  # 分时刻中标出力 - 日前
            xr = xrs[max_idx]  # 分时刻中标出力 - 实时
            x = xs[max_idx]    # 分段报价方案 - 出力
            p = ps[max_idx]    # 分段报价方案 - 价格

        # 返回最大收益
        tmp_profit = pd.DataFrame(columns=['generator', 'profit'])
        tmp_profit['generator'] = [g]
        tmp_profit['profit'] = [profit]    # 最大收益

        # 返回分段报价方案
        tmp_subsection = pd.DataFrame(columns=['generator', 'subsection', 'power_start', 'power_end', 'power', 'price'])
        tmp_subsection['power'] = x        # 分段报价方案 - 出力
        tmp_subsection['price'] = p        # 分段报价方案 - 报价
        tmp_subsection['subsection'] = range(1, len(tmp_subsection) + 1)
        tmp_subsection['generator'] = g
        tmp_subsection['power_end'] = x    # 分段报价方案 - 每段的上限
        tmp_subsection['power_start'] = tmp_subsection['power_end'].shift(1)  # 每段的上限
        if self.is_start_zero == 1:
            fill_value = 0
        else:
            fill_value = self.min_power
        tmp_subsection['power_start'] = tmp_subsection['power_start'].fillna(fill_value)
        tmp_subsection = tmp_subsection.dropna(axis=0)

        # 返回分时刻中标出力
        tmp_bided_power = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        tmp_bided_power['time'] = self.price['time'].copy()
        tmp_bided_power['generator'] = g
        tmp_bided_power['ahead_power'] = xa
        tmp_bided_power['real_power'] = xr
        return tmp_profit, tmp_subsection, tmp_bided_power, sign

    # def sel_result(self, sign_df):
    #     '''
    #     1、按场站处理最大、最小开机台数
    #     2、组合sing_df和场站对齐，对每个开机状态分组求和
    #     3、检查最小开机台数，除停机方案以外其他的机组数，如果小于最小开机台数，返回信息，要求修改停机时长数据
    #     4、检查最大开机台数，如果大于最大开机台数，去掉停机方案
    #     '''
    #     sign_df = sign_df.merge(self.plants)
    #     sign_min = sign_df[~(sign_df['type'] == '停机方案')]
    #     # 检查最小开机台数  去掉停机方案以外的台数
    #     sign_min = sign_min.groupby(['plant']).sum('type').reset_index()
    #     sign_min = sign_min.merge(self.min_gen, how='left', on='plant')
    #     # 如果type大于min，返回1，反之0
    #     sign_min['min_s'] = sign_min.apply(lambda row: 1 if row['type'] >= row['mini'] else 0, axis=1)
    #     # 如果type小于max，返回1，反之0
    #     sign_min['max_s'] = sign_min.apply(lambda row: 1 if row['type'] <= row['maxi'] else 0, axis=1)




    def predict(self):
        result = {}                    # 模型输出结果汇总
        profit = []                    # 最大收益
        subsection_declaration = []    # 分段报价结果
        bided_power = []               # 分时刻中标出力
        open_gens = {}
        # 1、循环generator；
        for g in self.generators['generator']:
            # print("---第%s台机组---" % g)
            generator = self.generators[self.generators['generator'] == g]
            # 1.1、获取当前机组边界信息及节点电价
            self.stopping_hour = int(generator['stopping_hour'])
            if self.stopping_hour > 0:
                continue

            self.price = self.prices[self.prices['generator'] == g].copy()
            freq = self.price.shape[0]
            gap = 1440 / freq
            self.gap = gap
            self.price['时间'] = self.price['time'].map(
                lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap) + 1))

            # 1.2、获取基本参数
            self.subsection = int(generator['subsection'])
            self.DNA_SIZE = int(self.gene_size * 2 * self.subsection)
            self.lower_power, self.upper_power, self.upward_speed = self._prepare_constraints(g)
            self.subsection_gap = float(generator['subsection_gap'])
            self.allowed_stop = int(generator['allowed_stop'])
            self.installed_capacity = int(generator['installed_capacity'])
            self.min_power = float(generator['min_power'])
            self.min_step = float(generator['min_step'])
            self.beginning_power = float(generator['beginning_power'])
            self.min_boot_hour = int(generator['min_boot_hour'])
            self.min_stop_hour = int(generator['min_stop_hour'])
            self.running_hour = int(generator['running_hour'])
            self.f_auxiliary_power_ratio = float(generator['f_auxiliary_power_ratio']) / 100
            self.b_auxiliary_power_ratio = float(generator['b_auxiliary_power_ratio']) / 100

            # 保存最优解
            self.gene_best_profit = []  # 每一代最优的收益
            self.gene_best_xas = []     # 每一代最优收益对应的日前中标出力
            self.gene_best_xrs = []     # 每一代最优收益对应的实时中标出力
            self.gene_best_xs = []      # 每一代最优收益对应的分段报价-出力
            self.gene_best_ps = []      # 每一代最优收益对应的分段报价-价格

            self.p2 = np.polyfit(self.costs[self.costs['generator'] == g]['load_rate'].values,
                                 self.costs[self.costs['generator'] == g]['cost'].values, 2)

            pop = np.random.randint(2, size=(self.POP_SIZE, self.DNA_SIZE))  # 二维 01 矩阵


            # 1.3，每个机组迭代N代
            for n in range(self.N_GENERATIONS):  # 迭代N代
                profits = self._get_fitness(pop)  # 现在只返回profits数组

                # 保存当前代的最优个体信息
                self.gene_best_profit.append(self.current_best_profit)
                self.gene_best_xas.append(self.current_best_xa)
                self.gene_best_xrs.append(self.current_best_xr)
                self.gene_best_xs.append(self.current_best_x)
                self.gene_best_ps.append(self.current_best_p)

                # 保留最优的10%
                top_index = profits.argsort()[::-1][0:int(profits.shape[0] / 10)]
                top_pop = pop[top_index]

                # 生成新的种群，这时候不删除最优的10%，以便于可以遗传后代
                pop = self._select(pop, profits)  # 选择生成新的种群
                # 补充新的随机样本，至总数减去保留10%，如果总数大于此数字，则把尾巴剪掉
                if pop.shape[0] > (self.POP_SIZE - top_pop.shape[0]):
                    pop = pop[0:(self.POP_SIZE - top_pop.shape[0])]
                else:
                    pop_append = np.random.randint(2, size=(
                    self.POP_SIZE - pop.shape[0] - top_pop.shape[0], self.DNA_SIZE))
                    pop = np.concatenate((pop, pop_append), axis=0)
                # 进行杂交、变异
                pop = np.array(self._crossover_and_mutation(pop))
                # 将最优的10%加回来
                pop = np.concatenate((pop, top_pop), axis=0)
                # print("补充完成之后，pop.shape为：", pop.shape)

            # 20230329 优先选择满足分段要求的最优解
            xs_idx = list(zip(range(len(self.gene_best_xs)), self.gene_best_xs))
            res_idx = list(filter(lambda x: len(x[1]) == self.subsection, xs_idx))
            if not res_idx:
                tmp_max_profit_idx = np.argmax(self.gene_best_profit)
            else:
                subsection_idx_lst = list(zip(*res_idx))[0]
                profits_sub = [self.gene_best_profit[i] for i in subsection_idx_lst]
                tmp_max_profit_idx = subsection_idx_lst[np.argmax(profits_sub)]

            tmp_result = [self.gene_best_profit, self.gene_best_xas, self.gene_best_xrs, self.gene_best_xs, self.gene_best_ps]

            # 获取并存储各机组结果
            tmp_profit, tmp_subsection_declaration, tmp_bided_power, scheme_type = self.deal_result(g, tmp_max_profit_idx, tmp_result)

            profit.append(tmp_profit)
            subsection_declaration.append(tmp_subsection_declaration)
            bided_power.append(tmp_bided_power)
            open_gens[f"{g}"] = scheme_type

        # 最小和最大开机台数

        # 2、输出结果
        # 2.1、整合各机组结果: 由list转为DataFrame
        profit = pd.concat(profit)
        subsection_declaration = pd.concat(subsection_declaration)
        bided_power = pd.concat(bided_power)

        # 2.2、输出结果汇总为result
        result['profit'] = profit.to_dict('list')
        result['subsection_declaration'] = subsection_declaration.to_dict('list')
        result['bided_power'] = bided_power.to_dict('list')

        logger.info(f'result = {result}')
        logger.info("---------------------------分段报价_收益最大_遗传算法_模型运行结束--------------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta
    time0 = time.time()
    file = r"C:\Users\<USER>\Desktop\input.xlsx"
    generators = pd.read_excel(file, sheet_name='generators', index_col=None)
    prices = pd.read_excel(file, sheet_name='prices', index_col=None)
    costs = pd.read_excel(file, sheet_name='costs', index_col=None)
    constraints = pd.read_excel(file, sheet_name='constraints', index_col=None)
    p = SubsectionDeclarProfitGD(generators=generators, prices=prices, costs=costs, constraints=constraints,
                                   lower_price=0, upper_price=1500, min_profit=0, POP_SIZE=1000, N_GENERATIONS=30,
                                   price_gap=1)
    result = p.predict()

    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1-time0))}")
    # writer = pd.ExcelWriter(r"D:\ToGeek\work\20220127 price_predict_model\optimize\subsection_declaration_using\test.xlsx")
    # pd.DataFrame(result['subsection_declaration']).to_excel(writer, sheet_name='subsection')
    # pd.DataFrame(result['bided_power']).to_excel(writer, sheet_name='bided_power')
    # # pd.DataFrame(result['profit']).to_excel(writer, sheet_name='profit')
    # writer.save()
    # print(pd.DataFrame(result['profit_test']))
    # print('*' * 80)
    print(pd.DataFrame(result['profit']))
    print('*'*80)
    print(pd.DataFrame(result['subsection_declaration']))
    print('*' * 80)
    print(pd.DataFrame(result['bided_power']))

