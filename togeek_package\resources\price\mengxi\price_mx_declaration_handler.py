# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_declaration_pre import Declaration


class DeclarationValueEvalHandlerMX(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        date_list = params.pop('date_list')
        province = params.get('province', '蒙西')
        jiedian_type = params.get('jiedian_type', '统一结算点')
        sj_sign = params.get('sj_sign', 0)
        sj_date = params.get('sj_date', [])
        min_value = params.get('min_value', 0)
        max_value = params.get('sj_date', 1500)
        pred = Declaration(source_data, date_list, province, jiedian_type, sj_sign=sj_sign, sj_date=sj_date)
        self.write(pred.pre_price(min_value, max_value, to_json=True))