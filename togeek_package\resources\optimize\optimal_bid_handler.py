# -*- coding: utf-8 -*-

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_optimal_bid import Biding


class OptimalBidingHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        source_data = params.pop('source_data')
        pred = Biding(source_data)
        self.write(pred.optimal_price(to_json=True))