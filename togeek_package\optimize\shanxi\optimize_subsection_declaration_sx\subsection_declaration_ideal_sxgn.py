#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/10/18 15:39
@Info    ：

'''

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from multiprocessing import Pool
from functools import partial
import logging
import json
import os


plt.rcParams['font.sans-serif'] = ['Simhei']  # 解决中文乱码问题
plt.rcParams['axes.unicode_minus'] = False    # 解决坐标轴刻度负号
logger = logging.getLogger()
cpu_num = os.cpu_count()


class SubsectDeclarIdealProfit:
    def __init__(self, engs, param, price, upward=0.05, func_mode='profit', min_price=0, max_price=1500, size_pop=1000, iter=20):
        logger.info("---------------理想机组分段报价模型开始运行----------------------")
        # print(f'"engs": {engs}, "param": {param}, "price": {price}, "func_mode": {func_mode}, "min_price": {min_price}, "max_price": {max_price}, "size_pop": {size_pop}, "iter": {iter}')
        self.engs, self.param, self.price = self._prepare_data(engs, param, price)
        self.func_mode = func_mode
        self.size_pop = (size_pop // 2) * 2  # 确保种群数量为偶数
        self.iter = iter
        self.upward = upward
        self.prob_mut = 0.001

        self.pmin = min_price
        self.pmax = max_price

    def _prepare_data(self, engs, param, price):
        # 检查有无空值
        msg = ""
        flag = True
        engs = pd.DataFrame(engs)
        param = pd.DataFrame(param)
        if "平均成本" not in param.columns:
            param['平均成本'] = param['标煤单价'] * param['平均供电煤耗'] / 1000
        price = pd.DataFrame(price)
        price['date_time'] = price['日期'].astype('datetime64')
        null_num1 = engs.isnull().sum().sum()
        null_num2 = param.isnull().sum().sum()
        null_num3 = price.isnull().sum().sum()

        if null_num1 != 0:
            msg += f"engs中有{null_num1}个空值， 请检查！"
            flag *= 0
        if null_num2 != 0:
            msg += f"param中有{null_num2}个空值， 请检查！"
            flag *= 0
        if null_num3 != 0:
            msg += f"price中有{null_num3}个空值， 请检查！"
            flag *= 0
        if flag:
            return engs, param, price
        else:
            raise Exception(msg)

    def selection(self, tourn_size=3):
        '''
        Select the best individual among *tournsize* randomly chosen
        Same with `selection_tournament` but much faster using numpy
        individuals,
        :param self:
        :param tourn_size:
        :return:
        '''
        aspirants_idx = np.random.randint(self.size_pop, size=(self.size_pop, tourn_size))
        aspirants_values = self.Y[aspirants_idx]
        winner = aspirants_values.argmax(axis=1)  # winner index in every team
        sel_index = [aspirants_idx[i, j] for i, j in enumerate(winner)]
        self.Chrom = self.Chrom[sel_index, :]
        return self.Chrom

    def crossover(self):
        '''
        3 times faster than `crossover_2point`, but only use for 0/1 type of Chrom
        :param self:
        :return:
        '''
        Chrom, size_pop, len_chrom = self.Chrom, self.size_pop, self.len_chrom
        half_size_pop = int(size_pop / 2)
        Chrom1, Chrom2 = Chrom[:half_size_pop], Chrom[half_size_pop:]
        mask = np.zeros(shape=(half_size_pop, len_chrom), dtype=int)
        for i in range(half_size_pop):
            n1, n2 = np.random.randint(0, self.len_chrom, 2)
            if n1 > n2:
                n1, n2 = n2, n1
            mask[i, n1:n2] = 1
        mask2 = (Chrom1 ^ Chrom2) & mask
        Chrom1 ^= mask2
        Chrom2 ^= mask2
        return self.Chrom

    def mutation(self):
        '''
        mutation of 0/1 type chromosome
        faster than `self.Chrom = (mask + self.Chrom) % 2`
        :param self:
        :return:
        '''
        #
        mask = (np.random.rand(self.size_pop, self.len_chrom) < self.prob_mut)
        self.Chrom ^= mask
        return self.Chrom

    def crtbp(self):
        # create the population
        self.Chrom = np.random.randint(low=0, high=2, size=(self.size_pop, self.len_chrom))
        return self.Chrom

    def gray2rv(self, gray_code):
        # Gray Code to real value: one piece of a whole chromosome
        # input is a 2-dimensional numpy array of 0 and 1.
        # output is a 1-dimensional numpy array which convert every row of input into a real number.
        _, len_gray_code = gray_code.shape
        b = gray_code.cumsum(axis=1) % 2
        mask = np.logspace(start=1, stop=len_gray_code, base=0.5, num=len_gray_code)
        return (b * mask).sum(axis=1) / mask.sum()

    def chrom2x(self, Chrom, seg):
        cumsum_len_segment = self.Lind.cumsum()
        X = np.zeros(shape=(self.size_pop, len(self.Lind)))
        for i, j in enumerate(cumsum_len_segment):
            if i == 0:
                Chrom_temp = Chrom[:, :cumsum_len_segment[0]]
            else:
                Chrom_temp = Chrom[:, cumsum_len_segment[i - 1]:cumsum_len_segment[i]]
            X[:, i] = self.gray2rv(Chrom_temp)

        # 将分段比例转化成递增
        X[:, :seg] = np.cumsum(X[:, :seg], axis=1) / X[:, :seg].sum(axis=1).reshape(-1, 1)
        X[:, seg:] = np.cumsum(X[:, seg:], axis=1) / X[:, seg:].sum(axis=1).reshape(-1, 1)

        # 转化到上下限区间内
        X = self.lb + (self.ub - self.lb) * X
        return X

    # 定义2个函数，a>0 和 a<0 的情况分别定义，减少内部循环
    def cube_pos_a(self, emin, emax, gap, pcost, a, b, c0, d, p):
        """
        当a>0时的情况
        :param emin: 机组最小出力
        :param emax: 机组最大出力
        :param gap: 机组分段报价最小步长
        :param pcost: 机组平均成本曲线
        :param a: 收益曲线的三次项系数
        :param b: 收益曲线的二次项系数
        :param c0: 成本曲线的常数项，收益曲线的部分一次项系数
        :param d: 收益曲线的三次项系数
        :param p: 该时刻的实时价格
        :return: 最优出力，成本，收入，收益
        """
        # income = p*x
        # profit = px - (成本)*x
        c = p * (gap / 60) - c0
        p3 = np.poly1d([a, b, c, d])  # 利润表达式
        # print("利润表达式：\n", p3)

        # 开始循环判断
        delta = b * b - 3 * a * c

        if delta <= 0:
            cur_max = emax
        else:
            xmin = (-b - np.sqrt(delta)) / a / 3
            xmax = (-b + np.sqrt(delta)) / a / 3
            if emax <= xmin:
                cur_max = xmin
            elif emax <= xmax:
                if emin <= xmin:
                    cur_max = xmin
                else:
                    cur_max = emin
            else:
                if emin <= xmin:
                    cur_max = -1  # 取 xmin 和 emax 中利润最大值对应的负荷
                elif emin <= xmax:
                    cur_max = -2  # 取 emin 和 emax 中利润最大值对应的负荷
                else:
                    cur_max = emax

            # 处理最大值在左边界或者右边界，此时cur_max=-1
            if cur_max == -1:
                if p3(xmin) > p3(emax):
                    cur_max = xmin
                else:
                    cur_max = emax

            if cur_max == -2:
                if p3(emin) > p3(emax):
                    cur_max = emin
                else:
                    cur_max = emax

        income = p * cur_max * (gap / 60)
        cost = pcost(cur_max) * cur_max * (gap / 60)
        profit = p3(cur_max)
        return cur_max, cost, income, profit

    def cube_neg_a(self, emin, emax, gap, pcost, a, b, c0, d, p):
        """
        当 a<0 时的情况
        :param emin: 机组最小出力
        :param emax: 机组最大出力
        :param gap: 机组分段报价最小步长
        :param pcost: 机组平均成本曲线
        :param a: 收益曲线的三次项系数
        :param b: 收益曲线的二次项系数
        :param c0: 成本曲线的常数项，收益曲线的部分一次项系数
        :param d: 收益曲线的三次项系数
        :param p: 该时刻的实时价格
        :return: 最优出力，成本，收入，收益
        """
        # income = p*x
        # profit = px - (成本)*x
        c = p * (gap / 60) - c0
        p3 = np.poly1d([a, b, c, d])  # 利润表达式
        # print("利润表达式：\n", p3)

        # 开始循环判断
        delta = b * b - 3 * a * c

        if delta <= 0:
            cur_max = emin
        else:
            xmin = (-b + np.sqrt(delta)) / a / 3
            xmax = (-b - np.sqrt(delta)) / a / 3
            if emax <= xmin:
                cur_max = emin
            elif emax <= xmax:
                if emin <= xmin:
                    cur_max = -2  # 取emin 和 emax 中利润最大值对应的负荷
                else:
                    cur_max = emax
            else:
                if emin <= xmin:
                    cur_max = -3  # 取emin 和 xmax 中利润最大值对应的负荷
                elif emin <= xmax:
                    cur_max = xmax
                else:
                    cur_max = emin

            # 处理最大值在左边界或者右边界
            if cur_max == -2:
                if p3(emin) > p3(emax):
                    cur_max = emin
                else:
                    cur_max = emax

            if cur_max == -3:
                if p3(emin) > p3(xmax):
                    cur_max = emin
                else:
                    cur_max = xmax
        income = p * cur_max * (gap / 60)
        cost = pcost(cur_max) * cur_max * (gap / 60)
        profit = p3(cur_max)
        return cur_max, cost, income, profit

    def cube_zero_a(self, emin, emax, gap, pcost, a, b, c0, d, p):
        """
        当 a=0 时的情况
        :param emin: 机组最小出力
        :param emax: 机组最大出力
        :param gap: 机组分段报价最小步长
        :param pcost: 机组平均成本曲线
        :param a: 收益曲线的三次项系数，即为0
        :param b: 收益曲线的二次项系数
        :param c0: 成本曲线的常数项，收益曲线的部分一次项系数
        :param d: 收益曲线的三次项系数
        :param p: 该时刻的实时价格
        :return: 最优出力，成本，收入，收益
        """
        # income = p*x
        # profit = px - (成本)*x
        c = p * (gap / 60) - c0
        p3 = np.poly1d([b, c, d])  # 利润表达式
        #     print("利润表达式：\n", p3)

        # 开始循环判断
        if b >= 0:
            cur_max = -2  # 取emin 和 emax 中利润最大值对应的负荷
        else:
            extremum = -c / 2 / b
            if emax <= extremum:
                cur_max = emax
            else:
                if emin <= extremum:
                    cur_max = extremum
                else:
                    cur_max = emin

                    # 处理最大值在左边界或者右边界
        if cur_max == -2:
            if p3(emin) > p3(emax):
                cur_max = emin
            else:
                cur_max = emax
        income = p * cur_max * (gap / 60)
        cost = pcost(cur_max) * cur_max * (gap / 60)
        profit = p3(cur_max)
        return cur_max, cost, income, profit

    def cal_cur_max(self, emin, emax, p2, gap, p96):
        """
        循环计算理想机组96点的最优出力、成本、收入和收益
        :param emin: 机组最小出力
        :param emax: 机组最大出力
        :param p2: 平均成本曲线的系数
        :param gap: 机组分段报价最小步长
        :param p96: 96点实时价格
        :return: 96点最优出力、成本、收入、收益数组
        """
        # 利润函数的系数，一次项系数c与价格有关，在循环内部
        a = -p2[0] * (gap / 60)
        b = -p2[1] * (gap / 60)
        c0 = p2[2] * (gap / 60)
        d = 0
        pcost = np.poly1d(p2)  # 成本函数

        if a > 0:
            print(f"a = {round(a, 5)}, 大于0")
            cube = self.cube_pos_a
        elif a < 0:
            print(f"a = {round(a, 5)}, 小于0")
            cube = self.cube_neg_a
        else:
            print(f"a = {a}, 等于0")
            cube = self.cube_zero_a

        # 绘图
        # plt.figure(figsize=(60, 56), dpi=100)
        # i = 1

        # 1 单进程
        # max_x = []   # 负荷
        # cost = []    # 成本
        # max_y = []   # 利润
        # income = []  # 收入
        # for p in p96:
        #     cur_max, cur_cost, cur_income, cur_profit = cube(emin, emax, gap, pcost, a, b, c0, d, p)
        #     max_x.append(cur_max)
        #     cost.append(cur_cost)
        #     income.append(cur_income)
        #     max_y.append(cur_profit)

            # ax = plt.subplot(12, 8, i)
            # ax.set_title(str(p) + "__" + str(i))
            # plt.xlim(-200, 1000)
            # plt.axvline(x=emin, color='g')
            # plt.axvline(x=emax, color='g')
            # plt.axhline(0, linestyle='--', color='g')
            # plt.plot(range(-200, 1000, 1), p3(range(-200, 1000, 1)), color='r')
            # plt.savefig(eng + "_slx.png")
            # i = i + 1

        # 2 多进程
        pool = Pool(processes=cpu_num)
        func = partial(cube, emin, emax, gap, pcost, a, b, c0, d)
        result = pool.map(func, p96)
        pool.close()
        result = np.asarray(result)
        max_x, cost, income, max_y = result[:, 0], result[:, 1], result[:, 2], result[:, 3]
        # 本地导出测试
        # cur_price = price[price['机组名称'] == eng]
        # cur_price["xmax"] = max_x
        # cur_price["ymax"] = max_y
        # cur_price["income"] = income
        # cur_price["cost"] = cost
        # cur_price.to_excel(eng + "_slx.xlsx", index=False)
        return np.array(max_x), np.array(cost), np.array(income), np.array(max_y)

    def cal_xp(self, emin, emax, seg_emin, seg, min_step, gap, p96, pcost, xp):
        """
        根据染色体计算分段出力和报价，然后根据最小步长修正分段数量，计算该分段报价对应的负荷、收入、成本及收益
        :param xp: 分段出力和报价的染色体
        :return: 成本、收益和发电量
        """
        bid_power = []   # 负荷
        cost = []        # 成本
        income = []      # 收入
        profit = []      # 收益

        x = xp[: seg]                   # 第一段起始出力为最小出力值
        p = np.insert(xp[seg:], 0, 0)   # 第一段报价为0值
        p = p[:-1]

        # 考虑最小步长，规整到最小步长的间隔，间隔太小就减少分段，所以分段数可能小于seg
        del_x = []
        # last_xi = seg_emin - min_step
        # for i in range(len(x)):
        #     if x[i] <= last_xi:
        last_xi = seg_emin
        for i in range(len(x)):
            if x[i] < last_xi:
                del_x.append(i)
                continue
            if (x[i] - last_xi) % min_step >= min_step / 2:  # 余数超过最小步长的一半时，向上修正
                x[i] = int(x[i] + min_step - (x[i] - last_xi) % min_step)
                last_xi = x[i]
            else:
                if x[i] - last_xi >= min_step:
                    x[i] = int(x[i] - (x[i] - last_xi) % min_step)
                    last_xi = x[i]
                else:
                    x[i] = int(x[i] - (x[i] - last_xi) % min_step)
                    del_x.append(i)

        x = np.delete(x, del_x)
        p = np.delete(p, del_x)
        x = [round(_) for _ in x]      # 出力保留整数
        p = [round(_, 2) for _ in p]   # 价格保留2位小数

        # 将出力的最后一段规整为最大出力
        if x[-1] < emax:
            x[-1] = emax

        # 模拟中标出力，考虑爬坡速率
        last_point_power = emin  # 前一天的最后一个点的出力
        xa = 0
        for n, p_ in enumerate(p96):
            # 1 出清价格大于报价，才能中标，最小到p[0]=0，一定中标，中了最小出力
            i = len(x) - 1
            while i >= 0:
                if p_ >= p[i]:
                    xa = x[i]
                    break
                else:
                    i = i - 1

            # 2 爬坡速率修正，且需要修正为整数
            if xa > last_point_power + self.upward * emax * gap:
                last_point_power = int(last_point_power + self.upward * emax * gap)
                xa = last_point_power
            elif xa < last_point_power - self.upward * emax * gap:
                last_point_power = np.ceil(last_point_power - self.upward * emax * gap)
                xa = last_point_power
            else:
                last_point_power = xa

            point_power = xa                          # 中标出力
            point_cost = pcost(xa) * xa * (gap / 60)  # 成本
            point_income = p_ * xa * (gap / 60)       # 收入
            point_profit = point_income - point_cost  # 收益

            bid_power.append(point_power)
            cost.append(point_cost)
            income.append(point_income)
            profit.append(point_profit)
        return np.array(bid_power), np.array(cost), np.array(income), np.array(profit), np.array(x), np.array(p)
        # return bid_power, cost, income, profit, x, p

    def cal_X(self, emin, emax, seg_emin, seg, min_step, gap, p96, pcost, ideal_power, X):
        """
        循环所有的染色体，计算中标出力、成本、收入和收益，根据目标函数的类型，返回不同的值
        :return: list, 成本/收益、发电量
        """
        # 单进程方法
        # powers = []    # 中标出力
        # costs = []     # 成本
        # incomes = []   # 收入
        # profits = []   # 收益
        #
        # for xp in X:
        #     power, cost, income, profit, x, p = self.cal_xp(emin, emax, seg_emin, seg, min_step, gap, p96, pcost, xp)
        #     powers.append(power)
        #     costs.append(cost)
        #     incomes.append(income)
        #     profits.append(profit)
        #
        # if self.func_mode == 'power':       # 电力偏差最小
        #     return -np.sum(abs(np.array(powers) - ideal_power), axis=1)
        # elif self.func_mode == 'profit':    # 收益最大
        #     return np.sum(np.array(profits), axis=1)

        # 使用多进程
        pool = Pool(processes=cpu_num)
        func = partial(self.cal_xp, emin, emax, seg_emin, seg, min_step, gap, p96, pcost)
        result = pool.map(func, X)
        pool.close()
        result = np.array(result)
        # print(result.shape)
        powers, costs, incomes, profits, x, p = result[:, 0], result[:, 1], result[:, 2], result[:, 3], result[:, 4], result[:, 5]

        if self.func_mode == 'power':       # 电力偏差最小
            return -np.sum(abs(np.array(powers.tolist()) - ideal_power), axis=1)
        elif self.func_mode == 'profit':    # 收益最大
            return np.sum(np.array(profits.tolist()), axis=1)

    def cal_best_seg(self, emin, seg_emin, emax, seg, min_step, gap, p96, pcost, ideal_power):
        """迭代计算最优分段报价方案，返回最优的染色体"""
        self.lb = np.array([seg_emin] * seg + [self.pmin] * seg)
        self.ub = np.array([emax] * seg + [self.pmax] * seg)
        self.Lind = np.ceil(np.log2((self.ub - self.lb) + 1)).astype(int)
        self.len_chrom = sum(self.Lind)

        # 每台机组开始前重新初始化参数
        self.Chrom = None
        self.X = None  # shape = (size_pop, n_dim)
        self.Y = None  # shape = (size_pop,) , value is f(x)

        self.generation_best_X = []  # 每一代最优的x
        self.generation_best_Y = []  # 每一代最优的y
        self.all_history_Y = []  # 所有的y
        self.best_x, self.best_y = None, None

        self.crtbp()  # 创建初始种群
        for i in range(self.iter):
            # logger.info(f"------第{i+1}次迭代")
            print(f"------第{i+1}次迭代")
            self.X = self.chrom2x(self.Chrom, seg)
            self.Y = self.cal_X(emin, emax, seg_emin, seg, min_step, gap, p96, pcost, ideal_power, self.X)

            self.selection()
            self.crossover()
            self.mutation()

            generation_best_index = self.Y.argmax()
            self.generation_best_X.append(self.X[generation_best_index, :])
            self.generation_best_Y.append(self.Y[generation_best_index])
            self.all_history_Y.append(self.Y)
        # print(self.generation_best_X)
        # print(self.generation_best_Y)
        global_best_index = np.array(self.generation_best_Y).argmax()
        self.best_x = self.generation_best_X[global_best_index]
        self.best_y = self.generation_best_Y[global_best_index]
        return self.best_x

    def run(self):
        decl = pd.DataFrame()
        res = pd.DataFrame()
        engs = self.engs
        param = self.param
        price = self.price
        for eng in engs['机组名称']:
            print("机组名称:", eng)
            emin = list(engs[engs['机组名称'] == eng]['最小出力'])[0]
            emax = list(engs[engs['机组名称'] == eng]['最大出力'])[0]
            seg_emin = list(engs[engs['机组名称'] == eng]['起始出力'])[0]
            seg = list(engs[engs['机组名称'] == eng]['段数'])[0]
            min_step = list(engs[engs['机组名称'] == eng]['最小步长'])[0]
            # print("机组出力区间：", [emin, emax])
            # 平均成本曲线
            p2 = np.polyfit(param[param['机组名称'] == eng]['负荷'], param[param['机组名称'] == eng]['平均成本'], 2)
            pcost = np.poly1d(p2)  # 平均成本曲线
            # logger.info("成本函数表达式：\n", pcost)
            # print("成本函数表达式：\n", pcost)
            # 实时出清电价
            p96 = list(price[price['机组名称'] == eng]['实时出清电价'])
            gap = int(24 * 60 / len(p96))

            # 1 计算理想中标出力、成本、收入、收益d
            ideal_power, ideal_cost, ideal_income, ideal_profit = self.cal_cur_max(emin, emax, p2, gap, p96)

            # 2 计算最佳分段报价方案
            best_xp = self.cal_best_seg(emin, seg_emin, emax, seg, min_step, gap, p96, pcost, ideal_power)
            best_power, best_cost, best_income, best_profit, best_x, best_p = self.cal_xp(emin, emax, seg_emin, seg, min_step,
                                                                                          gap, p96, pcost, best_xp)

            cur_decl = pd.DataFrame({"power_start": list(np.insert(best_x, 0, seg_emin)[:-1]), "power_end": list(best_x),
                                     "price": list(best_p)})
            cur_decl['机组名称'] = eng
            cur_res = pd.DataFrame({"best_power": best_power, "best_cost": best_cost, "best_income": best_income,
                                    "best_profit": best_profit, "ideal_power": ideal_power, "ideal_cost": ideal_cost,
                                    "ideal_income": ideal_income, "ideal_profit": ideal_profit})
            cur_res['机组名称'] = eng

            if decl.empty:
                decl = cur_decl
            else:
                decl = pd.concat([decl, cur_decl], axis=0)
            if res.empty:
                res = cur_res
            else:
                res = pd.concat([res, cur_res], axis=0)

            logger.info(f"分段报价方案：power_start: {list(np.insert(best_x, 0, seg_emin)[:-1])}, power_end: {list(best_x)}, price: {list(best_p)}")
        decl.reset_index(drop=True, inplace=True)
        res.reset_index(drop=True, inplace=True)
        result = {"declaration": decl.to_dict(), "profit": res.to_dict()}
        # logger.info(f"每次迭代最优的结果：{self.generation_best_Y}")
        # logger.info(f'result = {result}')
        logger.info("---------------理想机组分段报价模型运行结束----------------------")
        return result


if __name__ == '__main__':
    engs = pd.read_excel(r'D:\Togeek\code\optimize_ideal\input1.xlsx', sheet_name='engs', index_col=None)
    param = pd.read_excel(r'D:\Togeek\code\optimize_ideal\input1.xlsx', sheet_name='parameter', index_col=None)
    price = pd.read_excel(r'D:\Togeek\code\optimize_ideal\input1.xlsx', sheet_name='price', index_col=None)
    price['日期'] = price['日期'].astype(str)
    m = SubsectDeclarIdealProfit(engs.to_dict(), param.to_dict(), price.to_dict())
    res = m.run()
    # print(res)


