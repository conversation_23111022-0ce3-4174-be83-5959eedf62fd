#!/usr/bin/env python
# coding: utf-8

import datetime
import numpy as np
import pandas as pd
import jenkspy
import logging
import warnings

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class DeclarationFit5d:
    def __init__(self, source_data, pred_list, sj_sign, sj_date, min_price=0, max_price=1500):
        logger.info("-------------三段拟合法预测出清电价开始_5天-------------------")
        self.pred_list = pred_list  # 待预测日期列表
        self.source_data, self.flag, self.msg = self.pre_source(source_data)
        self.sj_sign = sj_sign
        self.sj_date = sj_date
        self.min_price = min_price      # 价格下限，默认为0
        self.max_price = max_price      # 价格上限，默认为1500

    def pre_source(self, source_data):
        """
        数据字段校验 + 数据整理
        :param source_data: json格式, 市场边界条件
        :return:
        """
        msg = ""
        flag = True

        # 1. json --> pd.DataFrame
        data = pd.DataFrame.from_dict(source_data, orient='index').T
        data['date'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[0])
        data['time'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[-1])
        data['week'] = pd.to_datetime(data['date']).dt.weekday.map(lambda s: int(s) + 1)
        date_lst = data['date'].tolist()

        # 1. 预测日期列表校验
        # 判断传入的数据中包含要预测日期的数据
        if set(self.pred_list) < set(date_lst):
            msg += "--’source_data‘中包括预测日的数据，检查通过--\n"
        elif set(self.pred_list) == set(date_lst):
            msg += "--’source_data‘中缺少历史日期的数据，请检查！\n"
            flag *= False
        else:
            msg += "--’source_data‘中预测日的数据不完整，请检查！\n"
            flag *= False

        # 2. 新能源数据校验
        num_null = data['日前新能源负荷预测'].isnull().sum()
        if num_null == 0:
            msg += "----'日前新能源负荷预测'数据不存在缺失值，检查通过；"
        else:
            msg += "----'日前新能源负荷预测'数据存在缺失值，请检查！\n"
            flag *= False

        # 3. 统调负荷、日前联络线计划校验
        num1 = data['负荷预测'].isnull().sum()
        num2 = data['日前联络线计划'].isnull().sum()

        if num1 > 0:
            if num1 % 96 == 0:
                n = num1 // 96
                lst = data['负荷预测'].dropna().tolist()
                data['负荷预测'] = lst + lst[-96:] * n  # 用最后一天的数据填充缺失值
                msg += f"'负荷预测'缺少{n}天的数据，已经使用最近一天的数据补充完整；"
            else:
                msg += "----'负荷预测'的历史数据有缺失值，请检查！\n"
                flag *= False

        if num2 > 0:
            if num2 % 96 == 0:
                n = num2 // 96
                lst = data['日前联络线计划'].dropna().tolist()
                data['日前联络线计划'] = lst + lst[-96:] * n  # 用最后一天的数据填充缺失值
                msg += f"'日前联络线计划'缺少{n}天的数据，已经使用最近一天的数据补充完整；"
            else:
                msg += "----'日前联络线计划'的历史数据有缺失值，请检查！\n"
                flag *= False

        if flag:
            # 2. 计算竞价空间
            data['竞价空间'] = data['负荷预测'] - data['日前新能源负荷预测'] - data['日前联络线计划']
            return data, flag, msg
        else:
            return data, flag, msg

    def acc(self, pred, real):
        pred_lst = pred.tolist()
        real_lst = real.tolist()
        x = np.abs(np.array(pred_lst) - np.array(real_lst)).sum() / real.sum()
        return 1 - x

    @staticmethod
    def get_history_date(history_data):
        """
        从传入历史数据 history_data 中选取价格不全为0的最大历史日期 history_date
        """
        history_date_list = history_data['date'].unique().tolist()  # 历史日期列表
        history_date_list = sorted(history_date_list, reverse=True)  # 历史日期倒序排列

        history_date = None     # 初始化
        for date in history_date_list:
            if history_data[history_data['date'] == date]['日前价格'].sum() == 0:
                # print(f"{date}价格数据全为0，继续寻找前一个历史日期！")
                continue
            elif history_data[history_data['date'] == date]['日前价格'].sum() == 1500 * 96:
                # print(f"{date}价格数据全为1500，继续寻找前一个历史日期！")
                continue
            elif history_data[history_data['date'] == date].shape[0] <= 3:
                # print(f"{date}价格数据去除边界点后剩余条数小于3，继续寻找前一个历史日期！")
                continue
            else:
                history_date = date
                break
        return history_date

    def pred_price(self, to_json=False):
        """
        从sj_date计算
        Parameters
        ----------
        to_json

        Returns
        -------

        """
        y1 = 0
        result = pd.DataFrame(columns=["date_time", "pred_price", "type"])

        for dt in self.pred_list:
            result_tmp = pd.DataFrame(columns=["date_time", "pred_price", "type"])
            pre_data = []

            # 选择用于拟合的历史日期: history_date
            if self.sj_sign == 1:
                # 如果星期在date列表里，表示省间现货，筛选历史表，取最大的一条
                # 如果历史数据没有符合条件的选项，选前一天
                week = int(datetime.datetime.strptime(dt, "%Y-%m-%d").weekday()) + 1
                try:
                    if week in self.sj_date:
                        history_data = self.source_data[(self.source_data['week'].isin(self.sj_date)) & ([self.source_data['日期'] < dt])]
                    else:
                        # 如果不在列表里，表示运行日非省间现货，删选历史表
                        history_data = self.source_data[(~(self.source_data['week'].isin(self.sj_date))) & ([self.source_data['日期'] < dt])]

                except:
                    history_data = self.source_data[self.source_data['日期'] < dt].dropna()
            else:
                history_data = self.source_data[self.source_data['date'] < dt].dropna()  # 历史数据

            # 历史数据去除边界点
            history_data = history_data[(history_data['日前价格'] > 0) & (history_data['日前价格'] < 1500)]  # 筛选价格
            history_date = self.get_history_date(history_data)
            logger.info(f"date_pred: {dt}, date_history: {history_date}")

            # 筛选历史日期数据curdata和待预测日期数据curdatab
            curdata = self.source_data[self.source_data['date'] == history_date]
            curdata = curdata[(curdata['日前价格'] > 0) & (curdata['日前价格'] < 1500)]  # 筛选价格
            curdatab = self.source_data[self.source_data['date'] == dt]

            # 自然分段法，分三段绘制散点图
            # 价格乘竞价空间
            curdata['jenkspy'] = curdata['日前价格'] * curdata['竞价空间']
            curdata.reset_index(inplace=True, drop=True)
            breaks = jenkspy.jenks_breaks(curdata['jenkspy'].to_list(), 3)
            b2 = curdata[(curdata['jenkspy'] > breaks[1]) & (curdata['jenkspy'] <= breaks[2])]
            b3 = curdata[curdata['jenkspy'] > breaks[2]]
            b2jingjiamean = b2['竞价空间'].mean()
            b2rqrpmean = b2['日前价格'].mean()
            k32_2m3all = ((b3['日前价格'] - b2rqrpmean) / (b3['竞价空间'] - b2jingjiamean)).mean()
            # 第三段的直线
            p13 = np.poly1d([k32_2m3all, np.mean(b3['日前价格']) - k32_2m3all * np.mean(b3['竞价空间'])])

            # 多项式拟合
            x_list = curdata['竞价空间'].to_list()      # 横坐标列表：竞价空间
            y_list = curdata['日前价格'].to_list()      # 纵坐标列表：价格数据

            parameter1 = np.polyfit(x_list, y_list, 1)
            p1 = np.poly1d(parameter1)
            parameter3 = np.polyfit(x_list, y_list, 3)
            p3 = np.poly1d(parameter3)

            todayuse = 1
            x1 = 0
            if parameter3[0] > 0:
                delta = parameter3[1] * parameter3[1] - 3 * parameter3[0] * parameter3[2]
                if delta < 0:
                    todayuse = 3
                else:
                    x1 = -0.33333333 * (parameter3[1] + np.sqrt(delta)) / parameter3[0]
                    x2 = -0.33333333 * (parameter3[1] - np.sqrt(delta)) / parameter3[0]
                    y1 = p3(x1)
                    y2 = p3(x2)
                    if x1 > 0 and y1 < y2 * 1.2:
                        todayuse = 33
            else:
                if k32_2m3all > parameter1[0] and k32_2m3all > 0:
                    todayuse = 13
                else:
                    todayuse = 1

            if todayuse == 1:
                pre_data = p1(curdatab['竞价空间'])  # 1次直线直接预测
            elif todayuse == 13:
                pre_data = np.maximum(np.maximum(p1(curdatab['竞价空间']), self.min_price), p13(curdatab['竞价空间']))  # 两根直线中的大值
            elif todayuse == 3:
                pre_data = np.maximum(p3(curdatab['竞价空间']), self.min_price)  # 3次曲线
            elif todayuse == 33:
                pre_data = np.maximum(p3(curdatab['竞价空间']), y1)  # 3次曲线与直线中的大值
                pre_data = np.where((curdatab['竞价空间'].values < x1), p3(curdatab['竞价空间']), pre_data)  # 小于第一个极值点x1的部分使用3次曲线

            # 修正价格上下限
            pre_data = np.maximum(pre_data, self.min_price)  # 价格下限修正
            pre_data = np.minimum(pre_data, self.max_price)  # 价格上限修正

            result_tmp['date_time'] = curdatab['date'] + ' ' + curdatab['time']
            result_tmp['pred_price'] = list(pre_data)
            result_tmp['type'] = [todayuse] * curdatab.shape[0]
            result = pd.concat([result, result_tmp], axis=0)
        if to_json:
            result = result.to_dict('list')
        logger.info(f'result = {result}')
        logger.info("-------------三段拟合法预测出清电价结束-------------------")
        return result


if __name__ == '__main__':
    file = r"D:\Togeek\work\model_data\price\三段拟合法预测日前出清电价\test.xlsx"
    data = pd.read_excel(file)
    date_list_pred = ['2021-07-30', '2021-07-31', '2021-08-01', '2021-08-02', '2021-08-03']
    p = DeclarationFit5d(data.to_dict('list'), date_list_pred, sj_sign=0, sj_date=[])
    pre = p.pred_price(False)
    print(pre)
