# -*- coding: utf-8 -*
"""
Author: san
Date: 2021-12-27 15:01:54
LastEditTime: 2022-01-01 09:40:06
Description:
输入：
    1、机组ID、额定容量、机组最小出力、最大出力、机组爬坡速率、机组下坡速率、分段数量；
    2、机组ID、日前节点电价与实时节点电价；
输出：
    1、总额收益：income
    2、最优分段报价：subsection_declaration
    3、分时点中标出力：bided_power
"""
import numpy as np
import pandas as pd
import operator
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()


class SubsectionDeclaration:
    def __init__(self, generators, prices, default_subsection=6, POP_SIZE=2500, N_GENERATIONS=4):
        # param generators: 机组参数
        # param prices: 机组节点电价
        self.generators = pd.DataFrame(generators)
        self.prices = pd.DataFrame(prices)
        self.default_subsection = default_subsection  # 默认申报分段数量
        self.POP_SIZE = POP_SIZE  # 种群数量
        self.N_GENERATIONS = N_GENERATIONS  # 迭代的次数

        # 默认值
        self.freqs = [24, 48, 96]  # 每日的时间点的数量只能是这些数字
        self.gap = 15  # 每个时间点的间隔分钟, 这里只做初始化，预测过程中根据传入数据修正
        self.subsection = 6  # 分段数量，这里仅做初始化
        self.gene_size = 8  # 基因大小，代表每个数字的精度
        self.DNA_SIZE = self.gene_size * (2 * (self.subsection - 1))  # 基因组大小
        self.CROSSOVER_RATE = 0.6  # 交叉可能性
        self.MUTATION_RATE = 0.1  # 变异可能性
        self.X_BOUND = [0, 1]  # 百分比
        self.P_BOUND = [0, 1500]  # 报价范围
        self.upward = 3  # 初始化爬坡速率：MW每分钟
        self.downward = self.upward

        # 写入log
        logger.info("------------------分段报价优化_遗传算法------------------------")
        msg = self._prepare_load()  # 数据预处理 + 数据校验
        # print(msg)
        logger.info(msg)

    def _prepare_load(self):
        # 修正 generators 数据类型
        self.generators['generator'] = self.generators['generator'].astype('str')
        self.generators['subsection'] = self.generators['subsection'].astype('int')
        for col in ['rated_capacity', 'min_capacity', 'max_capacity', 'upward', 'downward']:
            self.generators[col] = self.generators[col].astype('float')
        # 修正 prices 数据类型并排序
        self.prices['generator'] = self.prices['generator'].astype('str')
        self.prices['time'] = self.prices['time'].astype('str')
        self.prices['ahead_price'] = self.prices['ahead_price'].astype('float')
        self.prices['real_price'] = self.prices['real_price'].astype('float')
        self.prices = self.prices.sort_values(['generator', 'time']).reset_index(drop=True)  # 对价格数据进行排序

        msg = ""
        # 1、检测机组信息表中前5个字段(generator、rated_capacity、min_capacity、max_capacity、upward)是否有值；
        hasnull = self.generators.isnull().sum()  # 计算这些列里的空值数量
        num_null = hasnull['generator'] + hasnull['rated_capacity'] + hasnull['min_capacity'] + hasnull[
            'max_capacity'] + hasnull['upward']
        if num_null > 0:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中有" + str(
                num_null) + "个空值，请检查传入数据。"
            raise Exception()
        else:
            msg = msg + "1, generator、rated_capacity、min_capacity、max_capacity、upward中没有空值；"
        # 下坡速率要是空值则用上坡速率代替，分段数量如果是空值则用默认值代替；
        self.generators['downward'].fillna(self.generators['upward'], inplace=True)
        self.generators['subsection'].fillna(self.default_subsection, inplace=True)
        self.generators['subsection'] = self.generators['subsection'] - 1

        # 2、循环generator，检查价格；      
        for g in self.generators['generator']:
            try:
                # 1、价格必须有值，行数只能是self.freq中的一种
                price = self.prices[self.prices['generator'] == g].copy()
                freq = price.shape[0]
                if freq in self.freqs:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 在" + str(self.freqs) + "之中; "
                else:
                    msg = msg + "2.1, 机组" + str(g) + " 的节点电价行数为" + str(freq) + ", 不在" + str(self.freqs) + "之中; "
                    raise Exception()
                # 2、检测时间是不是从0点00到23点xx，否则不合格；
                #   gap为间隔分钟数，96为15,48为30,24为60
                gap = 1440 / freq
                price['时间'] = price['time'].map(lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap)))
                curtimes0 = list(range(freq))  # [0, 95]
                curtimes1 = list(range(1, freq + 1))  # [1, 96]
                if operator.eq(list(price['时间']), curtimes0):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过。"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 96):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:15 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过。"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 48):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 00:30 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过。"
                elif (operator.eq(list(price['时间']), curtimes1)) & (freq == 24):
                    msg = msg + "2.2, 要求 " + str(freq) + " 个点的时间从 01:00 开始，每隔 " + str(gap) + " 分钟一个点，确认正确，机组" + str(g) + " 数据验证通过。"
                else:
                    msg = msg + "2.2, 价格数据不完整，这组数据验证不通过，请检查传入的价格数据。"
                    raise Exception()
            except:
                msg = msg + "价格数据有异常，请检查传入的价格数据。"
            # finally:
            #     msg = msg + "数据验证结束！"
        return msg

    def _splitxy(self, xp_line):  # 分拆x(出力比例)和p(报价)，并返回累加x(出力)和p(报价)
        # print(xp_line)
        x = xp_line[0:self.subsection - 1]
        x = np.append(x, [1])  # 添加出力的最大比例
        p = xp_line[self.subsection - 1:]
        p = np.append(p, [1500])  # 添加最高报价
        for sub in range(0, self.subsection):
            if sub > 0:
                x[sub] = x[sub - 1] + (self.X_BOUND[1] - x[sub - 1]) * (
                            x[sub] / (self.X_BOUND[1] - self.X_BOUND[0]))  # 计算累计x
                p[sub] = p[sub - 1] + (self.P_BOUND[1] - p[sub - 1]) * (
                            p[sub] / (self.P_BOUND[1] - self.P_BOUND[0]))  # 计算累计p
        x = np.append([0], x) * (self.XR_BOUND[1] - self.XR_BOUND[0]) + self.XR_BOUND[0]  # 添加出力最小比例0，并将出力比例转换为出力
        p = np.append([0], p)  # 添加最低报价0
        # print(x,p)
        # TODO: 修改出力为整数，并且间隔 >= 1, 合并出力相同相同的分段
        x = np.floor(x).astype(np.int)     # 取整
        del_index = []  # 收集需要删除的段的索引
        for i in range(1, len(x)):
            if x[i] - x[i-1] < 1:
                del_index.append(i-1)
        x_fixed = np.delete(x, del_index)
        p_fixed = np.delete(p, del_index)
        # return x, p
        return x_fixed, p_fixed

    # 评价函数： 依据分段出力x和对应的分段报价p，计算日前和实时市场各时点的中标出力和总收益
    def _income(self, x, p):  # x表示分段出力，p表示当前分段的报价
        income = 0
        # 本应该是前一天的最后一个点的出力，但是由于每天0点的用电量并不大，所以就暂时按照机组最小出力替代
        point_xa_power = self.XR_BOUND[0]
        point_xr_power = self.XR_BOUND[0]
        xas = []
        xrs = []
        for arp in self.price.itertuples():
            # print("arp=",arp)
            xa = 0  # 初始化：日前市场，当前时刻出力
            xr = 0  # 初始化：实时市场，当前时刻出力
            i = len(p) - 1  # - 1
            while i >= 0:
                if arp.ahead_price >= p[i]:
                    xa = x[i]
                    break
                else:
                    i = i - 1
            i = len(p) - 1  # - 1
            while i >= 0:
                if arp.real_price >= p[i]:
                    xr = x[i]
                    break
                else:
                    i = i - 1
            #   日前市场爬坡及下坡速率修正
            if xa > point_xa_power + self.upward * self.gap:
                point_xa_power = point_xa_power + self.upward * self.gap  # 爬坡速率修正
                xa = point_xa_power
            elif xa < point_xa_power - self.upward * self.gap:
                point_xa_power = point_xa_power - self.upward * self.gap  # 下坡速率修正
                xa = point_xa_power
            else:
                point_xa_power = xa
            #   实时市场爬坡及下坡速率修正
            if xr > point_xr_power + self.upward * self.gap:
                point_xr_power = point_xr_power + self.upward * self.gap  # 爬坡速率修正
                xr = point_xr_power
            elif xr < point_xr_power - self.upward * self.gap:
                point_xr_power = point_xr_power - self.upward * self.gap  # 下坡速率修正
                xr = point_xr_power
            else:
                point_xr_power = xr

            xas.append(xa)
            xrs.append(xr)
            point_income = arp.ahead_price * xa + arp.real_price * (xr - xa)
            # 单位转换：间隔15分钟，相当于除以4；间隔30分钟，相当于除以2；间隔1小时，相当于除以1
            point_income = point_income * (self.gap / 60)
            income = income + point_income
        return income, xas, xrs

    # 基因组表达
    def _translateDNA(self, pop):  # pop表示种群矩阵，一行代表一个二进制编码表示的DNA，矩阵的行数为种群数目
        xp = np.ones([self.POP_SIZE, 1])
        for s in range(0, 2 * (self.subsection - 1)):
            if s < self.subsection - 1:
                b = pop[..., self.gene_size * s:self.gene_size * (s + 1)].dot(2 ** np.arange(self.gene_size)[::-1])
                c = float(2 ** self.gene_size - 1) * (self.X_BOUND[1] - self.X_BOUND[0])  # 精度为gene_size的二进制最大值
                d = (b / c) * (self.X_BOUND[1] - self.X_BOUND[0]) + self.X_BOUND[0]  # 将x转换到X_BOUND范围内
                e = d.reshape(self.POP_SIZE, 1)
                xp = np.concatenate((xp, e), axis=1)
            else:
                b = pop[..., self.gene_size * s:self.gene_size * (s + 1)].dot(2 ** np.arange(self.gene_size)[::-1])
                c = float(2 ** self.gene_size - 1)
                d = (b / c) * (self.P_BOUND[1] - self.P_BOUND[0]) + self.P_BOUND[0]  # 将p转换到P_BOUND范围内
                e = d.reshape(self.POP_SIZE, 1)
                xp = np.concatenate((xp, e), axis=1)
        return xp[..., 1:]

    # 单点变异
    def _mutation(self, child):
        if np.random.rand() < self.MUTATION_RATE:  # 以MUTATION_RATE的概率进行变异
            mutate_point = np.random.randint(0, self.DNA_SIZE)  # 随机产生一个实数，代表要变异基因的位置
            child[mutate_point] = child[mutate_point] ^ 1  # 将变异点的二进制为反转：按位异或，相同为0，相异为1

    # 杂交
    def _crossover_and_mutation(self, pop):
        new_pop = []
        for father in pop:  # 遍历种群中的每一个个体，将该个体作为父亲
            child = father  # 孩子先得到父亲的全部基因
            if np.random.rand() < self.CROSSOVER_RATE:  # 产生子代时不是必然发生交叉，而是以一定的概率发生交叉
                mother = pop[np.random.randint(self.POP_SIZE)]  # 再种群中选择另一个个体，并将该个体作为母亲
                cross_points = np.random.randint(low=0, high=self.DNA_SIZE)  # 随机产生交叉的点
                child[cross_points:] = mother[cross_points:]  # 孩子得到位于交叉点后的母亲的基因
            self._mutation(child)  # 每个后代有一定的机率发生变异
            new_pop.append(child)
        return new_pop

    def _select(self, pop, fitness_income):  # nature selection wrt pop's fitness
        fit = np.log10(np.array(fitness_income) + 10)  # 取对数，降低income的量级
        # 从一维数组(a)中随机抽取数字，组成指定大小(size)的数组，replace=True表示可以取相同数字，数组p与数组a对应，表示数组a中每个元素被抽到的概率
        # 在这里，income越大, 所在样本被抽到的概率越大
        idx = np.random.choice(a=np.arange(self.POP_SIZE), size=self.POP_SIZE, replace=True, p=fit / (fit.sum()))
        return pop[idx]

    def _get_fitness(self, pop):
        xp = self._translateDNA(pop)
        pred = []
        for xp_line in xp:
            x, p = self._splitxy(xp_line)
            # break
            pred.append(self._income(x, p))
        return pred

    # 输出结果处理：对于每个机组，通过迭代得到的pop, 分别输出对应的收益， 分段报价方案和分时刻中标出力
    def deal_result(self, g, pop):  # g为机组编号， pop为N代之后的种群矩阵
        # 结果生成
        fitness = self._get_fitness(pop)
        pred = np.array(fitness)
        incomes = pred[..., 0].tolist()
        max_income_index = np.argmax(incomes)
        INCOME = incomes[max_income_index]  # 最大收益
        xp_all = self._translateDNA(pop)
        XP = self._splitxy(xp_all[max_income_index])  # 分段报价方案
        fitness_xasxrs = pred[..., 1:3]
        XAS_XRS = fitness_xasxrs[max_income_index]  # 分时刻中标出力

        # 返回最大收益
        tmp_income = pd.DataFrame(columns=['generator', 'income'])
        tmp_income['generator'] = [g]
        tmp_income['income'] = [INCOME]

        # 返回分段报价方案
        tmp_subsection = pd.DataFrame(columns=['generator', 'subsection', 'power_start', 'power_end', 'power', 'price'])
        tmp_subsection['power'] = XP[0]
        tmp_subsection['power_end'] = XP[0]
        tmp_subsection['price'] = XP[1]
        tmp_subsection['generator'] = g
        tmp_subsection['subsection'] = range(1, len(tmp_subsection) + 1)
        tmp_subsection['power_start'] = tmp_subsection['power_end'].shift(1)
        tmp_subsection['power_start'] = tmp_subsection['power_start'].fillna(0).astype(np.int)

        # 返回分时刻中标出力
        tmp_bided_power = pd.DataFrame(columns=['generator', 'time', 'ahead_power', 'real_power'])
        tmp_bided_power['time'] = self.price['time'].copy()
        tmp_bided_power['generator'] = g
        tmp_bided_power['ahead_power'] = XAS_XRS[0]
        tmp_bided_power['real_power'] = XAS_XRS[1]
        return tmp_income, tmp_subsection, tmp_bided_power

    def predict(self):
        result = {}     # 模型输出结果汇总
        income = []  # 最大收益
        subsection_declaration = []    # 分段报价结果
        bided_power = []  # 分时刻中标出力

        # 1、循环generator；      
        for g in self.generators['generator']:
            # 1.1、获取当前机组边界信息及节点电价
            self.price = self.prices[self.prices['generator'] == g].copy()
            freq = self.price.shape[0]
            gap = 1440 / freq
            self.gap = gap
            self.price['时间'] = self.price['time'].map(
                lambda s: int(int(str(s)[0:2]) * (60 / gap) + (int(str(s)[3:5]) / gap) + 1))
            # 1.2、获取基本参数
            self.subsection = int(self.generators[self.generators['generator'] == g]['subsection'])
            self.DNA_SIZE = int(self.gene_size * (2 * (self.subsection - 1)))
            self.XR_BOUND = [int(self.generators[self.generators['generator'] == g]['min_capacity']),
                             int(self.generators[self.generators['generator'] == g]['max_capacity'])]
            self.upward = int(self.generators[self.generators['generator'] == g]['upward'])
            self.downward = int(self.generators[self.generators['generator'] == g]['downward'])
            pop = np.random.randint(2, size=(self.POP_SIZE, self.DNA_SIZE))  # 二维 01 矩阵
            tmp_fitness_income = []
            # 1.3，每个机组迭代N代
            for n in range(self.N_GENERATIONS):  # 迭代N代
                pop = np.array(self._crossover_and_mutation(pop))  # 交叉和变异，不改变种群数量和DNA_SIZE
                fitness = self._get_fitness(pop)  # 经过基因组表达和拟合，返回预测结果
                fitness_income = np.array(fitness)[..., 0].tolist()
                max_fitness_index = np.argmax(fitness_income)
                tmp_fitness_income.append(fitness_income[max_fitness_index])
                pop = self._select(pop, fitness_income)  # 选择生成新的种群
            # 1.4, 获取并储存各机组结果
            tmp_income, tmp_subsection, tmp_bided_power = self.deal_result(g=g, pop=pop)
            income.append(tmp_income)
            subsection_declaration.append(tmp_subsection)
            bided_power.append(tmp_bided_power)
        # 2、输出结果
        # 2.1、整合各机组结果: 由list转为DataFrame
        income = pd.concat(income)
        subsection_declaration = pd.concat(subsection_declaration)
        bided_power = pd.concat(bided_power)
        # 2.2、输出结果汇总为result
        result['income'] = income.to_dict('list')
        result['subsection_declaration'] = subsection_declaration.to_dict('list')
        result['bided_power'] = bided_power.to_dict('list')

        logger.info(f'result = {result}')
        logger.info("---------------------------遗传算法_分段报价模型运行结束--------------------------")
        return result


if __name__ == "__main__":
    import time
    from datetime import timedelta
    time0 = time.time()
    generators_test = pd.read_excel(r"F:\ToGeek\ToGeekModels\data\optimize\subsection_declaration\input1.xlsx", sheet_name='generators', index_col=None)
    prices_test = pd.read_excel(r"F:\ToGeek\ToGeekModels\data\optimize\subsection_declaration\input1.xlsx", sheet_name='prices', index_col=None)
    p = SubsectionDeclaration(generators=generators_test, prices=prices_test)
    result = p.predict()
    print(result)
    print('*' * 80)
    print(pd.DataFrame(result['income']))
    print('*'*80)
    print(pd.DataFrame(result['subsection_declaration']))
    print('*' * 80)
    print(pd.DataFrame(result['bided_power']))
    time1 = time.time()
    print(f"运行时长：{timedelta(seconds=int(time1-time0))}")
