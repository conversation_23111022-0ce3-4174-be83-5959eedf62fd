from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.nationwide.load_society_monthly import Prediction


class SocietyMonthlyHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        elec = params.pop('elec')
        pred_months = params.pop('pred_months', 12)
        cap = params.pop('cap', None)
        floor = params.pop('floor', None)
        include_history = params.pop('include_history', False)
        flexibility = params.pop('flexibility', 5)
        calc_daily_avg = params.pop('calc_daily_avg', True)
        pred = Prediction(elec, pred_months=pred_months, cap=cap, floor=floor,
                          include_history=include_history, flexibility=flexibility,
                          calc_daily_avg=calc_daily_avg)
        self.write(pred.predict(to_json=True))
