# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-12-19 09:17:36
# Description: 山东中长期合约电量分配
"""

import numpy as np
import pandas as pd
from decimal import *
import calendar
import logging
logger = logging.getLogger()


class ContractElectricityDistribution_v2:
    def __init__(self, year, month, package, package_selected_buyer, elec_demand_buyer, generator_params,
                 generator_overhaul, choice = "elec", upper_threshold = 50000, threshold_limit = 300, total_load_rate = 1.1):
        logger.info("---------------------山东中长期合约电量分配模型-------------------------")
        # 通用参数
        self.year = int(year)  # 目标年份
        self.month = int(month)  # 目标月份
        self.days_month = calendar.monthrange(self.year, self.month)[1]  # 目标月份天数
        self.time_list = pd.date_range("00:00", "23:00", freq="H").map(lambda s: str(s).split(" ")[1]).values  # 目标时刻列表
        self.choice = choice
        self.upper_threshold = upper_threshold
        self.threshold_limit = threshold_limit
        self.total_load_rate = total_load_rate

        # 套餐数据预处理：日分时电量比例字典, 日分时电价字典
        logger.info(f"1. 套餐数据预处理...")
        self.elec_ratio_dict, self.elec_price_dict = self.get_data_package(package)

        # 售电公司数据处理
        logger.info(f"2. 售电公司数据处理...")
        self.buyer_name_array, self.elec_buy_array, self.buyer_package_dict, self.df_elec_buy, self.elec_params_dict = self.get_data_buyer(
            package_selected_buyer, elec_demand_buyer)

        # 发电机组数据处理
        logger.info(f"3. 发电机组数据处理...")
        self.seller_name_array, self.generator_params_dict, self. df_generator = self.get_data_seller(generator_params,
                                                                                                       generator_overhaul)

    def get_data_package(self, package):
        """ 套餐数据处理 """
        package = pd.DataFrame(package)
        package["time"] = package["time"].map(lambda s: str(s)[:8])  # "00:00:00" ~ "23:00:00"
        package = package.sort_values(["month", "package", "time"])

        # 筛选当前月的套餐数据
        p_month = package[package["month"] == self.month]
        # 套餐日分时电量比例字典
        elec_ratio_dict = {p: p_month[p_month["package"] == p]["elec_ratio"].values for p in
                           p_month["package"].unique()}
        # 套餐日分时电价字典
        elec_price_dict = {p: p_month[p_month['package'] == p]['elec_price'].values for p in
                           p_month['package'].unique()}

        return elec_ratio_dict, elec_price_dict

    def get_data_buyer(self, package_selected_buyer, elec_demand_buyer):
        """ 售电侧电量需求数据 """

        # 1, 售电公司套餐选择
        package_buy = pd.DataFrame(package_selected_buyer)
        package_buy["month"] = package_buy["month"].astype(int)
        # 售电公司套餐字典
        buyer_package_dict = package_buy.drop("month", axis=1).set_index("buyer").to_dict()['package']

        # 售电公司指定机组字典
        elec_params_dict = package_buy.set_index("buyer").T.to_dict()

        # 2, 售电公司月分日电量需求
        elec_buy = pd.DataFrame(elec_demand_buyer)
        elec_buy["date"] = elec_buy["date"].map(lambda s: str(s).split(" ")[0])
        elec_buy["month"] = pd.to_datetime(elec_buy["date"]).map(lambda s: s.month)

        # 3, 数据组合，根据选择的套餐计算分时电量
        df_elec_buy = pd.merge(elec_buy, package_buy, on=["buyer", "month"], how="left")
        df_elec_buy = df_elec_buy[df_elec_buy["month"] == self.month]  # 筛选当月数据
        df_elec_buy["elec_ratio"] = df_elec_buy["package"].map(self.elec_ratio_dict)  # 电量比例
        # 日分时电量分解
        df_elec_buy[self.time_list] = (df_elec_buy["elec_demand"] * df_elec_buy["elec_ratio"]).apply(pd.Series)
        df_elec_buy = df_elec_buy.sort_values(["buyer", "date"]).reset_index(drop=True)
        df_elec_buy = df_elec_buy[["buyer", "package", "date"] + list(self.time_list)]

        # 4, 售电公司名称列表
        buyer_name_array = df_elec_buy["buyer"].unique()

        # 5, 结构化处理
        elec_array_list = []
        for buyer in buyer_name_array:
            df_tmp = df_elec_buy[df_elec_buy["buyer"] == buyer]
            df_tmp = df_tmp.sort_values("date")  # 按日期排序，保证日电量需求有序
            list_tmp = df_tmp[self.time_list].values.tolist()  # 二维列表，当前售电公司日分时电量需求
            elec_array_list.append(list_tmp)
            # print(f"{buyer}.shape = {np.array(list_tmp).shape}")

        elec_buy_array = np.array(elec_array_list)
        # print(elec_buy_array.shape)
        # print((len(buyer_name_array), self.days_month, 24))
        assert elec_buy_array.shape == (len(buyer_name_array), self.days_month, 24)

        return buyer_name_array, elec_buy_array, buyer_package_dict, df_elec_buy, elec_params_dict

    def get_data_seller(self, generator_params, generator_overhaul):
        """ 发电测: 参与目标月份发电工作的机组数据 """

        # 1. 机组参数
        generator_params = pd.DataFrame(generator_params)
        generator_params["month"] = generator_params["month"].astype(int)
        generator_params = generator_params[generator_params["month"] == self.month]

        # 2. 机组检修
        if len(generator_overhaul) != 0:
            generator_overhaul = pd.DataFrame(generator_overhaul)
            # 计算停检天数
            date_overhaul = generator_overhaul['date_overhaul']
            days_overhaul = []
            for i in range(len(date_overhaul)):
                days = 0
                for j in range(len(date_overhaul[i])):
                    date_over = date_overhaul[i][j]
                    date_over = pd.Series(date_over)
                    day = date_over.map(lambda d: d.split("-")[2])
                    count = int(day[1]) - int(day[0]) + 1
                    days = days + count
                days_overhaul.append(days)

            generator_overhaul["days_overhaul"] = pd.Series(days_overhaul)

            # 机组数据组合
            df_generator = pd.merge(generator_params, generator_overhaul, on=["month", "name_generator"], how="left")
            df_generator["days_overhaul"] = df_generator["days_overhaul"].fillna(0)  # 无检修的机组检修天数置为0
            df_generator = df_generator[df_generator["month"] == self.month]  # 筛选当月机组信息
        else:
            df_generator = generator_params.copy()
            df_generator["days_overhaul"] = 0

        # 3. 机组相关参数
        # 计算运行容量：用于计算双边合约负荷率
        df_generator["run_capacity"] = (df_generator["full_capacity"] / self.days_month) * (
                    self.days_month - df_generator["days_overhaul"])

        # 没有减去已分配总电量的当月上网电量：运行容量 * 24h * days_month天 * (1-厂用电率)
        df_generator["online_capacity"] = df_generator["run_capacity"] * 24 * self.days_month * (
                1 - df_generator["power_consumption_rate"])

        # 4. 机组名称列表
        generator_name_array = df_generator["name_generator"].unique()

        # 机组参数增加一个字段：机组目前指派给的售电公司个数
        count = [0] * (len(df_generator))
        counts_posses = pd.Series(count)
        df_generator["counts_posses"] = counts_posses

        # 机组参数增加一个字段：机组的此次分配电量
        elec_number = [0] * (len(df_generator))
        elec_numbers = pd.Series(elec_number)
        df_generator["elec_distribution_generator"] = elec_numbers   # 机组的此次分配电量
        df_generator["elec_distribution_generator1"] = elec_numbers

        # 5. 机组参数字典
        generator_params_dict = df_generator.set_index("name_generator").T.to_dict()

        return generator_name_array, generator_params_dict, df_generator

    # 修改run_test
    def run_test(self, elec_max_dict, marginal_cost_dict, name_max_buyer, nonzero_date_idx_buyer, elec_max_buyer, elec_ratio_daily_buyer, elec_ratio_hourly_buyer):
        """ 根据某个售电公司匹配机组，得到机组所发出的电量 """

        # elec_sell_dict_sorted = sorted(elec_sell_dict.items(), key=lambda s: s[1], reverse=True)  # 按照可发电总量排序
        # 如果某个机组的最大发电量都小于售电公司的需求量，则选择按电量从大到小排序的字典，否则选择按成本从小到大排序的字典
        if elec_max_buyer.sum() > max(elec_max_dict.values()):
            elec_sell_dict = elec_max_dict
            number = 1
            name_max_seller, elec_max_seller, param_max_seller, days_overhaul_max_seller = self.generator_select(elec_sell_dict, name_max_buyer, nonzero_date_idx_buyer, number)
        else:
            elec_sell_dict = marginal_cost_dict
            number = elec_max_buyer.sum()
            name_max_seller, elec_max_seller, param_max_seller, days_overhaul_max_seller = self.generator_select(elec_sell_dict, name_max_buyer, nonzero_date_idx_buyer, number)

        # logger.info(f"    第{i}次匹配: {name_max_buyer} - {name_max_seller}")
        # 3. 计算本次匹配电量
        elec_array, name_max_seller, elec_max_seller = self.run_match(name_max_seller, elec_max_seller, elec_max_buyer, days_overhaul_max_seller, param_max_seller,
                  elec_ratio_daily_buyer, elec_ratio_hourly_buyer)

        return elec_array, name_max_seller, elec_max_seller

    # 根据条件选择机组
    def generator_select(self, elec_sell_dict, name_max_buyer, nonzero_date_idx_buyer, number):
        """ 根据条件选择合适的机组 """
        elec_sell_dict_sorted = list(elec_sell_dict.items())

        # 1. 发电侧最大产能机组相关参数
        name_max_seller = None  # 最大发电能力对应机组名称
        elec_max_seller = None  # 当前机组本月最大上网电量
        param_max_seller = None  # 当前机组参数
        days_overhaul_max_seller = None  # 当前机组停检天数

        # 售电公司指定机组的名称
        names = self.elec_params_dict[name_max_buyer]['name_generator_distribution']

        for name_max_seller, elec_max_seller in elec_sell_dict_sorted:
            param_max_seller = self.generator_params_dict[name_max_seller]  # 当前机组参数
            days_overhaul_max_seller = int(param_max_seller["days_overhaul"])  # 当前机组停检天数
            counts_buyer = param_max_seller["counts_posses"]  # 当前机组分配给售电公司的个数
            counts_max_buyer = param_max_seller["counts_distribution_buyer"]  # 当前机组分配给售电公司的最多个数

            load_rate_computed, proportion = self.load_rate_computed(name_max_seller)   # 此机组对应的场站下的总负荷率

            # 售电公司没有指定机组
            if load_rate_computed < self.total_load_rate - proportion:  # 同一个场站下的机组的总负荷率小于指定的负荷率
                if elec_max_seller > number:
                    if not (name_max_seller in names):
                        if counts_buyer < counts_max_buyer:  # 当前机组分配给售电公司的个数小于分配给售电公司的最多个数
                            # 当前机组无检修
                            if param_max_seller["days_overhaul"] == 0:
                                param_max_seller["counts_posses"] = counts_buyer + 1  # 当前机组分配给售电公司的个数加1
                                break
                            # 若有检修，并且 售电公司需求量非0日期 与 当前发电机组开机日期 有重合, 则选择当前机组; 否则选择下一个机组
                            else:
                                # 发电测开机日期索引
                                date_overhaul = param_max_seller['date_overhaul']
                                # 循环将机组的多个检修时间段的电量占比设为0
                                nonzero_date_idx_seller = np.arange(self.days_month).tolist()
                                for i in range(len(date_overhaul)):
                                    dd = pd.Series(date_overhaul[i])
                                    day = dd.map(lambda d: d.split("-")[2])
                                    idx_start = int(day[0]) - 1
                                    idx_end = int(day[1]) - 1
                                    listt = np.arange(idx_start, idx_end).tolist()
                                    [nonzero_date_idx_seller.remove(l) for l in listt]

                                nonzero_date_idx_seller = np.array(nonzero_date_idx_seller)
                                # 需求和供给交叉的索引数组
                                idx_intersect = np.intersect1d(nonzero_date_idx_buyer, nonzero_date_idx_seller)
                                if len(idx_intersect) != 0:
                                    param_max_seller["counts_posses"] = counts_buyer + 1
                                    break

        return name_max_seller, elec_max_seller, param_max_seller, days_overhaul_max_seller

    # 计算售电公司需求量和发电机组电量的匹配量
    def run_match(self, name_max_seller, elec_max_seller, elec_distribution_buyer, days_overhaul_max_seller, param_max_seller, elec_ratio_daily_buyer, elec_ratio_hourly_buyer):
        """ 选定售电公司和选定机组的电量匹配 """

        scale_seller_buyer = elec_max_seller / elec_distribution_buyer.sum()  # 发电机组总电量 / 售电侧总需求电量
        if scale_seller_buyer < 1:  # 机组产能只能满足部分售电公司电量
            if days_overhaul_max_seller == 0:  # 当前机组无检修
                elec_array = elec_distribution_buyer * scale_seller_buyer
            else:  # 当前机组有检修，将所有产能分配至工作日
                date_overhaul = param_max_seller['date_overhaul']
                # 循环将机组的多个检修时间段的电量占比设为0
                for i in range(len(date_overhaul)):
                    dd = pd.Series(date_overhaul[i])
                    day = dd.map(lambda d: d.split("-")[2])
                    idx_start = int(day[0]) - 1
                    idx_end = int(day[1]) - 1
                    day_overhaul_max_seller = idx_end - idx_start + 1
                    elec_ratio_daily_buyer[idx_start: idx_end + 1] = np.zeros(
                        day_overhaul_max_seller)  # 检修日电量占比设为0

                elec_ratio_daily_seller = elec_ratio_daily_buyer / elec_ratio_daily_buyer.sum()  # 比例缩放至1, 即当前机组月分日电量比例
                elec_daily_seller = elec_ratio_daily_seller * elec_max_seller  # 当前机组月分日电量
                elec_array = np.array([elec_daily * elec_ratio_hourly_buyer for elec_daily in elec_daily_seller])  # 按照售电公司套餐比例分解
        else:  # 机组产能超过售电公司需求量
            elec_array = elec_distribution_buyer.copy()
            if days_overhaul_max_seller != 0:
                date_overhaul = param_max_seller['date_overhaul']
                # 循环将机组的多个检修时间段的电量占比设为0
                for i in range(len(date_overhaul)):
                    dd = pd.Series(date_overhaul[i])
                    day = dd.map(lambda d: d.split("-")[2])
                    idx_start = int(day[0]) - 1
                    idx_end = int(day[1]) - 1
                    day_overhaul_max_seller = idx_end - idx_start + 1
                    elec_array[idx_start: idx_end + 1] = np.zeros(shape=(day_overhaul_max_seller, 24))

        return elec_array, name_max_seller, elec_max_seller

    # 根据检修天数修改机组的双边合约负荷率上限
    def change_upper(self):
        """针对同一场站有检修的机组修改双边合约负荷率上限"""

        upper_load_rate_new = {}  # 机组名称：修改后的双边合约负荷率上限
        # 判断机组里面是否有石横#5和石横#6，若有，则在dataframe里删除有石横#5和石横#6的行
        dff = pd.DataFrame()  # 包含石横#5和石横6的机组数据库
        df_generator_new = self.df_generator.copy()
        seller_name_array = df_generator_new["name_generator"].unique().tolist()  # 机组列表
        seller_names = ["石横#5", "石横#6"]
        for seller_name in seller_names:
            if seller_name in seller_name_array:
                index = df_generator_new.loc[lambda d: d.name_generator == seller_name].index.tolist()[0]  # 取索引
                dff1 = df_generator_new.loc[[index], :]
                dff = pd.concat([dff, dff1], ignore_index=True)
                df_generator_new = df_generator_new.drop(index)  # 删除index行
                df_generator_new = df_generator_new.reset_index(drop=True)  # 重置索引
                seller_name_array.remove(seller_name)  # 机组列表中删除"石横#5", "石横#6"

        seller_name_array = pd.Series(seller_name_array)
        power_plant_names = seller_name_array.map(lambda d: d.split("#")[0]).unique()  # 电厂名称列表
        # 值为数据框的字典：
        df_dff = {}
        for name in power_plant_names:
            index = df_generator_new["name_generator"].map(lambda d: d.split("#")[0]) == name
            df = df_generator_new[index].reset_index(drop=True)  # 同一个电厂的机组
            df_dff[name] = df  # 更新df_dff

        # 将石横#5和石横#6添加到字典中
        if len(dff) > 0:
            df_dff["石横new"] = dff
        # 同一个电厂的机组名称
        same_station_names = list(df_dff.keys())

        # 遍历字典，修改双边合约负荷率
        for key, df in df_dff.items():
            if df["days_overhaul"].sum() > 0:  # 同一个电厂的机组存在检修,更新双边合约负荷率上限
                for i in range(len(df)):
                    if df.loc[i, "days_overhaul"] > 0:
                        upper_load_rate_new[df.loc[i, "name_generator"]] = df.loc[i, "upper_bilateral_load_rate"] * (
                                    1 - df.loc[i, "days_overhaul"] / self.days_month)
                    else:
                        upper_load_rate_new[df.loc[i, "name_generator"]] = df.loc[i, "upper_bilateral_load_rate"] * (
                                    1 - df["days_overhaul"].sum() / (self.days_month * len(df)))
            else:  # 同一个电厂的机组不存在检修,不更新双边合约负荷率上限
                for i in range(len(df)):
                    upper_load_rate_new[df.loc[i, "name_generator"]] = df.loc[i, "upper_bilateral_load_rate"]

        return upper_load_rate_new, same_station_names

    # 根据期望合约负荷率修改机组的上限和下限
    def change_upper_limit(self):
        """ 根据期望合约负荷率修改机组的上限和下限 """

        upper_total_load_rate_new, same_station_names = self.change_upper()

        self.df_generator["upper_load_rate_new"] = pd.Series([0] * len(self.df_generator))    # 修改后的双边合约负荷率上限
        self.df_generator["limit_load_rate_new"] = pd.Series([0] * len(self.df_generator))    # 修改后的总合约负荷率下限
        for name, value in upper_total_load_rate_new.items():
            index = self.df_generator.loc[lambda d: d.name_generator == name].index.tolist()[0]  # 取索引
            if self.df_generator.loc[index, "expected_load_rate"] == -1:  # 没有指定期望总体合约负荷率
                self.df_generator.loc[index, "upper_load_rate_new"] = value
                self.df_generator.loc[index, "limit_load_rate_new"] = self.df_generator.loc[index, "limit_total_load_rate"]
            else:   # 指定期望总体合约负荷率，则机组的合约负荷率上下限一样
                self.df_generator.loc[index, "upper_load_rate_new"] = self.df_generator.loc[index, "expected_load_rate"]
                self.df_generator.loc[index, "limit_load_rate_new"] = self.df_generator.loc[index, "expected_load_rate"]

        return self.df_generator["upper_load_rate_new"], self.df_generator["limit_load_rate_new"]

    # 重新修改售电公司的需求量数组
    def change_elec_buyer(self, elec_buy_array):
        """ 根据售电公司的电量大小重新修改售电公司的需求量数组 """
        buyer_name_array_new = []  # 修改后售电公司的名称列表
        elec_buy_array_new = []    # 修改后售电公司的分时电量需求量列表
        upper_threshold = self.upper_threshold   # 售电公司电量修改的上限值
        threshold_limit = self.threshold_limit   # 售电公司电量修改的下限值
        # 1，对所有售电公司进行初步整理
        for i in range(len(elec_buy_array)):
            elec_total = elec_buy_array[i].sum()    # 某个售电公司需要的电量总量
            if elec_total > upper_threshold * 1.2:
                k = int(elec_total / upper_threshold)
                elec_residue = elec_total - upper_threshold * k
                if elec_residue > threshold_limit:
                    for j in range(k):
                        elec_buy_array_new.append((elec_buy_array[i] * (upper_threshold / elec_total)).tolist())
                        buyer_name_array_new.append(self.buyer_name_array[i])
                    elec_buy_array_new.append((elec_buy_array[i] * (elec_residue /elec_total)).tolist())
                    buyer_name_array_new.append(self.buyer_name_array[i])
                else:
                    for j in range(k-1):
                        elec_buy_array_new.append((elec_buy_array[i] * (upper_threshold / elec_total)).tolist())
                        buyer_name_array_new.append(self.buyer_name_array[i])
                    elec_buy_array_new.append((elec_buy_array[i] * ((elec_residue + upper_threshold) / elec_total)).tolist())
                    buyer_name_array_new.append(self.buyer_name_array[i])
            else:
                elec_buy_array_new.append(elec_buy_array[i].tolist())
                buyer_name_array_new.append(self.buyer_name_array[i])
        elec_buy_array_new = np.array(elec_buy_array_new)

        # 2. 将售电公司需求量小于self.upper_threshold * 0.8的售电公司组合到一起
        elec_buy_array_small = []  # 售电公司需求量小于self.upper_threshold * 0.8的售电公司
        elec_buy_array_large = []  # 售电公司需求量大于self.upper_threshold * 0.8的售电公司
        buyer_name_array_small = []  # 售电公司需求量大于self.upper_threshold * 0.8的售电公司名称列表
        buyer_name_array_large = []  # 售电公司需求量大于self.upper_threshold * 0.8的售电公司名称列表

        for i in range(len(elec_buy_array_new)):
            if elec_buy_array_new[i].sum() > self.upper_threshold * 0.8:
                elec_buy_array_large.append(elec_buy_array_new[i])
                buyer_name_array_large.append(buyer_name_array_new[i])
            else:
                elec_buy_array_small.append(elec_buy_array_new[i])
                buyer_name_array_small.append(buyer_name_array_new[i])
        elec_buy_array_large = np.array(elec_buy_array_large)
        elec_buy_array_small = np.array(elec_buy_array_small)

        return elec_buy_array_large, elec_buy_array_small, buyer_name_array_large, buyer_name_array_small

    def buyer_seller_match(self, res_dict_new, dff_generator, elec_buy_array_new, buyer_name_array_new, elec_max_dict, marginal_cost_dict):
        """ 售电侧和需求侧匹配 """
        i = 1
        while (elec_buy_array_new.sum() > 1) & (i < 1000):  # 终止条件: 所有售电公司的电量需求都得到满足
            # logger.info(f"    第{i}次匹配")
            res = {}
            # 2.3.1 售电侧: 最大需求量售电公司相关参数
            elec_buy_sum = elec_buy_array_new.sum(axis=1).sum(axis=1)  # 当月售电公司需求总量, array, shape=(售电公司数量,)
            idx_max_buyer = elec_buy_sum.argmax()  # 当前售电公司索引, int
            name_max_buyer = buyer_name_array_new[idx_max_buyer]  # 当前售电公司名称, str
            elec_max_buyer = elec_buy_array_new[idx_max_buyer]  # 当前售电公司的电力需求曲线, array, shape=(当月天数, 24)
            package_max_buyer = self.buyer_package_dict[name_max_buyer]  # 当前售电公司所选套餐
            elec_ratio_hourly_buyer = self.elec_ratio_dict[package_max_buyer]  # 当前售电公司所选套餐的分时电量
            elec_ratio_daily_buyer = elec_max_buyer.sum(
                axis=1) / elec_max_buyer.sum()  # 当前售电公司月分日电量占比, array, shape=(days_month,)

            # 售电公司需求非0的日期索引
            nonzero_date_idx_buyer = np.nonzero(elec_max_buyer.sum(axis=1))[0]
            # print(f"{i}.1. 售电侧最大需求量： {elec_max_buyer.sum()}")

            # 2.3.3 售电侧和发电测匹配
            elec_array, name_max_seller, elec_max_seller = self.run_test(elec_max_dict, marginal_cost_dict,
                                                                         name_max_buyer, nonzero_date_idx_buyer,
                                                                         elec_max_buyer, elec_ratio_daily_buyer,
                                                                         elec_ratio_hourly_buyer)

            # 2.3.4 本轮分配结果存储
            res[f"{name_max_seller}_{name_max_buyer}"] = elec_array.tolist()
            # res_dict_new.append(list(res.items()))    # 将新匹配的机组和售电公司添加到res_dict_new中
            res_dict_new = res_dict_new + list(res.items())

            # 2.3.5 更新售电侧和发电侧电量数据
            index = dff_generator.loc[lambda d: d.name_generator == name_max_seller].index.tolist()[0]  # 取索引
            dff_generator.loc[index, "online_capacity_generator"] = elec_max_seller - elec_array.sum()
            dff_generator.loc[index, "elec_distribution_generator"] = dff_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()   # 更新机组已分配的电量
            self.df_generator.loc[index, "elec_distribution_generator"] = self.df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()
            elec_residue = elec_max_seller - elec_array.sum()  # 一次匹配后机组的电量

            marginal_cost_dict[name_max_seller] = elec_residue
            marginal_cost_dict.pop(name_max_seller)  # 从原始字典中删除此次匹配的元素
            marginal_cost_dict[name_max_seller] = elec_residue  # 将此次匹配的元素添加到字典的最后面

            elec_max_dict[name_max_seller] = elec_residue
            elec_max_dict.pop(name_max_seller)  # 从原始字典中删除此次匹配的元素
            elec_max_dict[name_max_seller] = elec_residue  # 将此次匹配的元素添加到字典的最后面
            # 售电侧
            elec_buy_array_new[idx_max_buyer] = elec_max_buyer - elec_array

            i += 1

        return res_dict_new, dff_generator

    # 同一个场站的所有机组
    def get_generator(self, name, df_generator):
        if name == "石横new":
            df = (df_generator[(df_generator["name_generator"] == "石横#5") | (df_generator["name_generator"] == "石横#6")]).reset_index(drop=True)
        elif name == "石横":
            df = (df_generator[(df_generator["name_generator"] == "石横#1") | (df_generator["name_generator"] == "石横#2") | (df_generator["name_generator"] == "石横#3") | (df_generator["name_generator"] == "石横#4")]).reset_index(drop=True)
        else:
            index = df_generator["name_generator"].map(lambda d: d.split("#")[0]) == name
            df = df_generator[index].reset_index(drop=True)  # 同一个电厂的机组

        return df

    # 计算同一个场站下机组的总负荷率
    def load_rate_computed(self, name_max_seller):
        if (name_max_seller == "石横#5") | (name_max_seller == "石横#6"):
            df = (self.df_generator[(self.df_generator["name_generator"] == "石横#5") | (self.df_generator["name_generator"] == "石横#6")]).reset_index(drop=True)
        elif (name_max_seller == "石横#1") | (name_max_seller == "石横#2") | (name_max_seller == "石横#3") | (name_max_seller == "石横#4"):
            df = (self.df_generator[(self.df_generator["name_generator"] == "石横#1") | (self.df_generator["name_generator"] == "石横#2") |(self.df_generator["name_generator"] == "石横#3") | (self.df_generator["name_generator"] == "石横#4")]).reset_index(drop=True)
        else:
            index = self.df_generator["name_generator"].map(lambda d: d.split("#")[0]) == (name_max_seller.split("#")[0])
            df = self.df_generator[index].reset_index(drop=True)  # 同一个电厂的机组

        load_rate_computed = (df["elec_distribution_generator"].sum() + df["total_elec_signed"].sum()) / df["online_capacity"].sum()   # 同一个场站下机组的总负荷率，计算时加上了拟分配的电量
        # 机组和售电公司的每一次匹配的电量占整个场站的比例
        proportion = self.upper_threshold * 1.2 / (df["online_capacity"].sum())

        return load_rate_computed, proportion

    # 根据成本和场站总负荷率调节机组发电量
    def elec_marginal_rate(self, total_load_rate1, name, rate_distribution, df_generator_middle):
        rate1 = total_load_rate1.pop(name)
        rate1_list = max(list(total_load_rate1.values()))
        df = self.get_generator(name, df_generator_middle)
        if rate1 > rate1_list:
            rates = rate_distribution
        else:
            rates = rate_distribution + (rate1_list - rate1) / 2

        for i in range(len(df)):
            name_distribution = df.loc[i, "name_generator"]
            index = df_generator_middle.loc[lambda d: d.name_generator == name_distribution].index.tolist()[0]  # 取索引
            elec_distribution = df.loc[i, "online_capacity"] * rates
            elec_distributions = np.minimum((elec_distribution + df.loc[i, "elec_distribution_generator"] + df.loc[i, "total_elec_signed"] + df.loc[i, "elec_middle"]), (df.loc[i, "online_capacity_generator"] - df.loc[i, "elec_middle"]) * 0.6)
            df_generator_middle.loc[index, "elec_middle"] = df_generator_middle.loc[index, "elec_middle"] + elec_distributions

        return df_generator_middle

    def run(self):
        """ 核心：中长期合约电量分配逻辑 """
        logger.info(f"5. while 循环匹配...")

        # 结果保存字典, key: value = "发电机组_售电公司": "匹配电量数组"
        res_dict = {}

        # 发售两侧数据
        elec_buy_array = self.elec_buy_array.copy()  # 售电侧需求数据, array
        df_generator = self.df_generator.copy()

        # 机组的双边合约负荷率上限和总合约负荷率下限
        upper_load_rate_new, limit_load_rate_new = self.change_upper_limit()

        # 机组可提供的最大上网电量：当月上网电量 * 双边合约负荷率上限 - 已签约总电量
        df_generator["online_capacity_generator"] = df_generator["online_capacity"] * upper_load_rate_new - df_generator["total_elec_signed"]

        # 消除电量为负值的情况
        dd = df_generator["online_capacity_generator"].tolist()
        dff = np.maximum(dd, 0)
        df_generator["online_capacity_generator"] = dff

        # 1. 给指定机组的售电公司进行电量匹配
        buyer_name_array = self.buyer_name_array    # 售电公司名称列表
        for j in range(len(buyer_name_array)):
            name_max_buyer = buyer_name_array[j]
            elec_max_buyer = elec_buy_array[j]
            package_max_buyer = self.buyer_package_dict[name_max_buyer]  # 当前售电公司所选套餐
            elec_ratio_hourly_buyer = self.elec_ratio_dict[package_max_buyer]  # 当前售电公司所选套餐的分时电量
            elec_ratio_daily_buyer = elec_max_buyer.sum(axis=1) / elec_max_buyer.sum()   # 当前售电公司月分日电量占比, array, shape=(days_month,)
            # 判断售电公司是否指定机组
            rates = self.elec_params_dict[name_max_buyer]['rate_generator_distribution']
            # logging.info(f": {rates}")
            elec_buyer = elec_max_buyer.copy()     # 确保每次计算时售电公司的需求量不改变
            # 售电公司指定机组
            if len(rates) != 0:
                names = self.elec_params_dict[name_max_buyer]['name_generator_distribution']
                rates = self.elec_params_dict[name_max_buyer]['rate_generator_distribution']
                # logging.info(f": {rates}")

                for i in range(len(names)):
                    # logging.info(f"i: {i}, {rates[i]}")
                    rate = float(rates[i])
                    elec_distribution_buyer = elec_buyer * rate
                    name_max_seller = names[i]
                    param_max_seller = self.generator_params_dict[name_max_seller]  # 当前机组参数
                    days_overhaul_max_seller = int(param_max_seller["days_overhaul"])  # 当前机组停检天数
                    elec_max_seller = df_generator[df_generator["name_generator"] == name_max_seller]["online_capacity_generator"].tolist()[0]      # 当前机组的发电量

                    # 计算匹配电量
                    elec_array, name_max_seller, elec_max_seller = self.run_match(name_max_seller, elec_max_seller, elec_distribution_buyer, days_overhaul_max_seller, param_max_seller, elec_ratio_daily_buyer, elec_ratio_hourly_buyer)

                    # 更新res_dict
                    res_dict[f"{name_max_seller}_{name_max_buyer}"] = elec_array.tolist()
                    # 5. 更新售电侧和发电侧电量数据
                    elec_max_buyer = elec_max_buyer - elec_array
                    elec_buy_array[j] = elec_max_buyer
                    # 更新df_generator的机组的可发电量
                    index = df_generator.loc[lambda d: d.name_generator == name_max_seller].index.tolist()[0]  # 取索引
                    df_generator.loc[index, "online_capacity_generator"] = df_generator.loc[index, "online_capacity_generator"] - elec_array.sum()
                    df_generator.loc[index, "elec_distribution_generator"] = df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()
                    self.df_generator.loc[index, "elec_distribution_generator"] = self.df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()

                    self.generator_params_dict[name_max_seller]["counts_posses"] += 1  # 更新机组分配给售电公司的个数

        # 2. 消除同一个场站机组总负荷率相差较大的情况，如果同一场站的机组的最大负荷率小于30%，则不做处理；若大于30,则设置负荷率小的固定为30%
        upper_load_rate_new, same_station_names = self.change_upper()
        # 计算同一个场站下总负荷率相差较大的机组可以发的电量
        df_generator_middle = df_generator.copy()   # 此dataframe用来存放 调节同一场站机组总负荷率相差较大的机组的发电量
        df_generator_middle["elec_middle"] = [0] * (len(df_generator_middle))
        for name in same_station_names:
            if name != "石横":
                df = self.get_generator(name, df_generator)   # 同一个场站的所有机组

                if df["days_overhaul"].sum() == 0:
                    df_new = df.copy()
                    df_new["rate"] = (df_new['elec_distribution_generator'] + df_new["total_elec_signed"]) / df_new["online_capacity"]
                    rate_max = df_new["rate"].max()
                    rate_min = df_new["rate"].min()
                    index_rate_max = df_new["rate"].idxmax()  # 同一个电厂的机组的总负荷率最大值的索引
                    df1 = df_new.drop(index_rate_max).reset_index(drop=True)
                    if (rate_max - rate_min) > 0.2:
                        rate = (rate_max - 0.1) - df1["rate"]
                    elif (rate_max - rate_min) > 0.1:
                        rate = rate_max - df1["rate"]
                    else:
                        rate = 0 * df1["rate"]
                    df1["elec_generated"] = df1["online_capacity"] * rate
                    for i in range(len(df1)):
                        elec_generated = np.minimum(df1.loc[i, "elec_generated"], df1.loc[i, "online_capacity_generator"])   # 此次计算出来的可分配的电量与可分配电量最大值比较
                        name_generator_middle = df1.loc[i, "name_generator"]
                        index = df_generator_middle.loc[lambda d: d.name_generator == name_generator_middle].index.tolist()[0]  # 取索引
                        df_generator_middle.loc[index, "elec_middle"] = elec_generated

        # 根据场站总负荷率和成本给机组分配电量,对羊口电厂和蓬莱考虑成本
        total_load_rate = {}
        station_unoverhaul = {}
        rate_distribution = elec_buy_array.sum() / df_generator["online_capacity_generator"].sum()  # 售电公司需求电量平均分配给机组的比例
        for name in same_station_names:
            df = self.get_generator(name, df_generator_middle)

            if df["days_overhaul"].sum() == 0:
                rate = (df["elec_distribution_generator"].sum() + df["total_elec_signed"].sum() + df["elec_middle"].sum()) / df["online_capacity"].sum()
                if rate < 1:  # 将场站负荷率大于1的场站删除
                    total_load_rate[name] = rate
                    station_unoverhaul[name] = df["marginal_cost"][0]

        # 从没有检修的机组里找出成本最低的两个场站
        if len(station_unoverhaul) >= 2:
            station_unoverhaul_sorted = sorted(station_unoverhaul.items(), key=lambda s: s[1])[:2]
            # 没有检修的机组里成本最低的机组提前发的电量
            total_load_rate1 = total_load_rate.copy()
            name = station_unoverhaul_sorted[0][0]
            index = df_generator_middle["name_generator"].map(lambda d: d.split("#")[0]) == name
            df = df_generator_middle[index].reset_index(drop=True)
            if df["days_overhaul"].sum() == 0:
                rate_distribution = rate_distribution / 4
                df_generator_middle = self.elec_marginal_rate(total_load_rate1, name, rate_distribution, df_generator_middle)
            # 没有检修的机组里成本次低的机组提前发的电量
            total_load_rate2 = total_load_rate.copy()
            name = station_unoverhaul_sorted[1][0]
            index = df_generator_middle["name_generator"].map(lambda d: d.split("#")[0]) == name
            df = df_generator_middle[index].reset_index(drop=True)
            if df["days_overhaul"].sum() == 0:
                rate_distribution = rate_distribution / 8
                df_generator_middle = self.elec_marginal_rate(total_load_rate2, name, rate_distribution, df_generator_middle)

        df_generator_middle_new = df_generator_middle[df_generator_middle["elec_middle"] > 0]
        generator_marginal = df_generator_middle_new.sort_values(["marginal_cost", "elec_middle"], ascending=[True, False])
        elec_generated_previence = generator_marginal[['name_generator', "elec_middle"]].set_index("name_generator").to_dict()["elec_middle"]

        # 2.2 对需要提前分配的机组电量进行电量匹配
        # 售电公司名称和电量需求量组成的字典
        name_elec_buyer = {}
        for i in range(len(elec_buy_array)):
            name_buyer = buyer_name_array[i]  # 当前售电公司名称, str
            elec_buyer = elec_buy_array[i]  # 当前售电公司的电力需求曲线, array, shape=(当月天数, 24)
            name_elec_buyer[name_buyer] = elec_buyer.sum()

        name_elec_buyer_sorted = sorted(name_elec_buyer.items(), key=lambda s: s[1], reverse=True)

        for name_distribution_seller, elec_distribution_seller in elec_generated_previence.items():   # 遍历机组发电量
            for name_distribution_buyer, elec_distribution_buyer in name_elec_buyer_sorted:   # 遍历售电公司需求量
                # 售电公司
                index_name = buyer_name_array.tolist().index(name_distribution_buyer)
                elec_max_buyer = elec_buy_array[index_name]
                # 售电公司指定机组的名称
                name_generator_distribution = self.elec_params_dict[name_distribution_buyer]['name_generator_distribution']
                if elec_max_buyer.sum() > 0:
                    package_max_buyer = self.buyer_package_dict[name_distribution_buyer]  # 当前售电公司所选套餐
                    elec_ratio_hourly_buyer = self.elec_ratio_dict[package_max_buyer]  # 当前售电公司所选套餐的分时电量
                    elec_ratio_daily_buyer = elec_max_buyer.sum(axis=1) / elec_max_buyer.sum()  # 当前售电公司月分日电量占比, array, shape=(days_month,)
                    # 机组参数
                    param_max_seller = self.generator_params_dict[name_distribution_seller]  # 当前机组参数
                    days_overhaul_max_seller = int(param_max_seller["days_overhaul"])  # 当前机组停检天数
                    if elec_distribution_seller > self.threshold_limit:
                        if not (name_distribution_seller in name_generator_distribution):
                            if elec_distribution_seller > self.upper_threshold * 0.8:
                                # 计算匹配电量
                                elec_array, name_max_seller, elec_max_seller = self.run_match(name_distribution_seller, elec_distribution_seller, elec_max_buyer, days_overhaul_max_seller, param_max_seller, elec_ratio_daily_buyer, elec_ratio_hourly_buyer)
                                # 更新res_dict
                                res_dict[f"{name_max_seller}_{name_distribution_buyer}"] = elec_array.tolist()
                                # 5. 更新售电侧和发电侧电量数据
                                elec_max_buyer = elec_max_buyer - elec_array
                                elec_buy_array[index_name] = elec_max_buyer
                                # 更新df_generator的机组的可发电量
                                index = df_generator.loc[lambda d: d.name_generator == name_max_seller].index.tolist()[0]  # 取索引
                                df_generator.loc[index, "online_capacity_generator"] = df_generator.loc[index, "online_capacity_generator"] - elec_array.sum()
                                df_generator.loc[index, "elec_distribution_generator"] = df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()
                                self.df_generator.loc[index, "elec_distribution_generator"] = self.df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()

                                elec_distribution_seller = elec_distribution_seller - elec_array.sum()
                            else:
                                if elec_distribution_seller > elec_distribution_buyer:
                                    # 计算匹配电量
                                    elec_array, name_max_seller, elec_max_seller = self.run_match(name_distribution_seller, elec_distribution_seller, elec_max_buyer, days_overhaul_max_seller, param_max_seller, elec_ratio_daily_buyer, elec_ratio_hourly_buyer)
                                    # 更新res_dict
                                    res_dict[f"{name_max_seller}_{name_distribution_buyer}"] = elec_array.tolist()
                                    # 5. 更新售电侧和发电侧电量数据
                                    elec_max_buyer = elec_max_buyer - elec_array
                                    elec_buy_array[index_name] = elec_max_buyer
                                    # 更新df_generator的机组的可发电量
                                    index = df_generator.loc[lambda d: d.name_generator == name_max_seller].index.tolist()[0]   # 取索引
                                    df_generator.loc[index, "online_capacity_generator"] = df_generator.loc[index, "online_capacity_generator"] - elec_array.sum()
                                    df_generator.loc[index, "elec_distribution_generator"] = df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()
                                    self.df_generator.loc[index, "elec_distribution_generator"] = self.df_generator.loc[index, "elec_distribution_generator"] + elec_array.sum()

                                    elec_distribution_seller = elec_distribution_seller - elec_array.sum()
                    else:
                        break

        # 3. 给未指定机组的售电公司进行电量匹配
        elec_buy_array_large, elec_buy_array_small, buyer_name_array_large, buyer_name_array_small = self.change_elec_buyer(elec_buy_array)
        # 售电侧需求总数据
        total_elec_sum = elec_buy_array_large.sum() + elec_buy_array_small.sum()

        # 机组可发电量的上限和下限
        dff_generator = df_generator.copy()
        # 计算机组拟分配合约用电量下限
        dff_generator["elec_distribution_limit"] = dff_generator["online_capacity"] * limit_load_rate_new - dff_generator["total_elec_signed"] - dff_generator["elec_distribution_generator"]

        # 消除上网电量为负值的情况
        dd2 = dff_generator["elec_distribution_limit"].tolist()
        dff2 = np.maximum(dd2, 0)
        dff_generator["elec_distribution_limit"] = dff2

        garator_elec_upper = dff_generator["online_capacity_generator"].sum()   # 机组可发电量的上限的和
        garator_elec_limit = dff_generator["elec_distribution_limit"].sum()     # 机组可发电量的下限的和

        # 3.1 售电公司总量小于发电机组的下限
        if total_elec_sum < garator_elec_limit:
            return list(res_dict.items())
        # 3.2 售电公司总量大于发电机组的上限
        if total_elec_sum > garator_elec_upper:
            return list(res_dict.items())

        # 3.3 售电公司和机组匹配
        res_dict_new = list(res_dict.items())

        # 3.3.11 发电测：没有检修的机组可以发电量的字典
        # 机组最低成本字典
        dff_generator_unoverhaul = dff_generator[dff_generator["days_overhaul"] == 0].reset_index(drop=True)
        generator_unoverhaul1 = dff_generator_unoverhaul.sort_values(["marginal_cost", "online_capacity_generator"], ascending=[True, False])
        marginal_cost_unoverhaul = generator_unoverhaul1[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]
        # 机组最大电量字典
        generator_unoverhaul2 = dff_generator_unoverhaul.sort_values(["online_capacity_generator", "marginal_cost"], ascending=[False, True])
        elec_max_unoverhaul = generator_unoverhaul2[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]

        # 3.3.12 发电测：所有机组可以发电量的字典
        # 机组最低成本字典
        dff_generator1 = dff_generator.sort_values(["marginal_cost", "online_capacity_generator"], ascending=[True, False])
        marginal_cost_dict = dff_generator1[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]
        # 机组最大电量字典
        dff_generator2 = dff_generator.sort_values(["online_capacity_generator", "marginal_cost"], ascending=[False, True])
        elec_max_dict = dff_generator2[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]

        # 无检修机组可发电量的和与售电公司需求量的和比较
        if dff_generator_unoverhaul["online_capacity_generator"].sum() > elec_buy_array_small.sum():
            # 3.3.21 售电公司需求量小于self.upper_threshold * 0.8的售电公司和机组匹配
            res_dict_new, dff_generator = self.buyer_seller_match(res_dict_new, dff_generator, elec_buy_array_small, buyer_name_array_small, elec_max_unoverhaul,
                                                                  marginal_cost_unoverhaul)
        else:
            res_dict_new, dff_generator = self.buyer_seller_match(res_dict_new, dff_generator, elec_buy_array_small, buyer_name_array_small, elec_max_dict,
                                                                  marginal_cost_dict)

        dff_generator3 = dff_generator.sort_values(["marginal_cost", "online_capacity_generator"], ascending=[True, False])
        marginal_cost_dict_new = dff_generator3[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]
        # 机组最大电量字典
        dff_generator4 = dff_generator.sort_values(["online_capacity_generator", "marginal_cost"], ascending=[False, True])
        elec_max_dict_new = dff_generator4[['name_generator', "online_capacity_generator"]].set_index("name_generator").to_dict()["online_capacity_generator"]
        # 3.3.22 售电公司需求量大于self.upper_threshold * 0.8的售电公司和机组匹配
        res_dict_new, dff_generator = self.buyer_seller_match(res_dict_new, dff_generator, elec_buy_array_large, buyer_name_array_large, elec_max_dict_new, marginal_cost_dict_new)

        # print("**" * 60)
        # print(f"售电侧名称列表: {self.buyer_name_array}")
        # print(f"售电侧剩余未满足: {elec_buy_array.sum(axis=1).sum(axis=1)}")
        # print("**" * 60)
        # print(f"发电侧剩余未分配: {elec_sell_dict}")
        # a = dff_generator["elec_distribution_generator"].sum()   # 拟分配的所有电量和

        return res_dict_new

    # 修改分配结果的格式
    def modify_results_format(self):
        """ 修改机组和售电公司匹配完后的列表结果为字典形式 """
        data_dict_old = self.run()
        data_dict = {}
        seller_buyer_name = []  # 机组名_售电公司名称
        for key, value in data_dict_old:
            seller_buyer_name.append(key)
        seller_buyer_names = pd.Series(seller_buyer_name).unique().tolist()   # 没有重复的机组名_售电公司名称

        for name in seller_buyer_names:
            seller_buyer_elec = np.zeros((self.days_month, 24))
            for key, value in data_dict_old:
                if name == key:
                    seller_buyer_elec = seller_buyer_elec + np.array(value)   # 将机组名_售电公司名称相同的分时电量相加
            data_dict[name] = seller_buyer_elec.tolist()

        return data_dict

    def arrange_result(self, data_dict):
        """ 输出数据整理 """
        logger.info(f"4. 发售两侧电量匹配...")
        # 1. 售电侧数据："result_data_buyer"
        df_buy_out = pd.melt(frame=self.df_elec_buy, id_vars=["buyer", "package", "date"], value_vars=self.time_list, var_name="time", value_name="elec_demand")
        df_buy_out['date_time'] = df_buy_out['date'] + " " + df_buy_out["time"]
        df_buy_out = df_buy_out.sort_values(['buyer', 'date_time']).reset_index(drop=True)
        df_buy_out['elec_price'] = df_buy_out.apply(lambda s: self.elec_price_dict[s['package']][(self.time_list == s['time']).argmax()], axis=1)
        df_buy_out = df_buy_out[["buyer", "date_time", "elec_demand", "elec_price"]]
        # print(f"df_buy_out: \n{df_buy_out}")
        result_data_buyer = df_buy_out.to_dict("records")

        # 2. 发电侧数据："result_data_seller"
        res_list = []
        for pair in data_dict.keys():
            name_seller = pair.split("_")[0]  # 发电机组名称
            name_buyer = pair.split("_")[-1]  # 售电公司名称

            df = pd.DataFrame(data={
                "date_time": pd.date_range(f"{self.year}-{self.month}-01",
                                           f"{self.year}-{self.month}-{self.days_month} 23:00:00", freq="H"),
                "elec_demand": np.array(data_dict[pair]).flatten()
            })

            df['date_time'] = df['date_time'].astype(str)
            df["time"] = df['date_time'].map(lambda s: s.split(" ")[1])
            df['seller'] = name_seller
            df['buyer'] = name_buyer

            # 根据售电公司所选套餐，添加分时价格
            df["package"] = df['buyer'].map(self.buyer_package_dict)
            df['elec_price'] = df.apply(
                lambda s: self.elec_price_dict[s['package']][(self.time_list == s['time']).argmax()], axis=1)

            res_list.append(df)

        df_sell = pd.concat(res_list).reset_index(drop=True)
        df_sell_out = df_sell[["seller", "buyer", "date_time", "elec_demand", "elec_price"]]
        # print(f"df_sell_out: \n{df_sell_out}")
        result_data_seller = df_sell_out.to_dict("records")

        # 3. 匹配电量数据："result_data_distribution"
        df_sell['month'] = self.month
        df_distribution = df_sell.groupby(['month', 'seller', 'buyer'])[
            'elec_demand'].sum().reset_index().sort_values("seller").reset_index(drop=True)
        # print(f"df_distribution: \n{df_distribution}")
        result_data_distribution = df_distribution.to_dict("records")

        return result_data_buyer, result_data_seller, result_data_distribution

    def get_data_result(self):
        """ 输出数据整理 """
        # 2. 发电侧数据："result_data_seller"
        data_dicts = self.modify_results_format().copy()  # 电量匹配结果字典

        # 计算机组分配的双边合约负荷率
        if len(data_dicts) != 0:     # 判断有没有分配
            # 从字典中删除字典值的和小于1的元素(基本上没有分配电量)
            list_del = []
            for key in data_dicts.keys():
                if np.array(data_dicts[key]).sum() < 1:
                    list_del.append(key)
            for i in list_del:
                data_dicts.pop(i)

            result_data_buyer, result_data_seller, result_data_distribution = self.arrange_result(data_dicts)

            # 电量结果保留为10的整数倍
            for i in result_data_distribution:
                i['elec_demand'] = 10 * round(float(i['elec_demand']) / 10)
                #i['elec_demand'] = round(i['elec_demand'])
            # result_data_seller的电量保留三位小数
            for i in result_data_seller:
                i['elec_demand'] = float(Decimal(i['elec_demand']).quantize(Decimal('0.000')))
                i['elec_price'] = float(Decimal(i['elec_price']).quantize(Decimal('0.0')))
            # result_data_buyer的电量保留三位小数
            for i in result_data_buyer:
                i['elec_demand'] = float(Decimal(i['elec_demand']).quantize(Decimal('0.000')))
                i['elec_price'] = float(Decimal(i['elec_price']).quantize(Decimal('0.0')))

            # ############################计算分配结果#######################
            # 计算合约负荷率，前面多加了一个变量elec_distribution_generator1
            for i in result_data_distribution:
                name_max_seller = i['seller']
                elec_demand = i['elec_demand']
                self.generator_params_dict[name_max_seller]["elec_distribution_generator1"] += elec_demand

            generator_name_array = self.df_generator["name_generator"].unique()
            for i in generator_name_array:
                index = self.df_generator.loc[lambda d: d.name_generator == i].index.tolist()[0]  # 取索引
                self.df_generator.loc[index, 'elec_distribution_generator1'] = self.generator_params_dict[i][
                    "elec_distribution_generator1"]  # elec_distribution_generator:分配完后每个机组分配的电量

            dd = (self.df_generator['elec_distribution_generator1'] + self.df_generator["total_elec_signed"]) / self.df_generator["online_capacity"]
            self.df_generator['rate_distribution_generator1'] = dd
            mm = self.df_generator["elec_distribution_generator1"].sum()

            ########################判断售电公司需求量和实际分配电量#######################################
            # 售电公司名称列表
            a = []  # 售电公司需求的电量
            for i in self.elec_buy_array:
                a.append(i.sum())

            buyer_name_array = self.buyer_name_array
            elec_distribution_buyer = {}  # 分配后售电公司的的电量
            for i in buyer_name_array:
                elec_distribution_buyer[i] = 0

            for i in result_data_distribution:
                name_max_buyer = i["buyer"]
                elec_demand = i['elec_demand']
                elec_distribution_buyer[name_max_buyer] += elec_demand  # 分配完后售电公司的电量

            # ###############################################################

            # 4. 数据组合输出
            result_data = {
                "result_data_distribution": result_data_distribution,  # 发售两侧月度匹配数据
                "result_data_buyer": result_data_buyer,  # 售电侧数据：分时电量 + 分时电价
                "result_data_seller": result_data_seller,  # 发电测数据：分时电量 + 分时电价
            }
            # print(f"res_dict: \n{result_data}")

            # logger.info(f'result: \n{result_data}')
            logger.info("--------------------- 山东中长期合约电量分配模型 计算完成-------------------------")

            return result_data

        else:
            result_data = {
                "result_data_distribution": ["售电公司总量不满足发电机组的上下限"],
                "result_data_buyer": ["售电公司总量不满足发电机组的上下限"],
                "result_data_seller": ["售电公司总量不满足发电机组的上下限"],
            }
            # print(f"res_dict: \n{result_data}")

            # logger.info(f'result: \n{result_data}')
            logger.info("--------------------- 山东中长期合约电量分配模型 计算完成-------------------------")

            return result_data


if __name__ == '__main__':
    path = r"C:\Users\<USER>\Desktop\请求参数1.json"

    import json
    with open(path, 'r') as f:
        data = json.loads(f.read())
    print(data)


    year_ = data['year']
    month_ = data['month']
    package_ = pd.DataFrame(data['package'])
    package_selected_buyer_ = pd.DataFrame(data['package_selected_buyer'])
    elec_demand_buyer_ = pd.DataFrame(data['elec_demand_buyer'])
    generator_params_ = pd.DataFrame(data['generator_params'])
    generator_overhaul_ = pd.DataFrame(data['generator_overhaul'])
    #
    model = ContractElectricityDistribution_v2(year=year_,
                                            month=month_,
                                            package=package_,
                                            package_selected_buyer=package_selected_buyer_,
                                            elec_demand_buyer=elec_demand_buyer_,
                                            generator_params=generator_params_,
                                            generator_overhaul=generator_overhaul_)
    result = model.get_data_result()
    print(result)
    # # import json
    # # with open('0_result.json', 'w') as file:  # 导出文件名：data.json
    # #     file.write(json.dumps(result,  # 需要导出的字典
    # #                           indent=4,  # 缩进4空格
    # #                           ensure_ascii=False))  # 中文字符不转义成Unicode
