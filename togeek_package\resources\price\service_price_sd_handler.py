"""
Author: Laney
Datetime: 2022/12/29/029 11:20
Info:
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.service.price.shandong import DataPredSD


class DataPredHandlerServiceCommonSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        date_bidding_start = params.pop('date_bidding_start')   # 竞价日起始日期
        date_bidding_end = params.pop('date_bidding_end')       # 竞价日结束日期
        method_acc = params.pop('method_acc', 1)       # 表示准确率计算方法，默认为1. 可选：[1, 2]， 1表示常用价格预测准确率，2表示国华计算方法

        m = DataPredSD(date_bidding_start=date_bidding_start, date_bidding_end=date_bidding_end, method_acc=method_acc)

        # 预测结果
        data_pred, data_acc = m.predict()

        # 控制填充: json格式中不能存在空值
        data_pred = data_pred.drop("价差方向相同", axis=1).fillna("")
        data_acc = data_acc.fillna("")

        data_pred_json = {
            "data_pred": data_pred.to_dict("record"),
            "method_acc": method_acc,
            "data_acc": data_acc.to_dict("record")
        }
        self.write(data_pred_json)
