import pandas as pd
import numpy as np
import xgboost as xgb
from datetime import timedelta
import json


class XGBLoadPredict:
    def __init__(self, load_data, weather_data):
        """
        初始化模型，接受列式字典格式的数据

        :param load_data: {'timestamp': [...], 'load': [...]}
        :param weather_data: {'timestamp': [...], 't_2m': [...], ...}
        """
        self.load_data = load_data
        self.weather_data = weather_data
        self.model = None
        self.df = None  # 用于保存合并后的 DataFrame

        # 特征列表（含天气特征）
        self.features = [
            'time_of_day', 'day_of_year', 'is_weekend',
            't_2m', 'rh_2m', 'tp_surface',
            'dswrf_surface', 'tcc_atmo',
        ]

    def _dict_to_dataframe(self):
        """将 column-oriented 字典转换为 DataFrame 并合并"""
        # 构建负荷数据 DataFrame
        load_df = pd.DataFrame({
            'timestamp': pd.to_datetime(self.load_data['timestamp']),
            'load': self.load_data['load']
        })

        # 构建天气数据 DataFrame
        weather_dict = {k: v for k, v in self.weather_data.items() if k != 'timestamp'}
        weather_df = pd.DataFrame({
            'timestamp': pd.to_datetime(self.weather_data['timestamp']),
            **{k: pd.Series(v) for k, v in weather_dict.items()}
        })

        # 合并数据
        df = pd.merge(load_df, weather_df, on='timestamp', how='outer')
        return df

    def preprocess_data(self, D, test_days=1):
        """
        预处理：构造特征、划分训练集 & 测试集

        :param D: 预测开始日期
        :param test_days: 预测天数
        :return: X_train, X_test, y_train
        """
        df = self._dict_to_dataframe()
        self.df = df  # 保留原始 df 供 predict 使用

        # 构造时间特征
        df['time_of_day'] = (df['timestamp'].dt.hour * 12) + (df['timestamp'].dt.minute // 5) + 1
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['is_weekend'] = df['timestamp'].dt.dayofweek.ge(5).astype(int)

        # 确保所有特征存在且无缺失
        valid_features = [f for f in self.features if f in df.columns]
        X = df[valid_features].copy()
        y = df['load']

        # 时间切分
        D = pd.to_datetime(D)
        test_end = D + pd.Timedelta(days=test_days)

        train_mask = df['timestamp'] < D
        valid_train_mask = train_mask & X[train_mask].notna().all(axis=1) & y[train_mask].notna()

        test_mask = (df['timestamp'] >= D) & (df['timestamp'] < test_end)

        X_train = X[valid_train_mask]
        y_train = y[valid_train_mask]
        X_test = X[test_mask]


        return X_train, X_test, y_train

    def train_model(self, X_train, y_train):
        """训练 XGBoost 模型"""
        self.model = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=5,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        self.model.fit(X_train, y_train)

    def predict(self, X_test):
        """返回列式字典结构：{'timestamp': [...], '预测值': [...]}"""
        y_pred = self.model.predict(X_test)

        # 提取 timestamp 列并转换为字符串格式列表
        timestamps = self.df.loc[X_test.index, 'timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()

        # 构造列式字典输出
        result = {
            'timestamp': timestamps,
            'load': [round(float(p), 2) for p in y_pred.tolist()]
        }

        return result

    def run(self, D, test_days=1):
        """执行整个流程，返回 {'timestamp': [...], '预测值': [...]}"""
        X_train, X_test, y_train = self.preprocess_data(D=D, test_days=test_days)
        self.train_model(X_train, y_train)
        predictions = self.predict(X_test)
        return predictions


if __name__ == "__main__":
    import os
    import json

    # 设置预测日期
    D = '2025-04-18'
    test_days = 1

    # 文件路径
    load_file = 'load_data.csv'
    weather_file = 'weather_data.csv'

    # 检查文件是否存在
    if not os.path.exists(load_file):
        raise FileNotFoundError(f"找不到负荷文件: {load_file}")
    if not os.path.exists(weather_file):
        raise FileNotFoundError(f"找不到天气文件: {weather_file}")

    # 读取负荷数据
    load_df = pd.read_csv(load_file)
    load_data = {
        'timestamp': load_df['Timestamp'].tolist(),
        'load': load_df['Load'].astype(float).tolist()
    }

    # 读取天气数据
    weather_df = pd.read_csv(weather_file)
    weather_data = {
        'timestamp': weather_df['t_datetime_cst'].tolist()
    }
    for col in weather_df.columns:
        if col != 't_datetime_cst':
            weather_data[col] = weather_df[col].astype(float).tolist()

    # ========== 构建输入字典，将 D 和 test_days 放在最前面 ==========
    input_all = {
        "D": D,
        "test_days": test_days,
        "load": load_data,
        "weather": weather_data
    }

    # 保存输入数据到 JSON 文件
    with open("input_column_oriented.json", "w", encoding="utf-8") as f:
        json.dump(input_all, f, ensure_ascii=False, indent=4)

    print("✅ 输入数据已保存为 input_column_oriented.json")

    # -----------进行预测------------
    forecaster = XGBLoadPredict(load_data=load_data, weather_data=weather_data)
    predictions = forecaster.run(D=D, test_days=test_days)

    print("🔮 预测结果：")
    for t, p in zip(predictions['timestamp'], predictions['load']):
        print(f"{t}: {p:.2f}")

    # ========== 保存预测结果到另一个 JSON 文件 ==========
    with open("output_predictions_column_oriented.json", "w", encoding="utf-8") as f:
        json.dump(predictions, f, ensure_ascii=False, indent=4)

    print("✅ 预测结果已保存为 output_predictions_column_oriented.json")