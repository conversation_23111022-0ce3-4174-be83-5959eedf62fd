# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_operation_capacity import OperationCapacity


class OperationCapacityHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        df = params.pop('operation_capacity')
        yuce_list = params.pop('yuce_list')
        p = OperationCapacity(operation_capacity=df, yuce_list=yuce_list)
        pred = p.predict(to_json=True)
        self.write(pred)

