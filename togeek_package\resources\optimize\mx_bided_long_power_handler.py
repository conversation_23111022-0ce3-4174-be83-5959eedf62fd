#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/5/24 15:51
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_longpower import MxLongBidedPower


class MxBidedLongPowerHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        prices = params.pop('prices')
        costs = params.pop('costs')
        model = MxLongBidedPower(generators=generators, prices=prices, costs=costs)
        result = model.predict()
        self.write(result)