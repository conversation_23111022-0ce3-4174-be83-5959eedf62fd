from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.mengxi import XGBMengxiNewenergyLoadPredict

class XGBMengxiNewenergyLoadPredictHandler(RequestHandlerBase):
    def put(self):

        params = j2o(self.request.body.decode())
        load_data = params.pop('load')
        weather_data = params.pop('weather')
        D = params.pop('D')
        test_days = params.pop('test_days')

        predictor = XGBMengxiNewenergyLoadPredict(load_data=load_data, weather_data=weather_data)
        result_dict = predictor.run(D=D, test_days=test_days)

        self.write(result_dict)