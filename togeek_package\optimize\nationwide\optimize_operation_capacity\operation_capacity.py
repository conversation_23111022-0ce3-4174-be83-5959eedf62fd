#!/usr/bin/env python
# coding: utf-8

import pandas as pd
from prophet import Prophet
from datetime import timedelta
import logging

logger = logging.getLogger()


class OperationCapacity:
    def __init__(self, operation_capacity, yuce_list):
        logger.info("---------------------火电机组运行容量-------------------------")
        self.operation_capacity = pd.DataFrame(operation_capacity)
        self.yuce_list = yuce_list

    def data_process(self):
        # 数据预处理，删除空值
        self.yuce_list.sort()
        yuce_list = self.yuce_list
        self.operation_capacity['date_time'] = self.operation_capacity['date_time'].astype('str')
        self.operation_capacity.sort_values('date_time', ascending=True)
        operation_capacity = self.operation_capacity.dropna()
        operation_capacity.columns = ['ds', 'y']
        return operation_capacity, yuce_list

    def predict(self, to_json=True):
        """
        使用传入数据的最后30天作为训练数据，要求至少有两天传入数据
        对结果进行极值修正: 最大值不超过传入历史数据最大值的10%，最小值不小于传入历史数据最小值的90%
        """
        data_input, yuce_list = self.data_process()
        floor = data_input['y'].min() * 0.9  # 最小值限制
        cap = data_input['y'].max() * 1.1    # 最大值限制
        data_input['floor'] = floor
        data_input['cap'] = cap

        days_train = 30  # 使用30天历史数据作为训练数据
        data_train = data_input.iloc[-days_train:, :]  # 最后days_train行，所有列
        if data_train.shape[0] < 2:
            raise ValueError('传入的有效历史数据少于2天,请检查！')
        logger.info(f'训练数据: \n{data_train}')
        logger.info(f'预测列表：{yuce_list}')

        future = pd.DataFrame({'ds': yuce_list})
        future['floor'] = floor
        future['cap'] = cap

        model = Prophet(growth='logistic')
        model.fit(data_train)
        res = model.predict(future)
        res = res[['ds', 'yhat']]
        # 负值修正
        res['yhat'] = res['yhat'].map(lambda s: floor if s <= 0 else s)
        res['ds'] = res['ds'].astype('str')
        res.columns = ['date_time', 'pred_capacity']

        if to_json:
            result = {'date_time': res['date_time'].to_list(),
                      'pred_capacity': res['pred_capacity'].to_list()}
        else:
            result = res
        logger.info(f'result: \n{res}')
        logger.info("---------------------火电机组运行容量 预测完成-------------------------")
        return result


if __name__ == '__main__':
    df = pd.read_excel(r'D:\ToGeek\work\model_data\optimize\operation_capacity\operation_capacity.xlsx', sheet_name='train')
    yuce_list1 = ['2021-10-28', '2021-10-29', '2021-10-30', '2021-10-31']
    p = OperationCapacity(operation_capacity=df, yuce_list=yuce_list1)
    pred = p.predict(to_json=False)
    print(pred)
