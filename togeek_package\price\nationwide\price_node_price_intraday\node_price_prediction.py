# -*- coding: utf-8 -*-
# valid license

import GPy
import numpy as np


class NodePricing:
    def __init__(self, train_x, train_y):
        self.train_model(train_x, train_y)

    def handle_data(self, x):
        x = np.asarray(x)
        # train_x保持不变，train_y转为二维一列数据
        return x if x.ndim == 2 else x.reshape(-1, 1)

    def train_model(self, train_x, train_y):
        train_x = self.handle_data(train_x)
        train_y = self.handle_data(train_y)
        input_dim = train_x.shape[1]
        k = GPy.kern.RBF(input_dim, lengthscale=0.01) + GPy.kern.Bias(input_dim)
        self.model = GPy.models.GPRegression(train_x, train_y, k)
        self.model.optimize()

    def predict(self, test_x):
        test_x = self.handle_data(test_x)
        return np.c_[self.model.predict(self.handle_data(test_x))]


class Prediction:
    def __init__(self, train, pred):
        self.train_price = np.asarray(train['price'])
        self.train_coal_p = np.asarray(train['coal_p'])
        self.train_gas_p = np.asarray(train['gas_p'])
        self.train_max_t = np.asarray(train['max_t'])
        self.train_min_t = np.asarray(train['min_t'])
        self.pred_coal_p = np.asarray(pred['coal_p'])
        self.pred_gas_p = np.asarray(pred['gas_p'])
        self.pred_max_t = np.asarray(pred['max_t'])
        self.pred_min_t = np.asarray(pred['min_t'])

    def predict(self):
        pred_y = []
        for train_y in self.train_price.T:
            # train_x = np.c_[self.train_coal_p, self.train_gas_p, self.train_max_t, self.train_min_t]
            train_x = np.c_[self.train_max_t, self.train_min_t]
            # pred_x = np.c_[self.pred_coal_p, self.pred_gas_p, self.pred_max_t, self.pred_min_t]
            pred_x = np.c_[self.pred_max_t, self.pred_min_t]
            pred = NodePricing(train_x, train_y)    # 训练模型
            pred_y.append(pred.predict(pred_x).reshape([-1, 1, 2]))
        y = np.concatenate(pred_y, axis=1)
        y[:, :, 1] = y[:, :, 1] ** 0.5
        return y


if __name__ == '__main__':
    x = np.random.uniform(-10, 20, 50)
    y = 10. + .1 * x + 2 * np.sin(x) / x + np.random.normal(0, .2, x.size)
    x = x.reshape(-1, 1)
    train = {'price': np.tile(y[:, None], [1, 4]), 'coal_p': x, 'gas_p': x, 'max_t': x, 'min_t': x}
    pred = {'coal_p': x[:3], 'gas_p': x[:3], 'max_t': x[:3], 'min_t': x[:3]}
    p = Prediction(train, pred)
    print(p.predict())
