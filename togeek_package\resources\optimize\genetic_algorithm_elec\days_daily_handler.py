# -*- coding: utf-8 -*-
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.nationwide.optimize_subsection_declaration_elec.SubsectionGA_period import GeneratorInfoDaysDaily, GADaysDaily


class DaysDailyElecHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        generators = params.pop('generators')
        costs = params.pop('costs')
        prices = params.pop('prices')
        elecs = params.pop('elecs')
        func_mode = params.pop('func_mode', 'profit')
        subsection = params.pop('subsection', 10)
        size_pop = params.pop('size_pop', 1000)
        max_iter = params.pop('max_iter', 8)
        precision = params.pop('precision', 0.1)
        generator = GeneratorInfoDaysDaily(generators=generators, costs=costs, prices=prices, elecs=elecs,
                                  func_mode=func_mode, subsection=subsection, max_iter=max_iter, precision=precision)

        lb = generator.lb
        ub = generator.ub
        n_dim = generator.n_dim

        # 约束条件：最小发电量约束
        # def constraint_min_power(elec):
        #     return generator.min_elec - sum(elec)
        #
        # # 约束条件：最大发电量约束
        # def constraint_max_power(elec):
        #     return sum(elec) - generator.max_elec

        model = GADaysDaily(func=generator.func, n_dim=n_dim, generator=generator, size_pop=size_pop, max_iter=max_iter, lb=lb, ub=ub,
                constraint_ueq=(generator.constraint_max_power, generator.constraint_min_power),
                   precision=precision)

        result = model.run()
        self.write(result)
