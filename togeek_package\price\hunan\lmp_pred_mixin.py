
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
# import os.path
import logging
logger = logging.getLogger()
# # logger全部输出到控制台
# logger.setLevel(logging.DEBUG) 

"""
用于预测日前价格/负荷
数据处理：1. 所有日前边界 2. 气象数据
数据聚合：1. 按小时聚合 2. 按天聚合
"""

REAL_IDX_NAME_LIST = [
    # real
    "新能源实际负荷",
    "系统实际负荷",
    "水电含抽蓄实际负荷",
    "系统实际发电负荷",
    "外送电实际负荷",
]

PRICE_IDX_NAME_LIST = [
   "实时节点出清电价",
   "日前节点出清电价", 
]

DEF_IDX_NAME_LIST = [
    "新能源预测负荷",
    "分省可再生能源发电能力预测",
    "系统预测负荷",
    "外来电预测负荷(祁韶直流)",
    "外来电预测负荷(主网送湘)",
    "外来电预测负荷",  # FORMULA 外来电预测负荷=外来电预测负荷(祁韶直流)+外来电预测负荷(主网送湘)
    "非市场化电源预测出力",
    "火电竞价空间预测",  # FORMULA 火电竞价空间预测=系统预测负荷-非市场化电源预测出力-外来电预测负荷-新能源预测负荷
    "火电运行容量-湖南",
    "火电运行机组台数-湖南",
    "火电运行容量(百分比)-湖南",  # FORMULA
   
    "发电机组检修容量-湖南",
    
    "省间购入日前出清电价",
    "省间购入日前出清电量",
    "省间售出日前出清电价",
    "省间售出日前出清电量",
    
] + REAL_IDX_NAME_LIST +PRICE_IDX_NAME_LIST

REAL_FEATS_LIST=[
    "新能源实际负荷",
    "系统实际负荷",
    "水电含抽蓄实际负荷",
    "系统实际发电负荷",
    "外送电实际负荷",
    "实时火电竞价空间",

    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    # "holiday",
    "real_price_D1",
    "real_price_D1_mean",
    "real_price_D1_std",
    "real_price_D1_median",
    'solar_unit',
]

AHEAD_FEATS_LIST=[
    "新能源预测负荷",
    "分省可再生能源发电能力预测",
    "系统预测负荷",
    "外来电预测负荷(祁韶直流)",
    "外来电预测负荷(主网送湘)",
    # "外来电预测负荷",  # FORMULA 外来电预测负荷=外来电预测负荷(祁韶直流)+外来电预测负荷(主网送湘)
    "非市场化电源预测出力",
    # "火电竞价空间预测",  # FORMULA 火电竞价空间预测=系统预测负荷-非市场化电源预测出力-外来电预测负荷-新能源预测负荷
    "火电运行容量-湖南",
    "火电运行机组台数-湖南",
    # "火电运行容量(百分比)-湖南",  # FORMULA
    
    "预测出力",
    "外来电预测负荷",
    "火电竞价空间预测",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    # "holiday",
    "ahead_price_D1",
    "ahead_price_D1_mean",
    "ahead_price_D1_std",
    "ahead_price_D1_median",
    'solar_unit',
    
]

DEF_FEATURES = [
    "新能源实际负荷",
    "系统实际负荷",
    "水电含抽蓄实际负荷",
    "系统实际发电负荷",
    "外送电实际负荷",
    
    "新能源预测负荷",
    "分省可再生能源发电能力预测",
    "系统预测负荷",
    "外来电预测负荷(祁韶直流)",
    "外来电预测负荷(主网送湘)",
    # "外来电预测负荷",  # FORMULA 外来电预测负荷=外来电预测负荷(祁韶直流)+外来电预测负荷(主网送湘)
    "非市场化电源预测出力",
    # "火电竞价空间预测",  # FORMULA 火电竞价空间预测=系统预测负荷-非市场化电源预测出力-外来电预测负荷-新能源预测负荷
    "火电运行容量-湖南",
    "火电运行机组台数-湖南",
    # "火电运行容量(百分比)-湖南",  # FORMULA
    
    "预测出力",
    # "预测新能源占比",
    "实时火电竞价空间",
    # "火电竞价空间误差",
    "外来电预测负荷",
    "火电竞价空间预测",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "period",
    # "holiday",
    "ahead_price_D1",
    "ahead_price_D1_mean",
    "ahead_price_D1_std",
    "ahead_price_D1_median",
    "real_price_D1",
    "real_price_D1_mean",
    "real_price_D1_std",
    "real_price_D1_median",
    'solar_unit',
]


class DataFetcherMixin:

    def _data(self, D: str, df):
        """
        获取基础数据
        :param D: 交易日
        :param df: 基础边界数据及价格数据
        :return: X、y，都是pd.DataFrame
        """
        # 运行日
        run_day = datetime.strptime(D, "%Y-%m-%d") + timedelta(days=1)
        run_day_str = datetime.strftime(run_day, "%Y-%m-%d")
        Year, Month, Day = D.split("-")
        Year, Month, Day = int(Year), int(Month), int(Day)
        # df=pd.read_json(df)
        df = pd.DataFrame.from_dict(df)
        
        df["dateTime"] = pd.to_datetime(df["dateTime"])
        df=df.set_index("dateTime")
        df = self._to_hourly(df)

        # 删除除dateTime外全为空的行
        # 定义需要检查的列（除 dateTime 外的所有列）
        columns_to_check = df.columns.drop("dateTime")
        # 创建一个布尔掩码，指示哪些行在检查的列中全为空
        all_empty_mask = df[columns_to_check].isna().all(axis=1)
        df = df[~all_empty_mask].copy()
        
        df["date"] = df["dateTime"].dt.date.astype(str)
        df["hour"] = df["dateTime"].dt.hour
        
        logger.info("[INFO] 数据列包括：" + str(df.columns))
        
        # 选取训练和测试数据
        df = df[df["date"] <= run_day_str]
        
        df = self._with_features(df)

        # start_day=datetime.strptime(D, "%Y-%m-%d") - timedelta(days=7)
        # start_day_str = datetime.strftime(start_day, "%Y-%m-%d")
        # mask=(df["date"] >= start_day_str) & (df["date"] < run_day_str)  # 组合条件
        # train = df[mask]
        train = df[df["date"] < run_day_str]
        test = df[df["date"] == run_day_str]

        # 筛选出缺失值比例大于等于 30% 的列
        missing_ratio = train.isna().mean()
        selected_columns = missing_ratio[missing_ratio < 0.3].index
        # 根据筛选结果保留相应的列
        train = train[selected_columns]
        delete_columns = missing_ratio[missing_ratio >= 0.3].index
        if "日前节点出清电价" in delete_columns:
            return ValueError("日前节点出清电价缺失值大于30%，无法预测")
        if "实时节点出清电价" in delete_columns:
            return ValueError("实时节点出清电价缺失值大于30%，无法预测")
        # 输出缺失值大于30%的列名
        if not missing_ratio[missing_ratio >= 0.3].empty:
            logger.warning("[WARNING] 训练数据中缺失值大于30%的列：" + str(missing_ratio[missing_ratio >= 0.3].index))
        train = train.dropna(axis=1, how="all")

        # 筛选值全部相同的列
        unique_values = train.nunique()
        constant_columns = unique_values[unique_values == 1].index.tolist()
        if constant_columns:
            logger.warning("[WARNING] 训练数据中值全部相同的列有：" + str(constant_columns))
            # 删除值全部相同的列
            train = train.drop(columns=constant_columns)
        # 填充缺失值
        for col in train.columns:
            train[col] = train.apply(
                lambda row: self.fill_missing_values(col, row, train) if pd.isna(row[col]) else row[col], axis=1
            )
            
        test = test[train.columns]

        if test.empty:
            raise ValueError("no test data")

        return train, test


    def fill_missing_values(self, col, row, df):
        current_hour = row["hour"]
        current_date = row["dateTime"]
        # 计算 7 天前的日期
        seven_days_ago = current_date - pd.Timedelta(days=7)
        # 筛选出过去 7 天同一小时的数据
        historical_data = df[
            (df["dateTime"] >= seven_days_ago) & (df["dateTime"] < current_date) & (df["hour"] == current_hour)
        ][col]

        # 若历史数据存在，则取平均值填充，否则保留缺失值
        if not historical_data.empty:
            return historical_data.mean()

        return row[col]

    def _to_hourly(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.resample("h").mean().reset_index()


    def _with_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特征工程
        必须字段：dateTime, 日前节点出清电价, 实时节点出清电价
        """
        df=df.copy()
        # 节假日特征

        # 定义特征及其依赖字段的映射关系
        power_features = {
            "实时火电竞价空间": ["系统实际负荷", "外送电实际负荷", "新能源实际负荷"],
            "外来电预测负荷": ["外来电预测负荷(祁韶直流)", "外来电预测负荷(主网送湘)"],
            "火电竞价空间预测": ["系统预测负荷", "非市场化电源预测出力", "外来电预测负荷(祁韶直流)","外来电预测负荷(主网送湘)", "新能源预测负荷"],
            # "火电竞价空间误差": ["实时火电竞价空间", "火电竞价空间预测"],
            "日前价格>实时价格": ["日前节点出清电价", "实时节点出清电价"],
            # "预测新能源占比": ["新能源预测负荷", "预测出力"],
            "预测出力": ["非市场化电源预测出力", "新能源预测负荷"],
        }
        for feature_name, required_cols in power_features.items():
            if all(col in df.columns for col in required_cols):
                if feature_name == "实时火电竞价空间":
                    df[feature_name] = df["系统实际负荷"] + df["外送电实际负荷"] - df["新能源实际负荷"]
                elif feature_name == "外来电预测负荷":
                    df[feature_name] = df["外来电预测负荷(祁韶直流)"] + df["外来电预测负荷(主网送湘)"]
                elif feature_name == "火电竞价空间预测":
                    df[feature_name] = df["系统预测负荷"] - df["非市场化电源预测出力"] - df["外来电预测负荷(祁韶直流)"] - df["外来电预测负荷(主网送湘)"] - df["新能源预测负荷"]
                elif feature_name == "日前价格>实时价格":
                    df[feature_name] = np.where(df["日前节点出清电价"] > df["实时节点出清电价"], 1, 0)
                elif feature_name == "预测出力":
                    df[feature_name] = df["非市场化电源预测出力"] + df["新能源预测负荷"]
            else:
                missing_cols = [col for col in required_cols if col not in df.columns]
                logger.warning(f"跳过特征 [{feature_name}]，缺失必要字段: {missing_cols}")
                

        # 时间特征
        df["hour"] = df["dateTime"].dt.hour
        df["dayofweek"] = df["dateTime"].dt.dayofweek
        df["hour_sin"] = np.sin(2 * np.pi * df["hour"] / 24)
        df["hour_cos"] = np.cos(2 * np.pi * df["hour"] / 24)
        df["dayofweek_sin"] = np.sin(2 * np.pi * df["dayofweek"] / 7)
        df["dayofweek_cos"] = np.cos(2 * np.pi * df["dayofweek"] / 7)
        # df['dayofyear']=df['dateTime'].dt.dayofyear

        # 时段特征：高峰时段 ：17 时 - 23 时-3。平段时段 ：6 时 - 12 时，15 时 - 17 时，23 时 - 次日 0 时-2。低谷时段 ：0 时 - 6 时，12 时 - 15 时-1
        df["period"] = df["hour"].apply(lambda x: 1 if 17 < x <= 23 else 2 if 6 < x <= 12 or 15 < x <= 17 else 3)

        # 时序统计特征
        # 实时特征
        df['real_price_D1'] = df['实时节点出清电价'].shift(24)  # 前一天同时刻
        df['real_price_D1_mean'] = df['real_price_D1'].rolling(
            24).mean()  # 前一天统计特征
        df['real_price_D1_std'] = df['real_price_D1'].rolling(24).std()
        df['real_price_D1_median'] = df['real_price_D1'].rolling(24).median()

       
        # 日前特征
        df['ahead_price_D1'] = df['日前节点出清电价'].shift(24)  # 前一天同时刻
        df['ahead_price_D1_mean'] = df['ahead_price_D1'].rolling(
            24).mean()  # 前一天统计特征
        df['ahead_price_D1_std'] = df['ahead_price_D1'].rolling(24).std()
        df['ahead_price_D1_median'] = df['ahead_price_D1'].rolling(24).median()
        
        # 太阳能开机台数
        df['solar_unit'] = df['hour'].apply(
            lambda x: 0 if x in [6, 19] else (1 if x in range(7, 19) else 2))

        return df

    # def _with_holiday_features(self, df: pd.DataFrame) -> pd.DataFrame:
    #     # 工作日和节假日
    #     start_date, end_date = df["dateTime"].min(), df["dateTime"].max()

    #     start_day = int(start_date.strftime("%Y%m%d"))
    #     end_day = int((end_date + timedelta(days=1)).strftime("%Y%m%d"))
    #     date_type = typical_day(start_day, end_day)

    #     date_type["date"] = pd.to_datetime(date_type["day"], format="%Y%m%d")
    #     date_type["date"] = date_type["date"].astype(str)

    #     type_mapping = {
    #         "调休节假日": 0,
    #         "周一": 1,
    #         "周二": 2,
    #         "周三": 3,
    #         "周四": 4,
    #         "周五": 5,
    #         "周六": 6,
    #         "周日": 7,
    #         "调休工作日": 8,
    #         "法定节假日": 9,
    #     }
    #     date_type["day_type"] = date_type["type"].map(type_mapping)
    #     date_type = date_type[["date", "day_type"]]

    #     df["date"] = df["dateTime"].dt.date.astype(str)
    #     df = pd.merge(df, date_type, on="date", how="left")
    #     df["day_type"] = df["day_type"].astype(int)
    #     df["holiday"] = df["day_type"].apply(lambda x: 1 if x in [0, 6, 7, 9] else 0)  # 节假日：1，工作日：0

    #     return df


if __name__ == "__main__":
    # 测试
    data_fetcher = DataFetcherMixin()
    # file_path=r"D:\fwx\gitlab\algorithms\lab\fanwenxuan\hunan\excel\df\11月.xlsx"
    # data=pd.read_excel(file_path)
    # print(data.list())
    # df = data_fetcher._data("2025-06-11",data)
     
    