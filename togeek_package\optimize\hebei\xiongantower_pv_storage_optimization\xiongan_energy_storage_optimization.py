import pandas as pd
import numpy as np
from pyscipopt import Model, quicksum
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle


class EnergyStorageOptimizer:
    def __init__(
            self,
            load_data_predicted: dict = None,
            solar_data_predicted: dict = None,
            price_data: dict = None,
            day_lens_for_optim: int = 1,
            dt: float = 5 / 60,
            discharge_efficiency: float = 0.9,
            charge_efficiency: float = 0.9,
            ess_capacity: float = 60.0,  # kWh
            charge_power_max: float = 10,
            charge_power_min: float = 0,
            discharge_power_max: float = 10,
            discharge_power_min: float = 0,
            soc_initial: float = 50.0,  # 百分比
            soc_min: float = 11.0,
            soc_max: float = 98.0,
            grid_power_max: float = 500.0,
    ):
        """
        初始化储能优化器

        :param load_data_predicted: 负载预测数据
        :param solar_data_predicted: 光伏预测数据
        :param price_data: 电价数据
        :param day_lens_for_optim: 优化天数（默认1天）
        :param dt: 时间间隔（小时），默认5分钟=5/60=0.0833
        :param discharge_efficiency: 放电效率
        :param charge_efficiency: 充电效率
        :param ess_capacity: 储能容量（kWh）
        :param charge_power_max: 最大充电功率
        :param charge_power_min: 最小充电功率
        :param discharge_power_max: 最大放电功率
        :param discharge_power_min: 最小放电功率
        :param soc_initial: 初始SOC（百分比）
        :param soc_min: SOC下限
        :param soc_max: SOC上限
        :param grid_power_max: 电网最大功率限制
        """
        self.load_data_predicted = load_data_predicted
        self.solar_data_predicted = solar_data_predicted
        self.price_data = price_data
        self.day_lens_for_optim = day_lens_for_optim
        self.num_intervals = self.day_lens_for_optim * 288  # 5分钟间隔
        self.dt = dt
        self.discharge_efficiency = discharge_efficiency
        self.charge_efficiency = charge_efficiency
        self.ess_capacity = ess_capacity
        self.charge_power_max = charge_power_max
        self.charge_power_min = charge_power_min
        self.discharge_power_max = discharge_power_max
        self.discharge_power_min = discharge_power_min
        self.soc_initial = soc_initial
        self.soc_min = soc_min
        self.soc_max = soc_max
        self.grid_power_max = grid_power_max
        # self.output_path = output_path

        self.model = None
        self.df_results = None
        self.ess_profit = None

        # 加载数据
        self._load_data()

    def _load_data(self):
        """加载负荷、光伏和电价数据，timestamp是要预测、优化的目标当天的时间"""

        # 解析 load_data_predicted 字典
        df_load = pd.DataFrame({
            'timestamp': pd.to_datetime(self.load_data_predicted['timestamp']),
            'load': self.load_data_predicted['load']
        })
        df_load = df_load.sort_values('timestamp')
        self.load_power = df_load['load'].values[:self.num_intervals]
        self.timestamps = df_load['timestamp'][:self.num_intervals]

        # 解析 solar_data_predicted 字典
        df_solar = pd.DataFrame({
            'timestamp': pd.to_datetime(self.solar_data_predicted['timestamp']),
            'solar': self.solar_data_predicted['solar']
        })
        df_solar = df_solar.sort_values('timestamp')
        self.solar_power = df_solar['solar'].values[:self.num_intervals]

        # 解析 price_data 字典
        df_price = pd.DataFrame({
            'timestamp': pd.to_datetime(self.price_data['timestamp']),
            'price': self.price_data['price']
        })
        df_price = df_price.sort_values('timestamp')
        self.electricity_price = df_price['price'].values[:self.num_intervals]

    def build_model(self):
        """构建混合整数规划模型"""
        model = Model("EnergyStorageOptimization")
        model.hideOutput()

        T = range(self.num_intervals)

        # 决策变量存储字典
        self.vars = {
            'soc': {},
            'charge_power': {},
            'discharge_power': {},
            'u_d': {},
            'u_c': {},
            'grid_positive_power': {},
            'grid_negative_power': {},
            'u_p': {},
            'u_n': {},
        }

        # 创建变量
        for t in T:
            # SOC 百分比
            self.vars['soc'][t] = model.addVar(lb=self.soc_min, ub=self.soc_max, name=f"soc[{t}]")
            # 充放电功率
            self.vars['charge_power'][t] = model.addVar(lb=0, name=f"charge_power[{t}]")
            self.vars['discharge_power'][t] = model.addVar(lb=0, name=f"discharge_power[{t}]")
            # 充放电互斥二进制变量
            self.vars['u_d'][t] = model.addVar(vtype="B", name=f"u_d[{t}]")
            self.vars['u_c'][t] = model.addVar(vtype="B", name=f"u_c[{t}]")
            # 电网正逆流
            self.vars['grid_positive_power'][t] = model.addVar(lb=0, name=f"grid_positive_power[{t}]")
            self.vars['grid_negative_power'][t] = model.addVar(lb=0, name=f"grid_negative_power[{t}]")
            self.vars['u_p'][t] = model.addVar(vtype="B", name=f"u_p[{t}]")
            self.vars['u_n'][t] = model.addVar(vtype="B", name=f"u_n[{t}]")

        # 表达式：ESS 功率
        self.vars['ess_power'] = {}
        for t in T:
            self.vars['ess_power'][t] = self.vars['discharge_power'][t] - self.vars['charge_power'][t]

        # 表达式：总成本（仅考虑电网购电）
        total_cost_expr = quicksum(
            self.vars['grid_positive_power'][t] * self.dt * self.electricity_price[t]
            for t in T
        )
        self.vars['total_cost'] = model.addVar(name="total_cost")
        model.addCons(self.vars['total_cost'] == total_cost_expr)

        # 目标函数：最小化 total_cost - 惩罚项（SOC）
        model.setObjective(
            self.vars['total_cost'] - quicksum(0.001 * self.vars['soc'][t] for t in T),
            sense="minimize"
        )

        # SOC 更新约束
        for t in T:
            if t == 0:
                model.addCons(self.vars['soc'][t] == self.soc_initial)
            else:
                charge_energy_kWh = self.vars['charge_power'][t] * self.dt * self.charge_efficiency
                discharge_energy_kWh = self.vars['discharge_power'][t] * self.dt / self.discharge_efficiency
                delta_soc = ((charge_energy_kWh - discharge_energy_kWh) / self.ess_capacity) * 100
                model.addCons(self.vars['soc'][t] == self.vars['soc'][t - 1] + delta_soc)

        # 充放电互斥约束
        for t in T:
            model.addCons(self.vars['u_d'][t] + self.vars['u_c'][t] <= 1)

        # 充电功率上下限
        for t in T:
            model.addCons(self.vars['charge_power'][t] >= self.charge_power_min * self.vars['u_c'][t])
            model.addCons(self.vars['charge_power'][t] <= self.charge_power_max * self.vars['u_c'][t])

        # 放电功率上下限
        for t in T:
            model.addCons(self.vars['discharge_power'][t] >= self.discharge_power_min * self.vars['u_d'][t])
            model.addCons(self.vars['discharge_power'][t] <= self.discharge_power_max * self.vars['u_d'][t])

        # 电网正逆流互斥
        for t in T:
            model.addCons(self.vars['u_p'][t] + self.vars['u_n'][t] <= 1)

        # 正流上下限
        for t in T:
            model.addCons(self.vars['grid_positive_power'][t] >= 0 * self.vars['u_p'][t])
            model.addCons(self.vars['grid_positive_power'][t] <= self.grid_power_max * self.vars['u_p'][t])

        # 逆流上下限
        for t in T:
            model.addCons(self.vars['grid_negative_power'][t] >= 0 * self.vars['u_n'][t])
            model.addCons(self.vars['grid_negative_power'][t] <= self.grid_power_max * self.vars['u_n'][t])

        # 功率平衡约束
        for t in T:
            model.addCons(
                self.solar_power[t] +
                self.vars['ess_power'][t] +
                self.vars['grid_positive_power'][t] -
                self.vars['grid_negative_power'][t]
                == self.load_power[t]
            )
            #将含有vars的表达式放在==前面，才能被正确识别为约束条件。若将常量self.load_power[t]放在==号前面，会被识别为布尔判断



        self.model = model

    def solve(self):
        """求解模型"""
        self.build_model()
        self.model.optimize()

        if self.model.getStatus() == "optimal":
            print("求解成功，找到最优解！")
            print("预期总费用：", self.model.getVal(self.vars['total_cost']))

            # 构造完整的 result_dict
            result_dict = {
                'Load_kW': self.load_power.tolist(),
                'Solar_power_kW': self.solar_power.tolist(),
                'Electricity_Price': self.electricity_price.tolist(),
                'ESS_Power_kW': [
                    round(self.model.getVal(self.vars['discharge_power'][t]) -
                          self.model.getVal(self.vars['charge_power'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'Grid_Power_kW': [
                    round(self.model.getVal(self.vars['grid_positive_power'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'Grid_Power_negative_kW': [
                    round(self.model.getVal(self.vars['grid_negative_power'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'SOC_percent': [
                    round(self.model.getVal(self.vars['soc'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'Charge_Power_kW': [
                    round(self.model.getVal(self.vars['charge_power'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'Discharge_Power_kW': [
                    round(self.model.getVal(self.vars['discharge_power'][t]), 2)
                    for t in range(self.num_intervals)
                ],
                'timestamp': self.timestamps.dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
            }

            # 保存到类属性中，供后续使用
            self.result_dict = result_dict

            # 筛选需要的字段返回给调用者
            selected_fields = ['timestamp', 'Load_kW', 'Solar_power_kW', 'ESS_Power_kW', 'SOC_percent']
            filtered_result = {key: result_dict[key] for key in selected_fields if key in result_dict}

            return filtered_result

        else:
            print("求解失败！")
            print(f"状态: {self.model.getStatus()}")
            return False

    # def plot_results(self):
    #     """根据列式字典结果绘制功率曲线、SOC曲线，并叠加电价时段背景阴影"""
    #
    #     # 提取数据
    #     timestamps = self.result_dict['timestamp']
    #     load_power = self.result_dict['Load_kW']
    #     solar_power = self.result_dict['Solar_power_kW']
    #     ess_power = self.result_dict['ESS_Power_kW']
    #     grid_power = self.result_dict['Grid_Power_kW']
    #     soc_percent = self.result_dict['SOC_percent']
    #     electricity_price = self.result_dict['Electricity_Price']
    #
    #     # 设置中文字体
    #     plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    #     plt.rcParams['axes.unicode_minus'] = False
    #
    #     # 转换时间戳格式
    #     hours = pd.to_datetime(timestamps)
    #
    #     fig, ax1 = plt.subplots(figsize=(18, 9))
    #     ax1.grid(True, linestyle='--', alpha=0.6, axis='y')
    #
    #     # 添加电价背景阴影
    #     prices = np.array(electricity_price)
    #
    #     unique_prices = np.unique(prices)
    #     if len(unique_prices) != 3:
    #         raise ValueError("电价数据必须恰好包含三种不同的价格值")
    #
    #     low_price, mid_price, high_price = sorted(unique_prices)
    #
    #     price_colors = {
    #         low_price: ('低电价', '#d4f4dd'),  # 浅绿
    #         mid_price: ('中电价', '#fff2cc'),  # 浅黄
    #         high_price: ('高电价', '#f4cccc')  # 浅红
    #     }
    #
    #     # 绘制背景区域
    #     current_period_start = hours[0]
    #     current_price = prices[0]
    #
    #     for i in range(1, len(prices)):
    #         if prices[i] != current_price:
    #             ax1.axvspan(current_period_start, hours[i - 1],
    #                         facecolor=price_colors[current_price][1],
    #                         edgecolor='none', alpha=0.5)
    #             current_period_start = hours[i]
    #             current_price = prices[i]
    #     # 填充最后一个时间段
    #     ax1.axvspan(current_period_start, hours[-1],
    #                 facecolor=price_colors[current_price][1],
    #                 edgecolor='none', alpha=0.5)
    #
    #     # 图例说明
    #     legend_patches = [
    #         Rectangle((0, 0), 1, 1, color=price_colors[p][1], label=price_colors[p][0])
    #         for p in sorted(price_colors.keys())
    #     ]
    #
    #     # 主图：功率曲线
    #     ax1.set_xlabel('时间', fontsize=16)
    #     ax1.set_ylabel('功率 (kW)', color='tab:blue', fontsize=16)
    #
    #     ax1.plot(hours, load_power, label='负载功率', linestyle='-', color='#0074D9')
    #     ax1.plot(hours, solar_power, label='光伏功率', linestyle='-', color='#2ECC40')
    #     ax1.plot(hours, ess_power, label='储能系统功率', linestyle='-', color='#FFDC00')
    #     ax1.plot(hours, grid_power, label='电网正向功率', linestyle='-', color='#FF4136')
    #
    #     ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    #     ax1.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    #     plt.xticks(rotation=45)
    #
    #     # 右侧轴：SOC
    #     ax2 = ax1.twinx()
    #     ax2.set_ylabel('SOC (%)', color='tab:orange', fontsize=16)
    #     soc_line, = ax2.plot(hours, soc_percent, label='SOC', linestyle='-', color='#B10DC9')
    #
    #     # 设置 SOC 轴从 0 开始
    #     ax2.set_ylim(bottom=0)
    #     soc_max_val = max(soc_percent) * 1.1
    #     ax2.set_ylim(0, soc_max_val)
    #
    #     # 合并图例
    #     lines1, labels1 = ax1.get_legend_handles_labels()
    #     lines2, labels2 = ax2.get_legend_handles_labels()
    #     all_lines = lines1 + lines2 + legend_patches
    #     all_labels = labels1 + labels2 + [p[0] for p in price_colors.values()]
    #
    #     ax1.legend(all_lines, all_labels, loc='upper left', fontsize=12)
    #
    #     plt.title(f'功率曲线、SOC曲线与电价时段 {hours[0].date()}预测值', fontsize=16)
    #     plt.tight_layout()
    #     plt.show()



# 示例 main 函数
# if __name__ == '__main__':
#
#     optimizer = EnergyStorageOptimizer(
#         load_predicted_path = "../data/output/predicted_load_data.csv",
#         solar_predicted_path = "../data/output/predicted_solar_data.csv",
#         price_path = "../data/input/price_data.csv",
#         soc_initial = 50
#     )
#     if optimizer.solve():
#         optimizer.plot_results()