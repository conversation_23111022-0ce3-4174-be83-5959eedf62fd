#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/8 15:09
# <AUTHOR> Dar<PERSON>

'''
    包头西节点4~8天价格预测
'''
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import ExtraTreesRegressor
import warnings
import logging

warnings.filterwarnings("ignore")
logger = logging.getLogger()

class ElPricePreLongtimeNode48:
    def __init__(self, history_data, pre_data, pre_date_list, min_value, max_value):
        logger.info("----------------------蒙西节点4~8天极端随机树ETR价格预测--------------------------")
        self.history_data, self.features = self._pre_data(history_data)
        self.pre_data = pd.DataFrame(pre_data)
        self.pre_date_list = pre_date_list
        self.min_value = min_value
        self.max_value = max_value

    def _pre_data(self, data):
        data = pd.DataFrame(data)
        if 'date_time' not in data.columns:
            data = data.reset_index()
            data = data.rename(columns={'index': 'date_time'})
        cols = ['date_time', 'date', '价格', 'city']
        features = [col for col in data.columns if col not in cols]
        return data, features

    def predict(self):
        '''
        Returns: 预测结果
        '''
        # 1、预测时间
        if 'date_time' not in self.pre_data.columns:
            self.pre_data = self.pre_data.reset_index()
            self.pre_data = self.pre_data.rename(columns={'index': 'date_time'})
        if 'date' not in self.history_data.columns:
            self.history_data['date'] = self.history_data['date_time'].map(lambda x: str(x).split(" ")[0])
        if 'date' not in self.pre_data.columns:
            self.pre_data['date'] = self.pre_data['date_time'].map(lambda x: str(x).split(" ")[0])
        self.history_data = self.history_data.set_index('date_time')
        self.pre_data = self.pre_data.set_index('date_time')
        data_pred_list = []
        # 2、循环预测
        for idx, date_pred in enumerate(self.pre_date_list, 1):
            date_start_train = str(pd.to_datetime(date_pred) - timedelta(50)).split(" ")[0]
            date_end_train = str(pd.to_datetime(date_pred) - timedelta(int(1))).split(" ")[0]
            data_train = self.history_data[
                (self.history_data['date'] >= date_start_train) & (self.history_data['date'] <= date_end_train)].dropna()
            data_test = self.pre_data[self.pre_data['date'] == date_pred]
            train_X, train_y = data_train[self.features], data_train[['价格']]
            test_X = data_test[self.features]
            df_pred = pd.DataFrame(index=data_test.index)

            # 数据标准化
            scaler_X = StandardScaler().fit(train_X)
            scaler_y = StandardScaler().fit(train_y)
            train_X_std = scaler_X.transform(train_X)
            train_y_std = scaler_y.transform(train_y)
            test_X_std = scaler_X.transform(test_X)
            etr = ExtraTreesRegressor(max_depth=10, n_estimators=50, random_state=24)
            etr.fit(train_X_std, train_y_std)
            pred_std = etr.predict(test_X_std).reshape(-1, 1)
            pred = scaler_y.inverse_transform(pred_std)  # 反向标准化
            # 当前模型预测结果保存
            df_pred[f'价格预测值'] = pred
            data_pred_list.append(df_pred)
            # 4. 回测结果整理
        data_pred = pd.concat(data_pred_list)

        return data_pred

if __name__ == '__main__':
    data = pd.read_csv(r"D:\02file\2023\05蒙西数据/data/0-包头天气数据48.csv").iloc[:, 1:]
    data = data.rename(columns={"wspd_surface": '地表风速', "wspd_10m": '10m风速', "wspd_80m": '80m风速',
                                "dswrf_surface": '向下短波辐射通量', "SUNSD_surface": '日照时长',
                                "albedo_surface": '反射率', 't_surface': '温度', 'uswrf_surface': '向上短波辐射通量',
                                'ulwrf_surface': '向上长波辐射通量', 'dlwrf_surface': '向下长波辐射通量'})
    data['date_time'] = data['date'].astype(str) + ' ' + data['time'].astype(str)
    data['date_time'] = pd.to_datetime(data['date_time']).astype(str)
    history_data = data[data['t'] == 1]
    pre_data = data[(data['t_date'] == '2023-09-11') & (data['t'] != 1)]
    pre_date_list = pre_data['date'].unique()
    del history_data['t_date']
    del history_data['t']
    del history_data['category']
    del history_data['time']
    del pre_data['t_date']
    del pre_data['t']
    del pre_data['category']
    del pre_data['time']
    history_data['city'] = '包头市'
    pre_data['city'] = '包头市'
    price_data = pd.read_csv(r"D:\02file\2023\05蒙西数据/data/1_包头西节点价格数据_2023-08-01_2023-09-21.csv").iloc[:, 1:]
    price_data['date_time'] = price_data['date_time'].map(lambda x: str(x)[:13])
    price_data = price_data.groupby('date_time').mean().reset_index()
    price_data['date_time'] = pd.to_datetime(price_data['date_time']).astype(str)
    price_data = price_data.rename(columns={'price': '价格'})
    history_data = history_data.merge(price_data, how='left', on='date_time')
    history_data = history_data.set_index('date_time')
    pre_data = pre_data.set_index('date_time')
    print(pre_date_list)
    # pre = ElPricePreLongtimeNode48(history_data, pre_data, pre_date_list, min_value=0, max_value=5180)
    # result = pre.predict()
    # print(result)