# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : z<PERSON><PERSON><PERSON>
# Date       : 2024-07-17 17:11:36
# Description: 山东发电侧，现货申报系数lambda
"""

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import Request<PERSON><PERSON><PERSON>Base
from togeek_package.optimize.shandong import SpotDeclarationLambdaSD


class Spot_Declaration_lambda_SD_Handler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        run_date = params.pop("run_date")
        lambda_min = params.pop("lambda_min", 0.7)
        days_history = params.pop("days_history", 30)

        model = SpotDeclarationLambdaSD(
            run_date=run_date,
            lambda_min=lambda_min,
            days_history=days_history,
        )
        result = model.predict()

        self.write(dict(result))
