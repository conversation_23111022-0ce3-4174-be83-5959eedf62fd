import os.path
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

import logging
logger = logging.getLogger()
# # logger全部输出到控制台
# logger.setLevel(logging.DEBUG)


"""
用于预测实时节点价格
数据处理：1. 所有日前、实时边界  2. 节点天气数据 3. 全省天气数据/新能源聚集  4. 检修容量
数据聚合：1. 按15min聚合 2. 按天聚合
"""
# REAL_IDX_NAME_LIST会处理实时边界条件，实时价格不能在此列表中
REAL_IDX_NAME_LIST = [
    # real
    '实时全网负荷',
    '实时新能源负荷',
    '实时风电负荷',
    '实时光伏负荷',
    '实时外送出力',
    '实时省内A类电源'
]


DEF_IDX_NAME_LIST = [
    # ahead
    "日前全网负荷",  # D+2
    "日前新能源负荷",  # D+2
    "日前风电负荷",
    "日前光伏负荷",
    "日前外送出力",  # D+2
    "日前负备用容量",
    "日前正备用容量",
    "日前检修容量",
    "日前省内A类电源",  # D+2
    # price
    "value",
] + REAL_IDX_NAME_LIST

D1_FEATS_LIST = [
    # real
    "实时全网负荷",
    "实时新能源负荷",
    "实时风电负荷",
    "实时光伏负荷",
    "实时外送出力",
    "实时省内A类电源",
    "日前检修容量",
    # 特征
    "实时出力",
    "实时负荷",
    "实时火电竞价空间",
    "实时新能源占比",
    "火电运行容量",
    "火电负荷率",
    "real_price_D1",
    "real_price_D1_mean",
    "real_price_D1_std",
    "real_price_D1_median",
    "real_weekly_mean",
    "real_weekly_std",
    "real_weekly_median",
    # 其他特征
    "火电紧张",
    "供需比",
    "新能源双抑制",
    "净负荷",
    "大风季",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "minute",
    "minute_sin",
    "minute_cos",
    # "period",
    "holiday",
    "day_type",
    # 天气特征
    "u_80m",
    "v_80m",
    "u_10m",
    "v_10m",
    "t_80m",
    "tp_surface",
    "wspd_surface",
    "t_surface",
    "csnow_surface",
    "cicep_surface",
    "cfrzr_surface",
    "crain_surface",
    "SUNSD_surface",
    "dswrf_surface",
    "dlwrf_surface",
    "albedo_surface",
    # "q_80m", "pres_80m", "vis_surface","uflx_surface", "vflx_surface","uswrf_surface", "ulwrf_surface",
    "温度敏感区间",
    "合成风速",
    "合成风速_80m",
    "风速区间_80m",
    "风速方向_80m",
    # 全省天气特征
    "u_80m_all",
    "v_80m_all",
    "SUNSD_surface_all",
    "dswrf_surface_all",
    "albedo_surface_all",
    "wspd_surface_all",
    "合成风速_80m_all",
    "有效辐射强度_all",
    # 检修特征
    "overhaul_count",
    "overhaul_线路",
    "overhaul_变压器",
    "overhaul_母线",
    "overhaul_开关",
    "sum_voltage",
    "sum_switch_voltage",
]

D2_FEATS_LIST = [
    # real
    "实时全网负荷",
    "实时新能源负荷",
    "实时风电负荷",
    "实时光伏负荷",
    "实时外送出力",
    "实时省内A类电源",
    "日前检修容量",
    # ahead
    "日前全网负荷",  # D+2
    "日前新能源负荷",  # D+2
    "日前风电负荷",
    "日前光伏负荷",
    "日前外送出力",  # D+2
    "日前负备用容量",
    "日前正备用容量",
    "日前检修容量",
    "日前省内A类电源",  # D+2
    # 特征
    "实时出力",
    "实时负荷",
    "实时火电竞价空间",
    "实时新能源占比",
    "火电运行容量",
    "火电负荷率",
    "日前出力",
    "日前负荷",
    "日前火电竞价空间",
    "日前新能源占比",
    "日前正备用比例",
    "日前负备用比例",
    "real_price_D1",
    "real_price_D1_mean",
    "real_price_D1_std",
    "real_price_D1_median",
    "real_weekly_mean",
    "real_weekly_std",
    "real_weekly_median",
    # 其他特征
    "火电紧张",
    "供需比",
    "新能源双抑制",
    "净负荷",
    "大风季",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "minute",
    "minute_sin",
    "minute_cos",
    # "period",
    "holiday",
    "day_type",
    # 天气特征
    "u_80m",
    "v_80m",
    "u_10m",
    "v_10m",
    "t_80m",
    "tp_surface",
    "wspd_surface",
    "t_surface",
    "csnow_surface",
    "cicep_surface",
    "cfrzr_surface",
    "crain_surface",
    "SUNSD_surface",
    "dswrf_surface",
    "dlwrf_surface",
    "albedo_surface",
    # "q_80m", "pres_80m", "vis_surface","uflx_surface", "vflx_surface","uswrf_surface", "ulwrf_surface",
    "温度敏感区间",
    "合成风速",
    "合成风速_80m",
    "风速区间_80m",
    "风速方向_80m",
    # 全省天气特征
    "u_80m_all",
    "v_80m_all",
    "SUNSD_surface_all",
    "dswrf_surface_all",
    "albedo_surface_all",
    "wspd_surface_all",
    "合成风速_80m_all",
    "有效辐射强度_all",
    # 检修特征
    "overhaul_count",
    "overhaul_线路",
    "overhaul_变压器",
    "overhaul_母线",
    "overhaul_开关",
    "sum_voltage",
    "sum_switch_voltage",
]

D3_FEATS_LIST = D2_FEATS_LIST

DEF_FEATURES = [
    # ahead
    "日前全网负荷",
    "日前新能源负荷",
    "日前风电负荷",
    "日前光伏负荷",
    "日前外送出力",
    "日前负备用容量",
    "日前正备用容量",
    "日前检修容量",
    "日前省内A类电源",
    # 特征
    "日前出力",
    "日前负荷",
    "日前火电竞价空间",
    "日前新能源占比",
    "日前正备用比例",
    "日前负备用比例",
    # real
    "实时全网负荷",
    "实时新能源负荷",
    "实时风电负荷",
    "实时光伏负荷",
    "实时外送出力",
    "实时省内A类电源",
    "日前检修容量",
    # 特征
    "火电紧张",
    "供需比",
    "新能源双抑制",
    "净负荷",
    "实时出力",
    "实时负荷",
    "实时火电竞价空间",
    "实时新能源占比",
    "火电运行容量",
    "火电负荷率",
    "real_price_D1",
    "real_price_D1_mean",
    "real_price_D1_std",
    "real_price_D1_median",
    "real_weekly_mean",
    "real_weekly_std",
    "real_weekly_median",
    # 其他特征
    "大风季",
    "hour",
    "dayofweek",
    "hour_sin",
    "hour_cos",
    "dayofweek_sin",
    "dayofweek_cos",
    "minute",
    "minute_sin",
    "minute_cos",
    # "period",
    "holiday",
    "day_type",
    # 天气特征
    "u_80m",
    "v_80m",
    "u_10m",
    "v_10m",
    "t_80m",
    "tp_surface",
    "wspd_surface",
    "t_surface",
    "csnow_surface",
    "cicep_surface",
    "cfrzr_surface",
    "crain_surface",
    "SUNSD_surface",
    "dswrf_surface",
    "dlwrf_surface",
    "albedo_surface",
    # "q_80m", "pres_80m", "vis_surface","uflx_surface", "vflx_surface","uswrf_surface", "ulwrf_surface",
    "温度敏感区间",
    "合成风速",
    "合成风速_80m",
    "风速区间_80m",
    "风速方向_80m",
    "平均风速_80m_3",
    "有效风速",
    "有效辐射强度",
    # 全省天气特征
    "u_10m_all",
    "v_10m_all",
    "u_80m_all",
    "v_80m_all",
    "SUNSD_surface_all",
    "dswrf_surface_all",
    "albedo_surface_all",
    "wspd_surface_all",
    "合成风速_80m_all",
    "有效风速_all",
    "有效辐射强度_all",
    # 检修特征
    "overhaul_count",
    "overhaul_线路",
    "overhaul_变压器",
    "overhaul_母线",
    "overhaul_开关",
    "sum_voltage",
    "sum_switch_voltage",
]


class DataFetcherMixin:

    def _data(
        self,
        df_price,
        D: str,
        df_real,
        df_holiday=None,
        df_ahead=None,
        df_overhaul=None,
        df_node_gfs=None,
        df_all_gfs=None,
        name_list=None,
        pred_day=1,
    ):
        """
        获取基础数据
        :param df_price: 节点价格数据 [dateTime,value]
        :param D: 交易日
        :param df_real: 实时边界数据 [dateTime,实时全网负荷,实时新能源负荷,实时风电负荷,实时光伏负荷,实时外送出力,实时省内A类电源]
        :param df_ahead: 日前边界数据 [dateTime,日前全网负荷,日前新能源负荷,日前风电负荷,日前外送出力,日前负备用容量,日前正备用容量,日前检修容量,日前省内A类电源]
        :param df_overhaul: 检修数据 ['dateTime','overhaul_count','overhaul_线路','overhaul_变压器','overhaul_母线','overhaul_开关','sum_voltage','sum_switch_voltage']
        :param df_node_gfs: 节点gfs数据 ['dateTime',"u_80m", "v_80m", "u_10m", "v_10m", "t_80m",  "tp_surface", "wspd_surface", "t_surface", "csnow_surface", "cicep_surface", "cfrzr_surface", "crain_surface", "SUNSD_surface", "dswrf_surface", "dlwrf_surface", "albedo_surface",]
        :param df_all_gfs: 新能源装机密集区域gfs数据 ['dateTime',"u_10m_all", "v_10m_all", "u_80m_all", "v_80m_all", "SUNSD_surface_all", "dswrf_surface_all", "albedo_surface_all","wspd_surface_all",]
        :param name_list: 节点检修计划对应的关键字名称列表
        :param pred_day: 预测D+?[1,2,3]
        :return: X、y，都是pd.DataFrame
        """
        # 运行日
        print("D:", D)
        run_day = datetime.strptime(D, "%Y-%m-%d") + timedelta(days=1)
        run_day_str = datetime.strftime(run_day, "%Y-%m-%d")

        df_real = pd.DataFrame.from_dict(df_real)
        df_price = pd.DataFrame.from_dict(df_price)
        df_holiday = pd.DataFrame.from_dict(df_holiday)
        df_ahead = pd.DataFrame.from_dict(df_ahead)
        df_overhaul_ori = pd.DataFrame.from_dict(df_overhaul)
        df_node_gfs = pd.DataFrame.from_dict(df_node_gfs)
        df_all_gfs = pd.DataFrame.from_dict(df_all_gfs)

        df_price['dateTime']=pd.to_datetime(df_price['dateTime'])
        df_real['dateTime']=pd.to_datetime(df_real['dateTime'])
        df_ahead["dateTime"] = pd.to_datetime(df_ahead["dateTime"])
        # df_overhaul["dateTime"] = pd.to_datetime(df_overhaul["dateTime"])
        df_node_gfs["dateTime"] = pd.to_datetime(df_node_gfs["dateTime"])
        df_all_gfs["dateTime"] = pd.to_datetime(df_all_gfs["dateTime"])

        df = pd.merge(df_real, df_price, on=["dateTime"], how="left")
        df = pd.merge(df, df_ahead, on=["dateTime"], how="left")
        # df = pd.merge(df, df_overhaul, on=["dateTime"], how="left")
        df_gfs = pd.merge(df_node_gfs, df_all_gfs, on=["dateTime"], how="left")

        start = datetime.strptime(D, "%Y-%m-%d") - timedelta(days=30)
        start_str = datetime.strftime(start, "%Y-%m-%d")
        end_date = datetime.strptime(D, "%Y-%m-%d") + timedelta(days=3)
        end_date_str = datetime.strftime(end_date, "%Y-%m-%d")

        df_overhaul = self.get_unit_overhaul(name_list, df_overhaul_ori, start_str, end_date_str)
        if df_overhaul is not None:
            df_overhaul = df_overhaul[
                [
                    "dateTime",
                    "overhaul_count",
                    "overhaul_线路",
                    "overhaul_变压器",
                    "overhaul_母线",
                    "overhaul_开关",
                    "sum_voltage",
                    "sum_switch_voltage",
                ]
            ]
            df_overhaul["dateTime"] = pd.to_datetime(df_overhaul["dateTime"])
            df = pd.merge(df, df_overhaul, on=["dateTime"], how="left")

        df['date'] = df['dateTime'].dt.date.astype(str)
        df["hour"] = df["dateTime"].dt.hour

        df = self._with_features(df)
        df = self._with_holiday_features(df,df_holiday=df_holiday)

        df_gfs_feats = self._weather_features(df_gfs)
        df = pd.merge(df, df_gfs_feats, on=["dateTime"], how="outer")

        mask=(df['date'] > start_str) & (df['date'] < run_day_str)  # 组合条件
        train = df[mask]
        if pred_day == 1:
            test = df[df["date"] == run_day_str]
        elif pred_day == 2:
            # D+2
            D2 = datetime.strptime(run_day_str, "%Y-%m-%d") + timedelta(days=1)
            D2_str = datetime.strftime(D2, "%Y-%m-%d")
            test = df[df["date"] == D2_str]
        elif pred_day == 3:
            # D+3
            D3 = datetime.strptime(run_day_str, "%Y-%m-%d") + timedelta(days=2)
            D3_str = datetime.strftime(D3, "%Y-%m-%d")
            test = df[df["date"] == D3_str]
        else:
            raise ValueError("仅支持预测D+1,D+2，D+3,请输入[1,2,3]中的值")

        # 筛选出缺失值比例大于等于 30% 的列
        missing_ratio = train.isna().mean()
        selected_columns = missing_ratio[missing_ratio < 0.3].index
        # 根据筛选结果保留相应的列
        train = train[selected_columns]
        # 输出缺失值大于30%的列名
        if not missing_ratio[missing_ratio >= 0.3].empty:
            logger.warning("训练数据中缺失值大于30%的列：" + str(missing_ratio[missing_ratio >= 0.3].index))

        train = train.dropna(axis=1, how="all")

        # 筛选值全部相同的列
        unique_values = train.nunique()
        constant_columns = unique_values[unique_values == 1].index.tolist()
        if constant_columns:
            logger.warning("训练数据中值全部相同的列有：" + str(constant_columns))
            # 删除值全部相同的列
            train = train.drop(columns=constant_columns)
        if "value" not in train.columns:
            return ValueError("价格数据缺失值较多，请检查")

        # 0价:绝对值小于10：
        train["zero_value"]= np.where(abs(train["value"]) <= 10, 1, 0)
        test["zero_value"]=0
        # 填充缺失数据
        for col in train.columns:
            train[col] = train.apply(
                lambda row: self.fill_missing_values(
                    col, row, train) if pd.isna(row[col]) else row[col],
                axis=1
            )

        test = test[train.columns]
        if test.empty:
            raise ValueError("no test data")

        return train, test

    def fill_missing_values(self, col, row, df):
        current_hour = row['hour']
        current_date = row['dateTime']
        # 计算 7 天前的日期
        seven_days_ago = current_date - pd.Timedelta(days=7)
        # 筛选出过去 7 天同一小时的数据
        historical_data = df[(df['dateTime'] >= seven_days_ago) & (
            df['dateTime'] < current_date) & (df['hour'] == current_hour)][col]

        # 若历史数据存在，则取平均值填充，否则保留缺失值
        if not historical_data.empty:
            return historical_data.mean()

        return row[col]

    def _to_hourly(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.resample("h").mean().reset_index()

    def _weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
         气象特征 15天天气预报
         [ 'u_10m', 'v_10m', 't_80m', 'q_80m', 'pres_80m', 'u_80m',
        'v_80m', 'tp_surface', 'vis_surface', 'wspd_surface', 't_surface',
        'crain_surface', 'SUNSD_surface', 'uflx_surface', 'vflx_surface',
        'dswrf_surface', 'dlwrf_surface', 'uswrf_surface', 'ulwrf_surface',
        'albedo_surface', 'dateTime']
        """
        # 负荷预测：电力负荷通常与温度、湿度、时间周期强相关
        # 1. 温度特征
        # 1.1 温度敏感区间：将 t_surface 转换为分段特征（如 <5°C 低温供暖、5-27°C 舒适区、>27°C 高温制冷）。
        df["温度敏感区间"] = df["t_surface"].apply(lambda x: 0 if x < 5 else (1 if 5 <= x <= 27 else 2))

        # 2. 湿度特征
        # 高温高湿组合 当 t_surface > 28°C 且 rh_2m > 70% 时标记为 1，其他为0 没有湿度数据

        # 风能出力预测：风能出力与风速、风向、气压强相关
        # 3. 风速特征
        # 3.1 合成风速 风能与风速^3呈正比
        df["合成风速"] = np.sqrt(df["u_10m"] ** 2 + df["v_10m"] ** 2)
        df["合成风速_80m"] = np.sqrt(df["u_80m"] ** 2 + df["v_80m"] ** 2)
        # 3.2 风速区间 离散化为 0-3m/s（停转）、3-12m/s（工作区）、>12m/s（切出）【待确认】
        df["风速区间_80m"] = df["合成风速_80m"].apply(lambda x: 0 if x < 3 else (1 if 3 <= x < 12 else 2))
        # 3.3 风速方向
        df["风速方向_80m"] = np.arctan2(df["v_80m"], df["u_80m"])
        # 3.4 风速统计特征 过去3小时平均风速（反应惯性）
        df["平均风速_80m_3"] = df["合成风速_80m"].rolling(3).mean()
        # 3.5 有效风速：风能与风速^3呈正比
        df["有效风速"] = df["合成风速"] ** 3
        # 全省特征
        df["合成风速_all"] = np.sqrt(df["u_10m_all"] ** 2 + df["v_10m_all"] ** 2)
        df["合成风速_80m_all"] = np.sqrt(df["u_80m_all"] ** 2 + df["v_80m_all"] ** 2)
        df["有效风速_all"] = df["合成风速_all"] ** 3

        # 光能出力预测：光伏发电依赖辐射强度、云量、温度等
        # 4. 光伏特征
        # 4.1 有效辐射强度 （扣除地表反射后的可用辐射）
        df["有效辐射强度"] = df["dswrf_surface"] * (100 - df["albedo_surface"]) / 100
        df["有效辐射强度_all"] = df["dswrf_surface_all"] * (100 - df["albedo_surface_all"]) / 100
        # 4.2 云雨衰减系数：cloud_rain_impact = 1 - (tp_surface/10 + (100 - SUNSD_surface)/100)（降水与少日照双重抑制） SUNSD_surfac不是百分数 待确认

        df["风电抑制"] = (df["合成风速_80m"] < 3.5).astype(int)
        df["光伏抑制"] = (df["有效辐射强度"] > 0.3).astype(int)
        df["新能源双抑制"] = df["风电抑制"] & df["光伏抑制"]

        return df

    def _with_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特征工程
        """
        shift_per_day = 96 # 15min
        # 日前特征：whole_load	new_energy_load	wind_load	photovoltaic_load	delivery_power	reserve_negative	reserve_positive	overhaul_capacity	no_market_power
        #           全网负荷，新能源负荷，风电负荷，光伏负荷，外送出力（负），负备用容量，正备用容量，检修容量，省内A类电源
        # 实时特征：whole_load	new_energy_load	wind_load	photovoltaic_load	delivery_power	no_market_power
        #           全网负荷，新能源负荷，风电负荷，光伏负荷，外送出力，省内A类电源

        # 日前边界特征 - 供需特征
        # 火电竞价空间=系统+正外送-非市场化-新能源
        df["日前出力"] = df["日前省内A类电源"] + df["日前新能源负荷"]
        df["日前负荷"] = df["日前全网负荷"] - df["日前外送出力"]
        df["日前火电竞价空间"] = df["日前负荷"] - df["日前出力"]
        # 新能源特征-新能源占比
        df["日前新能源占比"] = df["日前新能源负荷"] / df["日前出力"]
        # 正备用比例
        df["日前正备用比例"] = df["日前正备用容量"] / df["日前负荷"]
        # 负备用比例
        df["日前负备用比例"] = df["日前负备用容量"] / df["日前负荷"]

        #  实时边界特征
        # 火电竞价空间=系统+正外送-非市场化-新能源
        df['实时出力'] = df['实时省内A类电源'] + df['实时新能源负荷']
        df['实时负荷'] = df['实时全网负荷'] - df['实时外送出力']
        df['实时火电竞价空间'] = df['实时负荷'] - df['实时出力']
        df["火电调节裕度"] = df["实时火电竞价空间"] / df["实时全网负荷"]
        df["火电紧张"] = (df["火电调节裕度"] < 0.625).astype(int)
        df["净负荷"] = df["实时全网负荷"] - df["实时新能源负荷"]
        df["供需比"] = df["实时火电竞价空间"] / (df["净负荷"] + 1e-5)
        # 新能源特征-新能源占比
        df['实时新能源占比']= df['实时新能源负荷'] /df['实时出力']
        # 总装机：33188
        df["火电运行容量"] = 33188 - df["日前检修容量"]
        df["火电负荷率"] = df["实时火电竞价空间"] / df["火电运行容量"]

        # 时间特征
        df['hour'] = df['dateTime'].dt.hour
        df['dayofweek'] = df['dateTime'].dt.dayofweek
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['dayofweek_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dayofweek_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['minute'] = df['dateTime'].dt.minute
        df['minute_sin'] = np.sin(2 * np.pi * df['minute'] / 60)
        df['minute_cos'] = np.cos(2 * np.pi * df['minute'] / 60)
        # df['dayofyear']=df['dateTime'].dt.dayofyear

        df["大风季"] = df["dateTime"].dt.month.apply(lambda x: 1 if 1 <= x <= 5 or 9 < x <= 12 else 0)

        # 时序统计特征
        df['real_price_D1'] = df['value'].shift(shift_per_day)  # 前一天同时刻
        df['real_price_D1_mean'] = df['real_price_D1'].rolling(
            shift_per_day).mean()  # 前一天统计特征
        df['real_price_D1_std'] = df['real_price_D1'].rolling(shift_per_day).std()
        df['real_price_D1_median'] = df['real_price_D1'].rolling(shift_per_day).median()

        shifts_real = [shift_per_day * i for i in range(1, 8)]  # [24, 48,..., 168]
        df['real_weekly_mean'] = pd.concat([df['value'].shift(s) for s in shifts_real], axis=1).mean(
            axis=1, skipna=True)
        df['real_weekly_std'] = pd.concat([df['value'].shift(s) for s in shifts_real], axis=1).std(
            axis=1, skipna=True) 
        df['real_weekly_median'] = pd.concat([df['value'].shift(s) for s in shifts_real], axis=1).median(
            axis=1, skipna=True) 

        # 太阳能开机台数
        df['solar_unit'] = df['hour'].apply(
            lambda x: 0 if x in [6, 19] else (1 if x in range(7, 19) else 2))

        return df

    def typical_day(start_day: int = None, end_day: int = None, csv_path: str = 'togeek_package\price\mengxi\price_mx_node_etr\typical_day.csv'):
        """
        获取指定日期范围内的典型交易日。

        参数: 
            start_day (int)：起始日期，格式为 YYYYMMDD。
            end_day (int)：结束日期，格式为 YYYYMMDD。
            csv_path (str)：CSV 文件路径，默认为 'typical_day.csv'
        """
        # 读取 CSV 文件
        df = pd.read_csv(csv_path)

        # 转换日期格式
        df['day'] = df['day'].astype(int)

        if start_day is None:
            # start day设置为当年第一天
            start_day = int(datetime.now().strftime('%Y')) * 10000 + 101

        if end_day is None:
            # end day设置为start day的下一年的第一天
            start_year = start_day // 10000
            next_year = start_year + 1
            end_day = next_year * 10000 + 101

        # 筛选日期范围
        filtered_df = df[(df['day'] >= start_day) & (df['day'] <= end_day)]

        return filtered_df

    def get_unit_overhaul(self, name_list: list, df: pd.DataFrame, start_date, end_date) -> pd.DataFrame:
        """
        获取指定节点的检修计划。

        参数:
            name_list (int)：节点 ID 检修name包含的关键字段
            df (pd.DataFrame)：检修计划 DataFrame。
        """
        # if unit_id == 37:
        #     name_list = [
        #         "团结站",
        #         "兴旺站",
        #         "庆云站",
        #         "庆团Ⅰ线",
        #         "庆团Ⅱ线",
        #         "瑰团线",
        #         "庆旺Ⅰ线",
        #         "庆旺Ⅱ线",
        #         "辉庆Ⅰ线",
        #         "辉庆Ⅱ线",
        #         "兴旺Ⅰ线",
        #         "兴旺Ⅱ线",
        #     ]
        # elif unit_id == 28 or unit_id == 31:
        #     name_list = [
        #         "红塔站",
        #         "百灵站",
        #         "望海站",
        #         "固北站",
        #         "百望Ⅰ线",
        #         "百望Ⅱ线",
        #         "百固Ⅰ线",
        #         "百红Ⅰ线",
        #         "百红Ⅱ线",
        #         "茂百线",
        #         "茂明线",
        #     ]
        # elif unit_id == 21:
        #     name_list = [
        #         "文更站",
        #         "德岭山站",
        #         "金泉站",
        #         "国合站",
        #         "隆兴昌站",
        #         "文国Ⅰ线",
        #         "文国Ⅱ线",
        #         "力国线",
        #         "力德线",
        #         "川国线",
        #         "川德线",
        #         "德文Ⅰ线",
        #         "德文Ⅱ线",
        #         "德金Ⅰ线",
        #         "德金Ⅱ线",
        #         "德隆Ⅰ线",
        #         "德隆Ⅱ线",
        #     ]
        # elif unit_id == 26:
        #     name_list = [
        #         "获各琦站",
        #         "乌后期开闭站",
        #         "河套站",
        #         "厂汉站",
        #         "布拉格站",
        #         "套厂Ⅰ线",
        #         "套厂Ⅱ线",
        #         "套琦线",
        #         "套乌线",
        #         "厂布线",
        #     ]
        # elif unit_id == 30:
        #     name_list = [
        #         "杜尔伯特站",
        #         "武川站",
        #         "可镇站",
        #         "川可Ⅰ线",
        #         "川可Ⅱ线",
        #         "可杜线",
        #         "川杜线",
        #         "蓄武线",
        #     ]
        # elif unit_id == 32:
        #     name_list = [
        #         "元上都站",
        #         "白音高勒站",
        #         "滦河变站",
        #         "明安图站",
        #         "白明Ⅰ线",
        #         "白河Ⅰ线",
        #         "白河Ⅱ线",
        #         "白元Ⅰ线",
        #         "白元Ⅱ线",
        #         "唐滦线",
        #         "唐都线",
        #     ]
        # else:
        #     return ValueError("unit_id 不存在,请输入[21,26,28,30,31,32,37]中的一个")
        if name_list is None:
            logger.warning("检修name_list为空")
            return None
        status_type = "检修"
        # 选取name 包含 name_list中的 并且 status_type="检修" 的数据
        df = df[df["name"].str.contains("|".join(name_list)) & (df["status_type"] == status_type)]
        df2 = df.drop(columns=["is_top", "top_time"])
        # 删除相同的行
        df2 = df2.drop_duplicates()
        # 对name列进行排序
        df2 = df2.sort_values(by=["type", "name"])

        # 转换时间字段
        df2["start_time"] = pd.to_datetime(df2["start_time"])
        df2["end_time"] = pd.to_datetime(df2["end_time"])
        df2["date"] = pd.to_datetime(df2["date"])

        # 提取设备关键属性
        df2["voltage_level"] = df2["name"].str.extract(r"(\d+)kV")[0].astype(float)
        df2["voltage_level"] = df2["voltage_level"].fillna(110)  # 将NaN值填充为110
        df2["device_category"] = df2["type"].map(
            {"线路": "transmission", "变压器": "transformer", "母线": "bus", "开关": "switch"}
        )

        def generate_daily_features(target_date, keep_only_active_points=False):
            """
            生成单日检修特征
            """
            target_dt = pd.Timestamp(target_date)

            # 筛选当日有效检修
            mask = (df2["start_time"].dt.date <= target_dt.date()) & (df2["end_time"].dt.date >= target_dt.date())
            daily_events = df2[mask].copy()

            # 对当日有效检修信息按关键列进行去重
            daily_events = daily_events.drop_duplicates(subset=["type", "voltage_level", "start_time", "end_time"])

            # 创建时间点列表
            if keep_only_active_points:
                # 方法1：只保留有检修数据的时间点
                time_points = set()

                # 遍历每个检修事件，提取相关时间点
                for _, row in daily_events.iterrows():
                    start_time = row["start_time"]
                    end_time = row["end_time"]

                    # 如果事件开始或结束在目标日期
                    if start_time.date() == target_dt.date() or end_time.date() == target_dt.date():
                        # 计算事件在目标日期内的时间范围
                        event_start = max(start_time, pd.Timestamp(target_dt.date()))
                        event_end = min(
                            end_time, pd.Timestamp(target_dt.date()) + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
                        )

                        # 生成事件时间范围内的15分钟间隔时间点
                        current_time = event_start
                        while current_time <= event_end:
                            time_points.add(current_time.time())
                            current_time += pd.Timedelta(minutes=15)

                # 如果没有找到检修数据，返回空DataFrame
                if not time_points:
                    return pd.DataFrame()

                # 转换为有序的时间点列表
                time_points = sorted(time_points)
            else:
                # 方法2：生成全天所有15分钟间隔的时间点
                time_points = pd.date_range("00:00", "23:45", freq="15min").time

            # 创建DataFrame
            daily_df = pd.DataFrame(index=time_points)

            # 特征 1：总检修设备数量
            daily_df["overhaul_count"] = 0

            # 特征 2：加权检修影响指数
            daily_df["overhaul_impact"] = 0.0

            # 按设备类型统计
            device_types = ["线路", "变压器", "母线", "开关"]
            for dev_type in device_types:
                daily_df[f"overhaul_{dev_type}"] = 0

            # 初始化电压等级列为空列表
            daily_df["voltage_levels"] = [[] for _ in range(len(daily_df))]
            daily_df["switch_voltage_levels"] = [[] for _ in range(len(daily_df))]

            # 遍历每个时间点
            for time_point in time_points:
                current_dt = pd.Timestamp.combine(target_dt, time_point)

                # 检查当前时间点的检修设备
                active_overhauls = daily_events[
                    (daily_events["start_time"] <= current_dt) & (daily_events["end_time"] >= current_dt)
                ]

                # 计算基础特征
                daily_df.loc[time_point, "overhaul_count"] = len(active_overhauls)

                # 计算加权影响指数（示例权重）
                impact_weights = {"变压器": 1, "母线": 1, "线路": 1}
                impact = 0
                voltage_levels = []
                switch_voltage_levels = []

                for _, row in active_overhauls.iterrows():
                    if row["type"] == "开关":
                        switch_voltage_levels.append(row["voltage_level"])
                    else:
                        impact += impact_weights[row["type"]] * (row["voltage_level"])
                        voltage_levels.append(row["voltage_level"])

                daily_df.loc[time_point, "overhaul_impact"] = impact

                # 使用at而不是loc来赋值列表
                daily_df.at[time_point, "voltage_levels"] = voltage_levels
                daily_df.at[time_point, "switch_voltage_levels"] = switch_voltage_levels

                # 按类型计数
                for dev_type in device_types:
                    count = active_overhauls[active_overhauls["type"] == dev_type].shape[0]
                    daily_df.loc[time_point, f"overhaul_{dev_type}"] = count

            # 将时间点转换为datetime格式
            daily_df.index = [pd.Timestamp.combine(target_dt.date(), t) for t in daily_df.index]
            daily_df.index.name = "dateTime"

            # 重置索引并格式化datetime列
            daily_df = daily_df.reset_index()
            daily_df["dateTime"] = daily_df["dateTime"].dt.strftime("%Y-%m-%d %H:%M:%S")

            return daily_df

        # 获取数据中的日期范围
        date_range = pd.date_range(start_date, end_date)

        all_features = []
        for single_date in date_range:
            daily_features = generate_daily_features(single_date)
            if not daily_features.empty:
                all_features.append(daily_features)

        # 合并所有天的特征
        if all_features:
            all_features_df = pd.concat(all_features, ignore_index=True)

            # 提取电压等级特征
            all_features_df["sum_voltage"] = all_features_df["voltage_levels"].apply(lambda x: sum(x) if x else 0)
            all_features_df["sum_switch_voltage"] = all_features_df["switch_voltage_levels"].apply(
                lambda x: sum(x) if x else 0
            )

            # 保存结果
            # all_features_df.to_csv(
            #     f"D:/fwx/gitlab/algorithms/lab/fanwenxuan/neimenggu/csv3/overhaul_{unit_id}.csv", index=False
            # )
        else:
            all_features_df = None
            logger.warning("没有找到检修数据")
        return all_features_df

    def _with_holiday_features(self, df: pd.DataFrame, df_holiday:pd.DataFrame) -> pd.DataFrame:
        if df_holiday is None:
            return df
        # 工作日和节假日
        start_date, end_date = df['dateTime'].min(), df['dateTime'].max()

        date_type = df_holiday

        date_type['date'] = pd.to_datetime(date_type['day'], format='%Y%m%d')
        date_type['date'] = date_type['date'].astype(str)

        type_mapping = {'调休节假日': 0, '周一': 1, '周二': 2, '周三': 3, '周四': 4, '周五': 5, '周六': 6, '周日': 7,
                        '调休工作日': 8, '法定节假日': 9}
        date_type['day_type'] = date_type['type'].map(type_mapping)
        date_type = date_type[['date', 'day_type']]

        df['date'] = df['dateTime'].dt.date.astype(str)
        df = pd.merge(df, date_type, on='date', how='left')
        df['day_type'] = df['day_type'].astype(int)
        df["holiday"] = df["day_type"].apply(lambda x: 1 if x in [0, 6, 7, 9] else 0)  # 节假日：1，工作日：0

        return df

if __name__ == "__main__":
    DateGet=DataFetcherMixin()
    df_real = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\real.xlsx")
    df_ahead = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\ahead.xlsx")
    df_price = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_price.xlsx")
    df_overhaul = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\t_equipment_overhaul.xlsx")
    df_node_gfs = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_node_gfs.xlsx")
    df_all_gfs = pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\mengxi\excel\df_all_gfs.xlsx")
    name_list = [
        "红塔站",
        "百灵站",
        "望海站",
        "固北站",
        "百望Ⅰ线",
        "百望Ⅱ线",
        "百固Ⅰ线",
        "百红Ⅰ线",
        "百红Ⅱ线",
        "茂百线",
        "茂明线",
    ]
    df_holiday=pd.read_csv(r'fwx\typical_day.csv')
    df_price=df_price[['dateTime','value']]
    df = DateGet._data(
        df_price,
        "2025-06-01",
        df_real=df_real,
        df_holiday=df_holiday,
        df_ahead=df_ahead,
        df_overhaul=df_overhaul,
        df_node_gfs=df_node_gfs,
        df_all_gfs=df_all_gfs,
        name_list=name_list,
    )

    print(df)
