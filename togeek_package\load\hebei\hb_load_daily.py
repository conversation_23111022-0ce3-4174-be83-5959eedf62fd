#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/10/25 14:18 
@Info    ：

'''

import pandas as pd
import numpy as np
import logging


logger = logging.getLogger()


class LoadPredHB:
    def __init__(self, hist_weather, hist_elec, hist_meter_elec, hist_elec_hourly, future_weather, future_meter_elec, city_wgt=None):
        logger.info("---------------河北建投负荷预测模型开始运行：---------------")
        print(f'"hist_weather": {hist_weather}, "hist_elec": {hist_elec}, "hist_meter_elec": {hist_meter_elec}, '
                    f'"hist_elec_hourly": {hist_elec_hourly}, "future_weather": {future_weather}, '
                    f'"future_meter_elec": {future_meter_elec}, "city_wgt": {city_wgt}')
        if city_wgt == None:
            city_wgt = {"石家庄": 0.4, "保定": 0.05, "沧州": 0.05, "衡水": 0.05, "邯郸": 0.05, "邢台": 0.4}
        city_wgt_df = pd.DataFrame({"city_name": city_wgt.keys(), 'wgt': city_wgt.values()})
        hist_weather1 = self.weather_weighted(pd.DataFrame(hist_weather), city_wgt_df)
        hist_data = pd.merge(hist_weather1, pd.DataFrame(hist_elec), on='date')
        hist_meter_elec1 = pd.DataFrame(hist_meter_elec)
        hist_meter_elec1['avg_elec'] = hist_meter_elec1['总电量kWh'] / hist_meter_elec1['抄表天数'] / 10000  # 单位 万千瓦时
        hist_data = pd.merge(hist_data, hist_meter_elec1[['date', 'avg_elec']], on='date')
        self.t, self.s = self.cal_param(hist_data)
        self.coef_hourly = self.prepare_hourly_coef(pd.DataFrame(hist_elec_hourly))
        self.future_weather1 = self.weather_weighted(pd.DataFrame(future_weather), city_wgt_df)
        self.avg_elec = future_meter_elec['总电量kWh'] / future_meter_elec['抄表天数'] / 10000

    def weather_weighted(self, weather, city_wgt):
        """
        天气数据加权处理
        """
        # 天气按照权重处理
        weather_ = pd.merge(weather, city_wgt, on='city_name')
        weather_['max_temperature'] = weather_['max_temperature'] * weather_['wgt']
        weather_['wind_speed'] = weather_['wind_speed'] * weather_['wgt']
        weather2 = weather_.groupby('date').sum().reset_index()

        # 温度  风速 归一化出力
        # t_min, t_max = -30, 50
        # s_min, s_max = 0, 10
        t_min, t_max = 25, 50
        s_min, s_max = 4, 5
        weather2['max_temperature'] = (weather2['max_temperature'] - t_min) / (t_max - t_min)
        weather2['wind_speed'] = (weather2['wind_speed'] - s_min) / (s_max - s_min)
        return weather2

    def cal_param(self, hist_data):
        a = b = np.arange(10, 101) / 10000
        t, s = np.meshgrid(a, b)
        t = t.flatten()  # 温度系数
        s = s.flatten()  # 风速系数

        errs = []
        for t_, s_ in zip(t, s):
            err = sum(((hist_data['max_temperature'] * t_ + hist_data['wind_speed'] * s_ + 1) * hist_data['avg_elec'] -
                       hist_data['elec']) ** 2)
            errs.append(err)

        idx = errs.index(min(errs))
        t_best = t[idx]
        s_best = s[idx]
        return t_best, s_best

    def prepare_hourly_coef(self, data):
        data.sort_values('时间', inplace=True)
        data['时间'] = data['时间'].astype(str)
        data['date_time'] = pd.date_range(start=data['时间'].values[0][:10], freq='1h', periods=data.shape[0])
        data['time'] = data['date_time'].astype(str).map(lambda x: x.split(" ")[1])

        tmp_coef = data.groupby('time').mean().reset_index()
        coef = np.array(tmp_coef['用电量'] / tmp_coef['用电量'].sum())
        return coef

    def run(self):
        # 1 计算每天的总用电量
        self.future_weather1['pred_elec'] = (self.future_weather1['max_temperature'] * self.t +
                                             self.future_weather1['wind_speed'] * self.s + 1) * self.avg_elec
        # 2 计算每天的分时用电量
        pred_elec = np.array(self.future_weather1['pred_elec']).reshape(-1, 1)
        pred_hourly = pd.DataFrame({"date_time": pd.date_range(start=self.future_weather1['date'].values[0] + " 01:00:00",
                                                          freq="1h", periods=self.future_weather1.shape[0] * 24),
                                    "pred_elec": (pred_elec * self.coef_hourly).flatten()})
        pred_hourly['date_time'] = pred_hourly['date_time'].astype(str)
        self.future_weather1.set_index('date', drop=True, inplace=True)
        pred_hourly.set_index('date_time', drop=True, inplace=True)
        result = {"elec_daily": (self.future_weather1[['pred_elec']]).to_dict(), "elec_hourly": pred_hourly.to_dict()}
        logger.info("---------------河北建投负荷预测模型运行结束！---------------")
        return result


if __name__ == '__main__':
    # 天气权重
    city_wgt = {"石家庄": 0.4, "保定": 0.05, "沧州": 0.05, "衡水": 0.05, "邯郸": 0.05, "邢台": 0.4}

    # 历史天气数据
    hist_weather = pd.DataFrame({
        "date": ["2023-07-06", "2023-07-06", "2023-07-06", "2023-07-06", "2023-07-06", "2023-07-06", "2023-07-07",
                 "2023-07-07", "2023-07-07", "2023-07-07", "2023-07-07", "2023-07-07", "2023-07-08", "2023-07-08",
                 "2023-07-08", "2023-07-08", "2023-07-08", "2023-07-08", "2023-07-09", "2023-07-09", "2023-07-09",
                 "2023-07-09", "2023-07-09", "2023-07-09", "2023-07-10", "2023-07-10", "2023-07-10", "2023-07-10",
                 "2023-07-10", "2023-07-10"],
        "city_name": ["石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水",
                      "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台"],
        "max_temperature": [42, 38, 39, 39, 41, 41, 40, 38, 38, 39, 43, 41, 37, 36, 36, 37, 40, 38, 39, 37, 37, 38, 40, 39,
                            40, 39, 38, 39, 40, 39],
        "wind_speed": [2, 2, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 3, 3, 3, 3, 3, 2, 2, 2, 2, 1, 2, 1, 2, 3, 2, 2, 1]})
    hist_weather['date'] = hist_weather['date'].astype(str)

    # 历史实际用电量
    hist_elec = pd.DataFrame({
        "date": ["2023-07-06", "2023-07-07", "2023-07-08", "2023-07-09", "2023-07-10"],
        "elec": [2380.7921, 2357.4618, 2364.3177, 2372.1542, 2455.7541]  # 单位 万千瓦时
    })
    hist_elec['date'] = hist_elec['date'].astype(str)

    # 历史抄表数据
    hist_meter_elec = pd.DataFrame({
        "date": ["2023-07-06", "2023-07-07", "2023-07-08", "2023-07-09", "2023-07-10", "2023-07-11"],
        "抄表天数": [5, 6, 7, 8, 9, 10],
        "总电量kWh": [121491302.8, 145299224.3, 168873842.3, 192517018.6, 216238561.5, 240796101.5]
    })
    hist_meter_elec['date'] = hist_meter_elec['date'].astype(str)

    # 预测前的抄表数据
    future_meter_elec = {"抄表天数": 4, "总电量kWh": 89283451.97}

    # 未来天气数据
    future_weather = pd.DataFrame({
        "date": ["2023-09-06", "2023-09-06", "2023-09-06", "2023-09-06", "2023-09-06", "2023-09-06", "2023-09-07",
                 "2023-09-07", "2023-09-07", "2023-09-07", "2023-09-07", "2023-09-07", "2023-09-08", "2023-09-08",
                 "2023-09-08", "2023-09-08", "2023-09-08", "2023-09-08", "2023-09-09", "2023-09-09", "2023-09-09",
                 "2023-09-09", "2023-09-09", "2023-09-09", "2023-09-10", "2023-09-10", "2023-09-10", "2023-09-10",
                 "2023-09-10", "2023-09-10", "2023-09-11", "2023-09-11", "2023-09-11", "2023-09-11", "2023-09-11",
                 "2023-09-11", "2023-09-12", "2023-09-12", "2023-09-12", "2023-09-12", "2023-09-12", "2023-09-12"],
        "city_name": ["石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水",
                      "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定",
                      "沧州", "衡水", "邯郸", "邢台", "石家庄", "保定", "沧州", "衡水", "邯郸", "邢台"],
        "max_temperature": [32, 32, 31, 34, 30, 30, 32, 32, 34, 34, 32, 31, 31, 31, 33, 33, 32, 31, 26, 25, 28, 30, 30,
                            27, 27, 27, 29, 29, 28, 27, 28, 28, 29, 30, 30, 28, 25, 26, 28, 28, 26, 24],
        "wind_speed": [1, 1, 1, 1, 2, 1, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 3, 2, 2, 3, 3, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
                       1, 2, 2, 1, 2, 3, 4, 3, 3, 4],

    })

    # 历史分时用电量
    hist_elec_hourly = pd.read_excel(r"C:\Users\<USER>\Desktop\用户日用电量.xlsx")
    hist_elec_hourly['时间'] = hist_elec_hourly['时间'].astype(str)

    # 调用模型
    m = LoadPredHB(hist_weather.to_dict(), hist_elec.to_dict(), hist_meter_elec.to_dict(), hist_elec_hourly.to_dict(),
                   future_weather.to_dict(), future_meter_elec, city_wgt=city_wgt)
    res = m.run()
    print(res)
