# -*- coding: utf-8 -*
'''
Author: san
Date: 2022-09-26 13:19:15
LastEditors: offlinecat <EMAIL>
LastEditTime: 2022-09-27 02:20:34
供热机组参与深度调峰市场
'''

import numpy as np
import pandas as pd
import logging

logger = logging.getLogger()


class OptimizeDeepAdjustSingle:
    def __init__(self, generator_params, coal_consum_func, generator_constraints, plant_elec_rate, deep_adjust_params, market_params):
        # 写入log
        logger.info("-------------------------------供热机组参与深度调峰市场-单台机组---------------------------------")
        logger.info(f"generator_params: {generator_params}, coal_consum_func: {coal_consum_func}, "
                    f"generator_constraints: {generator_constraints}, plant_elec_rate: {plant_elec_rate}, "
                    f"deep_adjust_params: {deep_adjust_params}, market_params: {market_params}")

        # 1、generator_params
        self.generator_rload = generator_params[0]
        self.eboiler_rload = generator_params[1]
        self.eboiler_else_cost = generator_params[2] * 1000       # 元/MWh
        self.eboiler_dividend_ratio = generator_params[3]  # 占比
        self.eboiler_efficiency = generator_params[4]      #

        # 1.1、coal_consum_func，要求是关于发电电负荷和抽气热负荷的三次函数，参数p是个数组
        # f(P, D) = p[0]*P^3 + p[1]*P^2*D + p[2]*P*D^2 + p[3]*D^3 + p[4]*P^2 + p[5]*P*D + p[6]*D^2 + p[7]*P + p[8]*D + p[9]
        self.p_coal_consum_func = coal_consum_func
        
        # 1.2、generator_constraints
        self.p_min = generator_constraints[0]      # 二次函数，参数分别为2次项、1次项、常数项
        self.p_max = generator_constraints[1]
        
        # 1.3、plant_elec_rate 厂用电率
        self.plant_elec_rate = plant_elec_rate
        
        # 2、deep_adjust_params
        self.P_1 = deep_adjust_params[0]   # 有偿调峰补偿基准1d
        self.P_2 = deep_adjust_params[1]   # 有偿调峰补偿基准2d
        self.deep_adjust_uprice1 = deep_adjust_params[2] * 1000      # 元/MWh
        self.deep_adjust_uprice2 = deep_adjust_params[3] * 1000      # 元/MWh
        self.unit_run_correct_coef = deep_adjust_params[4]         # 单机运行修正系数
        self.deep_adjust_correct_coef = deep_adjust_params[5]      # 深调补偿修正系数
        
        # 3、market_params
        self.heat_load = market_params[0]                 # GJ/h
        self.elec_uprice = market_params[1] * 1000               # 元/MWh
        self.heat_uprice = market_params[2]               # 元/GJ
        self.scoal_uprice = market_params[3]              # 元/T
        self.elec_else_ucost = market_params[4] * 1000           # 元/MWh
        self.eboiler_use_elec_uprice = market_params[5] * 1000   # 元/MWh

    # plant_elec_rate 厂用电率
    def f_plant_elec_rate(self, P, t):
        if t == "ndarray":
            r = self.plant_elec_rate[0] * np.multiply(P, P) + self.plant_elec_rate[1] * P + self.plant_elec_rate[2]
        else:
            r = self.plant_elec_rate[0] * P * P + self.plant_elec_rate[1] * P + self.plant_elec_rate[2]
        return r

    # 煤耗
        # B：总煤耗，T/h
        # Bd：发电煤耗，g/kWh
        # Br：供热煤耗，kg/GJ
        # D：抽汽heat_load，GJ/h
        # P：发电机负荷，MW
    def f_coal_consum(self, P, D):
        p = self.p_coal_consum_func
        if isinstance(p, list):
            c = p[0]*np.multiply(np.multiply(P, P), P) + p[1]*np.multiply(np.multiply(P, P), D) + \
                p[2]*np.multiply(np.multiply(P, D), D) + p[3]*np.multiply(np.multiply(D, D), D) + p[4]*np.multiply(P, P)\
                + p[5]*np.multiply(P, D) + p[6]*np.multiply(D, D) + p[7]*P + p[8]*D + p[9]
        elif isinstance(p, str):
            c = eval(p)  # p为传入的煤耗表达式，是关于P,D的表达式，为字符串
        else:
            raise Exception(f'不支持[{p}]煤耗函数类型')
        return c
    
    def fit(self, s_generator_step=1, s_eboiler_step=1):
        eboiler_lst = list(range(self.eboiler_rload[0], self.eboiler_rload[1] + 1, s_eboiler_step))
        if self.eboiler_rload[0] != 0:
            eboiler_lst.insert(0, 0)
        c_P_generator, c_P_eboiler = np.meshgrid(range(self.generator_rload[0], self.generator_rload[1] + 1, s_generator_step),
                                                eboiler_lst)
        X0 = np.c_[c_P_generator.flatten(), c_P_eboiler.flatten()]
        lines = X0.shape[0]
        X_P_generator = X0[:, 0]
        X_P_eboiler = X0[:, 1]
        # D_抽气 = heat_load - P_电锅炉 * eboiler_efficiency * 3.6
        X_D_pump = self.heat_load - X_P_eboiler * self.eboiler_efficiency * 3.6

        # P_上网 = P_发电 - P_电锅炉 - P_发电 * f_plant_elec_rate(P_发电)   # 用来计算售电收入
        k_plant_elec_rate = self.f_plant_elec_rate(X_P_generator, "ndarray")
        X_P_on_grid = X_P_generator - X_P_eboiler - X_P_generator * k_plant_elec_rate / 100

        # 2023.02.28修改：计算深调一二档负荷时，不考虑厂用电，无不用减去厂用电负荷
        # P_计算深调 = P_发电 - P_电锅炉
        X_P_on_deep = X_P_generator - X_P_eboiler

        # 售电收入 = P_上网 * elec_uprice
        X_elec_income = X_P_on_grid * self.elec_uprice
        # 售热收入 = (D_抽气 + P_电锅炉 * eboiler_efficiency * 3.6)  = heat_load * heat_uprice
        X_heat_income = (X_D_pump + X_P_eboiler * self.eboiler_efficiency * 3.6) * self.heat_uprice

        # 深调补偿收入
        # 第一档
        X_deep_adjust1 = self.P_1 - X_P_on_deep
        filter_less_than_0 = X_deep_adjust1 < 0
        X_deep_adjust1[filter_less_than_0] = 0
        filter_more_than_2d = X_deep_adjust1 > (self.P_1 - self.P_2)
        X_deep_adjust1[filter_more_than_2d] = self.P_1 - self.P_2
        # 第二档
        X_deep_adjust2 = self.P_2 - X_P_on_deep
        filter_less_than_0 = X_deep_adjust2 < 0
        X_deep_adjust2[filter_less_than_0] = 0

        # 深调补偿收入
        X_deep_adjust_income = X_deep_adjust1 * self.deep_adjust_uprice1 + X_deep_adjust2 * self.deep_adjust_uprice2
            # 电锅炉分成
        # if X_深调补偿2档的量 >= X_P_电锅炉:                          ***filter_1
        #     X_电锅炉分成 = X_P_电锅炉 * self.深调二档补偿单价
        # elif X_P_电锅炉 - X_深调补偿2档的量 < self.P_1 - self.P_2:   ***filter_2
        #     X_电锅炉分成 = X_深调补偿2档的量 * self.深调二档补偿单价 + (X_P_电锅炉 - X_深调补偿2档的量) * self.深调一档补偿单价
        # else:                                                      ***filter_3
        #     X_电锅炉分成 = X_深调补偿2档的量 * self.深调二档补偿单价 + (self.P_1 - self.P_2) * self.深调一档补偿单价

        X_eboiler_cost = X_P_eboiler * (self.eboiler_use_elec_uprice + self.eboiler_else_cost)  # 电锅炉成本
        X_eboiler_dividend = X_P_eboiler * 0
        filter_1 = X_deep_adjust2 >= X_P_eboiler
        X_eboiler_dividend[filter_1] = X_P_eboiler[filter_1] * self.deep_adjust_uprice2
        filter_2 = (X_deep_adjust2 < X_P_eboiler) * (X_P_eboiler - X_deep_adjust2 <= self.P_1 - self.P_2)
        X_eboiler_dividend[filter_2] = X_deep_adjust2[filter_2] * self.deep_adjust_uprice2 + (X_P_eboiler[filter_2] - X_deep_adjust2[filter_2]) * self.deep_adjust_uprice1
        filter_3 = (X_deep_adjust2 < X_P_eboiler) * (X_P_eboiler - X_deep_adjust2 > self.P_1 - self.P_2)
        X_eboiler_dividend[filter_3] = X_deep_adjust2[filter_3] * self.deep_adjust_uprice2 + (self.P_1 - self.P_2) * self.deep_adjust_uprice1

        # X_eboiler_dividend = X_eboiler_dividend * (1-self.eboiler_dividend_ratio)
        #
        # X_deep_adjust_income = (X_deep_adjust_income - X_eboiler_dividend - X_eboiler_cost * self.eboiler_dividend_ratio) * self.unit_run_correct_coef * self.deep_adjust_correct_coef

        # 自主深调收入
        X_deep_adjust_income_zj = (X_deep_adjust_income - X_eboiler_dividend) * self.unit_run_correct_coef * self.deep_adjust_correct_coef

        # 电锅炉深调收入
        X_deep_adjust_income_gl = (X_eboiler_dividend * self.unit_run_correct_coef * self.deep_adjust_correct_coef - X_eboiler_cost) * self.eboiler_dividend_ratio

        X_deep_adjust_income = X_deep_adjust_income_zj + X_deep_adjust_income_gl

        # 电热炉用电收入 = P_电锅炉 * eboiler_use_elec_uprice
        X_eboiler_use_elec_income = X_P_eboiler * self.eboiler_use_elec_uprice

        # 深调总收入
        X_deep_adjust_income = X_deep_adjust_income + X_eboiler_use_elec_income

        # 总煤耗
        X_total_coal = self.f_coal_consum(X_P_generator, X_D_pump)
        X_cost = X_total_coal.reshape(lines,1) * self.scoal_uprice + self.elec_else_ucost * X_P_generator.reshape(lines,1)
        X_profit = X_elec_income.reshape(lines,1) + X_heat_income.reshape(lines,1) + X_deep_adjust_income.reshape(lines,1) - X_cost

        # 检查约束条件
            # P_min = 240 - 0.08006 * D + 0.0000469 * D * D
        filter_P_min = X_P_generator < self.p_min[2] + self.p_min[1] * X_D_pump + self.p_min[0] * np.multiply(X_D_pump, X_D_pump)
        X_profit[filter_P_min] = -1
            # P_max = 600 - 0.08618 * D + 0.0000026981 * D * D
        filter_P_max = X_P_generator > self.p_max[2] + self.p_max[1] * X_D_pump + self.p_max[0] * np.multiply(X_D_pump, X_D_pump)
        X_profit[filter_P_max] = -1
            # 抽气约束，≥0
        filter_D_pump = X_D_pump < 0
        X_profit[filter_D_pump] = -1
            # 上网负荷约束，≥0
        filter_P_on_grid = X_P_on_grid < 0
        X_profit[filter_P_on_grid] = -1

        # 整合结果
        Xr = np.c_[X0, X_D_pump, X_P_on_grid, X_elec_income, X_heat_income, X_deep_adjust1, X_deep_adjust2,
                   X_deep_adjust_income, X_eboiler_use_elec_income, X_total_coal, X_cost, X_profit]
        columns = ["X_P_generator", "X_P_eboiler", "X_D_pump", "X_P_on_grid", "X_elec_income", "X_heat_income",
                   "X_deep_adjust1", "X_deep_adjust2", "X_deep_adjust_income", "X_eboiler_use_elec_income",
                   "X_total_coal", "X_cost", "X_profit"]
        self.Xr_df = pd.DataFrame(Xr, columns=columns)
        self.Xr_df = self.Xr_df[self.Xr_df['X_profit'] != -1]
        # for col in columns:
        #     self.Xr_df[col] = round(self.Xr_df[col], 3)
        return True

    def get_result(self):

        # 一档
        deep_adjust_zj1 = self.Xr_df[(self.Xr_df['X_P_eboiler'] == 0) & (self.Xr_df['X_deep_adjust1'] > 0) & (self.Xr_df['X_deep_adjust2'] == 0)]  # 自主一档
        deep_adjust_lh1 = self.Xr_df[(self.Xr_df['X_P_eboiler'] > 0) & (self.Xr_df['X_deep_adjust1'] > 0) & (self.Xr_df['X_deep_adjust2'] == 0)]  # 联合一档
        deep_adjust_zj1.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_zj1.reset_index(drop=True, inplace=True)
        deep_adjust_lh1.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_lh1.reset_index(drop=True, inplace=True)
        # 二档
        deep_adjust_zj2 = self.Xr_df[(self.Xr_df['X_P_eboiler'] == 0) & (self.Xr_df['X_deep_adjust2'] > 0)]  # 自主二档
        deep_adjust_lh2 = self.Xr_df[(self.Xr_df['X_P_eboiler'] > 0) & (self.Xr_df['X_deep_adjust2'] > 0)]  # 联合二档
        deep_adjust_zj2.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_zj2.reset_index(drop=True, inplace=True)
        deep_adjust_lh2.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_lh2.reset_index(drop=True, inplace=True)

        # writer = pd.ExcelWriter(r"C:\Users\<USER>\Desktop\result2.xlsx")
        # deep_adjust_zj1.to_excel(writer, sheet_name='zj1')
        # deep_adjust_lh1.to_excel(writer, sheet_name='lh1')
        # deep_adjust_zj2.to_excel(writer, sheet_name='zj2')
        # deep_adjust_lh2.to_excel(writer, sheet_name='lh2')
        # writer.close()

        result = {"deep_adjust_zj1": None if deep_adjust_zj1.empty else deep_adjust_zj1.iloc[0, :].to_dict(),
                  "deep_adjust_lh1": None if deep_adjust_lh1.empty else deep_adjust_lh1.iloc[0, :].to_dict(),
                  "deep_adjust_zj2": None if deep_adjust_zj2.empty else deep_adjust_zj2.iloc[0, :].to_dict(),
                  "deep_adjust_lh2": None if deep_adjust_lh2.empty else deep_adjust_lh2.iloc[0, :].to_dict()
                  }

        # 将结果写入log
        logger.info("--------------------供热机组参与深度调峰市场-单台机组-优化完成--------------------")
        logger.info(f"result: {result}")
        return result


class OptimizeDeepAdjustDouble:
    def __init__(self, generator_params, coal_consum_func, generator_constraints, plant_elec_rate, deep_adjust_params,
                 market_params):
        # 写入log
        logger.info("-------------------------------供热机组参与深度调峰市场-两台机组---------------------------------")
        logger.info(f"generator_params: {generator_params}, coal_consum_func: {coal_consum_func}, "
                    f"generator_constraints: {generator_constraints}, plant_elec_rate: {plant_elec_rate}, "
                    f"deep_adjust_params: {deep_adjust_params}, market_params: {market_params}")
        # 1、generator_params
        self.generator_rload = generator_params[0]
        self.eboiler_rload = generator_params[1]
        self.eboiler_else_cost = generator_params[2] * 1000  # 元/MWh  电锅炉其他成本
        self.eboiler_dividend_ratio = generator_params[3]  # 占比
        self.eboiler_efficiency = generator_params[4]  #

        # 1.1、coal_consum_func，要求是关于发电电负荷和抽气热负荷的三次函数，参数p是个数组
        # f(P, D) = p[0]*P^3 + p[1]*P^2*D + p[2]*P*D^2 + p[3]*D^3 + p[4]*P^2 + p[5]*P*D + p[6]*D^2 + p[7]*P + p[8]*D + p[9]
        self.p_coal_consum_func = coal_consum_func

        # 1.2、generator_constraints
        self.p_min = generator_constraints[0]  # 二次函数，参数分别为2次项、1次项、常数项
        self.p_max = generator_constraints[1]

        # 1.3、plant_elec_rate
        self.plant_elec_rate = plant_elec_rate

        # 2、deep_adjust_params
        self.P_1 = deep_adjust_params[0]  # 有偿调峰补偿基准1d
        self.P_2 = deep_adjust_params[1]  # 有偿调峰补偿基准2d
        self.deep_adjust_uprice1 = deep_adjust_params[2] * 1000  # 元/MWh
        self.deep_adjust_uprice2 = deep_adjust_params[3] * 1000  # 元/MWh
        self.unit_run_correct_coef = deep_adjust_params[4]  # 单机运行修正系数
        self.deep_adjust_correct_coef = deep_adjust_params[5]  # 深调补偿修正系数

        # 3、market_params
        self.heat_load = market_params[0]  # GJ/h
        self.elec_uprice = market_params[1] * 1000  # 元/MWh
        self.heat_uprice = market_params[2]  # 元/GJ
        self.scoal_uprice = market_params[3]  # 元/T
        self.elec_else_ucost = market_params[4] * 1000  # 元/MWh
        self.eboiler_use_elec_uprice = market_params[5] * 1000  # 元/MWh

    # plant_elec_rate 厂用电率
    def f_plant_elec_rate(self, P, t, p):
        if isinstance(p, list):
            if t == "ndarray":
                r = p[0] * np.multiply(P, P) + p[1] * P + p[2]
            else:
                r = p[0] * P * P + p[1] * P + p[2]
        elif isinstance(p, str):
            r = eval(p)  # p必须是关于发电机负荷P的函数
        else:
            raise Exception(f'不支持[{p}]煤耗函数类型')
        return r

    # 煤耗
    # B：总煤耗，T/h
    # Bd：发电煤耗，g/kWh
    # Br：供热煤耗，kg/GJ
    # D：抽汽heat_load，GJ/h
    # P：发电机负荷，MW
    def f_coal_consum(self, P, D, p):
        if isinstance(p, list):
            c = p[0] * np.multiply(np.multiply(P, P), P) + p[1] * np.multiply(np.multiply(P, P), D) + \
                p[2] * np.multiply(np.multiply(P, D), D) + p[3] * np.multiply(np.multiply(D, D), D) + \
                p[4] * np.multiply(P, P) + p[5] * np.multiply(P, D) + p[6] * np.multiply(D, D) + p[7] * P + p[8] * D + \
                p[9]
        elif isinstance(p, str):
            c = eval(p)  # p为传入的煤耗表达式，是关于P,D的表达式，为字符串
        else:
            raise Exception(f'不支持[{p}]煤耗函数类型')
        return c

    def fit(self, s_generator_step=1, s_eboiler_step=1):
        self.result = {}
        self.keys = ['generator1', 'generator2']
        eboiler_lst = list(range(self.eboiler_rload[0], self.eboiler_rload[1] + 1, s_eboiler_step))
        if self.eboiler_rload[0] != 0:
            eboiler_lst.insert(0, 0)

        for i in range(2):
            c_P_generator1, c_P_eboiler1 = np.meshgrid(
                range(self.generator_rload[i][0], self.generator_rload[i][1], s_generator_step), eboiler_lst)
            X0 = np.c_[c_P_generator1.flatten(), c_P_eboiler1.flatten()]

            lines = X0.shape[0]
            X_P_generator = X0[:, 0]
            X_P_eboiler = X0[:, 1] / 2

            # D_抽气 = 供热负荷 - P_电锅炉 * 电锅炉效率 * 3.6
            X_D_pump = self.heat_load / 2 - X_P_eboiler * self.eboiler_efficiency * 3.6

            # P_上网 = P_发电 - P_电锅炉 - P_发电 * f_厂用电率(P_发电)
            p = self.plant_elec_rate[i]
            k_plant_elec_rate = self.f_plant_elec_rate(X_P_generator, "ndarray", p)
            X_P_on_grid = X_P_generator - X_P_eboiler - X_P_generator * k_plant_elec_rate / 100

            # 2023.02.28修改：计算深调一二档负荷时，不考虑厂用电，无不用减去厂用电负荷
            # P_计算深调 = P_发电 - P_电锅炉
            X_P_on_deep = X_P_generator - X_P_eboiler

            # 售电收入 = P_上网 * elec_uprice
            X_elec_income = X_P_on_grid * self.elec_uprice

            # 售热收入 = (D_抽气 + P_电锅炉 * eboiler_efficiency * 3.6)  = heat_load * heat_uprice
            X_heat_income = (X_D_pump + X_P_eboiler * self.eboiler_efficiency * 3.6) * self.heat_uprice

            # 深调补偿收入
            # 第一档
            X_deep_adjust1 = self.P_1[i] - X_P_on_deep
            filter_less_than_0 = X_deep_adjust1 < 0
            X_deep_adjust1[filter_less_than_0] = 0
            filter_more_than_2d = X_deep_adjust1 > (self.P_1[i] - self.P_2[i])
            X_deep_adjust1[filter_more_than_2d] = self.P_1[i] - self.P_2[i]
            # 第二档
            X_deep_adjust2 = self.P_2[i] - X_P_on_deep
            filter_less_than_0 = X_deep_adjust2 < 0
            X_deep_adjust2[filter_less_than_0] = 0

            # 深调补偿收入
            X_deep_adjust_income = X_deep_adjust1 * self.deep_adjust_uprice1 + X_deep_adjust2 * self.deep_adjust_uprice2
            # 电锅炉分成
            # if X_深调补偿2档的量 >= X_P_电锅炉:                          ***filter_1
            #     X_电锅炉分成 = X_P_电锅炉 * self.深调二档补偿单价
            # elif X_P_电锅炉 - X_深调补偿2档的量 < self.P_1 - self.P_2:   ***filter_2
            #     X_电锅炉分成 = X_深调补偿2档的量 * self.深调二档补偿单价 + (X_P_电锅炉 - X_深调补偿2档的量) * self.深调一档补偿单价
            # else:                                                      ***filter_3
            #     X_电锅炉分成 = X_深调补偿2档的量 * self.深调二档补偿单价 + (self.P_1 - self.P_2) * self.深调一档补偿单价

            X_eboiler_cost = X_P_eboiler * (self.eboiler_use_elec_uprice + self.eboiler_else_cost)  # 电锅炉成本
            X_eboiler_dividend = X_P_eboiler * 0
            filter_1 = X_deep_adjust2 >= X_P_eboiler
            X_eboiler_dividend[filter_1] = X_P_eboiler[filter_1] * self.deep_adjust_uprice2
            filter_2 = (X_deep_adjust2 < X_P_eboiler) * (X_P_eboiler - X_deep_adjust2 <= self.P_1[i] - self.P_2[i])
            X_eboiler_dividend[filter_2] = X_deep_adjust2[filter_2] * self.deep_adjust_uprice2 + (
                    X_P_eboiler[filter_2] - X_deep_adjust2[filter_2]) * self.deep_adjust_uprice1
            filter_3 = (X_deep_adjust2 < X_P_eboiler) * (X_P_eboiler - X_deep_adjust2 > self.P_1[i] - self.P_2[i])
            X_eboiler_dividend[filter_3] = X_deep_adjust2[filter_3] * self.deep_adjust_uprice2 + (
                    self.P_1[i] - self.P_2[i]) * self.deep_adjust_uprice1
            # X_eboiler_dividend = X_eboiler_dividend * (1-self.eboiler_dividend_ratio)
            # 错误原因：成本 * 深调补偿修正系数
            # X_deep_adjust_income = (X_deep_adjust_income - X_eboiler_dividend - X_eboiler_cost * self.eboiler_dividend_ratio) * self.unit_run_correct_coef * self.deep_adjust_correct_coef

            # 自主深调收入
            X_deep_adjust_income_zj = (X_deep_adjust_income - X_eboiler_dividend) * self.unit_run_correct_coef * self.deep_adjust_correct_coef

            # 电锅炉深调收入
            X_deep_adjust_income_gl = (X_eboiler_dividend * self.unit_run_correct_coef * self.deep_adjust_correct_coef - X_eboiler_cost) * self.eboiler_dividend_ratio

            X_deep_adjust_income = X_deep_adjust_income_zj + X_deep_adjust_income_gl

            # 电热炉用电收入 = P_电锅炉 * eboiler_use_elec_uprice
            X_eboiler_use_elec_income = X_P_eboiler * self.eboiler_use_elec_uprice

            # 深调总收入
            X_deep_adjust_income = X_deep_adjust_income + X_eboiler_use_elec_income

            # 总煤耗
            p = self.p_coal_consum_func[i]
            X_total_coal = self.f_coal_consum(X_P_generator, X_D_pump, p)
            X_cost = X_total_coal.reshape(lines, 1) * self.scoal_uprice + self.elec_else_ucost * X_P_generator.reshape(
                lines, 1)
            X_profit = X_elec_income.reshape(lines, 1) + X_heat_income.reshape(lines, 1) + X_deep_adjust_income.reshape(
                lines, 1) - X_cost

            # 检查约束条件
            # P_min = 240 - 0.08006 * D + 0.0000469 * D * D
            filter_P_min = X_P_generator < self.p_min[i][2] + self.p_min[i][1] * X_D_pump + self.p_min[i][0] * np.multiply(X_D_pump, X_D_pump)
            X_profit[filter_P_min] = -1
            # P_max = 600 - 0.08618 * D + 0.0000026981 * D * D
            filter_P_max = X_P_generator > self.p_max[i][2] + self.p_max[i][1] * X_D_pump + self.p_max[i][0] * np.multiply(X_D_pump, X_D_pump)
            X_profit[filter_P_max] = -1
                # 抽气约束，≥0
            filter_D_pump = X_D_pump < 0
            X_profit[filter_D_pump] = -1
                # 上网负荷约束，≥0
            filter_P_on_grid = X_P_on_grid < 0
            X_profit[filter_P_on_grid] = -1

            # 整合结果
            Xr = np.c_[X0, X_D_pump, X_P_on_grid, X_elec_income, X_heat_income, X_deep_adjust1, X_deep_adjust2,
                       X_deep_adjust_income, X_eboiler_use_elec_income, X_total_coal, X_cost, X_profit]
            columns = ["X_P_generator", "X_P_eboiler", "X_D_pump", "X_P_on_grid", "X_elec_income", "X_heat_income",
                       "X_deep_adjust1", "X_deep_adjust2", "X_deep_adjust_income", "X_eboiler_use_elec_income",
                       "X_total_coal", "X_cost", "X_profit"]
            Xr_df = pd.DataFrame(Xr, columns=columns)
            Xr_df = Xr_df[Xr_df['X_profit'] != -1]
            self.result[self.keys[i]] = Xr_df
        return True

    def get_result(self):
        df1 = self.result[self.keys[0]]
        df2 = self.result[self.keys[1]]

        # merge方法 -- 合并速度较慢，不再使用
        # Xr_df = pd.merge(df1, df2, on=['X_P_eboiler', 'X_D_pump'])
        # columns = ["X_P_generator", "X_P_eboiler", "X_D_pump", "X_P_on_grid", "X_elec_income", "X_heat_income",
        #            "X_deep_adjust1", "X_deep_adjust2", "X_deep_adjust_income", "X_eboiler_use_elec_income",
        #            "X_total_coal", "X_cost", "X_profit"]

        # concat方法
        df1.set_index(['X_P_eboiler', 'X_D_pump'], drop=True, inplace=True)
        df2.set_index(['X_P_eboiler', 'X_D_pump'], drop=True, inplace=True)
        Xr_df = pd.concat([df1, df2], axis=1)
        # print(Xr_df.head())
        columns = ["X_P_generator_x", "X_P_on_grid_x", "X_elec_income_x", "X_heat_income_x", "X_deep_adjust1_x",
                    "X_deep_adjust2_x", "X_deep_adjust_income_x", "X_eboiler_use_elec_income_x", "X_total_coal_x",
                   "X_cost_x", "X_profit_x",
                   "X_P_generator_y", "X_P_on_grid_y", "X_elec_income_y", "X_heat_income_y", "X_deep_adjust1_y",
                    "X_deep_adjust2_y", "X_deep_adjust_income_y", "X_eboiler_use_elec_income_y", "X_total_coal_y",
                   "X_cost_y", "X_profit_y"]
        Xr_df.columns = columns
        # Xr_df.to_excel(r"C:\Users\<USER>\Desktop\xr_df2.xlsx")
        cal_cols = ["X_P_generator", "X_P_on_grid", "X_elec_income", "X_heat_income", "X_deep_adjust1",
                    "X_deep_adjust2", "X_deep_adjust_income", "X_eboiler_use_elec_income",
                    "X_total_coal", "X_cost", "X_profit"]
        for col in cal_cols:
            Xr_df[col] = Xr_df[col + "_x"] + Xr_df[col + "_y"]
            del Xr_df[col + "_x"]
            del Xr_df[col + "_y"]
        Xr_df = Xr_df[cal_cols]
        Xr_df = Xr_df.reset_index()
        # print(Xr_df.head())
        # 一档
        deep_adjust_zj1 = Xr_df[(Xr_df['X_P_eboiler'] == 0) & (Xr_df['X_deep_adjust1'] > 0) & (Xr_df['X_deep_adjust2'] == 0)]  # 自主一档
        deep_adjust_lh1 = Xr_df[(Xr_df['X_P_eboiler'] > 0) & (Xr_df['X_deep_adjust1'] > 0) & (Xr_df['X_deep_adjust2'] == 0)]   # 联合一档
        deep_adjust_zj1.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_zj1.reset_index(drop=True, inplace=True)
        deep_adjust_lh1.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_lh1.reset_index(drop=True, inplace=True)
        # 二档
        deep_adjust_zj2 = Xr_df[(Xr_df['X_P_eboiler'] == 0) & (Xr_df['X_deep_adjust2'] > 0)]  # 自主二档
        deep_adjust_lh2 = Xr_df[(Xr_df['X_P_eboiler'] > 0) & (Xr_df['X_deep_adjust2'] > 0)]   # 联合二档
        deep_adjust_zj2.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_zj2.reset_index(drop=True, inplace=True)
        deep_adjust_lh2.sort_values("X_profit", ascending=False, inplace=True)
        deep_adjust_lh2.reset_index(drop=True, inplace=True)

        result = {"deep_adjust_zj1": None if deep_adjust_zj1.empty else deep_adjust_zj1.iloc[0, :].to_dict(),
                  "deep_adjust_lh1": None if deep_adjust_lh1.empty else deep_adjust_lh1.iloc[0, :].to_dict(),
                  "deep_adjust_zj2": None if deep_adjust_zj2.empty else deep_adjust_zj2.iloc[0, :].to_dict(),
                  "deep_adjust_lh2": None if deep_adjust_lh2.empty else deep_adjust_lh2.iloc[0, :].to_dict()
                  }

        # writer = pd.ExcelWriter(r"C:\Users\<USER>\Desktop\result2.xlsx")
        # deep_adjust_zj1.to_excel(writer, sheet_name='zj1')
        # deep_adjust_lh1.to_excel(writer, sheet_name='lh1')
        # deep_adjust_zj2.to_excel(writer, sheet_name='zj2')
        # deep_adjust_lh2.to_excel(writer, sheet_name='lh2')
        # writer.close()

        # 将结果写入log
        logger.info("--------------------供热机组参与深度调峰市场-两台机组-优化完成--------------------")
        logger.info(f"result: {result}")
        return result


if __name__ == "__main__":
    # 1、generator_params
    generator_rload = [0, 600]        # 发电机额定功率
    eboiler_rload = [0, 240]          # 电锅炉额定功率
    eboiler_else_cost = 148           # 电锅炉其他成本, 元/MWh
    eboiler_dividend_ratio = 0.3      # 占比
    eboiler_efficiency = 0.95         #
    generator_params = [generator_rload, eboiler_rload, eboiler_else_cost, eboiler_dividend_ratio, eboiler_efficiency]
    # 1.1、coal_consum_func，要求是关于发电电负荷和抽气热负荷的三次函数，参数p是个数组
    # f(P, D) = p[0]*P^3 + p[1]*P^2*D + p[2]*P*D^2 + p[3]*D^3 + p[4]*P^2 + p[5]*P*D + p[6]*D^2 + p[7]*P + p[8]*D + p[9]
    coal_consum_func = [3.91814446e-07, 2.96883672e-09, 1.62200000e-09, 3.59627000e-09, -7.64434271e-04,
                        -1.13581270e-04, -3.47832435e-06, 7.50203180e-01, 3.90635749e-02, 0]
    # 1.2、generator_constraints
    generator_constraints = [[0.0000469, -0.08006, 240],      # 二次函数，参数分别为2次项、1次项、常数项
                             [0.0000026981, -0.08618, 600]]   # 二次函数，参数分别为2次项、1次项、常数项
    # 1.3、plant_elec_rate 厂用电率
    plant_elec_rate = [0.0000095556, 0.0061666667, 8.07]      # 二次函数，参数分别为2次项、1次项、常数项
    
    # 2、deep_adjust_params
    deep_adjust_base1 = 300   # 有偿调峰补偿基准1d
    deep_adjust_base2 = 240   # 有偿调峰补偿基准2d
    deep_adjust_uprice1 = 400         # 元/MWh
    deep_adjust_uprice2 = 1000        # 元/MWh
    unit_run_correct_coef = 0.5   # 单机运行修正系数
    deep_adjust_correct_coef = 1  # 深调补偿修正系数
    deep_adjust_params = [deep_adjust_base1, deep_adjust_base2, deep_adjust_uprice1, deep_adjust_uprice2,
                          unit_run_correct_coef, deep_adjust_correct_coef]
    
    # 3、market_params
    heat_load = 643.25               # GJ/h
    elec_uprice = 324.69             # 元/MWh
    heat_uprice = 21.58              # 元/GJ
    scoal_uprice = 349.06            # 元/T
    elec_else_ucost = 78.62          # 元/MWh
    eboiler_use_elec_uprice = 353    # 元/MWh
    market_params = [heat_load, elec_uprice, heat_uprice, scoal_uprice, elec_else_ucost, eboiler_use_elec_uprice]

    oda = OptimizeDeepAdjustSingle(generator_params, coal_consum_func, generator_constraints, plant_elec_rate,
                                   deep_adjust_params, market_params)
    import matplotlib.pyplot as plt
    if oda.fit(1, 1):  # s_generator_step, s_eboiler_step
        Xr_df = oda.get_result()
        print(Xr_df)
        # Xr_df.to_excel(r'C:\Users\<USER>\Desktop\deep_adjust.xlsx')
        fig = plt.figure(figsize=(12, 12))
        ax = fig.add_subplot(111, projection='3d')
        ax.scatter(Xr_df['deep_adjust_zj1']["X_P_generator"], Xr_df['deep_adjust_zj1']["X_P_eboiler"], Xr_df['deep_adjust_zj1']["X_profit"], s=10)
        ax.scatter(Xr_df['deep_adjust_lh1']["X_P_generator"], Xr_df['deep_adjust_lh1']["X_P_eboiler"], Xr_df['deep_adjust_lh1']["X_profit"], s=20)
        ax.scatter(Xr_df['deep_adjust_zj2']["X_P_generator"], Xr_df['deep_adjust_zj2']["X_P_eboiler"], Xr_df['deep_adjust_zj2']["X_profit"], s=30)
        ax.scatter(Xr_df['deep_adjust_lh2']["X_P_generator"], Xr_df['deep_adjust_lh2']["X_P_eboiler"], Xr_df['deep_adjust_lh2']["X_profit"], s=50)
        plt.show()
    else:
        print("失败")

