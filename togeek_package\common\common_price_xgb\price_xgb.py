# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Laney
# Date       : 2023-03-23 14:54:45
# Description: 通用XGBoost模型，可用于日前价格预测，实时价格预测
"""


import numpy as np
import pandas as pd
import xgboost as xgb
import warnings
import logging

pd.set_option('display.max_columns', None)      # 显示所有列
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class PricePredXGB:
    def __init__(self, all_data, date_list_pred, days_train=6, min_value=0, max_value=1500):

        logger.info("---------------------- 通用价格预测方法：XGB提升树 --------------------------")

        self.all_data = all_data    # 传入json数据
        self.date_list_pred = date_list_pred    # 预测日期列表
        self.days_train = days_train    # 训练数据天数，默认30天
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

        logger.info(f"预测日期列表: {self.date_list_pred}")

    def data_process(self, data_input_json):
        """
        传入数据整理，划分数据集
        :param data_input_json: 传入dict类型的数据
        :return:
        """

        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame(data_input_json)
        # 日期索引升序排列
        data = data.sort_index()

        # 预测字段检测: label
        if "label" not in data.columns:
            logger.info(f"传入数据中不包含预测字段 'label', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段 'label', 请核对传入数据字段!")
        # 关键特征字段检测: "bidding_space"
        if "bidding_space" not in data.columns:
            logger.info(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")

        cols_input = data.columns.tolist()  # 传入数据的所有字段

        data['date'] = data.index.map(lambda s: str(s).split(' ')[0])    # 日期
        data['time'] = data.index.map(lambda s: str(s).split(' ')[1])    # 时间
        data['hour'] = data['time'].map(lambda s: int(s.split(":")[0]))    # 小时：作为特征之一

        # 每日数据点数
        time_point_list = [1, 2, 3, 4, 6, 8, 12, 24, 48, 96]
        time_point = data['time'].nunique()
        if time_point not in time_point_list:
            logger.info(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")
            raise ValueError(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")

        # 划分数据集
        cols_all = cols_input + ['hour']    # 含特征字段和目标字段
        cols_feature = [col for col in cols_all if col != "label"]    # 仅含特征字段

        # 训练集
        data_train = data[data['date'].map(lambda s: s not in self.date_list_pred)]
        data_train = data_train.dropna(axis=0)  # 舍弃含空值的训练数据
        data_train = data_train.sort_index()  # 按日期升序排序

        # 若训练数据最后一天的目标值全部相同（应对实时价格预测），则舍弃最后一日的训练数据
        date_last_train = data_train['date'].max()
        if data_train[data_train['date'] == date_last_train]["label"].nunique() == 1:
            data_train = data_train[data_train['date'].map(lambda s: s != date_last_train)]

        data_train = data_train.iloc[-self.days_train * time_point:, :]     # 筛选指定长度的训练数据

        # 预测集
        data_pred = data[data['date'].map(lambda s: s in self.date_list_pred)]

        # 预测集数据长度检查
        days_pred = len(self.date_list_pred)            # 预测天数
        num_should = days_pred * time_point             # 理论数据量
        num_true = data_pred.shape[0]                   # 实际数据量
        if num_should != num_true:
            raise ValueError(
                f"待预测日期合计{days_pred}天({self.date_list_pred})，相应数据长度应为{num_should}条，实为{num_true}条，请检查传入数据！")

        # 划分X, y
        X_train = data_train[cols_feature]
        y_train = data_train["label"]

        X_pred = data_pred[cols_feature]
        y_pred = data_pred[["label"]]      # 'label' 列应全为空值或全部为相同的填充值，作为输出DataFrame. 索引为日期

        # 预测日传入数据空值检测
        if X_pred.isnull().sum().sum() != 0:
            null_dict = X_pred.isnull().sum().to_dict()
            message = ""
            for k, v in null_dict.items():
                if v != 0:
                    message += f"'{k}'存在{v}个缺失值, "
            message += "请检查预测日数据! "

            logger.info(message)
            raise ValueError(message)

        logger.info(f"训练数据日期范围：'{data_train['date'].min()}' ~ '{data_train['date'].max()}'")
        logger.info(f"训练数据使用字段: {cols_feature}")

        return X_train, y_train, X_pred, y_pred

    def predict(self, learning_rate=0.05, n_estimators=100, to_json=True):
        """
        模型训练、预测、输出
        """

        # 1. 数据处理
        X_train, y_train, X_pred, y_pred = self.data_process(data_input_json=self.all_data)

        # 2. 创建
        # 山西省调优后的参数
        # params = {'learning_rate': learning_rate, 'n_estimators': n_estimators,
        #           'min_child_weight': 4, 'seed': 0, 'subsample': 0.7, 'colsample_bytree': 0.9,
        #           'gamma': 0.4, 'reg_alpha': 1, 'reg_lambda': 3,  'booster': 'gbtree'}

        params = {'learning_rate': learning_rate, 'n_estimators': n_estimators, 'seed': 0,
                  'booster': 'gbtree', 'objective': 'reg:squarederror'}

        model = xgb.XGBRegressor(**params)

        # 3. 训练
        model.fit(X=X_train, y=y_train)

        # 4. 预测及存储
        data_pred = model.predict(X_pred)
        data_pred = np.maximum(data_pred, self.min_value)   # 最小值修正
        data_pred = np.minimum(data_pred, self.max_value)   # 最大值修正

        y_pred['label'] = data_pred
        y_pred = y_pred.rename(columns={'label': 'label_pred'})

        # 5. 输出为json格式
        if to_json:
            result_json = y_pred.to_dict()
            # 6. 写入log
            logger.info("----------------- 通用价格预测方法：XGB提升树模型预测完成！ ---------------------")
            return result_json
        else:
            return y_pred

    @staticmethod
    def cal_acc(data):
        """data中的字段包括：date_time, pred(预测价格), real(实际价格)"""
        data['date_time'] = data['date_time'].astype(str)
        data['date'] = data['date_time'].map(lambda x: x.split(" ")[0])

        aprice = data.groupby('date').mean()['real'].reset_index()
        aprice.columns = ['date', 'areal']
        res = pd.merge(data, aprice, on='date')

        # 计算准确率
        # 除以实际值
        res['acc1'] = res.apply(
            lambda x: 1 - abs(x['pred'] - x['real']) / x['real'] if x['real'] != 0 else 1 if x['pred'] == x[
                'real'] else 0, axis=1)
        res['acc1'] = res['acc1'].map(lambda x: x if x >= 0 else 0)

        # 项目在用
        res['acc2'] = res.apply(lambda x: 1 - abs(x['pred'] - x['real']) / x['areal'] if x['areal'] != 0 else 0, axis=1)
        res['acc2'] = res['acc2'].map(lambda x: x if x >= 0 else 0)

        res['date_time'] = pd.to_datetime(res['date_time'])
        res.set_index('date_time', drop=True, inplace=True)

        return res


if __name__ == '__main__':
    from datetime import datetime, timedelta
    data = pd.read_excel(r"C:\Users\<USER>\Desktop\train_sx.xlsx")
    all_data = data.set_index('date_time', drop=True)

    xgb_res = pd.DataFrame()
    for i in range(437):
        dt0 = str(datetime(2021, 12, 31) + timedelta(i - 6)).split(" ")[0]            # 历史开始日期
        dt1 = str(datetime(2021, 12, 31) + timedelta(1 + i)).split(" ")[0]            # 预测日
        data1 = all_data[(all_data['date'] >= dt0) & (all_data['date'] <= dt1)]       # 获取历史数据
        del data1['date']
        try:
            tmp = PricePredXGB(data1, [dt1]).predict(to_json=False)
            xgb_res = pd.concat([xgb_res, tmp], axis=0)
        except:
            print(dt1)
        # break
    print(xgb_res.head())
    xgb_res.reset_index(inplace=True)
    xgb_res.columns = ['date_time', 'pred']

    xgb_res = pd.merge(data[['date_time', 'label']], xgb_res, on='date_time')
    xgb_res.columns = ['date_time', 'real', 'pred']

    xgb_acc = PricePredXGB.cal_acc(xgb_res)
    xgb_acc.to_excel(r"C:\Users\<USER>\Desktop\xgb_pred.xlsx")
    print('finished!')
