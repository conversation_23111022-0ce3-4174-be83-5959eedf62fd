#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/5/13 11:14
# <AUTHOR> Darlene
import logging
import pandas as pd
from sklearn.ensemble import ExtraTreesRegressor
from sktime.clustering.k_means import TimeSeriesKMeans
import numpy as np
from concurrent.futures import ThreadPoolExecutor
from multiprocessing.pool import ThreadPool
import concurrent.futures
import warnings
warnings.filterwarnings('ignore')
warnings.simplefilter('ignore')


'''
    1、分时段，0时段为 0，1，2；1时段为 8，9；2时段为 17, 18, 19, 20；3时段为 3, 4, 5, 6, 7；4时段为 10, 11, 12, 13, 14, 15, 16；5时段为 21, 22, 23
    2、同时训练各时段
    3、组合结果
'''

class MengxiPriceDtwKmeans:
    def __init__(self, input_data, date_list, time_list, province, jiedian_type, days, min_value=0, max_value=5180):
        self.input_data = self.pre_data(input_data)
        self.province = province
        self.jiedian_type = jiedian_type
        self.time_list = list(range(0, 24)) if time_list == [] else time_list
        self.date_list = date_list
        self.min_value = min_value
        self.max_value = max_value
        self.days = days


    def pre_data(self, data):
        '''
        处理时间列，算竞价空间
        :param data:
        :return:
        '''
        data = pd.DataFrame(data)
        if 'date_time' in data.columns:
            data.set_index('date_time', inplace=True)
        data.sort_index(inplace=True)  # 升序排列

        # 传入数据中若含有列"时间"和"日期"，将其删掉
        if '时间' in data.columns:
            data = data.drop('时间', axis=1)
        if '日期' in data.columns:
            data = data.drop('日期', axis=1)
        data = data.applymap(lambda x: float(x))  # 所有数据转为float类型

        # 新增列：['日期', '时间', '小时', '火电竞价空间']
        data['日期'] = data.index.map(lambda x: x.split(' ')[0])
        data['时间'] = data.index.map(lambda x: x.split(' ')[1])
        data['小时'] = data['时间'].map(lambda x: x.split(':')[0])
        data['小时'] = data['小时'].astype(float)
        data['星期'] = pd.to_datetime(data['日期']).dt.weekday
        # 给东送取负值
        data['日前东送计划'] = data['日前东送计划'].map(lambda x: 0 - abs(x))
        data['预测火电竞价空间'] = data['统调负荷预测'] - data['日前新能源负荷预测'] - data['非市场化出力'] + data['日前东送计划']
        return data


    def dtw_fit(self, t_list):
        pre_etr0 = pd.DataFrame()
        hour_data = self.input_data[self.input_data['小时'].isin(t_list)]
        n = len(t_list) * 4
        for index, date in enumerate(self.date_list):
            print(date)


            # 取今天以前的数据, 重新聚类
            seqs_0 = np.array([])
            his_date = hour_data[hour_data['日期'] < date]['日期'].drop_duplicates().to_list()
            for i, d in enumerate(his_date):
                seq = hour_data[hour_data['日期'] == d]['预测火电竞价空间'].map(lambda x: (x + 20000) / 50).to_list() + \
                                 hour_data[hour_data['日期'] == d]['价格'].to_list()
                seq = np.array(seq)
                if seqs_0.size == 0:
                    seqs_0 = np.copy(seq)
                else:
                    seqs_0 = np.vstack((seqs_0, seq))
            # print(seqs_0)
            # seqs_0 = np.array(seqs_0)
            n_clusters = 4
            model0 = TimeSeriesKMeans(n_clusters=n_clusters, metric="dtw", max_iter=10, random_state=0)
            labels0 = model0.fit_predict(seqs_0)

            # 取历史数据
            print(his_date[-3:])
            price_mean = hour_data[hour_data['日期'].isin(his_date[-3:])][['时间', '价格']]
            price_mean = price_mean.groupby('时间').mean('价格').reset_index()
            tmp_jingjia = hour_data[hour_data['日期'] == date]['预测火电竞价空间'].map(
                lambda x: (x + 20000) / 50).to_list()
            tmp_jiage = price_mean['价格'].to_list()
            test_data = []
            test_data.append(tmp_jingjia + tmp_jiage)


            pre_labels = model0.predict(np.array(test_data))[0]
            cluster_data = seqs_0[labels0 == pre_labels]

            # 先用etr预测一个结果
            train_X = cluster_data[- self.days:, :n].reshape(-1, 1)
            train_y = cluster_data[- self.days:, n:].reshape(-1, 1)

            test_X = np.array(tmp_jingjia).reshape(-1, 1)
            # 模型训练、预测
            etr = ExtraTreesRegressor(n_estimators=100, max_depth=6, random_state=80)
            etr.fit(X=train_X, y=train_y)
            res = etr.predict(X=test_X)  # np.array类型
            # 预测结果处理
            result = hour_data[hour_data['日期'] == date][['价格']]
            result['价格预测值'] = res

            if pre_etr0.empty:
                pre_etr0 = result.copy(deep=True)
            else:
                pre_etr0 = pd.concat([pre_etr0, result])
        return pre_etr0

    def predict(self):
        '''
        多线程训练
        传入时间段、input_data
        :param input_data:
        :return:
        '''
        # 1、分了几个时间段
        times = len(self.time_list)
        result = pd.DataFrame()
        # 单线程
        for i in range(times):
            re_df = self.dtw_fit(self.time_list[i])
            if result.empty:
                result = re_df.copy(deep=True)
            else:
                result = pd.concat([result, re_df])
        # try:
        #     with concurrent.futures.ThreadPoolExecutor(max_workers=times) as executor:
        #         # 提交任务到线程池
        #         futures = [executor.submit(self.dtw_fit, self.time_list[i]) for i in range(times)]
        #
        #         # 获取任务的返回值
        #         for future in concurrent.futures.as_completed(futures):
        #                 re_df = future.result()  # 获取任务的返回值
        #                 if result.empty:
        #                     result = re_df.copy(deep=True)
        #                 else:
        #                     result = pd.concat([result, re_df])
        #     result = result.reset_index()
        #     result = result.rename(columns={'index': 'date_time'})
        #     result = result.sort_values('date_time')
        #     result = result.set_index('date_time')
        #
        # except Exception as err:
        #     logging.error(f"并行任务出错：{err}")
        return result



if __name__ == '__main__':
    input_data = pd.read_excel(r'D:\02file\2025\01蒙西\input\0_20240601_20250402market.xlsx').iloc[-30 * 96:, 1:]
    input_data = input_data.rename(
        columns={'whole_load': '统调负荷预测', 'new_energy_load': '日前新能源负荷预测', 'delivery_power': '日前东送计划',
                 'price': '价格', 'reserve_negative': '负备用', 'reserve_positive': '正备用', 'no_market_power': '非市场化出力'})
    print(input_data.set_index('date_time').to_dict())
    # date_list = ['2025-03-29', '2025-03-30']
    # time_list = [[0, 1, 2], [3, 4, 5, 6, 7], [8, 9], [10, 11, 12, 13, 14, 15, 16], [17, 18, 19, 20], [21, 22, 23]]
    # provinces = '蒙西'
    # jiedian_type = '统一结算点'
    # dtw = MengxiPriceDtwKmeans(input_data, date_list, time_list, provinces, jiedian_type, min_value=0, max_value=5180)
    # pre_price = dtw.predict()
    # print(pre_price)
