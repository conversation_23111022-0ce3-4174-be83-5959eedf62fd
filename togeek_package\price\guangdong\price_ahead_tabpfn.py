import datetime

import pandas as pd
import torch
from tabpfn import TabPFNRegressor
from sklearn.preprocessing import StandardScaler
import numpy as np
import os
import logging

from togeek_package.price.guangdong.usp_pred_mixin_weather import DataFetcherMixin, AHEAD_FEATS_LIST

logger = logging.getLogger()

os.environ["TABPFN_ALLOW_CPU_LARGE_DATASET"] = "1"

class PriceAheadTabGD(DataFetcherMixin):
    def __init__(self,D,df,df_holiday,df_gfs):
        logger.info(f"----------开始运行：广东日前价格预测-----------")
        self.scalar = StandardScaler()
        self.D = D
        self.df = df
        self.df_holiday = df_holiday
        self.df_gfs = df_gfs
        self.best_model=self._default_model()

    def _standalone_train(self) -> bool:
        return False

    def _default_model(self) -> bool:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        return TabPFNRegressor(random_state=80, device=device)

    def _train(self, D: str):
        raise NotImplementedError

    def _pred(self,tojson = True) -> pd.DataFrame:
        """
        :param D: 交易日
        :return:
        """
        train, test = self._data(D=self.D,df=self.df,df_holiday=self.df_holiday,df_gfs=self.df_gfs)

        if train.empty:
            logger.warning(f"---------------注意！交易日{D} train 清洗后没有数据，跳过预测---------------")
            return None

        if test.empty:
            logger.warning(f"---------------注意！交易日{D} test 没有数据，跳过预测---------------")
            return None
            # 检查特征是否存在

        valid_features = [feat for feat in AHEAD_FEATS_LIST if feat in train.columns]
        missing_features = set(AHEAD_FEATS_LIST) - set(valid_features)

        if missing_features:
            logger.warning(f"[WARNING] 缺失以下特征列，已自动剔除: {missing_features}")

        X_train = train[valid_features]
        y_train = train['日前统一结算点电价']
        X_test = test[valid_features]
        # 替换无穷大为 NaN
        X_train = X_train.replace([np.inf, -np.inf], np.nan)
        X_train_scaled = self.scalar.fit_transform(X_train)
        self.best_model.fit(X_train_scaled, y_train)

        X_test_scaled = self.scalar.transform(X_test)
        y_pred = self.best_model.predict(X_test_scaled)

        if y_pred is not None:
            df_pred = pd.DataFrame({"value": y_pred}, index=test['dateTime'])
            if tojson:
                return df_pred.to_dict(orient='list')
            return df_pred

        return None

if __name__ == '__main__':
    D = "2025-07-24"
    df=pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\guangdong\df.xlsx")
    df_gfs=pd.read_excel(r"D:\fwx\gitlab\data_analysis\model_center\fwx\guangdong\df_gfs.xlsx")
    df_holiday=None
    df_pred = PriceAheadTabGD(D,df,df_holiday,df_gfs)._pred()
    print(df_pred)

