from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.nationwide.sun_power.sun_power_pred import SunPowerPred


class SunPowerPredHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        t = params.pop('t',1)
        start_datetime = params.pop('start_datetime', 12)
        his_power = params.pop('his_power')
        lon_lat = params.pop('lon_lat')
        point_day = params.pop('point_day', 96)
        point_movement = params.pop('point_movement', 0)
        cap = params.pop('cap')
        sp_instance = SunPowerPred(t=t, start_datetime=start_datetime, his_power=his_power, lon_lat=lon_lat,
                                    point_day=point_day, point_movement=point_movement,
                                    cap=cap)
        his_power = sp_instance.data_prepare()
        # 3. 获取历史气象数据、补充点数
        his_weather = sp_instance.get_data_his_obs()
        his_weather_p,cols,min_period = sp_instance.weather_point_fill(weather = his_weather)
        # 4. 气象数据挪点、添加时点特征、准备历史训练数据
        train_data = sp_instance.process_train_data(weather = his_weather_p)
        result = sp_instance.train_pred(train_data = train_data,cols = cols,min_period = min_period)
        self.write(result)