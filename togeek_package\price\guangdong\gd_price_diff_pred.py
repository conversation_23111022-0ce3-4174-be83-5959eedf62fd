#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/8/23 10:07 
@Info    ：价差方向预测，输出价差方向、概率及申报系数，
            1 日期 > 实时；
            0 日前 < 实时；
'''


import numpy as np
import pandas as pd
from datetime import datetime, date, timedelta
# from chinese_calendar import is_workday
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier                 # 随机森林分类
from sklearn.ensemble import GradientBoostingClassifier             # 梯度提升树分类
from xgboost import XGBClassifier                                   # xgb分类器
from lightgbm import LGBMClassifier                                 # lgb分类器
from catboost import CatBoostClassifier                             # 可给出概率，clf.predict_proba(test)
import logging
import warnings


logger = logging.getLogger()
warnings.filterwarnings("ignore")


class PriceDiffPredGD:
    def __init__(self, data, price, pred_lst, periods=30, scale=0.2, threshold_proba=0.6, model='xgbc'):
        logger.info("----------开始运行：价差方向预测模型-----------")
        logger.info(f"data:{data}, price: {price}, pred_lst:{pred_lst}, model:{model}")
        self.model = self.init_model(model)
        self.data, self.features = self.prepare_data(data, price)
        self.pred_lst = pred_lst
        self.periods = periods
        self.scale = scale
        self.threshold_proba = threshold_proba

    def init_model(self, model, random_state=8099):
        if model == "gbc":  # 梯度提升树
            return GradientBoostingClassifier(random_state=random_state)
        elif model == "xgbc":
            return XGBClassifier(random_state=random_state)
        elif model == "lgbmc":
            return LGBMClassifier(random_state=random_state)
        elif model == "cbc":
            return CatBoostClassifier(silent=True, random_state=random_state)
        elif model == "rfc":
            return RandomForestClassifier(random_state=random_state)
        else:
            raise ModuleNotFoundError

    def prepare_data(self, df, price):
        data0 = pd.DataFrame.from_dict(df, orient='index').T
        price0 = pd.DataFrame.from_dict(price, orient='index').T
        for df_ in [data0, price0]:
            if "date_time" in df_.columns:
                df_.set_index("date_time", inplace=True)
            df_.index = pd.to_datetime(df_.index)
            for col in df_.columns:
                df_[col] = df_[col].astype(float)
        data1 = data0.resample("H").mean()
        price1 = price0.resample("H").mean()
        data = pd.merge(data1, price1, left_index=True, right_index=True)
        data['日前>实时'] = data.apply(lambda s: 1 if s['日前电价'] > s['实时电价'] else 0, axis=1)
        if ("竞价空间" not in data.columns) and ("日前竞价空间" not in data.columns):
            data['日前竞价空间'] = data['日前统调负荷'] - data[['日前省内A类电源', '日前地方电源出力', '日前西电东送电力', '日前粤港联络线']].sum(axis=1)
        features1 = list(data.columns)
        for c in ['日前>实时', '日前电价', '实时电价']:
            features1.remove(c)
        data, features2 = self.prepare_date(data)
        features = features1 + features2 + ['week', 'hour']  # , 'is_holiday'
        return data, features

    def prepare_date(self, df):
        df['date'] = df.index.astype(str).map(lambda x: x.split(" ")[0])
        df['time'] = df.index.astype(str).map(lambda x: x.split(" ")[1])
        df['hour'] = df['time'].str[:2]
        df['date'] = pd.to_datetime(df['date'])
        # df['is_holiday'] = df['date'].apply(lambda x: is_workday(x) * 1)
        df['week'] = df['date'].apply(lambda x: str(x.weekday()))  # 计算星期：0~6表示星期一到星期日

        # 处理hour和week字段
        if self.model == 'cbc':
            hw = pd.get_dummies(df[['hour', 'week']], prefix=['hour', 'dayofweek'])
            df = pd.concat([df, hw], axis=1)
            cols = list(hw.columns)
        else:
            cols = []
        hw = pd.get_dummies(df[['hour', 'week']], prefix=['hour', 'dayofweek'])
        df = pd.concat([df, hw], axis=1)
        df['week'] = df['week'].astype(int)
        df['hour'] = df['hour'].astype(int)
        return df, cols

    def run(self, is_standard=True, to_json=False):
        res = pd.DataFrame()
        for pred_date in self.pred_lst:
            bid_date = str(pd.to_datetime(pred_date) - timedelta(1))[:10]  # 竞价日 D-1日
            start_date = str(pd.to_datetime(pred_date) - timedelta(self.periods + 1))[:10]  # 历史数据的开始日期
            train = self.data[(self.data['date'] >= start_date) & (self.data['date'] < bid_date)]
            train.dropna(inplace=True)
            test = self.data[self.data['date'] == pred_date]
            train_X, train_y = train[self.features].values, train['日前>实时'].values
            test_X = test[self.features].values

            pred = pd.DataFrame({'date_time': test.index.astype(str)})
            # 数据标准化
            if is_standard:
                scaler = StandardScaler().fit(train_X)  # fit only on training data
                X_train = scaler.transform(train_X)
                X_test = scaler.transform(test_X)

            self.model.fit(X_train, train_y)
            test_y = self.model.predict(X_test).reshape(-1, 1)
            prob = self.model.predict_proba(X_test)
            pred['pred'] = test_y
            pred['prob'] = np.max(prob, axis=1)
            pred["declaration"] = pred["pred"].map(lambda s: 1 - self.scale if s == 1 else self.scale + 1)
            pred.loc[pred["prob"] < self.threshold_proba, "declaration"] = 1  # 概率值小于给定阈值时，不做调整
            res = pd.concat([res, pred], axis=0)
        logger.info("----------价差方向模型预测完成！---------")
        res.reset_index(inplace=True, drop=True)
        if to_json:
            res = res.to_dict()
        return res


if __name__ == '__main__':
    data = pd.read_excel(r"C:\Users\<USER>\Desktop\SHANDONG.xlsx", sheet_name='Sheet1')
    pred_lst = ['2023-08-19', '2023-08-20', '2023-08-21']
    pdp = PriceDiffPredGD(data.to_dict(), price.to_dict(), pred_lst, model='rfc')
    result = pdp.run(to_json=False)
    print(result)
