#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2023/11/24 10:44 
@Info    ：

'''

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.shandong.sim_bidding_space_fitting import SimSegFittingPriceSD


class SimSegFittingPriceHandlerSD(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get('data')
        periods = params.get('periods', 7)
        min_price = params.get('min_price', -80)
        max_price = params.get('max_price', 1300)
        pred_date = params.get('pred_date')
        is_sim = params.get('is_sim', True)
        sim_method = params.get('sim_method', 'euclidean')
        mode = params.get('mode', 'seg')
        pred = SimSegFittingPriceSD(data=data, periods=periods, min_price=min_price, max_price=max_price)
        result = pred.pred_price(pred_date, is_sim=is_sim, sim_method=sim_method, mode=mode, to_json=True)
        self.write(result)
