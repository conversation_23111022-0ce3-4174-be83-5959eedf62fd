# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-07-20 14:14:57
Description :   储能系统调度模型：收益最大化
"""

import numpy as np
import pandas as pd
from datetime import timedelta
from calendar import monthrange
import pyomo.environ as pyo
import warnings
import logging
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class OptimizeEnergyStorage:

    def __init__(self, date_pred, data_load, data_price, is_selected_demand, max_demand_month, price_max_demand,
                 time_point_load, time_point_price, time_point_out, elec_total, dod, soc_init, max_power_charge,
                 max_power_discharge, coef_charge, capacity_transformer, price_capacity, ):
        logger.info("---------------------- START: 广东古瑞瓦特智慧园区 储能需量调节&峰谷套利 收益优化 --------------------------")
        self.date_pred = date_pred  # 目标日期
        self.data_load = data_load  # 历史实际负荷, json格式
        self.data_price = data_price  # 历史实际电价, json格式
        self.is_selected_demand = is_selected_demand  # 目标日期是否选择容量电费模式。0表示未选择，1表示已选择
        self.max_demand_month = max_demand_month  # 目标需量：增加储能系统后，每月最大需量的预期值
        self.price_max_demand = price_max_demand  # 需量月度单价（元/兆瓦时·月）
        self.time_point_load = time_point_load  # 历史实际负荷的点数
        self.time_point_price = time_point_price  # 历史实际电价的点数
        self.time_point_out = time_point_out  # 输出的储能指令点数
        self.elec_total = elec_total  # 储能系统总容量，MWh
        self.dod = dod  # depth of discharge，储能放电深度，0-1之间的浮点数
        self.soc_init = soc_init  # 目标日初始时刻(00:00:00)的soc，0-1之间的浮点数
        self.max_power_charge = max_power_charge  # 最大充电功率MW，正值
        self.max_power_discharge = max_power_discharge  # 最大放电功率MW，正值
        self.coef_charge = coef_charge  # 储能系统充放电效率，0-1之间的浮点数，一般为0.85-0.9之间
        self.capacity_transformer = capacity_transformer  # 变压器计费容量KVA
        self.price_capacity = price_capacity  # 容量月度单价（元/千伏安·月），即：基本电费单价

        self.message = ""   # 数据校验说明

    def data_validation(self):
        """ 数据校验 """

        # 1. 历史数据起止日期
        date_start_input = str(pd.to_datetime(self.date_pred) - timedelta(8)).split(" ")[0]
        date_end_input = str(pd.to_datetime(self.date_pred) - timedelta(2)).split(" ")[0]
        message1 = f"目标日期为:{self.date_pred}, 历史数据日期范围应为:{date_start_input} ~ {date_end_input}。"

        # 2. 历史实际负荷数据校验
        data_load = pd.DataFrame.from_dict(self.data_load, orient="index")    # index为date_time
        data_load.columns = ["load"]
        data_load.index = pd.to_datetime(data_load.index).astype(str)
        data_load["date"] = data_load.index.map(lambda s: s.split(" ")[0])
        data_load["time"] = data_load.index.map(lambda s: s.split(" ")[1])
        data_load = data_load[(data_load["date"] >= date_start_input) & (data_load["date"] <= date_end_input)]
        num_should_load = self.time_point_load * 7
        num_true_load = len(data_load)
        pass_load = num_should_load == num_true_load
        describe_load = "通过" if pass_load else "不通过"
        message2 = f"负荷数据点数为:{self.time_point_load}, 负荷数据量应为:{num_should_load}, 实为:{num_true_load}, 负荷数据校验'{describe_load}'。"

        # 3. 历史实际价格数据校验
        data_price = pd.DataFrame.from_dict(self.data_price, orient="index")  # index为date_time
        data_price.columns = ["price"]  # 特征重命名为"price"
        data_price.index = pd.to_datetime(data_price.index).astype(str)
        data_price["date"] = data_price.index.map(lambda s: s.split(" ")[0])
        data_price["time"] = data_price.index.map(lambda s: s.split(" ")[1])
        data_price = data_price[(data_price["date"] >= date_start_input) & (data_price["date"] <= date_end_input)]
        num_should_price = self.time_point_price * 7
        num_true_price = len(data_price)
        pass_price = num_should_price == num_true_price
        describe_price = "通过" if pass_price else "不通过"
        message3 = f"价格数据点数为:{self.time_point_price}, 价格数据量应为:{num_should_price}, 实为:{num_true_price}, 价格数据校验'{describe_price}'。"

        # 4. 只有两者都校验通过，才算校验成功
        status = pass_load & pass_price
        describe_status = "通过" if status else "不通过"
        self.message = f"数据校验'{describe_status}'：" + message1 + message2 + message3

        return status, data_load, data_price

    def data_pred(self, data_load, data_price):
        """
        根据传入历史数据 预测目标日的价格和负荷
        :param data_load: 校验通过的负荷数据
        :param data_price: 校验通过的价格数据
        :return: df_pred with columns is ["date_time", "load_pred", "price_pred"]
        """
        # 1. 目标日的负荷预测
        data_load = data_load.pivot_table(index="time", columns="date", values="load")
        load_pred = data_load.max(axis=1).values    # 取历史7天同时刻的最大值作为目标日的负荷预测值

        # 将负荷预测值的维度降到输出维度
        step_load = int(self.time_point_load / self.time_point_out)
        load_pred = load_pred.reshape((self.time_point_out, step_load))
        load_pred = load_pred.max(axis=1)

        # 2. 目标日的价格预测
        data_price = data_price.pivot_table(index="time", columns="date", values="price")
        price_pred = data_price.mean(axis=1).values     # 取历史7天同时刻的均值作为目标日的价格预测值
        # 将价格预测值的维度升到输出维度
        step_price = int(self.time_point_out / self.time_point_price)
        price_pred = price_pred.repeat(step_price)

        # 3. 预测值组合数据
        df_pred = pd.DataFrame(
            data=pd.date_range(self.date_pred, periods=self.time_point_out, freq=f"{int(1440/self.time_point_out)}T").astype(str),
            columns=["date_time"]
        )
        df_pred["load_pred"] = load_pred
        df_pred["price_pred"] = price_pred

        return df_pred

    def model_pyomo(self, data_pred):
        """
        建模
        :param data_pred: 目标日的负荷与价格预测数据
        :return:
        """
        # 1. 全局参数计算
        price_pred = tuple(data_pred["price_pred"].values)  # 目标日预测电价
        load_pred = tuple(data_pred["load_pred"].values)    # 目标日预测负荷
        year, month = self.date_pred.split("-")[:2]
        days_month = monthrange(int(year), int(month))[1]   # 目标日期所在月份的天数
        elec_init = self.soc_init * self.elec_total     # 储能初始时刻电量MWh
        hour_period = 24 / self.time_point_out   # 每个时段的时长h

        # 2. 创建模型
        model = pyo.ConcreteModel()

        # 3. 添加索引集合
        model.idx = pyo.RangeSet(0, self.time_point_out - 1)    # 首尾均包含

        # 4. 添加预测电价参数
        model.price_pred = pyo.Param(model.idx, initialize={k: v for k, v in enumerate(price_pred)})

        # 5. 添加决策变量1：储能系统功率列表
        model.power_list = pyo.Var(model.idx, domain=pyo.Reals, bounds=(-self.max_power_discharge, self.max_power_charge))

        # 6. 添加决策变量2：每个时段终点储能的电量值
        model.elec_list = pyo.Var(model.idx, domain=pyo.Reals, bounds=(self.elec_total * (1 - self.dod), self.elec_total))

        # 7. 添加目标函数
        def obj_rule(model):
            """ 定义目标函数 """
            # 目标日峰谷套利收益: -充电功率MW*充电时长h*充电价格(元/MWh)
            income_fenggu = sum([-model.power_list[i] * model.price_pred[i] for i in model.idx])

            # 若采用需量电费，则存在需量调节收益: 容量电费 - 需量电费
            if self.is_selected_demand:
                income_demand = (self.capacity_transformer * self.price_capacity - self.max_demand_month * self.price_max_demand) / days_month
            else:
                income_demand = 0

            # 总收益
            income = income_fenggu + income_demand

            return income
        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.maximize)

        # 8. 添加约束条件1：目标日每个时段的总负荷不超过目标需量
        def const_rule1(model, i):
            """ 定义约束条件1 """
            return load_pred[i] + model.power_list[i] <= self.max_demand_month

        # 若选择了需量电费，则添加该约束
        if self.is_selected_demand:
            model.constraint1 = pyo.Constraint(model.idx, rule=const_rule1)

        # 9. 添加约束条件2：每个时段终点电量=该时段起点电量+该时段功率*该时段时长
        def const_rule2(model, i):
            """ 定义约束条件2 """
            if i == 0:
                return model.elec_list[i] == elec_init + model.power_list[i] * hour_period
            else:
                return model.elec_list[i] == model.elec_list[i - 1] + model.power_list[i] * hour_period
        model.constraint2 = pyo.Constraint(model.idx, rule=const_rule2)

        return model

    def optimize(self):

        # 创建输出格式
        res_dict = {
            "load_pred": {},
            "price_pred": {},
            "power_of_storage": {},
            "soc_of_storage": {},
            "max_income": 0,
            "message": self.message,
        }

        # 传入历史数据校验, 若校验不通过，直接返回空值
        logger.info("start: 历史实际负荷 & 历史实际价格校验 ... ")
        status, data_load, data_price = self.data_validation()
        res_dict["message"] = self.message  # 更新校验失败说明
        logger.info(self.message)
        if not status:
            return res_dict

        # 数据预测
        logger.info("start: 目标日期 负荷&价格 预测 ... ")
        df_pred = self.data_pred(data_load=data_load, data_price=data_price)

        # 优化问题建模
        logger.info("start: 建模 ... ")
        model = self.model_pyomo(data_pred=df_pred)

        # 调用求解器ipopt求解
        logger.info("start: 优化求解 ... ")
        opt = pyo.SolverFactory("ipopt")
        solution = opt.solve(model)

        # 最优解 & 对应参数提取
        max_income = model.obj()    # 目标日期最大收入(元)
        power_of_storage = [pyo.value(model.power_list[i]) for i in range(self.time_point_out)]     # 储能充放电功率
        elec_of_storage = [pyo.value(model.elec_list[i]) for i in range(self.time_point_out)]       # 储能电量

        # 数据后处理
        df_pred["power_of_storage"] = power_of_storage
        df_pred["soc_of_storage"] = np.array(elec_of_storage) / self.elec_total
        res_dict = df_pred.set_index("date_time").to_dict()
        res_dict["max_income"] = max_income
        res_dict["message"] = self.message

        logger.info("---------------------- END: 广东古瑞瓦特智慧园区 储能需量调节&峰谷套利 收益优化 --------------------------")

        return res_dict


if __name__ == '__main__':
    path_data_demo = r"F:\ToGeek\20230710_广东储能\data\data_model_input\data_input.xlsx"
    # 历史实际负荷
    df_load = pd.read_excel(path_data_demo, sheet_name="data_load")
    data_load_dict = df_load.set_index("date_time").to_dict()["load"]
    # 历史实际电价
    df_price = pd.read_excel(path_data_demo, sheet_name="data_price")
    data_price_dict = df_price.set_index("date_time").to_dict()["price"]
    # 其他参数
    df_params = pd.read_excel(path_data_demo, sheet_name="params")
    params_dict = df_params.set_index("param").to_dict()["value"]

    m = OptimizeEnergyStorage(data_load=data_load_dict, data_price=data_price_dict, **params_dict)

    res_out = m.optimize()
    print(res_out)
    # print(res_out["load_pred"])
    # print(res_out["price_pred"])
    # print(res_out["power_of_storage"])
    # print(res_out["soc_of_storage"])
    # print(res_out["max_income"])
    # print(res_out["message"])
