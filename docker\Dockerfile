FROM togeek_data_py310:v1.8

USER root
ENV PATH=/opt/conda/bin:$PATH
RUN pip install pyscipopt && pip cache purge
RUN mkdir -p /root/dockerapp/togeek_package
# 更新源并安装所需工具
RUN apt-get update && \
    apt-get install -y iputils-ping telnet curl

# 可选：清理缓存减小镜像体积
RUN apt-get clean && rm -rf /var/lib/apt/lists/*
COPY togeek_package-2.3.5-py3-none-any.whl /root/dockerapp/togeek_package

WORKDIR /root/dockerapp/togeek_package

RUN pip install --default-timeout=100000 togeek_package-2.3.5-py3-none-any.whl


CMD ["togeek_package"]
