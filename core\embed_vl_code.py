# -*- coding: utf-8 -*-

import os


def embed(source, target=None, vl_type='VLA', code=None, path='.'):
    assert os.path.exists(source), f'源文件[{source}]不存在'
    assert vl_type in ['VLA', 'VLB']
    code = code if code is None else f"'{code}'"
    with open(source, encoding='utf-8') as f:
        lines = f.readlines()

    for i, line in enumerate(lines):
        if line.strip().replace(' ', '').lower().startswith('#validlicense'):
            break

    with open('license.py', encoding='utf-8') as f:
        vl_code = f.read()

    lines.insert(i + 1, vl_code)
    lines.insert(i + 2, '\n\n')
    lines.insert(i + 3, 'from tglibs.easy_dir import join\n\n')
    lines.insert(i + 4, f"{vl_type}(join(__file__, '{path}', 'license'), {code})\n")
    lines.insert(i + 5, 'del VLA\ndel VLB\n')

    target = target or f'vl_{source}'
    with open(target, 'w', encoding='utf-8') as f:
        f.writelines(lines)

    return target
