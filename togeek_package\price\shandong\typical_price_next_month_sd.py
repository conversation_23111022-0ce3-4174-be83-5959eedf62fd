# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-05-19 10:07:03
Description :   山东次月月典型电价预测，用于国华山东项目
"""


import numpy as np
import pandas as pd
import calendar
from dateutil.relativedelta import relativedelta
import logging
import warnings


logger = logging.getLogger()
warnings.filterwarnings('ignore')


class TypicalPriceNextMonth:

    def __init__(self, data, year_pred: str, month_pred: str, time_points: int = 96, weight_m_1=0.8, min_price=-80, max_price=1300):
        logger.info("---------------------- Start: 山东次月月典型电价预测 --------------------------")
        self.data = data                    # 传入参数数据, json格式
        self.year_pred = year_pred          # 待预测年份
        self.month_pred = month_pred        # 待预测月份
        self.time_points = time_points      # 每天的时刻点数
        self.weight_m_1 = weight_m_1        # 预测月前1月的权重, 经调参，0.8最佳
        self.min_price = min_price          # 价格下限
        self.max_price = max_price          # 价格上限
        self.message_error = ""             # 数据错误信息

    def data_process(self):
        """
        数据校验, 重构
        如果数据校验失败，只返回message,
        否则，顺次返回 (df_m_2, df_m_1)
        :return:
        """

        # 日期相关参数
        year_month_pred = f"{self.year_pred}-{self.month_pred}"  # 待预测月份, "2023-09"
        year_month_start = str(pd.to_datetime(year_month_pred) - relativedelta(months=2))[:7]  # 预测月前2月, "2023-07"
        year_month_end = str(pd.to_datetime(year_month_pred) - relativedelta(months=1))[:7]  # 预测月前1月, "2023-08"

        logger.info(f"year_month_start: {year_month_start}")
        logger.info(f"year_month_end  : {year_month_end}")
        logger.info(f"year_month_pred : {year_month_pred}")

        # 读取数据
        df = pd.DataFrame(self.data)
        df["date"] = df.index.map(lambda s: s.split(" ")[0])
        df["time"] = df.index.map(lambda s: s.split(" ")[1])
        df["year"] = df["date"].map(lambda s: s.split("-")[0])
        df["month"] = df["date"].map(lambda s: s.split("-")[1])
        df["year_month"] = df["year"] + "-" + df["month"]

        # 数据筛选 & 校验
        df_m_list = []
        for year_month in [year_month_start, year_month_end]:
            df_m = df[df["year_month"] == year_month]
            days_m = calendar.monthrange(int(year_month[:4]), int(year_month[5:]))[1]  # 当月天数
            num_null = df_m['price'].isnull().sum()         # 当月价格缺失值
            num_sample_should = days_m * self.time_points   # 当月理论数据条数
            num_sample_true = len(df_m)                     # 当月实际数据条数

            # 数据错误信息保存至：self.message_error，若无错误，self.message=""
            if num_null != 0:
                self.message_error += year_month.replace("-", "年") + f"月缺失值数量：{num_null}, 请检查传入数据！"
                return self.message_error
            # elif num_sample_true != num_sample_should:
            #     self.message_error += year_month_end.replace("-", "年") + f"月, {days_m}天, 实际价格数据应为{num_sample_should}条, 实为{num_sample_true}条, 请检查传入数据！"
            #     return self.message_error
            else:
                logger.info(year_month.replace("-", "年") + f"月, {days_m}天, 实际价格数据应为{num_sample_should}条, 实为{num_sample_true}条, 数据校验通过；")

            df_m_list.append(df_m)

        return df_m_list

    def pred_monthly_price(self):
        # 构造输出数据结构
        result = {
            "year_pred": self.year_pred,
            "month_pred": self.month_pred,
            "price_pred": {},
            "message_error": ""
        }

        # 数据校验
        data_res = self.data_process()
        if len(self.message_error) != 0:
            result["message_error"] = self.message_error
        else:
            df_m_2, df_m_1 = data_res[0],  data_res[1]   # 预测月前2月数据, 预测月前1月数据
            # 加权平均
            df_price_m_2 = df_m_2.pivot_table(index="time", columns="date", values="price").mean(axis=1)
            df_price_m_1 = df_m_1.pivot_table(index="time", columns="date", values="price").mean(axis=1)
            df_pred = (df_price_m_1 * self.weight_m_1 + df_price_m_2 * (1 - self.weight_m_1)).reset_index()

            # 上下限约束
            df_pred.columns = ["time", "price_pred"]
            df_pred["price_pred"] = np.clip(df_pred["price_pred"].values, self.min_price, self.max_price)

            # 线性插值为96点
            df_pred_96 = pd.DataFrame({"time": pd.date_range("00:00", "23:45", freq="15T").map(lambda s: str(s).split(" ")[1]).values})
            df_pred = pd.merge(df_pred_96, df_pred, how="left")
            df_pred["price_pred"] = df_pred["price_pred"].interpolate(method='linear')

            price_pred_dict = df_pred.set_index("time").to_dict()["price_pred"]
            result["price_pred"] = price_pred_dict

        logger.info("---------------------- End: 山东次月月典型电价预测 --------------------------")

        return result


if __name__ == '__main__':
    import json
    data_path = r"E:\data_research\data_shandong\data\data_demo_monthly_price\data_input_shandong_month.json"
    with open(data_path, "r", encoding="utf-8") as f:
        data_json = json.loads(f.read())
    model = TypicalPriceNextMonth(data=data_json, year_pred="2023", month_pred="05", time_points=24)
    res = model.pred_monthly_price()
    print(res)
