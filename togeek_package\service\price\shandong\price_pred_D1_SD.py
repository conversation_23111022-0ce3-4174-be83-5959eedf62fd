# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-12-05 15:30:41
Description :   山东 D+1 日前/实时 统一出清电价预测 (96点)
    内容：
        D+1统一出清电价_日前：通用随机森林模型 + 补丁1 + 补丁2
        D+1统一出清电价_实时：通用随机森林模型
"""

import os
# import jenkspy
import numpy as np
import pandas as pd
import requests as rs
# from scipy import optimize
# from scipy.spatial import distance
from datetime import timedelta
from sklearn.ensemble import ExtraTreesRegressor
import warnings
import logging

pd.set_option('display.max_columns', None)      # 显示所有列
warnings.filterwarnings("ignore")
logger = logging.getLogger()


def get_data_datasets(grid: str, key_dict: dict, date_start: str, date_end: str, path_save: str = None):
    """
    从 datasets 数据库查询数据
    Parameters
    ----------
    grid: 省份, 可选["SHANXI", "SHANDONG", "MENGXI"]
    key_dict: 字段名称: 索引，示例： {"/日前/统一结算点电价": 399, "/实时/统一结算点电价": 405}。从表 datasets/ds_index_meta 中查询
    date_start: 查询起始日期
    date_end: 查询终止日期
    path_save: 查询数据保存地址: xxx.xlsx

    Returns data
    -------

    """
    # 1. 构造请求参数
    url = f'http://************/datasets/api/indexes/{", ".join([str(i) for i in key_dict.values()])}'
    params = {
        'grid': grid,
        'startTime': f"{date_start} 00:00:00",
        'endTime': f"{date_end} 23:45:00",
        'appId': 'XvBUb-pzgy0ZsXdPyaCz-',
        'token': '1037f6593cd59a4243a919f87a1ada1ffcb05d5224c22ef51ff18a108606fd0306b7d215c29b95bec2390d276c3c6dbb'
    }

    # 2. 获取数据
    res_dict = rs.get(url, params=params)

    # 3. 数据整理
    df_list = []

    for key in list(key_dict.keys()):
        df_tmp = pd.DataFrame(res_dict.json()['data'][key]['points'])
        df_tmp = df_tmp.rename(columns={'value': key})
        df_list.append(df_tmp)

    date_time_list = pd.date_range(f"{date_start} 00:00:00", f"{date_end} 23:45:00", freq="15T").astype(str).values
    df = pd.DataFrame({"time": date_time_list})
    for i in range(len(list(key_dict.keys()))):
        df = pd.merge(df, df_list[i], on='time', how="left")
    df = df.rename(columns={"time": "date_time"})
    df['date'] = df['date_time'].map(lambda s: s.split(" ")[0])
    df = df.set_index(["date_time", "date"])

    if path_save:
        df.to_excel(path_save)
        print(f"查询数据已保存，请查看：{os.path.abspath(path_save)}")

    return df


class PricePredETR:

    def __init__(self, all_data, date_list_pred, days_train=30, min_value=0, max_value=1500):

        logger.info("---------------------- START 实时电价预测：极端随机森林ETR --------------------------")

        self.all_data = all_data    # 传入json数据
        self.date_list_pred = date_list_pred    # 预测日期列表
        self.days_train = days_train    # 训练数据天数，默认30天
        self.min_value = min_value  # 价格下限
        self.max_value = max_value  # 价格上限

        logger.info(f"预测日期列表: {self.date_list_pred}")

    def data_process(self, data_input_json):
        """
        传入数据整理，划分数据集
        :param data_input_json: 传入dict类型的数据
        :return:
        """

        # 将传入的json转为pd.DataFrame类型
        data = pd.DataFrame(data_input_json)
        # 日期索引升序排列
        data = data.sort_index()

        # 预测字段检测: label
        if "label" not in data.columns:
            logger.info(f"传入数据中不包含预测字段 'label', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段 'label', 请核对传入数据字段!")
        # 关键特征字段检测: "bidding_space"
        if "bidding_space" not in data.columns:
            logger.info(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")
            raise ValueError(f"传入数据中不包含目标字段'bidding_space', 请核对传入数据字段!")

        cols_input = data.columns.tolist()  # 传入数据的所有字段

        data['date'] = data.index.map(lambda s: str(s).split(' ')[0])    # 日期
        data['time'] = data.index.map(lambda s: str(s).split(' ')[1])    # 时间
        data['hour'] = data['time'].map(lambda s: int(s.split(":")[0]))    # 小时：作为特征之一

        # 每日数据点数
        time_point_list = [1, 2, 3, 4, 6, 8, 12, 24, 48, 96]
        time_point = data['time'].nunique()
        if time_point not in time_point_list:
            logger.info(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")
            raise ValueError(f"传入数据时间点数为: {time_point}, 不在{time_point_list}中, 请核对传入数据！")

        # 划分数据集
        cols_all = cols_input + ['hour']    # 含特征字段和目标字段
        cols_feature = [col for col in cols_all if col != "label"]    # 仅含特征字段

        # 训练集
        self.date_list_pred.sort()  # 将预测日期升序排
        pred_d1 = self.date_list_pred[0]
        data_train = data[data['date'] < pred_d1]
        data_train = data_train.dropna(axis=0)  # 舍弃含空值的训练数据
        data_train = data_train.sort_index()  # 按日期升序排序

        # 若训练数据最后一天的目标值全部相同（应对实时价格预测），则舍弃最后一日的训练数据
        date_last_train = data_train['date'].max()
        if data_train[data_train['date'] == date_last_train]["label"].nunique() == 1:
            data_train = data_train[data_train['date'].map(lambda s: s != date_last_train)]

        data_train = data_train.iloc[-self.days_train * time_point:, :]     # 筛选指定长度的训练数据

        # 预测集
        data_pred = data[data['date'].map(lambda s: s in self.date_list_pred)]

        # 预测集数据长度检查
        days_pred = len(self.date_list_pred)            # 预测天数
        num_should = days_pred * time_point             # 理论数据量
        num_true = data_pred.shape[0]                   # 实际数据量
        if num_should != num_true:
            raise ValueError(
                f"待预测日期合计{days_pred}天({self.date_list_pred})，相应数据长度应为{num_should}条，实为{num_true}条，请检查传入数据！")

        # 划分X, y
        X_train = data_train[cols_feature]
        y_train = data_train["label"]

        X_pred = data_pred[cols_feature]
        y_pred = data_pred[["label"]]      # 'label' 列应全为空值或全部为相同的填充值，作为输出DataFrame. 索引为日期

        # 预测日传入数据空值检测
        if X_pred.isnull().sum().sum() != 0:
            null_dict = X_pred.isnull().sum().to_dict()
            message = ""
            for k, v in null_dict.items():
                if v != 0:
                    message += f"'{k}'存在{v}个缺失值, "
            message += "请检查预测日数据! "

            logger.info(message)
            raise ValueError(message)

        logger.info(f"训练数据日期范围：'{data_train['date'].min()}' ~ '{data_train['date'].max()}'")
        logger.info(f"训练数据使用字段: {cols_feature}")

        return X_train, y_train, X_pred, y_pred

    def predict(self, n_est=100, max_depth=6):
        """
        模型训练、预测、输出
        """

        # 1. 数据处理
        X_train, y_train, X_pred, y_pred = self.data_process(data_input_json=self.all_data)

        # 2. 创建
        etr = ExtraTreesRegressor(n_estimators=n_est, max_depth=max_depth, random_state=8099)

        # 3. 训练
        etr.fit(X=X_train, y=y_train)

        # 4. 预测及存储
        data_pred = etr.predict(X_pred)
        data_pred = np.maximum(data_pred, self.min_value)   # 最小值修正
        data_pred = np.minimum(data_pred, self.max_value)   # 最大值修正

        y_pred['label'] = data_pred
        y_pred = y_pred.rename(columns={'label': 'label_pred'})

        # 5. 输出为json格式
        result_json = y_pred.to_dict()

        # 6. 写入log
        logger.info("---------------------- END 实时电价预测：极端随机森林ETR --------------------------")

        return result_json


class DataPredD1SD:
    def __init__(self, bid_date, days_train=30, threshold_top=95, threshold_ratio=0.25):
        logger.info(f"----------------------- START: 山东D1价格预测 竞价日期: {bid_date} -------------------------")

        self.bid_date = bid_date                # 竞价日期
        self.days_train = days_train            # 训练数据天数
        self.threshold_top = threshold_top      # 历史价格分位数，高于该值的训练样本呢将被丢弃
        self.threshold_ratio = threshold_ratio  # 竞价空间/直调负荷的比例，低于该比例，预测价格修正为-80

        self.date_selected_start = str(pd.to_datetime(self.bid_date) - timedelta(self.days_train)).split(" ")[0]     # 数据查询起始日期
        self.d1 = str(pd.to_datetime(self.bid_date) + timedelta(1)).split(" ")[0]        # 数据查询结束日期
        self.data_all = self.get_data_all()
        self.ratio = None

    def get_data_all(self):
        """ 从 datasets 数据库查询训练数据 """
        # 1. 待查询字段
        key_dict = {
            "/日前/统一出清电价": 996,  # 96点
            "/实时/统一出清电价": 995,  # 96点
            "/日前/直调负荷": 357,
            "/日前/风电总加": 359,
            "/日前/光伏总加": 360,
            "/日前/联络线受电负荷": 358,
            "/日前/地方电厂发电总加": 363,
            # "/日前/核电总加": 361,    # 核电为"非市场化出力"
            "/日前/自备机组总加": 362,
        }
        data_all = get_data_datasets(
            grid="SHANDONG",
            key_dict=key_dict,
            date_start=self.date_selected_start,
            date_end=self.d1,
            path_save=None).reset_index()

        # 3. 数据整理
        data_all.columns = [col.replace("/", "") for col in data_all.columns]

        return data_all

    def get_pred_D1_ahead(self):
        """
        D+1日前价格预测, 随机森林+补丁1+补丁2
        :return: df_pred_ahead
        """
        # 1. 输入数据整理
        df_ahead = self.data_all.drop(["实时统一出清电价"], axis=1)
        df_ahead.loc[df_ahead["date"] >= self.d1, "日前统一出清电价"] = None  # D+1及以后的日前价格未知
        df_ahead = df_ahead.rename(columns={"日前统一出清电价": "label"})
        df_ahead["bidding_space"] = df_ahead['日前直调负荷'] - df_ahead[['日前联络线受电负荷', '日前风电总加', '日前光伏总加', '日前自备机组总加']].sum(axis=1)

        # 1.1. 补丁1: 训练数据中Top5%的价格, drop
        train = df_ahead[df_ahead["date"] <= self.bid_date].sort_values("date_time").iloc[-self.days_train * 96:, :]
        test = df_ahead[df_ahead["date"] == self.d1]
        self.ratio = (test["bidding_space"] / test["日前直调负荷"]).values

        threshold_price = np.percentile(a=train["label"].values, q=self.threshold_top)
        train_fixed = train[train["label"] < threshold_price]
        df_ahead_fixed = pd.concat([train_fixed, test])

        df_ahead_fixed = df_ahead_fixed.drop(labels="date", axis=1)
        df_ahead_fixed = df_ahead_fixed.set_index("date_time")

        # 2. 初始化模型
        method_ahead = PricePredETR(
            all_data=df_ahead_fixed,
            date_list_pred=[self.d1],
            days_train=self.days_train,
            min_value=-80,
            max_value=1300
        )

        # 3. 预测结果整理
        df_pred_ahead = pd.DataFrame(method_ahead.predict())
        df_pred_ahead.index.name = "date_time"
        df_pred_ahead = df_pred_ahead.reset_index()
        df_pred_ahead = df_pred_ahead.rename(columns={"label_pred": "日前电价预测值"})

        # 4. 补丁2：竞价空间 / 直调负荷 低于25% 的时刻，预测值修正为 -80
        df_pred_ahead["ratio"] = self.ratio
        df_pred_ahead.loc[df_pred_ahead["ratio"] < self.threshold_ratio, "日前电价预测值"] = -80
        df_pred_ahead = df_pred_ahead.drop("ratio", axis=1)

        return df_pred_ahead

    def get_pred_D1_real(self):
        """
        D+1实时价格预测, ETR
        :return: df_pred_real
        """
        # 1. 输入数据构造
        df_real = self.data_all.drop("日前统一出清电价", axis=1)
        df_real.loc[df_real["date"] >= self.bid_date, "实时统一出清电价"] = None    # 竞价日及以后的实时价格未知
        df_real = df_real.rename(columns={"实时统一出清电价": "label"})
        df_real["bidding_space"] = df_real['日前直调负荷'] - df_real[['日前联络线受电负荷', '日前风电总加', '日前光伏总加', '日前自备机组总加']].sum(axis=1)
        df_real = df_real.drop("date", axis=1)
        df_real = df_real.set_index("date_time")

        # 2. 模型构造
        method_real = PricePredETR(
            all_data=df_real,
            date_list_pred=[self.d1],
            days_train=self.days_train,
            min_value=-80,
            max_value=1300
        )

        # 3. 预测结果处理
        df_pred_real = pd.DataFrame(method_real.predict())
        df_pred_real.index.name = "date_time"
        df_pred_real = df_pred_real.reset_index()
        df_pred_real = df_pred_real.rename(columns={"label_pred": "实时价格预测值"})

        return df_pred_real

    def predict(self):
        """ D+1 日前/实时 出清电价预测值 96点 """
        # 1. D+1 日前出清价格预测
        df_pred_ahead = self.get_pred_D1_ahead()

        # 2. D+1 实时出清价格预测
        df_pred_real = self.get_pred_D1_real()

        # 数据整理
        df_pred_D1 = pd.merge(df_pred_ahead, df_pred_real)
        df_pred_D1["竞价日期"] = self.bid_date
        df_pred_D1["类型"] = "D+1"
        df_pred_D1 = df_pred_D1.rename(columns={"date_time": "运行日期"})
        df_pred_D1 = df_pred_D1.set_index(["竞价日期", "运行日期", "类型"]).reset_index()

        logger.info(f"----------------------- END: 山东D1价格预测 竞价日期: {self.bid_date} -------------------------")

        return df_pred_D1


if __name__ == '__main__':
    m = DataPredD1SD(bid_date="2023-12-21")
    df_pred_d1 = m.predict()
    print(df_pred_d1)
