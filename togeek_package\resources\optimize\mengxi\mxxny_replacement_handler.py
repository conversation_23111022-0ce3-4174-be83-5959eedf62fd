#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/8/2 11:33
# <AUTHOR> <PERSON><PERSON>
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.optimize.mengxi.optimize_mx_replacement import ReplacementMxXny
from togeek_package.optimize.mengxi.optimize_mx_replacement import OptimizeMxContrace
class MxReplacementXnyHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        num_n = params.pop('num_n')
        num_m = params.pop('num_m')
        longterm_data = params.pop('longterm_data')
        node_price = params.pop('node_price')
        general_price = params.pop('general_price')
        high_line = params.pop('high_line', 1.1)
        low_line = params.pop('low_line', 0.85)
        points = params.pop('points', 96)
        model = ReplacementMxXny(num_n=num_n, num_m=num_m, longterm_data=longterm_data, node_price=node_price, general_price=general_price,
                           points=points, high_line=high_line, low_line=low_line)
        result = model.get_result(json=True)
        self.write(result)

class MxReplacementLyHandler(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        gen_data = params.pop('gen_data')
        node_data = params.pop('node_data')
        p_data = params.pop('p_data')
        con_data = params.pop('con_data')
        e_data = params.pop('e_data')
        o_data = params.pop('o_data')
        lv_price = params.pop('lv_price')
        opt_date = params.pop('opt_date')
        points = params.pop('points', 96)
        limit_num = params.pop('limit_num', 96)
        return_url = params.pop('return_url', {'url':'', 'authorization':''})
        model = OptimizeMxContrace(gen_data=gen_data, node_data=node_data, price_data=p_data, contract_data=con_data,
                                 coe_data=e_data, lv_price=lv_price, other_price=o_data, opt_date=opt_date,
                                points=points, limit_num=limit_num, return_url=return_url)
        result = model.get_result(json=True)
        self.write(result)