#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2023/10/20 17:52
# <AUTHOR> Darlene
from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.mengxi.price_mx_provinces import PredProvinces


class PriceProvincesHandlerMx(RequestHandlerBase):
    def put(self):
        params = j2o(self.request.body.decode())
        data = params.get('data')
        pre_start = params.get('pre_start')
        pre_days = params.get('pre_days', 1)
        point_day = params.get('point_day', 96)
        cap = params.get('max_price', 5180)
        floor = params.get('min_pirce', 0)
        pred = PredProvinces(data, pre_start, pre_days, point_day, cap, floor)
        result = pred.predict()
        self.write(result)
