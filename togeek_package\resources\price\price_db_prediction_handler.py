# -*- coding: utf-8 -*-

from concurrent.futures import ThreadPoolExecutor
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.price.dongbei.price_db_forest_pre import *
from tornado import gen, concurrent
from tglibs.easy_json import j2o

class ElPricePredictionValueEvalHandlerDB(RequestHandlerBase):
    executor = ThreadPoolExecutor()

    @gen.coroutine
    def put(self):
        data = j2o(self.request.body.decode())

        province = data.get('province', '东北')
        jiedian_type = data.get('jiedian_type', '统一结算点')
        date_list_yuce = data['date_list_yuce']
        all_data = data['all_data']
        days = data.get('days', 30)
        n_est = data.get('n_est', 100)
        min_value = data.get('min_value', 0)
        max_value = data.get('max_value', 1500)
        threshold_value = data.get('threshold_value', None)
        quantile_night = data.get('quantile_night', 0.5)
        quantile_day = data.get('quantile_day', 0.75)
        special_sign = data.get('special_sign', 0)
        special_days = data.get('special_days', [])
        r = yield self.predict(province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value,
                               threshold_value, quantile_night, quantile_day, special_sign, special_days)

        self.write(r)

    # def write_error(self, status_code, **kwargs):
    #     self.write("Gosh darnit, user! You caused a %d error." % status_code)

    @concurrent.run_on_executor
    def predict(self, province, jiedian_type, date_list_yuce, days, all_data, n_est, min_value, max_value,
                threshold_value, quantile_night, quantile_day, special_sign, special_days):
        elpd_val = ElPriceValueDataset(all_data, province, date_list_yuce, special_sign,
                                       special_days, jiedian_type, threshold_value, quantile_night, quantile_day)
        result = elpd_val.predict_day_price(days=days, n_est=n_est, min_value=min_value, max_value=max_value)

        return result.to_dict()
