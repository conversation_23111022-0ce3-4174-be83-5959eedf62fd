# !/usr/bin/env python
# -*- coding:utf-8 -*-

"""
# Author     : Shone
# Date       : 2022-07-25 13:26:12
# Description: 距离计算
"""

import pandas as pd
from scipy.spatial import distance
from scipy.stats import pearsonr
import logging

logger = logging.getLogger()


class DistanceCalculation:

    def __init__(self, data_history, data_object):
        # 写入log
        logger.info("---------------------------------------------------------")
        logger.info("--------------------------计算距离-------------------------")
        logger.info(f"data_history\n: {data_history}\n, data_object\n: {data_object}")
        self.data_history = self.process_data(data_history)
        self.data_object = self.process_data(data_object)
        self.v = self.data_object.mean(axis=1).values

    @staticmethod
    def process_data(data):
        """
        处理数据，以 DataFrame 形式返回
        """
        # 读取数据
        data = pd.DataFrame.from_dict(data, orient='index', columns=['value'])
        data['date'] = data.index.astype(str).map(lambda s: s.split(' ')[0]).values
        data['time'] = data.index.astype(str).map(lambda s: s.split(' ')[1]).values
        df = data.pivot(index='time', columns='date', values='value')

        # 空值填充
        df = df.fillna(method='ffill').fillna(method='bfill')

        return df

    def calc_distance(self):
        """
        计算距离：[余弦距离, 曼哈顿距离, 欧式距离, 皮尔逊相关系数]
        """
        date_list_history = self.data_history.columns.to_list()
        result = {}
        for date in date_list_history:
            distance_list = []
            u = self.data_history[date].values
            # 余弦距离
            distance_list.append(distance.cosine(u, self.v))
            # 曼哈顿距离
            distance_list.append(distance.cityblock(u, self.v))
            # 欧氏距离
            distance_list.append(distance.euclidean(u, self.v))
            # 皮尔逊距离
            distance_list.append(pearsonr(u, self.v)[0])
            result[date] = distance_list

        # 将结果写入log
        logger.info("---------------------------距离计算完成--------------------------")
        logger.info(f'result:\n{result}')
        return result


if __name__ == '__main__':
    import json
    with open("data/data_history.json", 'r') as f:
        data_history_test = json.loads(f.read())
    with open("data/data_object.json", 'r') as f:
        data_object_test = json.loads(f.read())

    model_distance = DistanceCalculation(data_history=data_history_test, data_object=data_object_test)
    res = model_distance.calc_distance()
    print(res)
