# -*- coding:utf-8 -*-
"""
Author      :   Shone
Date        :   2023-08-25 13:33:41
Description :   山东售电侧申报策略
"""

import numpy as np
import pandas as pd
from datetime import timedelta
from chinese_calendar import is_workday
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import logging
import warnings

logger = logging.getLogger()
warnings.filterwarnings("ignore")


class DeclarationStrategySD:

    def __init__(self, data, run_date, time_point=96, days_train=30, scale=0.2, threshold_proba=0.6):
        """
        山东售电侧申报策略
        :param data: 传入数据，json格式
        :param run_date: 目标日期
        :param time_point: 时点数量
        :param days_train: 训练数据天数
        :param scale: 最大波动比率
        :param threshold_proba: 概率阈值，只有预测概率大于该值时，才进行调整
        """

        logger.info("---------- 开始运行：山东售电侧申报策略模型 -----------")

        self.data = data                # 边界条件数据
        self.run_date = run_date        # 目标日期, 明日日期
        self.time_point = time_point    # 传入数据时点数量
        self.days_train = days_train    # 训练数据天数
        self.scale = scale              # 最大波动比率
        self.threshold_proba = threshold_proba  # 概率阈值
        logger.info(f"目标日期: {self.run_date}")

        # 数据校验不通过返回提示r
        self.message_error = ""

        # 日期相关参数
        self.d = str(pd.to_datetime(run_date) - timedelta(1)).split(" ")[0]      # D
        self.d_1 = str(pd.to_datetime(run_date) - timedelta(2)).split(" ")[0]    # D-1, 训练数据结束日期

    @staticmethod
    def create_ts_feature(df):
        """
        时间序列日期特征构造
        :param df: 必须包含字段: "date_time"
        :return: df with index "date_time"
        """
        # 将 date_time 设置为索引, 并且转换为日期类型，
        if "date_time" in df.columns:
            df = df.set_index("date_time")
        df.index = pd.to_datetime(df.index)

        # 添加类别特征
        df["hour"] = df.index.hour
        df['dayofweek'] = df.index.dayofweek  # 星期, 0表示周一, 6表示周日
        df['is_weekend'] = df.dayofweek.apply(lambda x: 1 if x > 5 else 0)  # 是否周末, 1表示为周末，0表示周内
        df["is_workday"] = df.index.map(lambda x: is_workday(x) * 1).values

        # 添加 sin/cos 特征
        df["hour_sin"] = np.sin(2 * np.pi * (df["hour"] / 24))
        df["hour_cos"] = np.cos(2 * np.pi * (df["hour"] / 24))
        df["dayofweek_sin"] = np.sin(2 * np.pi * (df["dayofweek"] / 7))
        df["dayofweek_cos"] = np.cos(2 * np.pi * (df["dayofweek"] / 7))

        # # 类别特征转换
        # cat_cols = ["hour", "dayofweek", "is_weekend", "is_workday"]
        # df[cat_cols] = df[cat_cols].astype("category")

        # 转换为str类型
        df.index = df.index.astype(str)

        return df

    def data_process(self):
        """
        数据处理
        数据校验 & 特征工程 & 划分数据集 & 标准化
        :return: X_train_std, y_train, X_pred_std, df_pred
        """
        # 1. 数据读取 & 筛选
        logger.info(f"数据读取 & 筛选 ... ")
        data = pd.DataFrame.from_dict(self.data, orient="index").T
        if "date_time" in data.columns:
            data.set_index("date_time", inplace=True)
        data["date"] = data.index.map(lambda s: s.split(" ")[0])

        # 筛选数据，合计 (days_train + 2) 天的数据
        # 2表示D和D+1这两天；D日无实时价格，丢弃；D+1用作预测，
        date_start_train = str(pd.to_datetime(self.run_date) - timedelta(self.days_train + 1)).split(" ")[0]  # 训练数据起始日期
        data = data[(data["date"] >= date_start_train) & (data["date"] <= self.run_date)]

        # 2. 数据校验：时刻点数校验 + 空值校验
        logger.info(f"数据校验 ... ")
        cols_features = [
            '日前直调负荷', '日前风电总加', '日前光伏总加', '日前联络线受电负荷', '日前核电总加',
            '日前自备机组总加', '日前地方电厂发电总加', '日前电价', '实时电价'
        ]
        date_list = pd.date_range(date_start_train, self.run_date).astype(str).values
        for date_ in date_list:
            tmp = data[data["date"] == date_]
            for col in cols_features:
                # 跳过明日的日前价格
                if (date_ == self.run_date) & (col == "日前电价"):
                    # print(f"数据校验忽略：{date_}, {col}")
                    continue

                # 跳过今日和明日的实时电价
                if (date_ >= self.d) & (col == "实时电价"):
                    # print(f"数据校验忽略：{date_}, {col}")
                    continue

                # 时刻点数校验
                num_point = tmp.shape[0]
                if num_point != self.time_point:
                    self.message_error += f"{date_}, {col}, 应为{self.time_point}条数据，实为{num_point}; "

                # 空值检查
                num_null = tmp[col].isnull().sum()
                if num_null != 0:
                    self.message_error += f"{date_}, {col}, 存在{num_null}个空值；"

        # 3. 特征工程
        logger.info(f"特征工程 ... ")
        data['日前竞价空间'] = data['日前直调负荷'] - data[['日前联络线受电负荷', '日前风电总加', '日前光伏总加', '日前核电总加', '日前自备机组总加', '日前地方电厂发电总加']].sum(axis=1)
        data["日前>实时"] = data.apply(lambda s: 1 if s['日前电价'] > s['实时电价'] else 0, axis=1)
        df = self.create_ts_feature(data)

        # 4. 划分数据集
        logger.info(f"划分数据集 ... ")
        data_train = df[(df["date"] >= date_start_train) & (df["date"] <= self.d_1)]    # 选择D_31 ~ D_1 之间30天的数据作为训练集
        data_train = data_train.dropna()
        data_pred = df[df["date"] == self.run_date]
        target = "日前>实时"
        features = [
            '日前竞价空间', '日前直调负荷', '日前风电总加', '日前光伏总加', '日前联络线受电负荷', '日前核电总加',
            '日前自备机组总加', '日前地方电厂发电总加', 'dayofweek', 'is_weekend', 'is_workday',
            'hour_sin', 'hour_cos', 'dayofweek_sin', 'dayofweek_cos',
        ]
        X_train = data_train[features]
        y_train = data_train[target].values
        X_pred = data_pred[features]
        df_pred = data_pred[[target]]

        # 5. 标准化
        logger.info(f"标准化 ... ")
        scaler = StandardScaler().fit(X_train)  # fit only on training data
        X_train_std = scaler.transform(X_train)
        X_pred_std = scaler.transform(X_pred)

        return X_train_std, y_train, X_pred_std, df_pred

    def predict(self):
        """
        模型训练 & 预测 & 输出
        :return:
        """
        # 1. 构造输出dict
        res_dict = {
            "message_error": {},
            "pred": {},
            "prob": {},
            "declaration": {},
        }

        # 2. 数据处理
        X_train_std, y_train, X_pred_std, df_pred = self.data_process()

        # 若数据校验不通过，直接返回
        if len(self.message_error) != 0:
            res_dict["message_error"] = self.message_error
            # return res_dict

        # 3. 价差方向预测
        rfc = RandomForestClassifier(n_estimators=100, max_depth=8, random_state=8099)
        rfc.fit(X_train_std, y_train)
        df_pred["pred_diff"] = rfc.predict(X_pred_std)  # 价差方向预测结果保存
        df_pred["prob"] = rfc.predict_proba(X_pred_std).max(axis=1)     # 当前预测结果的概率

        # 4. 申报策略
        # 预测结果为1，表示预测日前>实时，售电侧买方申报下调，从实时市场多买入
        df_pred["declaration"] = df_pred["pred_diff"].map(lambda s: 1-self.scale if s == 1 else self.scale+1)
        df_pred.loc[df_pred["prob"] < self.threshold_proba, "declaration"] = 1      # 概率值小于给定阈值时，不做调整

        # 5. 结果输出
        res_dict["pred"] = df_pred["pred_diff"].to_dict()
        res_dict["prob"] = df_pred["prob"].to_dict()
        res_dict["declaration"] = df_pred["declaration"].to_dict()
        logger.info("---------- 运行完成：山东售电侧申报策略模型 -----------")

        return res_dict


if __name__ == '__main__':
    date_pred = "2023-08-22"
    path_json = rf"F:\ToGeek\data_research\售电侧申报策略_山东\2023-08-24_zl\data_input\data_demo_{date_pred}.json"
    import json
    with open(path_json, "r") as f:
        data_dict = json.loads(f.read())

    m = DeclarationStrategySD(data=data_dict,
                              run_date=date_pred,
                              time_point=96,
                              days_train=30,
                              scale=0.2,
                              threshold_proba=0.6)
    print(m.predict())
