# -*- coding: utf-8 -*-
"""
Author : lmm
Time : 2024/02/21 09:06
Description :  河北建投：求电费最小情况下的月竞电量
      目标函数：电费 = 基本电费 + 回收电费（是24个时刻的和）
              基本电费 = 月竞电量总和 * 交易电价 + 月持仓电量 * 持仓电价 + （实际用电量 - 持仓电量 - 月竞电量总和）* 日前电价

              回收电费 = 超额电费 或 差额电费（当偏差回收电价 < 日前电价 时， 回收电费 = 超额电费；当偏差回收电价 > 日前电价 时， 回收电费 = 差额电费）
              超额电费 = 超额电量 * 超额电价
              差额电费 = 差额电量 * 差额电价
              超额电量 = 合约电量 - 实际用电量 * 上限系数1.1    <=0  => 0 （超额电量 < 0时，置为0）
              差额电量 = 实际用电量 *下限系数0.9 - 合约电量   <=0  => 0 （差额电量 < 0时，置为0）
              超额电价 = （日前电价 - 偏差回收电价） * 系数1.05
              差额电价 = （偏差回收电价 - 日前电价）* 系数 1.05

      约束条件：月竞电量 >= 0
              月竞电量总和 + 月持仓电量 >= 月实际用电量 * 0.8（针对24个时刻的每个时刻都满足）
              月竞电量总和 + 月持仓电量 <= 月实际用电量 * 1.2（针对24个时刻的每个时刻都满足）
      其他知识：合约电价 = (月持仓电量 * 持仓电价 + 月竞电量总和 * 交易电价） / (月持仓电量 + 月竞电量总和)
              合约电量 = 月竞电量总和 + 月持仓电量
      注1： 回收电费是大于0的，基本电费可以大于0，也可以小于0
      注2：月竞电量总和 = 各个时段的月竞电量的线性组合。例如：4月份0点的月竞电量总和 = T3/ 8 + T6/24；

      求解：定义决策变量：月竞电量：T1, T2, T3, T4, T5, T6
         注：在计算回收电费时，回收电费 = 超额电费 + 差额电费，将两部分写到一起，根据abs函数，当合约电价 > 日前电价 时，选择超额电费，此时差额电费应为0，
            差额电费的计算为：差额电费 = 差额电量 * （abs(差额电价） + 差额电价）/ 2（此时差额电价小于0， abs(差额电价） + 差额电价）= 0，也就是回收电费只考虑了超额电费，没有考虑差额电费）
"""


import pandas as pd
import pyomo.environ as pyo
import warnings
import logging
warnings.filterwarnings("ignore")
logger = logging.getLogger()


class OptimizeCost:

    def __init__(self, data, trading_price, limit_ratio=0.4, upper_ratio=1.5, month=4, basic_cost=False, debug=False):
        logger.info("---------------------- START: 河北建投电费最小 --------------------------")
        self.data = data  # 数据, json格式
        self.trading_price = trading_price   # 交易电价
        self.limit_ratio = limit_ratio     # 月实际用电量的下限比例
        self.upper_ration = upper_ratio    # 月实际用电量的上限比例
        self.month = month    # 月份
        self.basic_cost = basic_cost  # basic_cost为True时，计算基本电费；否则，计算基本电费+回收电费
        self.debug = debug

    def get_month_hours(self, month):
        """获取不同月份的尖峰平谷时段的小时
        0 : T1，1 : T2， 2 : T3， 3 : T4， 4 : T5， 5 : T6
        """

        if month in [12, 1, 2]:
            dict_month_hour = {0: [17, 18], 1: [16, 19, 20, 21, 22, 23], 2: [0, 15, 6, 7, 8, 9, 10, 11], 3: [1, 2, 3, 4, 5, 12, 13, 14], 4: [17, 18, 16, 19, 20, 21, 22, 23], 5: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}
        elif month in [6, 7, 8]:
            dict_month_hour = {0: [19, 20, 21], 1: [15, 16, 17, 18, 22], 2: [8, 9, 10, 11, 12, 13, 14, 23], 3: [0, 1, 2, 3, 4, 5, 6, 7], 4: [19, 20, 21, 15, 16, 17, 18, 22], 5: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}
        else:
            dict_month_hour = {0: [], 1: [16, 17, 18, 19, 20, 21, 22, 23], 2: [0, 6, 7, 8, 9, 10, 11, 15], 3: [1, 2, 3, 4, 5, 12, 13, 14], 4: [16, 17, 18, 19, 20, 21, 22, 23], 5: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}

        return dict_month_hour

    def get_hour_period(self, hour):
        """ 根据传入的hour，判断hour位于的时段, 可能位于一个时段，也可能位于多个时段"""

        month = self.month
        dict_month_hour = self.get_month_hours(month)
        keys = []
        for key, val in dict_month_hour.items():
            if hour in val:
                keys.append(key)
        return dict_month_hour, keys

    def model_pyomo(self, df):
        """ 建立模型  """

        # 1. 全局参数计算
        holding_power = df["holding_power"].values.tolist()  # 月持仓电量
        holding_price = df["holding_price"].values.tolist()  # 持仓电价
        recovery_price = df["recovery_price"].values.tolist()  # 偏差回收电价
        ahead_price = df["ahead_price"].values.tolist()  # 日前电价
        real_power = df["real_power"].values.tolist()  # 实际用电量

        excess_price = [(ahead_price[i] - recovery_price[i]) * 1.05 for i in range(len(ahead_price))]
        difference_price = [(recovery_price[i] - ahead_price[i]) * 1.05 for i in range(len(ahead_price))]

        excess_price = [(abs(excess_price[i]) + excess_price[i]) / 2 for i in range(len(excess_price))]
        difference_price = [(abs(difference_price[i]) + difference_price[i]) / 2 for i in range(len(excess_price))]

        # 2. 创建模型
        model = pyo.ConcreteModel()

        # 3. 添加索引集合
        model.idx = pyo.RangeSet(0, 23)    # 首尾均包含，24个小时
        # 时段的索引
        model.id = pyo.RangeSet(0, 5)    # 首尾均包含，6个时段

        # 5. 添加决策变量
        # 5.0 决策变量0：月竞电量，6个时段
        model.bid_power = pyo.Var(model.id, domain=pyo.NonNegativeReals)
        # 5.1 决策变量1：月竞电量，每个小时由6个时段组成的
        model.bid_powers = pyo.Var(model.idx, domain=pyo.NonNegativeReals)
        # 5.2 添加决策变量2:超额电量
        model.excess_power_X = pyo.Var(model.idx)
        model.excess_power_absX = pyo.Var(model.idx)
        model.excess_power_negX = pyo.Var(model.idx)
        model.excess_power_posX = pyo.Var(model.idx)
        model.excess_power_y = pyo.Var(model.idx, domain=pyo.Binary, bounds=(0, 1))
        # 5.3 添加决策变量3:差额电量
        model.difference_power_X = pyo.Var(model.idx)
        model.difference_power_absX = pyo.Var(model.idx)
        model.difference_power_negX = pyo.Var(model.idx)
        model.difference_power_posX = pyo.Var(model.idx)
        model.difference_power_y = pyo.Var(model.idx, domain=pyo.Binary, bounds=(0, 1))

        # 6. 添加目标函数
        def obj_rule(model):
            """ 定义目标函数
            总费用 = 基本电费 + 回收电费
            基本电费 = 月竞电量总和 * 交易电价 + 月持仓电量 * 持仓电价 + （实际用电量 - 持仓电量 - 月竞电量总和）* 日前电价
            回收电费 = 超额电费 + 差额电费
            超额电费 = 超额电量 * 超额电价
            差额电费 = 差额电量 * 差额电价
            """

            trading_price = self.trading_price
            basic_cost1 = 0
            for i in range(24):
                dict_month_hour, keys = self.get_hour_period(i)
                for key in keys:
                    basic_cost1 += model.bid_power[key] / len(dict_month_hour[key]) * trading_price[str(key)]
            basic_cost2 = sum([holding_power[i] * holding_price[i] + (real_power[i] - holding_power[i] - model.bid_powers[i]) * ahead_price[i] for i in model.idx])  # 基本收益
            basic_cost = basic_cost1 + basic_cost2
            excess_cost = sum([(model.excess_power_absX[i] + model.excess_power_X[i]) / 2 * excess_price[i] for i in model.idx])    # 超额电费
            difference_cost = sum([(model.difference_power_absX[i] + model.difference_power_X[i]) / 2 * difference_price[i] for i in model.idx])    # 差额电费

            # 总费用
            if self.basic_cost == True:
                cost = basic_cost
            else:
                cost = basic_cost + excess_cost + difference_cost

            return cost / 10000

        model.obj = pyo.Objective(rule=obj_rule, sense=pyo.minimize)

        # 7. 添加约束条件
        # 7.0 约束条件0：每个小时月竞电量的计算
        def const_rule0(model, i):
            """ 定义约束条件0 """

            dict_month_hour, keys = self.get_hour_period(i)
            bid_powers = 0
            for key in keys:
                bid_powers += model.bid_power[key] / len(dict_month_hour[key])
            return model.bid_powers[i] == bid_powers
        model.constraint0 = pyo.Constraint(model.idx, rule=const_rule0)

        # 7.1 约束条件1：超额电量计算
        def const_rule1(model, i):
            """ 定义约束条件1 """
            return model.excess_power_X[i] == model.bid_powers[i] + holding_power[i] - real_power[i] * 1.1
        model.constraint1 = pyo.Constraint(model.idx, rule=const_rule1)

        # 7.2 约束条件2：差额电量计算
        def const_rule2(model, i):
            """ 定义约束条件2 """
            return model.difference_power_X[i] == real_power[i] * 0.9 - model.bid_powers[i] - holding_power[i]
        model.constraint2 = pyo.Constraint(model.idx, rule=const_rule2)

        # 7.3 约束条件3：持仓下限
        def const_rule3(model, i):
            """ 定义约束条件1 """
            return model.bid_powers[i] + holding_power[i] >= real_power[i] * self.limit_ratio
        model.constraint3 = pyo.Constraint(model.idx, rule=const_rule3)

        # 7.4 约束条件4：持仓上线
        def const_rule4(model, i):
            """ 定义约束条件1 """
            return model.bid_powers[i] + holding_power[i] <= real_power[i] * self.upper_ration
        model.constraint4 = pyo.Constraint(model.idx, rule=const_rule4)

        # 7.5 约束条件5：其他月份的T1的月竞电量为0
        def const_rule5(model):
            """ 定义约束条件5 """
            month = self.month
            if month in [3, 4, 5, 9, 10, 11]:
                return model.bid_power[0] == 0.0
            else:
                return model.bid_power[0] >= 0.0
        model.constraint5 = pyo.Constraint(rule=const_rule5)

        # 7.6 约束条件6：其他月份的T2的月竞电量为0
        def const_rule6(model):
            """ 定义约束条件6 """
            month = self.month
            if month in [3, 4, 5, 9, 10, 11]:
                return model.bid_power[1] == 0.0
            else:
                return model.bid_power[1] >= 0.0
        model.constraint6 = pyo.Constraint(rule=const_rule6)

        # 7.7 约束条件：求解器不理解abs，通过转换替代abs
        model.cons = pyo.ConstraintList()
        M = 10000000

        for i in model.idx:
            model.cons.add(model.excess_power_absX[i] == model.excess_power_negX[i] + model.excess_power_posX[i])
            model.cons.add(model.excess_power_negX[i] <= M * model.excess_power_y[i])
            model.cons.add(model.excess_power_posX[i] <= M * (1 - model.excess_power_y[i]))
            model.cons.add(model.excess_power_X[i] == model.excess_power_posX[i] - model.excess_power_negX[i])
            model.cons.add(model.excess_power_posX[i] >= 0)
            model.cons.add(model.excess_power_negX[i] >= 0)

            model.cons.add(
                model.difference_power_absX[i] == model.difference_power_negX[i] + model.difference_power_posX[i])
            model.cons.add(model.difference_power_negX[i] <= M * model.difference_power_y[i])
            model.cons.add(model.difference_power_posX[i] <= M * (1 - model.difference_power_y[i]))
            model.cons.add(
                model.difference_power_X[i] == model.difference_power_posX[i] - model.difference_power_negX[i])
            model.cons.add(model.difference_power_posX[i] >= 0)
            model.cons.add(model.difference_power_negX[i] >= 0)

        for i in model.id:
            model.cons.add(model.bid_power[i] >= 0)

        for i in model.idx:
            model.cons.add(model.bid_powers[i] >= 0)

        return model

    def optimize(self):

        data = pd.DataFrame(self.data)

        # 优化问题建模
        logger.info("start: 建模 ... ")
        model = self.model_pyomo(df=data)

        # 调用求解器ipopt求解
        logger.info("start: 优化求解 ... ")
        opt = pyo.SolverFactory("cplex", executable=r"D:\cplex\bin\x64_win64\cplex.exe")    # 本地cplex路径
        # opt = pyo.SolverFactory("cplex", executable=r"/opt/ibm/ILOG/CPLEX_Studio1210/cplex/bin/x86-64_linux/cplex")     # 镜像内cplex绝对路径
        solution = opt.solve(model)

        # 最优解 & 对应参数提取
        cost = model.obj()  # 目标日期收入(元)
        bid_power = [pyo.value(model.bid_power[i]) for i in range(0, 6)]
        bid_powers = [pyo.value(model.bid_powers[i]) for i in range(0, 24)]

        # 模型输出结果整理
        data_out_dict = {}
        if solution['Solver'][0]['Status'] == 'ok':
            data_out_dict["message"] = "求解成功"
            data_out_dict["cost"] = cost
            data_out_dict["bid_power"] = {"T1": round(bid_power[0], 4), "T2": round(bid_power[1], 4), "T3": round(bid_power[2], 4), "T4": round(bid_power[3], 4), "T5": round(bid_power[4], 4), "T6": round(bid_power[5], 4)}
            # data_out_dict["bid_powers"] = bid_powers
        else:
            data_out_dict["message"] = "求解失败，请重新输入上下限系数"

        logger.info("---------------------- END: 河北建投电费最小  --------------------------")

        return data_out_dict


if __name__ == '__main__':
    path = r"D:\项目\2024\河北\河北建投\20230221_lm\data\data_original\售电竞价策略条件_v2.xlsx"
    data = pd.read_excel(path)

    import json
    price_path = r"D:\项目\2024\河北\河北建投\20230221_lm\data\data_input\trading_price.json"
    with open(price_path, 'r', encoding='UTF-8') as f:
        trading_price = json.loads(f.read())

    m = OptimizeCost(data=data, trading_price=trading_price)

    data_out_dict = m.optimize()
    print(data_out_dict)








