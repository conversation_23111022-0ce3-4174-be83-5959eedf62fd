# -*- coding: utf-8 -*-
# valid license

import re
import pandas as pd
from calendar import monthrange
from tglibs.easy_date_time import DateTimeParser
from json import loads
from prophet import Prophet


class Prediction:
    def __init__(self, elec, pred_months, cap=None, floor=None, include_history=True, flexibility=5,
                 calc_daily_avg=True):
        assert 1 <= flexibility <= 10
        self.calc_daily_avg = calc_daily_avg
        self.params = {'yearly_seasonality': True,
                       'seasonality_mode': 'multiplicative',
                       'changepoint_prior_scale': flexibility / 100}
        self.elec = self._prepare_elec(elec, cap, floor, self.params)
        self.pred_months = pred_months
        self.inc_his = include_history

    def _get_month_last_day(self, day):
        if isinstance(day, str) and re.match('^[0-9]{4}-[0-9]{1,2}$', day):
            day = f'{day}-1'
        p = DateTimeParser(day)
        d = p.datetime.date()
        p.set_date((d.year, d.month, monthrange(d.year, d.month)[1]))
        return p.datetime.date()

    def _prepare_elec(self, elec, cap, floor, params):
        if isinstance(elec, str):
            elec = loads(elec)
        if isinstance(elec, dict):
            elec['ds'] = pd.to_datetime([self._get_month_last_day(day) for day in elec['ds']])
            elec = pd.DataFrame(elec)
        days = pd.DatetimeIndex(elec.ds).day
        if self.calc_daily_avg:
            elec['y'] /= days
        if cap is not None:
            elec['cap'] = cap
            if self.calc_daily_avg:
                elec['cap'] /= days
            if floor is None:
                elec['floor'] = floor
                if self.calc_daily_avg:
                    elec['floor'] /= days
        if 'cap' in elec:
            params['growth'] = 'logistic'
        return elec

    def predict(self, to_json=False):
        model = Prophet(**self.params)
        model.fit(self.elec)
        future = model.make_future_dataframe(periods=self.pred_months, freq='M', include_history=self.inc_his)
        result = model.predict(future)[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]
        if self.calc_daily_avg:
            days = pd.DatetimeIndex(result.ds).day
            result['yhat'] *= days
            result['yhat_lower'] *= days
            result['yhat_upper'] *= days
        if to_json:
            result = {'ds': result.ds.astype(str).tolist(),
                      'yhat': result.yhat.tolist(),
                      'yhat_lower': result.yhat_lower.tolist(),
                      'yhat_upper': result.yhat_upper.tolist()}
        return result
